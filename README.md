# welab-crm-operate

这是 WeLab 的 CRM（客户关系管理）运营系统，专门设计用于处理客户服务运营及相关功能。

## 项目结构

项目分为三个主要模块：

1. **welab-crm-operate-api**：包含 API 定义和 DTO（数据传输对象）
2. **welab-crm-operate-core**：包含核心业务逻辑、数据库配置和服务实现
3. **welab-crm-operate-web**：包含 Web 相关组件和应用程序入口点

## 主要功能

- 客户投诉管理
- 呼叫中心运营处理
- 工单管理
- 员工管理和排班
- 数据报告和分析
- 通过 Dubbo 与第三方服务集成
- 使用 RabbitMQ 进行异步消息处理
- 使用 Elastic Job 进行定时任务调度

## 技术栈

- Java
- Spring 框架
- MyBatis-Plus
- Dubbo 用于服务通信
- RabbitMQ 用于消息传递
- Elastic Job 用于任务调度
- MySQL 用于数据存储
- Logback 用于日志记录

## 快速开始

### 前置条件

- Java 8 或更高版本
- Maven 3.6+
- MySQL 5.7+
- RabbitMQ
- Dubbo 注册中心（Zookeeper/Nacos）

### 安装

1. 克隆代码仓库
2. 在 `welab-crm-operate-core/src/main/resources/application-datasource.xml` 中配置数据库连接
3. 在 `welab-crm-operate-core/src/main/resources/application-rabbitmq.xml` 中配置 RabbitMQ
4. 在 `welab-crm-operate-core/src/main/resources/dubbo/` 中配置 Dubbo
5. 构建项目：
   ```bash
   mvn clean install
   ```

### 配置

主要配置文件位于 `welab-crm-operate-core/src/main/resources/`：
- `applicationContext.xml` - 主要的 Spring 配置
- `application-datasource.xml` - 数据库连接设置
- `application-rabbitmq.xml` - RabbitMQ 配置
- `dubbo/` - Dubbo 消费者和提供者配置
- `job/elasticJob.xml` - 定时任务配置

### 数据库迁移

数据库迁移脚本位于 `welab-crm-operate-core/src/main/resources/db/migration/`

## 文档

- API 文档：参见 喵达API项目 接口文档.md
- 自动回复查询逻辑：`welab-crm-operate-core/src/main/resources/docs/miaoda-auto-reply-query-logic.md`
- 本地查询指南：`welab-crm-operate-core/src/main/resources/docs/miaoda-local-query-guide.md`

## 贡献

请遵循标准的 Git 工作流程：
1. 从 develop 分支创建功能分支
2. 进行修改
3. 根据需要编写/更新测试
4. 提交 Pull Request 以供审查

## 许可证

该项目为 WeLab 专有，不得在组织外部分发。