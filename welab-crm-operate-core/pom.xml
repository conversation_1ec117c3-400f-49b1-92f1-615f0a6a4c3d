<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.welab</groupId>
    <artifactId>welab-crm-operate</artifactId>
    <version>1.1.1-RELEASE</version>
  </parent>

  <artifactId>welab-crm-operate-core</artifactId>
  <name>welab-crm-operate-core</name>
  <packaging>jar</packaging>
  <url>https://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <dependencies>

    <!-- 依赖的内部jar包 begin -->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-dds</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-operate-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-base-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-base-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-redis-springboot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-lang</artifactId>
    </dependency>
    <!-- 依赖的内部jar包 end -->

    <!-- Spring Boot相关依赖 begin -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <!-- Spring Boot相关依赖 end -->

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt</artifactId>
      <version>0.9.1</version>
    </dependency>

    <!-- Spring 相关依赖-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-websocket</artifactId>
      <version>8.5.15</version>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-recipes</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>

    <!-- mybatis-plus Begin -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
    </dependency>
    <!-- mybatis-plus Begin -->

    <!--加解密 begin-->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-privacy-api</artifactId>
      <version>1.0.8-RELEASE</version>
    </dependency>
    <!--加解密 end-->


    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.7.2</version>
    </dependency>

    <!-- 测试相关依赖 Begin -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- 测试相关依赖 end -->

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-common</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.el</groupId>
      <artifactId>javax.el-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.glassfish.web</groupId>
      <artifactId>javax.el</artifactId>
    </dependency>

    <!-- 支持excel -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans</artifactId>
    </dependency>

    <!--结清文件依赖 -->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>application-center-api</artifactId>
      <version>2.9.11-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-account-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-payment-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.welab.base</groupId>
          <artifactId>welab-springboot-web</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
    </dependency>

    <dependency>
      <groupId>com.dangdang</groupId>
      <artifactId>elastic-job-lite-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dangdang</groupId>
      <artifactId>elastic-job-lite-spring</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-repayment-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-ext-jdk16</artifactId>
      <version>1.45</version>
    </dependency>

    <!--审批相关服务-->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>approval-center-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-loan-procedure-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-logback</artifactId>
      <version>1.0-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>fluent-hc</artifactId>
      <version>4.5.9</version>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>finance-common</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>


    <!-- 图形验证码生成 -->
    <dependency>
      <groupId>com.github.axet</groupId>
      <artifactId>kaptcha</artifactId>
      <version>0.0.9</version>
    </dependency>
  </dependencies>

</project>
