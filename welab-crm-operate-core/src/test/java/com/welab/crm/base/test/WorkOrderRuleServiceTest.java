package com.welab.crm.base.test;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleAdjustReqDTO;
import com.welab.crm.operate.service.WorkOrderRuleService;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/4
 */
public class WorkOrderRuleServiceTest extends SimpleTest {

    @Resource
    private WorkOrderRuleService workOrderRuleService;

    @Test
    public void adjustWorkOrderRuleTest() {
        WorkOrderRuleAdjustReqDTO reqDTO = new WorkOrderRuleAdjustReqDTO();
        reqDTO = JSONObject.parseObject(
            "{\"method\":\"detail\",\"groupCode\":[],\"staffId\":[],\"type\":[],\"orderCase\":[],\"curPage\":1,"
                + "\"pageSize\":10,\"assignList\":[{\"assignGroupCode\":\"zcz\",\"assignNum\":2},{\"assignGroupCode\""
                + ":\"tsz\",\"assignNum\":1},{\"assignGroupCode\":\"clz\",\"assignNum\":1}],\"idList\""
                + ":[\"task683704924810252288\",\"task683704958180134912\",\"task683702906418888704\","
                + "\"task683690673823551488\"]}", WorkOrderRuleAdjustReqDTO.class);
        reqDTO.setOperatorId("39");
        long start = System.currentTimeMillis();
        workOrderRuleService.adjustWorkOrderRule(reqDTO);
        System.out.println("耗时："+(System.currentTimeMillis()-start)+"ms");
    }
}
