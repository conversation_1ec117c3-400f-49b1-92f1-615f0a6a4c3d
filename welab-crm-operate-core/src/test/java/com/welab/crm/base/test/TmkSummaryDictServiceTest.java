package com.welab.crm.base.test;

import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.operate.dto.dict.tmk.TmkSummaryDictDTO;
import com.welab.crm.operate.service.TmkSummaryDictService;
import com.welab.crm.operate.service.impl.SoftPhoneCallServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/18
 */
public class TmkSummaryDictServiceTest extends SimpleTest {

    /**
     * 为了不启动interview的服务，需要mock dubbo相关的服务，否则无法直接运行测试
     */
    @MockBean
    private LoanApplicationService loanApplicationService;

    @Autowired
    private TmkSummaryDictService tmkSummaryDictService;

    @Autowired
    private SoftPhoneCallServiceImpl softPhoneCallService;

    @Test
    public void addTest() {
        TmkSummaryDictDTO summary = new TmkSummaryDictDTO();
        summary.setSummaryContent("营销小结内容插入测试99");
        summary.setBusinessType("income");
        summary.setSort(1);
        summary.setResultCode("200");
        tmkSummaryDictService.addTmkSummaryDict("spencer_insert", summary);
    }

    @Test
    public void updateTest() {
        TmkSummaryDictDTO summary = new TmkSummaryDictDTO();
        summary.setSummaryCode("su0001");
        summary.setSummaryContent("营销小结内容插入测试993");
        summary.setBusinessType("income");
        summary.setSort(2);
        summary.setResultCode("200");
        tmkSummaryDictService.updateTmkSummaryDict("spencer_update", summary);
    }

    @Test
    public void addBatchTest() {
        TmkSummaryDictDTO summary = new TmkSummaryDictDTO();
        summary.setSummaryContent("营销小结内容多选批量插入");
        summary.setBusinessType("income,creditline,superVip,qjgg");
        summary.setSort(1);
        summary.setResultCode("300");
        tmkSummaryDictService.addTmkSummaryDict("spencer_batch_insert", summary);
    }

    @Test
    public void testQueryOnlineSystem(){
        softPhoneCallService.queryOnlineSummary(new ArrayList<>(),24877117L);
    }
}
