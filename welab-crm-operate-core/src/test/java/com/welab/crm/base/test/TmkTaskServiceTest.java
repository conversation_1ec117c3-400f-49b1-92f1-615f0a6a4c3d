package com.welab.crm.base.test;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.constant.TmkTaskTypeConstant;
import com.welab.crm.operate.dto.telemarketing.TmkAppointReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReportBaseReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.mapper.InPhoneLoginInfoMapper;
import com.welab.crm.operate.mapper.TmkLoanInviteMapper;
import com.welab.crm.operate.service.impl.TmkRuleServiceImpl;
import com.welab.crm.operate.service.impl.TmkTaskServiceImpl;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.telemarketing.TmkStaffWorkInfo;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Description: 电销服务测试
 * @date 2022/2/22 15:35
 */
public class TmkTaskServiceTest extends SimpleTest{

    @Resource
    private TmkTaskServiceImpl tmkTaskService;

    @Resource
    private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;

    @Resource
    private TmkRuleServiceImpl tmkRuleService;


    @Test
    public void testQueryTmkTask(){
        TmkReqDTO tmkReqDTO = new TmkReqDTO();
        tmkReqDTO.setTmkType(TmkTaskTypeConstant.LOAN);
//        tmkReqDTO.setLastCalledAtStart(DateUtils.parseDate("2022-03-02 12:30:00", "yyyy-MM-dd HH:mm:ss"));
//        tmkReqDTO.setLastCalledAtEnd(DateUtils.parseDate("2022-03-02 13:00:00", "yyyy-MM-dd HH:mm:ss"));
        tmkReqDTO.setCurPage(1);
        tmkReqDTO.setPageSize(10);
        tmkReqDTO.setMobile("13402371341");
        System.out.println("JSON.toJSONString(tmkReqDTO) = " + JSON.toJSONString(tmkReqDTO));
        Page<TmkTaskDetailVO> tmkTaskDetailVOS = tmkTaskService.queryTmkTask(tmkReqDTO);
        System.out.println("tmkTaskDetailVOS.getRecords() = " + tmkTaskDetailVOS.getRecords());

    }

    @Test
    public void testTmkTaskAppoint(){
        TmkAppointReqDTO reqDTO = new TmkAppointReqDTO();
        reqDTO.setTmkTaskId("credit703921889294155780");
        reqDTO.setAppointTime("2022-03-01 14:00:00");
        reqDTO.setComment("test test");
        tmkTaskService.appoint(reqDTO);
    }

    @Test
    public void testTmkReport(){
        TmkReportBaseReqDTO dto = new TmkReportBaseReqDTO();
        dto.setStartTime("2022-03-10 00:00:00");
        dto.setEndTime("2022-03-10 23:00:00");
        dto.setCnos("6501,6144");
        List<TmkStaffWorkInfo> tmkStaffWorkInfos = tmkTaskService.queryTmkCallInfo(dto);
        System.out.println("tmkStaffWorkInfos = " + tmkStaffWorkInfos);
    }

    @Test
    public void testQueryLoginInfoByStaffId(){
        List<String> staffIdList = Arrays.asList("2,30,40".split(","));
        System.out.println("inPhoneLoginInfoMapper.queryPhoneLoginInfoByStaffId(staffIdList) = " +
                inPhoneLoginInfoMapper.queryCnoListByStaffId(staffIdList));
    }

    @Test
    public void testTransformReport(){
        TmkTransformReportReqDTO dto = new TmkTransformReportReqDTO();
        dto.setTmkType("wallet");
        dto.setStartTime("2022-03-01 00:00:00");
        dto.setEndTime("2022-03-29 00:00:00");
        System.out.println("tmkTaskService.queryTmkTransformData(dto) = " + tmkTaskService.queryTmkTransformData(dto));

    }

    @Test
    public void testDaysBetween(){
        Date date = DateUtil.plusDays(new Date(), -14);
        System.out.println("date.toString() = " + date.toString());
        Date date1 = DateUtils.parseDate("2022-03-22 23:00:00", "yyyy-MM-dd HH:mm:ss");
        long daysBetween = DateUtil.getDaysBetween(date, date1);
        System.out.println("daysBetween = " + daysBetween);
    }

    @Test
    public void testPublishNotice(){
//        List<String> list = Arrays.asList("uuid","uuid","loan","loan","credit");
//        tmkRuleService.publishTmkMsg(list,30,"1498187253681352706");

    }



}
