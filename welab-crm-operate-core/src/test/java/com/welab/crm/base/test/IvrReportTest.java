package com.welab.crm.base.test;

import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.service.impl.IvrReportServiceImpl;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/3/10 10:29
 */
public class IvrReportTest extends SimpleTest{

    @Resource
    private IvrReportServiceImpl ivrReportService;
    @Test
    public void test01(){

        IvrReportReqDTO dto = new IvrReportReqDTO();
        dto.setHotline("10100518,4006000799,4006040888");
        dto.setStartTime(DateUtil.stringToDate("2021-03-01 00:00:00"));
        dto.setEndTime(new Date());
        dto.setPeriod("day");
        System.out.println("ivrReportService.ivrShuntStatisticsReport(dto) = " + ivrReportService.ivrShuntStatisticsReport(dto));

    }

    @Test
    public void test02() {
        int size = 8;
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
        for (int i = 0; i < list.size(); i += size) {
            int endIndex = Math.min(list.size(), i + size);
            List<Integer> list1 = list.subList(i, endIndex);
            System.out.println("list1 = " + list1);

        }
    }

}
