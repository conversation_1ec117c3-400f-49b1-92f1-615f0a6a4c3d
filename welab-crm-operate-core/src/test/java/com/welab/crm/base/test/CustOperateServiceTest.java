package com.welab.crm.base.test;

import com.welab.crm.operate.dto.operate.OperateHistoryQueryReqDTO;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.vo.operate.CustHisOperateVO;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/13
 */
public class CustOperateServiceTest extends SimpleTest {

    @Resource
    private CustOperateService custOperateService;

    @Test
    public void queryOperateHistoryTest(){
        OperateHistoryQueryReqDTO reqDTO = new OperateHistoryQueryReqDTO();
        reqDTO.setUserId(2470768);
        List<CustHisOperateVO> list = custOperateService.queryOperateHistory(reqDTO);
        list.forEach(System.err::println);
    }
}
