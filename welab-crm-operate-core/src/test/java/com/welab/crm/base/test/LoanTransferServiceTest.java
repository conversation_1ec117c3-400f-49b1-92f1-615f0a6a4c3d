package com.welab.crm.base.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.operate.domain.LoanTransferAttachment;
import com.welab.crm.operate.dto.loan.LoanTransferDTO;
import com.welab.crm.operate.dto.loan.LoanTransferExcelDTO;
import com.welab.crm.operate.dto.loan.LoanTransferIdDTO;
import com.welab.crm.operate.service.LoanTransferService;
import com.welab.crm.operate.vo.loan.LoanTransferVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/11
 */
@Slf4j
public class LoanTransferServiceTest extends SimpleTest {

    /**
     * 为了不启动interview的服务，需要mock dubbo相关的服务，否则无法直接运行测试
     */
    @MockBean
    private LoanApplicationService loanApplicationService;

    @Autowired
    private LoanTransferService transferService;

    @Test
    public void saveTest() {
        String fileName = "债转结清导入文件名-专用测试";
        ExcelList<LoanTransferExcelDTO> list = new ExcelList<>();
        list.setFileName(fileName);
        LoanTransferExcelDTO dto1 = new LoanTransferExcelDTO();
        LoanTransferExcelDTO dto2 = new LoanTransferExcelDTO();
        dto1.setContractNo("1588965895478555555");
        dto2.setContractNo("1588965895479555555");
        list.add(dto1);
        list.add(dto2);
        transferService.saveContractNoList("spencer", list);
    }

    @Test
    public void queryTest() {
        saveTest();
        LoanTransferDTO dto = new LoanTransferDTO();
        dto.setCurPage(1);
        dto.setPageSize(10);
        dto.setFileName("债转结清导入文件名-专用测试");
        Date lastDay = Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        dto.setStartCreateTime(lastDay);
        dto.setEndCreateTime(new Date());
        Page<LoanTransferVO> voPage = transferService.listTransfer(dto);
        log.info("test result records : {}", voPage.getRecords());
        Assert.assertNotNull(voPage.getRecords().get(0));
    }

    @Test
    public void updateApproveStateTest() {
        LoanTransferIdDTO dto = new LoanTransferIdDTO();
        dto.setApproveState(1);
        dto.setRemark("审批说明");
        dto.setId(Long.valueOf("1493882440290738177"));
        transferService.updateApproveState("spencer_approve", dto);
    }

    @Test
    public void updatePushStateTest() {
        transferService.updatePushState("spencer_update", Long.valueOf("1494148629948555266"));
    }

    @Test
    public void saveAttachmentsTest() {
        String operator = "spencer";
        List<LoanTransferAttachment> list = new ArrayList<>();
        LoanTransferAttachment attachment = new LoanTransferAttachment();
        attachment.setAttachmentName("粮食危机.mobi");
        attachment.setTransferId(Long.valueOf("1495694171705720833"));
        attachment.setCreateUser(operator);
        attachment.setFilePath("439bad39-283c-48ec-9e15-83584d800558粮食危机.mobi");
        list.add(attachment);
        transferService.saveAttachments("spencer", list);
    }

    @Test
    public void resolveUrlDecoderTest() throws UnsupportedEncodingException {
        String fileName="%E5%80%BA%E8%BD%AC%E7%BB%93%E6%B8%85%E5%AF%BC%E5%85%A5%E8%B4%B7%E6%AC%BE%E5%8F%B7.xlsx";
        String decode = URLDecoder.decode(fileName, "UTF-8");
        System.out.println(decode);
    }
}
