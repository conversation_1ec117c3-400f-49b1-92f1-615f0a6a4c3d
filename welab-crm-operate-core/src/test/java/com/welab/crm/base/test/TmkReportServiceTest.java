package com.welab.crm.base.test;

import com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class TmkReportServiceTest extends SimpleTest {

    @Resource
    private TmkReportService tmkReportService;

    @Test
    public void getRedistributedOutboundEfficiencyTest() {
        ReassignOutboundEfficiencyDTO dto = new ReassignOutboundEfficiencyDTO();
        dto.setStartTime("2022-03-01");
        dto.setEndTime("2022-03-22");
        dto.setGroupCode("wddxz");
        dto.setBusinessType("loan");
        List<RedistributedOutboundEfficiencyVO> list = tmkReportService.getReassignOutboundEfficiency(dto);
        list.forEach(System.out::println);
    }
}
