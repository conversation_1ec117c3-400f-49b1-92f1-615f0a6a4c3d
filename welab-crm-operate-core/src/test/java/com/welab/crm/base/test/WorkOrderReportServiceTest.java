package com.welab.crm.base.test;

import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.dto.report.*;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.model.AgentStatistic;
import com.welab.crm.operate.model.StatisticAgentWorkloadDay;
import com.welab.crm.operate.service.WorkOrderReportService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.woReport.*;
import com.welab.xdao.context.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/27
 */
@Slf4j
public class WorkOrderReportServiceTest extends SimpleTest {

    @Resource
    private WorkOrderReportService workOrderReportService;

    @Resource
    private TrUtil trUtil;

    @Test
    public void DateTest() {
        Date start = DateUtil.stringToDate("2021-09-11 00:00:00");
        Date end = DateUtil.stringToDate("2021-10-09 00:00:00");
        // 该方法超过2个月小于3个月返回2
        long interval = DateUtil.containMonths(start, end);
        if (interval >= 3) {
            throw new CrmOperateException("查询时间不能超过3个自然月");
        }
        Date date = DateUtil.stringToDate("2022-03-28");
        System.err.println(DateUtil.dateToString(DateUtils.getLastDayOfWeek(date)));
    }

    @Test
    public void getSummaryTypeTest() {
        ReportSummaryTypeDTO dto = new ReportSummaryTypeDTO();
        dto.setStartTime("2021-12-31 00:00:00");
        dto.setEndTime("2021-12-31 23:59:59");
        //dto.setType("1458710770133348353");
        dto.setFirstType("1458711790687203330");
        Page<ReportSummaryTypeVO> page = workOrderReportService.getSummaryType(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getSummaryComplaintTest() {
        WoReportDTO dto = new WoReportDTO();
        dto.setStartTime("2021-12-28 00:00:00");
        dto.setEndTime("2021-12-28 23:59:59");
        ReportSummaryVO vo = workOrderReportService.getSummaryComplaint(dto);
        System.out.println(vo.toString());
    }

    @Test
    public void getSummaryProductTest() {
        WoReportDTO dto = new WoReportDTO();
        dto.setStartTime("2021-12-28 00:00:00");
        dto.setEndTime("2021-12-28 23:59:59");
        ReportSummaryVO vo = workOrderReportService.getSummaryProduct(dto);
        System.out.println(vo.toString());
    }

    @Test
    public void getSummaryTest() {
        WoReportDTO dto = new WoReportDTO();
        dto.setStartTime("2021-12-28 00:00:00");
        dto.setEndTime("2021-12-28 23:59:59");
        List<ReportSummaryVO> list = workOrderReportService.getSummary(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getDetailsTest() {
        WoReportDTO dto = new WoReportDTO();
        dto.setStartTime("2022-01-21 00:00:00");
        dto.setEndTime("2022-01-21 23:59:59");
        dto.setCurrentPage(1);
        dto.setRowsPerPage(10);
        Long start = System.currentTimeMillis();
        Page<ReportDetailsVO> page = workOrderReportService.getDetails(dto, false);
        System.out.println("takes " + (System.currentTimeMillis() - start) + "ms");
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getAssignmentSummaryTest() {
        ReportAssignmentSummaryDTO dto = new ReportAssignmentSummaryDTO();
        dto.setStartTime("2021-12-31 00:00:00");
        dto.setEndTime("2022-01-07 23:59:59");
        dto.setRuleId("28");
        Page<ReportAssignmentSummaryVO> page = workOrderReportService.getAssignmentSummary(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getAssignmentDetailsTest() {
        WoReportDTO dto = new WoReportDTO();
        dto.setStartTime("2022-01-04 00:00:00");
        dto.setEndTime("2022-01-04 23:59:59");
        Page<ReportAssignmentDetailsVO> page = workOrderReportService.getAssignmentDetails(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void trTest() {
        long start = System.currentTimeMillis();
        List<StatisticAgentWorkloadDay> list = trUtil.reportWorkLoad("2021-01-04", "2022-01-04", 2, 4);
        System.err.println("takes" + (System.currentTimeMillis() - start));
        list.forEach(System.err::println);
    }

    @Test
    public void tr2Test() {
        List<AgentStatistic> list = trUtil.monitorStatistics();
        list.forEach(System.err::println);
    }

    @Test
    public void getOutboundEfficiencySummaryTest() {
        OutboundEfficiencySummaryDTO dto = new OutboundEfficiencySummaryDTO();
        dto.setStartTime("2022-01-04 00:00:00");
        dto.setEndTime("2022-01-21 23:59:59");
        Page<OutboundEfficiencySummaryVO> page = workOrderReportService.getOutboundEfficiencySummary(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getEfficiencySummaryTest() {
        ReportEfficiencySummaryDTO dto = new ReportEfficiencySummaryDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2021-12-31 00:00:00");
        dto.setEndTime("2022-01-30 23:59:59");
        dto.setType("1458710770133348353");
        dto.setGroupCode("gdz");
        Page<ReportEfficiencySummaryVO> page = workOrderReportService.getEfficiencySummary(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getCentralMonitoringTest() {
        CentralMonitoringDTO dto = new CentralMonitoringDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2021-12-31 00:00:00");
        dto.setEndTime("2022-01-10 23:59:59");
        dto.setGroupCode("zcz");
        //dto.setType("1458710770133348353");
        Page<CentralMonitoringVO> page = workOrderReportService.getCentralMonitoring(dto);
        System.out.println("TotalRows=" + page.getTotalRows());
        System.out.println("CurrentPage=" + page.getCurrentPage());
        System.out.println("RowsPerPage=" + page.getRowsPerPage());
        System.out.println("TotalPage=" + page.getTotalPage());
        if (Objects.nonNull(page.getList())) {
            page.getList().forEach(System.out::println);
        }
    }
}
