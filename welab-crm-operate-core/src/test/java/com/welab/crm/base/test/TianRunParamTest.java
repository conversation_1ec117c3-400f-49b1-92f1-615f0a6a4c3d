package com.welab.crm.base.test;

import com.welab.crm.operate.util.Md5Util;
import org.junit.Test;

public class TianRunParamTest {

    /**
     * 可用于生成天润访问参照中的时间戳和电子签名
     */
    @Test
    public void queryAuthTest(){
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        System.out.println(timestamp);
        String sign = Md5Util.md5( "7600088" + timestamp + "1081f81e6c51a8502df8034312d5dce5");
        System.out.println(sign);
    }
}
