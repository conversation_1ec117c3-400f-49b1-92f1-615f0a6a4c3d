package com.welab.crm.base.test;

import com.welab.common.response.Response;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.operate.service.impl.WithholdServiceImpl;
import com.welab.finance.repayment.vo.UserRepaysVO;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Description: 代扣测试
 * @date 2022/4/24 9:50
 */
public class WithholdServiceTest extends SimpleTest{

    @Resource
    private FinanceService financeService;
    @Resource
    private WithholdServiceImpl withholdService;

    @Test
    public void test01(){
////        RepaymentDTO dto = new RepaymentDTO();
////        dto.setRepaymentMode("YD");
////        dto.setApplicationId("20052815570299212225915");
////        dto.setUserId(2415179);
////        dto.setCustomerId(724985325918621696L);
////        Response<RepaymentVO> repayment = withholdService.repayment(dto);
//        System.out.println("repayment = " + repayment);
    }

    @Test
    public void test02(){
        Response<List<UserRepaysVO>> listResponse = financeService
                .queryUserPayByApplicationId("18072710253167978322749");
    }

//    public void test03(){
//        financeService.
//    }

}
