package com.welab.crm.base.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.service.WorkStatusReportService;
import com.welab.crm.operate.vo.woReport.ReportCallInWorkStatusVO;
import com.welab.crm.operate.vo.woReport.ReportStaffEfficiencyVO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/27
 */
@Slf4j
public class WorkStatusReportServiceTest extends SimpleTest {

    @Resource
    private WorkStatusReportService statusReportService;


    @Test
    public void getWorkStatusSummaryTest() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2022-02-14 00:00:00");
        dto.setEndTime("2022-02-22 23:59:59");
        Page<ReportWorkStatusSummaryVO> pageVo = statusReportService.getWorkStatusSummaryPage(dto);
        System.out.println(pageVo);
    }

    @Test
    public void getWorkStatusSummaryListTest() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2022-03-01 00:00:00");
        dto.setEndTime("2022-03-22 23:59:59");
        dto.setGroupCode("KF");
        List<ReportWorkStatusSummaryVO> list = statusReportService.listWorkStatusSummary(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getCallInWorkStatusTest() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2022-03-01 00:00:00");
        dto.setEndTime("2022-03-10 23:59:59");
        Page<ReportCallInWorkStatusVO> pageVo = statusReportService.getCallInWorkStatus(dto);
        System.out.println(pageVo);
    }

    @Test
    public void getCallInWorkStatusListTest() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2022-02-22 00:00:00");
        dto.setEndTime("2022-03-02 23:59:59");
        List<ReportCallInWorkStatusVO> list = statusReportService.getCallInWorkStatusList(dto);
        list.forEach(System.out::println);
    }

    @Test
    public void getStaffEfficiencyTest() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(50);
        dto.setStartTime("2022-03-01 00:00:00");
        dto.setEndTime("2022-03-31 23:59:59");
        dto.setPeriod("day");
        Page<ReportStaffEfficiencyVO> list = statusReportService.getStaffEfficiencyPage(dto);
        System.out.println(list);
    }
}
