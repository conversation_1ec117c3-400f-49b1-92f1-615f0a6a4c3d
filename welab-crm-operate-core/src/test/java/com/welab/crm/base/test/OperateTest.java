package com.welab.crm.base.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.dto.operate.UserInfoModifyReqDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.CustomerService;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.xml.crypto.Data;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: OperateTest
 * @Description:
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2021/11/19 14:43
 */
public class OperateTest extends SimpleTest{

    @Autowired
    private CustOperateService custOperateService;
    @Autowired
    private CustomerService customerService;

    @Test
    public void testBatchInsert(){
        CustHisOperate custHisOperate = new CustHisOperate();
        custHisOperate.setUserId(12345);
        custHisOperate.setStaffId("leon.li");
        custHisOperate.setGroupCode("system");
        custHisOperate.setOperateType(OperateTypeEnum.APPROVAL_URGENT.getCode());
        custHisOperate.setLoanId("12314241424");
        custHisOperate.setComment("test");

        CustHisOperate custHisOperate2 = new CustHisOperate();
        custHisOperate2.setUserId(12345);
        custHisOperate2.setStaffId("leon.li");
        custHisOperate2.setGroupCode("system");
        custHisOperate2.setOperateType(OperateTypeEnum.APPROVAL_URGENT.getCode());
        custHisOperate2.setLoanId("12314241424");
        custHisOperate2.setComment("test");

        List<CustHisOperate> custHisOperates = new ArrayList<>();
        custHisOperates.add(custHisOperate);
        custHisOperates.add(custHisOperate2);

        custOperateService.saveOperateHistoryBatch(custHisOperates);
    }

    @Test
    public void test2(){
        String s = "{\"adminMobile\n"
                + "\":\"13077973938\",\"groupCode\":\"KF\",\"name\":\"位桂芳3\",\"oldName\":\"位桂芳2\",\"staffId\":\"Leon\",\"userId\":2474848}";
        UserInfoModifyReqDTO userInfoModifyReqDTO = JSON.parseObject(s, UserInfoModifyReqDTO.class);

        custOperateService.saveUpdateUserInfoOperateHistory(userInfoModifyReqDTO);
    }
    

    @Test
    public void test03(){


        String s2 = "☺少时诵诗书";

        System.out.println("s2 = " + s2);

    }


    private static String convertUnicodeToCh(String str) {
        Pattern pattern = Pattern.compile("(\\\\u(\\w{4}))");
        Matcher matcher = pattern.matcher(str);

        // 迭代，将str中的所有unicode转换为正常字符
        while (matcher.find()) {
            String unicodeFull = matcher.group(1); // 匹配出的每个字的unicode，比如\u67e5
            String unicodeNum = matcher.group(2); // 匹配出每个字的数字，比如\u67e5，会匹配出67e5

            // 将匹配出的数字按照16进制转换为10进制，转换为char类型，就是对应的正常字符了
            char singleChar = (char) Integer.parseInt(unicodeNum, 16);

            // 替换原始字符串中的unicode码
            str = str.replace(unicodeFull, singleChar + "");
        }
        return str;
    }

}
