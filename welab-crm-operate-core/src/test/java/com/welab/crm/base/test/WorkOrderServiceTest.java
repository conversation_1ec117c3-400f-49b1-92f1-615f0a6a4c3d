package com.welab.crm.base.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigNodeDTO;
import com.welab.crm.operate.dto.InitCodeConfigDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderBatchReplyDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderBatchSaveDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderSearchReqDTO;
import com.welab.crm.operate.service.WorkOrderService;
import com.welab.crm.operate.vo.workorder.WorkOrderDetailVO;
import com.welab.crm.operate.vo.workorder.WorkOrderHisVO;
import com.welab.crm.operate.vo.workorder.WorkOrderInfoVO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/9
 */
public class WorkOrderServiceTest extends SimpleTest {

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private StaffService staffService;

    @Test
    public void getInitNodeConfigTest(){
        InitCodeConfigDTO reqDTO = new InitCodeConfigDTO();
        reqDTO.setProcessCode("NornalWO");
        StaffVO staffVO = staffService.getStaffByMobile("18823715353");
        WFConfigNodeDTO wFConfigNodeDTO = workOrderService.getInitNodeConfig(reqDTO, staffVO);
        System.out.println(wFConfigNodeDTO);
    }

    @Test
    public void queryWorkOrderHistoryTest(){
        List<WorkOrderHisVO> list = workOrderService.queryWorkOrderHistory("678556214866481152", "15896565899",
                null, "NornalWO");
        list.forEach(System.out::println);
    }

    @Test
    public void queryWorkOrderDetailTest(){
        Long id=Long.valueOf("1469202252524535862");
        String executionId="exec678546306674724864";
        String staffId="39";
        WorkOrderDetailVO workOrderDetailVO = workOrderService.queryWorkOrderDetail(id, executionId, staffId);
        System.out.println(Objects.isNull(workOrderDetailVO) ? "null" : workOrderDetailVO.toString());
    }

    @Test
    public void queryWorkOrderListTest(){
        WorkOrderSearchReqDTO dto = new WorkOrderSearchReqDTO();
        dto.setCurPage(1);
        dto.setPageSize(20);
        dto.setStaffId("39");
        Page<WorkOrderInfoVO> page = workOrderService.queryWorkOrderList(dto);
        page.getRecords().forEach(System.out::println);
    }

    @Test
    public void batchSaveTest(){
        WorkOrderBatchSaveDTO dto = new WorkOrderBatchSaveDTO();
        dto.setComment("测试批量回复意见");
        List<WorkOrderBatchReplyDTO> list = new ArrayList();
        WorkOrderBatchReplyDTO dto1 = new WorkOrderBatchReplyDTO();
        dto1.setId(Long.valueOf("1469202252524536054"));
        dto1.setExecutionId("exec680720335447789568");
        dto1.setTaskId("task680720479702487040");
        dto1.setNodeCode("nwoInitiator");
        list.add(dto1);
        WorkOrderBatchReplyDTO dto2 = new WorkOrderBatchReplyDTO();
        dto2.setId(Long.valueOf("1469202252524536049"));
        dto2.setExecutionId("exec680468631313715200");
        dto2.setTaskId("task680721130679439360");
        dto2.setNodeCode("nwoInitiator");
        list.add(dto2);
        dto.setList(list);
        workOrderService.batchSave(dto, "39");
        System.out.println("end");
    }
}
