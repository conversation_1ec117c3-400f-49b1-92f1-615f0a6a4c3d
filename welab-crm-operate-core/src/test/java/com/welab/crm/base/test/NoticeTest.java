package com.welab.crm.base.test;

import com.welab.crm.base.workflow.common.constant.NoticeTypeEnum;
import com.welab.crm.operate.dto.notice.WoNoticeDTO;
import com.welab.crm.operate.service.impl.NoticeMsgServiceImpl;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: NoticeTest
 * @Description:
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2021/12/20 14:46
 */
public class NoticeTest extends SimpleTest {

    @Resource
    private NoticeMsgServiceImpl noticeMsgService;

    @Test
    public void noticeWechatTest() {
        WoNoticeDTO woNoticeDTO = new WoNoticeDTO();
        woNoticeDTO.setMobile("13077778888");
        woNoticeDTO.setContent("你好，测试");
        woNoticeDTO.setTitle("这是标题");
        woNoticeDTO.setReceiver("leon.li");
        woNoticeDTO.setNoticeType(NoticeTypeEnum.ENTERPRISE_WECHAT.getCode());
        noticeMsgService.workOrderNoticeByType(woNoticeDTO);
    }

}
