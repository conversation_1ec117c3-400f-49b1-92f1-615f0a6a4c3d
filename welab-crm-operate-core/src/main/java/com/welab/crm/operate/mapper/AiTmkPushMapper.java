package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.AiTmkPush;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 电销-ai推送数量历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
public interface AiTmkPushMapper extends BaseMapper<AiTmkPush> {

    /**
     * 查询指定时间内指定规则的推送数量
     * @param startTime
     * @param endTime
     * @param configId
     * @return
     */
    Integer queryTotalPushCount(@Param("startTime") String startTime, @Param("endTime") String endTime,
            @Param("configId") Long configId);

}
