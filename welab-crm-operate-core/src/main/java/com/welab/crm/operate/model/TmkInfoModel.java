package com.welab.crm.operate.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-02-26
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class TmkInfoModel implements Serializable {
	private static final long serialVersionUID = -1717534575550992970L;

	/**
	 * 超级会员
	 */
	private String cjhy;

	/**
	 * 钱夹谷谷
	 */
	private String wallet;

	/**
	 * 进件模式
	 */
	private String loan;

	/**
	 * 额度模式
	 */
	private String credit;

	/**
	 * uuid
	 */
	private String uuid;
}
