package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.RepaymentCalculateDTO;
import com.welab.collection.interview.enums.WithholdEnum;
import com.welab.collection.interview.service.FinanceService;
import com.welab.collection.interview.service.IRepaymentForWebService;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.MessageService;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.CsRepayLinkRecord;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkQueryDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkSendDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.enums.RepayLinkModel;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.CsRepayLinkRecordMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.H5RepayLinkService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.repaylink.H5RepayLinkVO;
import com.welab.crm.operate.vo.repaylink.H5RepaySummaryReportVO;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.common.enums.ChannelCodeEnum;
import com.welab.finance.common.enums.PartnerCodeEnum;
import com.welab.finance.repayment.dto.HelpRepayWithholdChannelDTO;
import com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService;
import com.welab.finance.repayment.enums.RepayOriginEnum;
import com.welab.finance.repayment.enums.UserRepaysStatusEnum;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import com.welab.thirdparty.partner.wld.ProxyInteractionRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * h5还款链接服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class H5RepayLinkServiceImpl implements H5RepayLinkService {

    public static final String ORG_ID = "0";

    @Resource
    private FinanceService collectionFinanceService;

    @Resource
    private IRepaymentForWebService iRepaymentForWebService;

    @Resource
    private CsRepayLinkRecordMapper csRepayLinkRecordMapper;

    @Resource
    private CustOperateService custOperateService;

    @Resource
    private MessageService messageService;

    @Resource
    private RepaymentCalculationDubboService repaymentCalculationDubboService;

    @Resource
    private LoanApplicationService loanApplicationService;

    @Autowired
    private OpDictInfoMapper opDictInfoMapper;
    
    
    @Resource
    private LoanApplicationServiceFacade loanApplicationServiceFacade;

    @Override
    public Response<Map<String, Boolean>> isAllowOfflineRepayment(String appNo) {
        Response<Map<String, Boolean>> response = new Response();
        Map<String, Boolean> result = new HashMap<String, Boolean>();
        Response<Boolean> allowOfflineRepayment = collectionFinanceService.isAllowOfflineRepayment(appNo);
        if (Objects.nonNull(allowOfflineRepayment) && allowOfflineRepayment.getResult().equals(Boolean.TRUE)) {
            result.put("YC", Boolean.TRUE);
        }
        HelpRepayWithholdChannelDTO dto = new HelpRepayWithholdChannelDTO();
        dto.setApplicationId(appNo);
        dto.setRepaymentOrigin(RepayOriginEnum.HELPPAYMENT.getCode());
        Response<String> res = repaymentCalculationDubboService.getHelpRepayWithholdChannel(dto);
        if ("0".equals(res.getCode()) && ("allinpay".equals(res.getResult()) || "baofu".equals(res.getResult()))) {
            result.put("YD", Boolean.TRUE);
            result.put("YH", Boolean.TRUE);
        }
        response.setResult(result);
        return response;
    }

    @Override
    public String calRepayAmount(RepaymentCalculateDTO dto) {
        String result = "";

        dto.setOrgId(ORG_ID);
        dto.setRepaymentMode(
            StringUtils.isEmpty(dto.getRepaymentMode()) ? WithholdEnum.WITHHOLD_YB.getCode() : dto.getRepaymentMode());

        if (dto.getApplicationId().startsWith(WithholdEnum.WL_PRODUCTION.getCode())) {
            result = collectionFinanceService.getRepaymentFeeItemsForWL(dto).getResult();
        } else {
            result = collectionFinanceService.getRepaymentFeeItemsN(dto).getResult();
        }
        return result;
    }

    @Override
    public Response<String> getRepayLink(ProxyInteractionRequest reqDTO) {
        // 代扣金额为空或者金额小数位超过3位数报错
        if (reqDTO.getAmount() == null || BigDecimal.ZERO.compareTo(reqDTO.getAmount()) >= 0) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "代扣金额不能为空或者0且不能超过两位小数！", null);
        } else {
            int index = String.valueOf(reqDTO.getAmount()).indexOf(".");
            if (index != -1) {
                if (String.valueOf(reqDTO.getAmount()).substring(index + 1).length() > 2) {
                    return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "代扣金额不能为空或者0且不能超过两位小数！", null);
                }
            }
        }

        Response<String> res = iRepaymentForWebService.getProxyRepayLink(reqDTO);
        if (Response.isSuccess(res) && StringUtils.isNotBlank(res.getResult())){
            return res;
        } else {
            throw new FastRuntimeException("获取h5还款链接失败");
        }
    }

    @Override
    public Page<H5RepayLinkVO> queryRecord(H5RepayLinkQueryDTO dto) {

        if (StringUtils.isBlank(dto.getAppNo()) && StringUtils.isBlank(dto.getUuid()) && StringUtils.isBlank(dto.getMobile())) {
            CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        }
        Page<H5RepayLinkVO> page = csRepayLinkRecordMapper.queryRecord(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(vo -> {
                vo.setRepayMode(RepayLinkModel.getDescByCode(vo.getRepayMode()));
                if (StringUtils.isBlank(vo.getPartnerName())) {
                    if (StringUtils.isBlank(vo.getPartnerCode())) {
                        vo.setPartnerName(PartnerCodeEnum.getPartnerNameByCode(loanApplicationServiceFacade.getLoanApplicationByApplicationId(vo.getApplicationId()).getPartnerCode()));
                    } else {
                        vo.setPartnerName(PartnerCodeEnum.getPartnerNameByCode(vo.getPartnerCode()));
                    }
                }
                vo.setMobile(SecurityUtil.maskMobile2(vo.getMobile()));
                vo.setPaymentChannel(ChannelCodeEnum.getChannelNameByChannelCode(vo.getPaymentChannel()));
                vo.setRepayResult(UserRepaysStatusEnum.getCommentByStatus(vo.getRepayResult()));
            });
        }

        return page;
    }

    @Override
    public String sendH5RepayLink(H5RepayLinkSendDTO dto, StaffVO staffVO) {
        log.info("sendH5RepayLink:{}", JSON.toJSONString(dto));
        int count = queryRepayLinkRecordSendCount(dto.getApplicationId());
        //获取字典配置上限
        OpDictInfo repaymentNum = opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "H5RepaymentNum").eq(OpDictInfo::getStatus, 1));
        if (count >= Integer.parseInt(repaymentNum.getType())) {
            throw new CrmOperateException("当前合同号今天发送次数已经超过上限: " + repaymentNum.getType());
        }
        MessageSendDTO sendDTO = new MessageSendDTO();
        BeanUtils.copyProperties(dto, sendDTO);

        sendDTO.setStaffId(staffVO.getId().toString());
        sendDTO.setLoginName(staffVO.getLoginName());
        HashMap<String, String> otherParams = new HashMap<>();
        otherParams.put("amount", String.valueOf(dto.getRepayAmount()));
        otherParams.put("repayCmd", dto.getRepayCmd());
        otherParams.put("repayLink", dto.getRepayLink());
        otherParams.put("repaymentType", dto.getRepayMode());
        otherParams.put("repayMethod", dto.getRepayMethod());
        otherParams.put("applicationId", dto.getApplicationId());
        sendDTO.setOtherParams(otherParams);
        String errMsg = messageService.sendMessage(sendDTO);
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.MESSAGE_SEND.getCode());
        if (StringUtils.isBlank(errMsg)) {
            saveRepayLinkRecord(dto, staffVO);
        }
        return errMsg;
    }

    @Override
    public Integer queryRepayLinkRecordSendCount(String appNo) {
        return csRepayLinkRecordMapper.selectCount(Wrappers.lambdaQuery(CsRepayLinkRecord.class)
            .ge(CsRepayLinkRecord::getSendTime, DateUtils.getStartOfToday()).eq(CsRepayLinkRecord::getApplicationId,appNo));
    }


    @Override
    public List<H5RepaySummaryReportVO> queryReportSummaryReport(ReportBaseDTO dto) {
        switch (dto.getPeriod()) {
            case Constant.DAY:
                dto.setPeriod("date_format(t0.send_time ,'%Y-%m-%d')");
                break;
            case Constant.WEEK:
                dto.setPeriod(
                    "CONCAT(DATE_SUB(date_format(t0.send_time,'%Y-%m-%d'),INTERVAL WEEKDAY(date_format(t0.send_time,'%Y-%m-%d')) DAY) ,'-' ,DATE_SUB(date_format(t0.send_time,'%Y-%m-%d'),INTERVAL WEEKDAY(date_format(t0.send_time,'%Y-%m-%d'))-6 DAY))");
                break;
            case Constant.MONTH:
                dto.setPeriod("date_format(t0.send_time ,'%Y-%m')");
                break;
            default:
                dto.setPeriod("");
                break;

        }
        List<H5RepaySummaryReportVO> list = csRepayLinkRecordMapper.queryRepaySummary(dto);

        for (H5RepaySummaryReportVO vo : list) {
            vo.setSuccessRate(CommonUtil.calcPercentage(vo.getSuccessCount(), vo.getOrderCount()));
            vo.setFailRate(CommonUtil.calcPercentage(vo.getFailCount(), vo.getOrderCount()));
        }
        return list;
    }

    private void saveRepayLinkRecord(H5RepayLinkSendDTO dto, StaffVO staffVO) {
        CsRepayLinkRecord repayLinkRecord = new CsRepayLinkRecord();
        repayLinkRecord.setCustomerName(dto.getName());
        repayLinkRecord.setUuid(dto.getUuid());
        repayLinkRecord.setApplicationId(dto.getApplicationId());
        repayLinkRecord.setSendMobile(dto.getMobile());
        repayLinkRecord.setRepayMode(dto.getRepayMode());
        repayLinkRecord.setRepayAmount(dto.getRepayAmount());
        repayLinkRecord.setSendTime(new Date());
        repayLinkRecord.setSendStaffId(staffVO.getId());
        repayLinkRecord.setSendStaffName(staffVO.getStaffName());
        repayLinkRecord.setSendStaffGroup(staffVO.getGroupName());
        repayLinkRecord.setCustomerId(dto.getCustomerId());
        repayLinkRecord.setUserId(Long.valueOf(dto.getUserId()));
        repayLinkRecord.setConsultationStatus(dto.getConsultationStatus());
        repayLinkRecord.setRepayMethod(dto.getRepayMethod());
        String partnerCode = loanApplicationServiceFacade.getLoanApplicationByApplicationId(dto.getApplicationId()).getPartnerCode();
        repayLinkRecord.setPartnerCode(partnerCode);
        repayLinkRecord.setPartnerName(loanApplicationService.getPartnerName(partnerCode));
        csRepayLinkRecordMapper.insert(repayLinkRecord);
    }
}
