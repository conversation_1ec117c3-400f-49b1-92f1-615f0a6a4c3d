package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.vo.woReport.ReportCallInWorkStatusVO;
import com.welab.crm.operate.vo.woReport.ReportStaffEfficiencyVO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;

import java.util.List;

/**
 * 员工状态效能报表服务
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/05/07
 */
public interface WorkStatusReportService {

    /**
     * 工作状态统计报表分页查询
     */
    Page<ReportWorkStatusSummaryVO> getWorkStatusSummaryPage(ReportWorkStatusDTO dto);

    /**
     * 工作状态统计报表
     */
    List<ReportWorkStatusSummaryVO> listWorkStatusSummary(ReportWorkStatusDTO dto);

    /**
     * 客服呼入工作状态报表分页查询
     */
    Page<ReportCallInWorkStatusVO> getCallInWorkStatus(ReportWorkStatusDTO dto);

    /**
     * 客服呼入工作状态报表
     */
    List<ReportCallInWorkStatusVO> getCallInWorkStatusList(ReportWorkStatusDTO dto);

    /**
     * 员工效能报表分页查询
     */
    Page<ReportStaffEfficiencyVO> getStaffEfficiencyPage(ReportWorkStatusDTO dto);

    /**
     * 员工效能报表全部查出用于导出
     */
    List<ReportStaffEfficiencyVO> listStaffEfficiency(ReportWorkStatusDTO dto);
}
