package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 坐席工作状态历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_staff_status_his")
public class OpStaffStatusHis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 员工Id
     */
    private String staffId;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态开始时间
     */
    private Date startTime;

    /**
     * 状态结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 持续时长
     */
    private Long duration;


}
