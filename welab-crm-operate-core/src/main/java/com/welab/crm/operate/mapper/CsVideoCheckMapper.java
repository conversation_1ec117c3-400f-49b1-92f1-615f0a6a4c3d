package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CsVideoCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.video.VideoTaskReqDTO;
import com.welab.crm.operate.vo.video.VideoTaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客服视频核验记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
public interface CsVideoCheckMapper extends BaseMapper<CsVideoCheck> {

    /**
     * 分页查询视频核验记录
     * @param videoTaskVOPage
     * @param dto
     * @return
     */
    Page<VideoTaskVO> queryVideoTask(Page<VideoTaskVO> videoTaskVOPage, @Param("dto") VideoTaskReqDTO dto);

    /**
     * 按条件查询视频核验记录
     * @param dto
     * @return
     */
    List<VideoTaskVO> queryVideoTask(@Param("dto") VideoTaskReqDTO dto);
}
