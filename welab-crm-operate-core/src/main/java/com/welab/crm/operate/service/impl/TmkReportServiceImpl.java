package com.welab.crm.operate.service.impl;

import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.TmkAssignHistoryMapper;
import com.welab.crm.operate.mapper.TmkReportMapper;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TmkReportServiceImpl implements TmkReportService {

    @Value("${report.valid.time}")
    private Integer validTime;

    @Resource
    private TmkReportMapper tmkReportMapper;

    @Resource
    private TmkAssignHistoryMapper historyMapper;

    @Override
    public List<RedistributedOutboundEfficiencyVO> getReassignOutboundEfficiency(ReassignOutboundEfficiencyDTO dto) {
        validTime(dto.getStartTime(), dto.getEndTime());
        List<String> groupCodeList = null;
        List<String> businessTypeList = null;
        // 话务组和业务类型都是可多选的
        if (StringUtils.isNotBlank(dto.getGroupCode())) {
            groupCodeList = Arrays.asList(dto.getGroupCode().split(","));
        }
        if (StringUtils.isNotBlank(dto.getBusinessType())) {
            businessTypeList = Arrays.asList(dto.getBusinessType().split(","));
        }

        // 1.查询分配量
        List<RedistributedOutboundEfficiencyVO> reList = tmkReportMapper.selectReassignData(dto, groupCodeList, businessTypeList);
        if (CollectionUtils.isEmpty(reList)) {
            return reList;
        }

        // 2.拼装被回收数据量
        List<RedistributedOutboundEfficiencyVO> recycleList = historyMapper.selectRecycleByTime(dto, groupCodeList, businessTypeList);
        Map<String, RedistributedOutboundEfficiencyVO> recycleMap = getStaffIdKeyMap(recycleList);
        int serialNo = 1;
        for (RedistributedOutboundEfficiencyVO vo : reList) {
            vo.setSerialNo(serialNo++);
            if (recycleMap.get(vo.getStaffId()) != null) {
                vo.setRecoveryNum(recycleMap.get(vo.getStaffId()).getRecoveryNum());
            } else {
                vo.setRecoveryNum(0);
            }
        }

        // 3.查询外呼量、接通量、转化量等数据并计算外呼连通率和转化率
        List<RedistributedOutboundEfficiencyVO> outList = tmkReportMapper.selectReassignOutboundData(dto,
                groupCodeList, businessTypeList);
        Map<String, RedistributedOutboundEfficiencyVO> outMap = getStaffIdKeyMap(outList);
        for (RedistributedOutboundEfficiencyVO vo : reList) {
            RedistributedOutboundEfficiencyVO out = outMap.get(vo.getStaffId());
            if (out != null) {
                vo.setGroupCode(out.getGroupCode());
                vo.setBusinessType(out.getBusinessType());
                vo.setConnectedNum(out.getConnectedNum());
                vo.setConversionNum(out.getConversionNum());
                vo.setOutboundNum(out.getOutboundNum());
                vo.setConnectedRate(CommonUtil.calcPercentage(vo.getConnectedNum(), vo.getOutboundNum()));
                vo.setConversionRate(CommonUtil.calcPercentage(vo.getConversionNum(), vo.getConnectedNum()));
            }
        }
        return reList;
    }

    @Override
    public void validTime(String startTime, String endTime) {
        Date start = DateUtil.stringToDate(startTime);
        Date end = DateUtil.stringToDate(endTime);
        if (!DateUtil.isBefore(start, end)) {
            throw new CrmOperateException("开始时间必须小于结束时间");
        }
        if (DateUtil.containMonths(start, end) >= validTime) {
            throw new CrmOperateException("查询时间不能超过" + validTime + "个自然月");
        }
    }

    private Map<String, RedistributedOutboundEfficiencyVO> getStaffIdKeyMap(List<RedistributedOutboundEfficiencyVO> list) {
        Map<String, RedistributedOutboundEfficiencyVO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(
                    RedistributedOutboundEfficiencyVO::getStaffId, v -> v));
        }
        return map;
    }
}
