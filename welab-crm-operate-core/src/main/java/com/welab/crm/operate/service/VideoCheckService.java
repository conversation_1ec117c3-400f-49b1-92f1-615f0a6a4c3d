package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.video.VideoTaskReqDTO;
import com.welab.crm.operate.vo.video.VideoTaskVO;

import java.util.List;

/**
 * 视频校验服务接口
 * <AUTHOR>
 */
public interface VideoCheckService {

    /**
     * 查询视频录制任务
     * @param dto 请求参数
     * @return 视频任务列表(分页)
     */
    Page<VideoTaskVO> queryVideoTask(VideoTaskReqDTO dto);

    /**
     *
     * @param dto
     * @return
     */
    List<VideoTaskVO> queryVideoTasks(VideoTaskReqDTO dto);

    /**
     * 查询视频链接下载地址
     * @param id 视频任务主键ID
     * @return 视频下载地址
     */
    String queryVideoUrl(Long id);


    void collectVideo(VideoTaskReqDTO dto);
}
