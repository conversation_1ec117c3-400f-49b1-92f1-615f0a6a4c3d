package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.service.LabelService;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.CsCallOutBlackList;
import com.welab.crm.operate.domain.CsCalloutInterceptionRule;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.BlackListCheckDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListApprovalDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutInterceptionRulesReqDTO;
import com.welab.crm.operate.mapper.CsCallOutBlackListMapper;
import com.welab.crm.operate.mapper.CsCalloutInterceptionRuleMapper;
import com.welab.crm.operate.mapper.WoTaskMapper;
import com.welab.crm.operate.service.CallOutBlackListService;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.blacklist.callout.CalloutBlackListVO;
import com.welab.exception.FastRuntimeException;
import com.welab.usercenter.service.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 外呼黑名单服务
 * 
 * <AUTHOR>
 */
@Service
public class CallOutBlackListServiceImpl implements CallOutBlackListService {

    @Resource
    private CsCallOutBlackListMapper csCallOutBlackListMapper;

    @Resource
    private StaffService staffService;

    @Resource
    private CsCalloutInterceptionRuleMapper csCalloutInterceptionRuleMapper;
    
    @Resource
    private WoTaskMapper woTaskMapper;
    
    @Resource
    private LabelService labelService;
    
    @Resource
    private UserService userService;
    
    @Resource
    private CrmOrgServiceImpl crmOrgService;

    @Resource
    private NoticeMsgService noticeMsgService;

    @Override
    public void addCallOutBlackList(CalloutBlackListReqDTO dto) {
        validAddDto(dto);
        csCallOutBlackListMapper.insert(convertDtoToDomain(dto));
        //待审批消息发送跑马灯提示
        /*List<OpDictInfo> dictList = CommonUtils.getDict("callout-blacklist", null);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dictList)) {
            return;
        }
        Map<String, List<String>> approverMap = new HashMap<>();
        for (OpDictInfo info : dictList) {
            approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> firstApprover = approverMap.get("1");
        NoticeMsgReqDTO req = new NoticeMsgReqDTO();
        req.setTitle("外呼黑名单审批通知");
        req.setContent("【外呼黑名单审批】:外呼黑名单审批" + "\t" + "【数量】:" + 1);
        noticeMsgService.publishBannerNotice(firstApprover, req);*/
    }

    /**
     * 添加黑名单 限制条件
     */
    private void validAddDto(CalloutBlackListReqDTO dto) {
        if (CollectionUtils.isNotEmpty(queryValidBlackListByUuid(dto.getUuid()))) {
            throw new FastRuntimeException("该客户存在生效中的黑名单，无需重复添加");
        }
    }

    @Override
    public Page<CalloutBlackListVO> queryCallOutBlackList(CalloutBlackListReqDTO dto) {
        Page<CsCallOutBlackList> domainPage = queryCallOutBlackListPage(dto);
        Page<CalloutBlackListVO> voPage = new Page<>();
        BeanUtils.copyProperties(domainPage, voPage);
        List<String> idList =
            domainPage.getRecords().stream().map(item -> String.valueOf(item.getApplyStaffId())).collect(Collectors.toList());
        Map<Long, String> staffMap = queryStaffIdNameMap(idList);
        List<CalloutBlackListVO> voList = domainPage.getRecords().stream()
            .map(item -> convertDomainToVo(item, staffMap)).collect(Collectors.toList());
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public void approvalCalloutBlackList(CalloutBlackListApprovalDTO dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            return;
        }
        List<OpDictInfo> dictList = CommonUtils.getDict("callout-blacklist", null);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dictList)) {
            return;
        }
        Map<String, List<String>> approverMap = new HashMap<>();
        for (OpDictInfo info : dictList) {
            approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> firstApprover = approverMap.get("1");
        if (!firstApprover.contains(CommonUtils.getCurrentlogged())) {
            throw new FastRuntimeException("无审批权限");
        }
        for (Long id : dto.getIds()) {
            CsCallOutBlackList blackList = new CsCallOutBlackList();
            blackList.setApprovalStatus(dto.getApprovalStatus());
            blackList.setApprovalStaffId(CommonUtils.getCurrentloggedStaffId());
            if (dto.getApprovalStatus().equals(ApprovalStatusEnum.APPROVED.code)) {
                blackList.setValidTime(dto.getValidTime());
                blackList.setValidStartTime(DateUtils.getStartOfToday());
                if (Objects.isNull(dto.getValidTime())) {
                    blackList.setValidEndTime(DateUtil.stringToDate("3001-01-01"));
                } else {
                    blackList.setValidEndTime(DateUtil.plusDays(DateUtils.getStartOfToday(), dto.getValidTime()));
                }
            }
            csCallOutBlackListMapper.update(blackList,
                Wrappers.lambdaUpdate(CsCallOutBlackList.class).eq(CsCallOutBlackList::getId, id));
        }
    }


    @Override
    public void cancelCalloutBlackList(List<Long> ids) {
        for (Long id : ids) {
            CsCallOutBlackList csCallOutBlackList = new CsCallOutBlackList();
            csCallOutBlackList.setIsDeleted(Boolean.TRUE);
            csCallOutBlackListMapper.update(csCallOutBlackList,
                Wrappers.lambdaUpdate(CsCallOutBlackList.class).eq(CsCallOutBlackList::getId, id));
        }
    }

    @Override
    public void addCalloutInterceptionRules(CalloutInterceptionRulesReqDTO dto) {
        checkReqDTO(dto);

        csCalloutInterceptionRuleMapper.insert(convertInterceptDtoToDomain(dto));
    }

    private void checkReqDTO(CalloutInterceptionRulesReqDTO dto) {
        if (CollectionUtils.isEmpty(dto.getGroupCodes())){
            throw new FastRuntimeException("组别不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getLabelName()) && CollectionUtils.isEmpty(dto.getOrderStatus())){
            throw new FastRuntimeException("工单状态和标签名称不能同时为空");
        }
    }

    @Override
    public void updateCalloutInterceptionRules(CalloutInterceptionRulesReqDTO dto) {
        checkReqDTO(dto);
        csCalloutInterceptionRuleMapper.updateById(convertInterceptDtoToDomain(dto));
    }

    @Override
    public Page<CalloutInterceptionRulesReqDTO> queryCalloutInterceptionRules(BaseReqDTO dto) {
        Page<CsCalloutInterceptionRule> domainPage =
            csCalloutInterceptionRuleMapper.selectPage(new Page<>(dto.getCurPage(), dto.getPageSize()), null);
        Page<CalloutInterceptionRulesReqDTO> resultPage = new Page<>();
        BeanUtils.copyProperties(domainPage, resultPage);
        List<String> idList =
            domainPage.getRecords().stream().map(item -> String.valueOf(item.getCreateStaffId())).collect(Collectors.toList());
        Map<Long, String> staffMap = queryStaffIdNameMap(idList);
        Map<String, String> orderStatusMap = queryOrderStatusMap();
        Map<String, String> groupMap = queryGroupMap();
        List<CalloutInterceptionRulesReqDTO> resultList = domainPage.getRecords().stream()
            .map(item -> convertInterceptDomainToDto(item, staffMap, orderStatusMap, groupMap))
                .collect(Collectors.toList());
        resultPage.setRecords(resultList);
        return resultPage;
    }

    /**
     * 查询组别Map
     */
    private Map<String, String> queryGroupMap() {
        return crmOrgService.getGroupCodeNameMap();
        
    }

    /**
     * 查询工单状态字典
     */
    private Map<String, String> queryOrderStatusMap() {
        List<OpDictInfo> dict = CommonUtils.getDict("formState", "");
        if (CollectionUtils.isEmpty(dict)){
            return new HashMap<>();
        }
        return dict.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent));
    }

    @Override
    public void deleteInterceptionRules(List<Long> ids) {
        csCalloutInterceptionRuleMapper.deleteBatchIds(ids);
    }


    @Override
    public boolean checkBlackList(BlackListCheckDTO blackListCheckDTO) {
        if (StringUtils.isBlank(blackListCheckDTO.getUuid()) && StringUtils.isNotBlank(blackListCheckDTO.getMobile())){
            String mobile = blackListCheckDTO.getMobile();
            boolean matches = Pattern.matches(Constant.MOBILE, mobile);
            if (!matches) {
                mobile = AesUtils.decrypt(mobile);
            }
            List<Long> uuidList = userService.selectUuidByMobile(mobile);
            if (CollectionUtils.isEmpty(uuidList)){
                return false;
            }
            blackListCheckDTO.setUuid(String.valueOf(uuidList.get(0)));
        }
        // 判断是否满足外呼黑名单
        boolean isMatchBlackList = queryIsMatchBlackList(blackListCheckDTO.getUuid());
        if (isMatchBlackList) {
            return true;
        }
        // 判断该uuid是否符合拦截规则
        return queryIsIntercept(blackListCheckDTO);
        
    }

    private boolean queryIsMatchBlackList(String uuid) {
        boolean matchGroup = false;
        String currentGroup = CommonUtils.getCurrentloggedOrg();
        // 查询生效组别
        List<OpDictInfo> dict = CommonUtils.getDict("callBlackGroup", null);
        if (CollectionUtils.isNotEmpty(dict)) {
            for (OpDictInfo info : dict) {
                if (info.getType().equals(currentGroup)) {
                    matchGroup = true;
                    break;
                }
            }
        }
        if (matchGroup) {
            List<CsCallOutBlackList> list = queryValidBlackListByUuid(uuid);
            return CollectionUtils.isNotEmpty(list);
        }

        return false;

    }

    private boolean queryIsIntercept(BlackListCheckDTO blackListCheckDTO) {
        // 查询全部生效的拦截规则
        List<CsCalloutInterceptionRule> rules = queryAllValidInterceptRules();
        return isMatchRules(rules, blackListCheckDTO.getUuid());
    }

    private boolean isMatchRules(List<CsCalloutInterceptionRule> rules, String uuid) {
        String currentGroup = CommonUtils.getCurrentloggedOrg();
        List<String> statusList = null;
        List<String> labels = null;
        for (CsCalloutInterceptionRule rule : rules) {
            // 判断组别
            List<String> groupList = Arrays.asList(rule.getGroupCodes().split(","));
            // 如果组别不满足拦截条件，则直接跳到下一个规则
            if (!groupList.contains(currentGroup)) {
                continue;
            }

            // 判断标签名称是否满足条件
            boolean matchLabel = false;
            if (StringUtils.isNotBlank(rule.getLabelName())) {
                if (Objects.isNull(labels)){
                    labels = labelService.getUserAllLabel(Long.valueOf(uuid));
                }
                List<String> ruleLabels = Arrays.asList(rule.getLabelName().split(","));
                for (String label : labels) {
                    if (ruleLabels.contains(label)) {
                        matchLabel = true;
                        break;
                    }
                }
            }
            
            // 判断工单状态是否满足
            boolean matchOrderStatus = false;
            if (StringUtils.isNotBlank(rule.getOrderStatus())) {
                // 查询该用户全部工单状态
                if (Objects.isNull(statusList)) {
                    statusList = woTaskMapper.queryAllStatusByUuid(uuid);
                }
                List<String> ruleStatusList = Arrays.asList(rule.getOrderStatus().split(","));
                for (String status : statusList) {
                    if (ruleStatusList.contains(status)) {
                        matchOrderStatus =  true;
                        break;
                    }
                }
            } 
            
            if (matchLabel || matchOrderStatus){
                return true;
            }
            
        }

        return false;
    }

    private List<CsCalloutInterceptionRule> queryAllValidInterceptRules() {
        return csCalloutInterceptionRuleMapper.selectList(
            Wrappers.lambdaQuery(CsCalloutInterceptionRule.class).eq(CsCalloutInterceptionRule::getIsDeleted, false));
    }

    private CalloutInterceptionRulesReqDTO convertInterceptDomainToDto(CsCalloutInterceptionRule interceptionRule,
        Map<Long, String> staffMap, Map<String, String> orderStatusMap, Map<String, String> groupMap) {
        CalloutInterceptionRulesReqDTO result = new CalloutInterceptionRulesReqDTO();
        BeanUtils.copyProperties(interceptionRule, result);
        if (StringUtils.isNotBlank(interceptionRule.getOrderStatus())) {
            List<String> orderStatusNameList = new ArrayList<>();
            String[] orderStatusArr = interceptionRule.getOrderStatus().split(",");
            for (String statusCode : orderStatusArr) {
                orderStatusNameList.add(orderStatusMap.get(statusCode));
            }
            result.setOrderStatusName(orderStatusNameList);
            result.setOrderStatus(Arrays.asList(orderStatusArr));
        }

        if (StringUtils.isNotBlank(interceptionRule.getLabelName())) {
            result.setLabelName(Arrays.asList(interceptionRule.getLabelName().split(",")));
        }

        if (StringUtils.isNotBlank(interceptionRule.getGroupCodes())) {
            List<String> groupNameList = new ArrayList<>();
            String[] groupArr = interceptionRule.getGroupCodes().split(",");
            for (String groupCode : groupArr) {
                groupNameList.add(groupMap.get(groupCode));
            }
            result.setGroupNames(groupNameList);
            result.setGroupCodes(Arrays.asList(groupArr));
        }
        result.setCreateTime(interceptionRule.getGmtCreate());
        result.setCreateStaff(staffMap.get(interceptionRule.getCreateStaffId()));
        return result;
    }

    private CsCalloutInterceptionRule convertInterceptDtoToDomain(CalloutInterceptionRulesReqDTO dto) {
        CsCalloutInterceptionRule interceptionRule = new CsCalloutInterceptionRule();
        BeanUtils.copyProperties(dto, interceptionRule);
        interceptionRule.setCreateStaffId(CommonUtils.getCurrentloggedStaffId());
        interceptionRule.setGmtModify(new Date());
        if (CollectionUtils.isNotEmpty(dto.getOrderStatus())) {
            interceptionRule.setOrderStatus(String.join(",", dto.getOrderStatus()));
        }
        if (CollectionUtils.isNotEmpty(dto.getLabelName())) {
            interceptionRule.setLabelName(String.join(",", dto.getLabelName()));
        }
        if (CollectionUtils.isNotEmpty(dto.getGroupCodes())) {
            interceptionRule.setGroupCodes(String.join(",", dto.getGroupCodes()));
        }

        return interceptionRule;
    }

    public Map<Long, String> queryStaffIdNameMap(List<String> ids) {
        return staffService.getStaffById(ids).stream().collect(Collectors.toMap(StaffVO::getId, StaffVO::getStaffName));
    }

    private CalloutBlackListVO convertDomainToVo(CsCallOutBlackList item, Map<Long, String> staffMap) {
        CalloutBlackListVO vo = new CalloutBlackListVO();
        BeanUtils.copyProperties(item, vo);
        vo.setApprovalStatus(ApprovalStatusEnum.getDescByCode(item.getApprovalStatus()));
        vo.setApplyStaff(staffMap.get(item.getApplyStaffId()));
        vo.setIsDeleted(Boolean.TRUE.equals(item.getIsDeleted()) ? "是" : "否");
        return vo;
    }

    public Page<CsCallOutBlackList> queryCallOutBlackListPage(CalloutBlackListReqDTO dto) {
        return csCallOutBlackListMapper.selectPage(new Page<>(dto.getCurPage(), dto.getPageSize()), Wrappers
            .lambdaQuery(CsCallOutBlackList.class)
            .eq(StringUtils.isNotBlank(dto.getUuid()), CsCallOutBlackList::getUuid, dto.getUuid())
            .eq(StringUtils.isNotBlank(dto.getCustName()), CsCallOutBlackList::getCustName, dto.getCustName())
            .ge(StringUtils.isNotBlank(dto.getApplyStartTime()), CsCallOutBlackList::getApplyTime,
                dto.getApplyStartTime())
            .le(StringUtils.isNotBlank(dto.getApplyEndTime()), CsCallOutBlackList::getApplyTime, dto.getApplyEndTime())
            .in(CollectionUtils.isNotEmpty(dto.getApprovalStatus()), CsCallOutBlackList::getApprovalStatus,
                dto.getApprovalStatus()).orderByDesc(CsCallOutBlackList::getId));
    }

    private CsCallOutBlackList convertDtoToDomain(CalloutBlackListReqDTO dto) {
        CsCallOutBlackList callOutBlackList = new CsCallOutBlackList();
        BeanUtils.copyProperties(dto, callOutBlackList);
        callOutBlackList.setApplyTime(new Date());
        callOutBlackList.setApplyStaffId(CommonUtils.getCurrentloggedStaffId());
        callOutBlackList.setApprovalStatus(ApprovalStatusEnum.APPLY.code);
        callOutBlackList.setIsDeleted(false);
        return callOutBlackList;
    }

    /**
     * 查询该uuid是否存在生效中的黑名单
     */
    public List<CsCallOutBlackList> queryValidBlackListByUuid(String uuid) {
        return csCallOutBlackListMapper.selectList(Wrappers.lambdaQuery(CsCallOutBlackList.class)
            .eq(CsCallOutBlackList::getUuid, uuid).ge(CsCallOutBlackList::getValidEndTime, DateUtils.getStartOfToday())
            .eq(CsCallOutBlackList::getApprovalStatus, ApprovalStatusEnum.APPROVED.code)
            .eq(CsCallOutBlackList::getIsDeleted, 0));
    }

    public enum ApprovalStatusEnum {
        APPLY(0, "待审批"), APPROVED(1, "审批通过"), REFUSE(2, "拒绝"),;

        private Integer code;

        private String desc;

        ApprovalStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static String getDescByCode(Integer code) {
            for (ApprovalStatusEnum statusEnum : values()) {
                if (statusEnum.code.equals(code)) {
                    return statusEnum.desc;
                }
            }
            return code + "";
        }

    }
}
