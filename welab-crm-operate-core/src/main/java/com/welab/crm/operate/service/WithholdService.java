package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.withhold.RepaymentExtDTO;
import com.welab.crm.operate.dto.withhold.WithholdReportVO;
import com.welab.crm.operate.dto.withhold.WithholdReqDTO;
import com.welab.crm.operate.vo.withhold.WithholdVO;
import com.welab.finance.repayment.dto.RepayChannelReq;
import com.welab.finance.repayment.vo.RepayChannelVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 代扣相关服务接口
 * @date 2022/4/19 17:44
 */
public interface WithholdService {

    /**
     * 查询代扣记录
     * @param dto
     * @return
     */
    Page<WithholdVO> queryWithholdRecord(WithholdReqDTO dto);


    /**
     * 代扣
     * @param repaymentDTO
     * @return
     */
    Response<RepaymentVO> repayment(RepaymentExtDTO repaymentDTO);


    /**
     * 根据贷款号和代扣方式查询代扣金额
     * @param dto
     * @return
     */
    BigDecimal getRepayAmountByMode(WithholdReqDTO dto);

    /**
     * 查询代扣报表
     * @param dto
     * @return
     */
    List<WithholdReportVO> queryWithholdReport(ReportBaseDTO dto);

    /**
     * 根据合同号查询可支持的还款方式
     * @param appNo
     * @return
     */
    List<String> queryRepaymentModeByAppNo(String appNo);


    /**
     * 获取还款通道
     * @param repayChannelReq
     * @return
     */
    RepayChannelVO getRepayChannel(RepayChannelReq repayChannelReq);


}
