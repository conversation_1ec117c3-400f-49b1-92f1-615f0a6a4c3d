package com.welab.crm.operate.service.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.db.sql.SqlBuilder.Join;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.welab.crm.interview.service.LabelService;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.vo.vip.PrivilegeCardVO;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.mapper.ConPhoneSummaryMapper;
import com.welab.crm.operate.service.IReportPhoneResultService;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.vo.phone.ReportPhoneResultSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultVO;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.dto.User;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/2
 */
@Service
@Slf4j
public class ReportPhoneResultServiceImpl implements IReportPhoneResultService {

    @Autowired
    private ConPhoneSummaryMapper conPhoneSummaryMapper;

    @Resource
    private LabelService labelService;
    @Resource
    private VipService vipService;
    @Resource
    private TmkReportService tmkReportService;

    /**
     * 查询标签数据线程池
     */
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(50, 50, 300L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(), ThreadFactoryBuilder.create().setNamePrefix("summary-label-query-pool-").build());

    @Override
    public Page<ReportPhoneResultVO> queryDetail(ReportPhoneResultDTO dto) {
        try {
            checkTime(dto);
            covert(dto);
            // uuid 标签map
            Map<String, String> uuidLabelMap = new HashMap<>();
            Page<ReportPhoneResultVO> result = conPhoneSummaryMapper.queryReportSummaryDetail(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
            if(Objects.nonNull(result) && Objects.nonNull(result.getRecords()) && !result.getRecords().isEmpty()){
                List<ReportPhoneResultVO> dataList = result.getRecords().stream().map(t -> {
                    if(StringUtils.isNotBlank(t.getCallSummary())){
                        String[] summaries = t.getCallSummary().split("#");
                        t.setCallSummaryOne(summaries.length >= 1 && StringUtils.isNotBlank(summaries[0]) ? summaries[0] : "");
                        t.setCallSummaryTwo(summaries.length >= 2 && StringUtils.isNotBlank(summaries[1]) ? summaries[1] : "");
                        t.setCallSummaryThree(summaries.length >= 3 && StringUtils.isNotBlank(summaries[2]) ? summaries[2] : "");
                    }
                    if (dto.getIsQueryUserLabel()){
                        String label2 = queryUserLabel(t.getUuid(), uuidLabelMap);
                        t.setUserLabel(label2);
                    }
                    return t;
                }).collect(Collectors.toList());
                result.setRecords(dataList);
            }
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    private void checkTime(ReportPhoneResultDTO callDTO) {
        // 查询结束时间和开始时间的最大间隔是3个月
        tmkReportService.validTime(callDTO.getStartTime(), callDTO.getEndTime());
    }

    /**
     * 查询标签信息
     */
    private String queryUserLabel(String uuid, Map<String, String> uuidLabelMap) {
        if (StringUtils.isBlank(uuid)){
            return "";
        }
        if (uuidLabelMap.containsKey(uuid)){
            return uuidLabelMap.get(uuid);
        }
        List<String> label = labelService.getUserAllLabel(Long.parseLong(uuid));
        PrivilegeCardVO cardVO = vipService.getUserPrivilegeCardList(Long.parseLong(uuid));
        if(Objects.nonNull(cardVO) && cardVO.isCardUser()){
            label.add("特权卡用户");
        }
        String labelStr = Joiner.on(",").join(label);
        uuidLabelMap.put(uuid,labelStr);

        return labelStr;
    }

    @Override
    public List<ReportPhoneResultVO> queryDetailExcel(ReportPhoneResultDTO dto) {
        try {
            checkTime(dto);
            covert(dto);
            Map<String, String> uuidLabelMap = new HashMap<>();
            List<ReportPhoneResultVO> result = conPhoneSummaryMapper.queryReportSummaryDetail(dto);
            long start = System.currentTimeMillis();
            if (dto.getIsQueryUserLabel()) {
                List<String> uuidList = result.parallelStream().map(ReportPhoneResultVO::getUuid)
                        .filter(StringUtils::isNoneBlank).distinct().collect(Collectors.toList());
                CountDownLatch countDownLatch = new CountDownLatch(uuidList.size());
                executor.allowCoreThreadTimeOut(true);
                for (String uuid : uuidList) {
                    executor.execute(() -> {
                        queryUserLabel(uuid, uuidLabelMap);
                        countDownLatch.countDown();
                    });
                }
                countDownLatch.await();
            }
            log.info("queryUserLabel user time:{}ms",System.currentTimeMillis() - start);

            if(Objects.nonNull(result) && !result.isEmpty()){
                List<ReportPhoneResultVO> datas = result.stream().map(t -> {
                    if(StringUtils.isNotBlank(t.getCallSummary())){
                        String[] summarys = t.getCallSummary().split("#");
                        t.setCallSummaryOne(summarys.length >= 1 && StringUtils.isNotBlank(summarys[0]) ? summarys[0] : "");
                        t.setCallSummaryTwo(summarys.length >= 2 && StringUtils.isNotBlank(summarys[1]) ? summarys[1] : "");
                        t.setCallSummaryThree(summarys.length >= 3 && StringUtils.isNotBlank(summarys[2]) ? summarys[2] : "");
                    }
                    if (dto.getIsQueryUserLabel()) {
                        t.setUserLabel(uuidLabelMap.get(t.getUuid()));
                    }
                    return t;
                }).collect(Collectors.toList());
            }
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new FastRuntimeException("system.excel.error.default", e.getMessage());
        } catch (Exception e) {
            log.warn("导出电话小结明细异常", e);
            throw new FastRuntimeException("system.excel.error.default", e.getMessage());
        }
    }

    @Override
    public Page<ReportPhoneResultSummaryVO> query(ReportPhoneResultDTO dto) {
        try {
            checkTime(dto);
            covert(dto);
            Page<ReportPhoneResultSummaryVO> result = conPhoneSummaryMapper.queryReportSummary(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
            if (Objects.nonNull(result) && Objects.nonNull(result.getRecords()) && !result.getRecords().isEmpty()) {
                BigDecimal summaryCount = conPhoneSummaryMapper.queryReportSummaryCount(dto);
                List<ReportPhoneResultSummaryVO> records = result.getRecords().stream().map(t -> {
                    t.setSummaryPercent((double) Math
                            .round(t.getNum().divide(summaryCount, 4, RoundingMode.HALF_UP).doubleValue() * 10000) / 100
                            + "%");
                    t.setSummaryCount(summaryCount);
                    if (StringUtils.isNotBlank(t.getCallSummary())) {
                        String[] summarys = t.getCallSummary().split("#");
                        t.setCallSummaryOne(
                                summarys.length >= 1 && StringUtils.isNotBlank(summarys[0]) ? summarys[0] : "");
                        t.setCallSummaryTwo(
                                summarys.length >= 2 && StringUtils.isNotBlank(summarys[1]) ? summarys[1] : "");
                        t.setCallSummaryThree(
                                summarys.length >= 3 && StringUtils.isNotBlank(summarys[2]) ? summarys[2] : "");
                    }
                    return t;
                }).sorted(Comparator.comparing(ReportPhoneResultSummaryVO::getNum, BigDecimal::compareTo).reversed())
                        .collect(Collectors.toList());
                result.setRecords(records);
            }
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public List<ReportPhoneResultSummaryVO> queryExcel(ReportPhoneResultDTO dto) {
        try {
            checkTime(dto);
            covert(dto);
            List<ReportPhoneResultSummaryVO> result = conPhoneSummaryMapper.queryReportSummary(dto);
            if (Objects.nonNull(result) && !result.isEmpty()) {
                BigDecimal summaryCount = conPhoneSummaryMapper.queryReportSummaryCount(dto);
                List<ReportPhoneResultSummaryVO> records = result.stream().map(t -> {
                    t.setSummaryPercent((double) Math
                            .round(t.getNum().divide(summaryCount, 4, RoundingMode.HALF_UP).doubleValue() * 10000) / 100
                            + "%");
                    t.setSummaryCount(summaryCount);
                    if (StringUtils.isNotBlank(t.getCallSummary())) {
                        String[] summarys = t.getCallSummary().split("#");
                        t.setCallSummaryOne(
                                summarys.length >= 1 && StringUtils.isNotBlank(summarys[0]) ? summarys[0] : "");
                        t.setCallSummaryTwo(
                                summarys.length >= 2 && StringUtils.isNotBlank(summarys[1]) ? summarys[1] : "");
                        t.setCallSummaryThree(
                                summarys.length >= 3 && StringUtils.isNotBlank(summarys[2]) ? summarys[2] : "");
                    }
                    return t;
                }).sorted(Comparator.comparing(ReportPhoneResultSummaryVO::getNum, BigDecimal::compareTo).reversed())
                        .collect(Collectors.toList());
                result = records;
            }
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.excel.error.default", e.getMessage());
        }
    }

    // 转换dto
    private ReportPhoneResultDTO covert(ReportPhoneResultDTO dto) {
        if (StringUtils.isNotBlank(dto.getCdrHotline())) {
            dto.setCdrHotlines(Arrays.asList(dto.getCdrHotline().split(",")));
        }
        return dto;
    }
}
