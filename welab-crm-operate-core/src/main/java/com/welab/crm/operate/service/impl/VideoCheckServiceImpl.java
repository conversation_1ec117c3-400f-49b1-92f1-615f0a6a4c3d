package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.domain.CsVideoCheck;
import com.welab.crm.operate.dto.video.VideoTaskReqDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.CsVideoCheckMapper;
import com.welab.crm.operate.service.VideoCheckService;
import com.welab.crm.operate.vo.video.VideoTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 视频校验服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class VideoCheckServiceImpl implements VideoCheckService {
    @Resource
    private CsVideoCheckMapper csVideoCheckMapper;
    @Resource
    private IUploadService uploadService;

    @Override
    public Page<VideoTaskVO> queryVideoTask(VideoTaskReqDTO dto) {
        return csVideoCheckMapper.queryVideoTask(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
    }

    @Override
    public List<VideoTaskVO> queryVideoTasks(VideoTaskReqDTO dto) {
        return csVideoCheckMapper.queryVideoTask(dto);
    }

    @Override
    public String queryVideoUrl(Long id) {
        CsVideoCheck videoCheck =
            csVideoCheckMapper.selectOne(Wrappers.lambdaQuery(CsVideoCheck.class).eq(CsVideoCheck::getId, id));

        if (Objects.isNull(videoCheck)) {
            log.warn("queryVideoUrl 根据主键查询视频校验记录失败,id:{}", id);
            throw new CrmOperateException("该记录不存在");
        }

        String videoName = videoCheck.getVideoName();
        if (StringUtils.isBlank(videoName)) {
            log.warn("queryVideoUrl 改记录还未录制视频,id:{}", id);
            throw new CrmOperateException("该记录还未录制视频");
        }

        Map<String, Object> fileMap = uploadService.getUploadFileSurvivalTime(Collections.singletonList(videoName), 60 * 60 * 12L);
        return (String) fileMap.get(videoName);
    }

    @Override
    public void collectVideo(VideoTaskReqDTO dto) {
        CsVideoCheck csVideoCheck = new CsVideoCheck();
        csVideoCheck.setIsCollect(dto.getIsCollect());
        csVideoCheckMapper.update(csVideoCheck,
            Wrappers.lambdaUpdate(CsVideoCheck.class).eq(CsVideoCheck::getId, dto.getId()));
    }
}
