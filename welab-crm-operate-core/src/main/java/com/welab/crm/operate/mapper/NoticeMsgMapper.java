package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.NoticeMsg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.vo.notice.NoticeMsgVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 通知公告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface NoticeMsgMapper extends BaseMapper<NoticeMsg> {

    Page<NoticeMsgVO> selectNoticeSendByPage(Page<NoticeMsgVO> objectPage, @Param("filter") NoticeMsgReqDTO dto);

    Page<NoticeMsgVO> selectNoticeReceiveByPage(Page<NoticeMsgVO> noticeMsgVOPage, @Param("filter") NoticeMsgReqDTO dto);

    Page<NoticeMsgVO> selectReadNoticeByPage(Page<NoticeMsgVO> objectPage, @Param("filter") NoticeMsgReqDTO dto);

    Page<NoticeMsgVO> selectUnReadNoticeByPage(Page<NoticeMsgVO> objectPage, @Param("filter") NoticeMsgReqDTO dto);

    /**
     * 查询全部未读消息
     * @param staffId
     * @param type
     * @return
     */
    List<NoticeMsg> selectAllUnReadNotice(@Param("staffId") String staffId, @Param("type") String type);


    /**
     * 查询改电销任务的未读消息
     * @param tmkTaskId
     * @return
     */
    Integer selectUnReadNoticeByTmkTaskId(@Param("tmkTaskId") String tmkTaskId);

}
