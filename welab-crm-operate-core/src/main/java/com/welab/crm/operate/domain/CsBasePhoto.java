package com.welab.crm.operate.domain;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.Instant;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_base_photo")
@NoArgsConstructor
public class CsBasePhoto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 是否默认底照，默认最新的为最新底照。
     */
    private Boolean isDefault;

    /**
     * 上传人
     */
    private Long uploadStaffId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    public CsBasePhoto(Long staffId, String fileName, Boolean isDefault, Long uploadStaffId) {
        this.staffId = staffId;
        this.fileName = fileName;
        this.isDefault = isDefault;
        this.uploadStaffId = uploadStaffId;
        this.gmtCreate = new Date();
        this.gmtModify = new Date();
    }
}
