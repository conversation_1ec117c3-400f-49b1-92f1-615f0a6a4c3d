package com.welab.crm.operate.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.AuditBatchInfoReqDTO;
import com.welab.crm.operate.dto.ContractSendDTO;
import com.welab.crm.operate.dto.ContractSendReqDTO;
import com.welab.crm.operate.vo.ContractSendVO;

import java.util.List;

public interface ContractSendService {
	Page<ContractSendVO> queryContractSendPage(ContractSendReqDTO reqDTO);

	void sendContract(ContractSendDTO reqDTO, StaffVO staffVO);

	void auditContract(AuditBatchInfoReqDTO reqDTO);

	List<ContractSendVO> queryContractSendList(ContractSendReqDTO reqDTO);

	JSONObject querySendType();
}
