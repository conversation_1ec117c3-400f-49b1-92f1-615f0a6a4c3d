package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 员工状态时长开始表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("staff_status_start_time")
public class StaffStatusStartTime implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 员工Id
     */
    private Long staffId;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态开始时间
     */
    private Date startTime;

    /**
     * 开始时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
