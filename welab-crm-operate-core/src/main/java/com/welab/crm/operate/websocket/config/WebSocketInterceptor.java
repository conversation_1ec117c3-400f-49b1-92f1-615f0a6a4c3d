package com.welab.crm.operate.websocket.config;

import java.util.Map;
import java.util.Objects;

import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.util.RSAEncryptUtil;
import com.welab.crm.operate.websocket.util.WebSocketUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * websocket拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class WebSocketInterceptor extends HttpSessionHandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
            Map<String, Object> attributes) throws Exception {
        if (request instanceof ServletServerHttpRequest) {
            ServletServerHttpRequest serverHttpRequest = (ServletServerHttpRequest) request;
            //获取参数
            String endata = serverHttpRequest.getServletRequest().getParameter("mobile");
            try {
                String mobile = RSAEncryptUtil.decrypt(endata, Constant.RSA_PRI_KEY);
                WebSocketSession session = WebSocketUtil.getSessionByMobile(mobile);
                if (Objects.nonNull(session)){
                    log.warn(mobile + "不能重复连接");
                    return false;
                }
                attributes.put("mobile", mobile);
            } catch (Exception e) {
                log.error("websocket 加解密异常", e);
                return false;
            }

        }

        return super.beforeHandshake(request, response, wsHandler, attributes);
    }
}  