package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 停催名单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stop_collection_list")
public class StopCollectionList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 有效开始时间
     */
    private Date validStartTime;

    /**
     * 有效结束时间
     */
    private Date validEndTime;

    /**
     * 添加原因
     */
    private String addReason;

    /**
     * 加黑类型
     */
    private String blackType;

    /**
     * 创建人
     */
    private String staffName;

    /**
     * 创建组
     */
    private String groupName;

    /**
     * 操作类型；1-新增 2-修改
     */
    private String operateType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 当前在途贷款笔数
     */
    private Integer currentLoanCount = 0;

    /**
     * 拉黑天数
     */
    private Integer blackDay;

    /**
     * 审批人
     */
    private String operateUser;

    /**
     * 审批状态 0初审 1复审 2审批完成 3拒绝
     */
    private Integer operateState;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 是否告知
     */
    private Boolean isInform;
}
