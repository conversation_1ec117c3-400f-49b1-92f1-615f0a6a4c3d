package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.IvrKeyDict;
import com.welab.crm.operate.domain.IvrSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.vo.ivr.*;

import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ivr小结表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IvrSummaryMapper extends BaseMapper<IvrSummary> {




    /**
     * 根据ivr字典获取ivr明细
     * @param ivrKeyDict
     * @return
     */
    Page<IvrKeyDetailVOPlus> getIvrDetailByDict(Page<IvrKeyDetailVO> page, @Param("list") List<IvrKeyDict> ivrKeyDict, @Param("dto") IvrReportReqDTO reqDTO);

    /**
     * 查询通话记录
     * @param page
     * @param reqDTO
     * @return
     */
    Page<IvrKeyDetailVOPlus> getPhoneInfo(Page<IvrKeyDetailVO> page,@Param("dto") IvrReportReqDTO reqDTO);


    /**
     * 查询IVR记录
     * @param ivrKeyDict
     * @param reqDTO
     * @return
     */
    List<IvrKeyDetailVOPlus> queryIvrDetailByCondition(@Param("list") List<IvrKeyDict> ivrKeyDict,
        @Param("dto") IvrReportReqDTO reqDTO);

    /**
     * 查询IVR记录通过mainId
     * @param ivrKeyDict
     * @param cdrMainRequestIds
     * @return
     */
    List<IvrKeyDetailVOPlus> queryIvrDetailByMainId(@Param("list") List<IvrKeyDict> ivrKeyDict,
        @Param("idList") List<String> cdrMainRequestIds);


    /**
     * 获取ivr按键总和根据字典
     * @param reqDTO
     * @param ivrKeyDict
     * @return
     */
    IvrKeyDetailVOPlus countIvrDetailByDict(@Param("list") List<IvrKeyDict> ivrKeyDict, @Param("dto") IvrReportReqDTO reqDTO);




    /**
     * 查询指定时间间隔内的按键总数 根据按键字典
     * @param ivrKeyDict
     * @param reqDTO
     * @return
     */
    List<IvrStatisticsArtificialVOPlus> getIvrKeyTotalByDict(@Param("list") List<IvrKeyDict> ivrKeyDict,
                                                             @Param("dto") IvrReportReqDTO reqDTO);




    /**
     * 查询转人工按键数
     * @param reqDTO
     * @return
     */
    List<IvrCountVO> getIvrArtificialPro(@Param("dto") IvrReportReqDTO reqDTO);


    /**
     * 查询贷后服务转人工数量
     * @param ivrReportReqDTO
     * @return
     */
    List<IvrStatisticsArtificialVO> queryDhfwArtificialCount(@Param("dto") IvrReportReqDTO ivrReportReqDTO);
}
