package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.ConRepeatCall;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 重复来电表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-18
 */
public interface ConRepeatCallMapper extends BaseMapper<ConRepeatCall> {

    List<ReportDupCallTotalVO> selectManDupCalls(ReportDupCallDTO callDTO);

    int selectRepeatNumber(@Param("filter") ReportWorkStatusDTO dto);
}
