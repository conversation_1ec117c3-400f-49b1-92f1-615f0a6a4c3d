package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CustHisOperate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.operate.ApplicationOperateReqDTO;
import com.welab.crm.operate.dto.operate.OperateHistoryQueryReqDTO;
import com.welab.crm.operate.vo.operate.CustHisOperateVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 操作历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Mapper
public interface CustHisOperateMapper extends ExpandBaseMapper<CustHisOperate> {

    List<CustHisOperateVO> selectHis(
            @Param("filter") OperateHistoryQueryReqDTO reqDTO);
}
