package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ConPhoneSummary;
import com.welab.crm.operate.dto.phone.PhoneSummaryReqDTO;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultVO;
import com.welab.crm.operate.vo.screen.CountVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 通话小结表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface ConPhoneSummaryMapper extends BaseMapper<ConPhoneSummary> {

    List<PhoneSummaryVO> selectSummary(@Param("filter") PhoneSummaryReqDTO reqDTO);

    List<PhoneSummaryVO> selectSummaryByCdrMainUniqueId(@Param("cdrMainUniqueId") String cdrMainUniqueId);

    Page<ReportPhoneResultVO> queryReportSummaryDetail(Page<ReportPhoneResultVO> page, @Param("filter") ReportPhoneResultDTO reqDTO);

    List<ReportPhoneResultVO> queryReportSummaryDetail(@Param("filter") ReportPhoneResultDTO reqDTO);

    Page<ReportPhoneResultSummaryVO> queryReportSummary(Page<ReportPhoneResultSummaryVO> page, @Param("filter") ReportPhoneResultDTO reqDTO);

    List<ReportPhoneResultSummaryVO> queryReportSummary(@Param("filter") ReportPhoneResultDTO reqDTO);

    BigDecimal queryReportSummaryCount(@Param("filter") ReportPhoneResultDTO reqDTO);

    List<PhoneSummaryVO> selectCallInSummaryByCustomerIdAndStaffIdAndTime(@Param("cid") Long customerId,
            @Param("sid") Long staffId, @Param("callTime") Date callTime);

    /**
     * 获取前十数量问题
     * @return
     */
    List<CountVO> queryTop10Question();
}
