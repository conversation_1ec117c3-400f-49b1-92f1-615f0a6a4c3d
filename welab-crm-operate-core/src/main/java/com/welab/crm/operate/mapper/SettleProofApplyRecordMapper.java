package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.SettleProofApplyRecord;
import com.welab.crm.operate.vo.settlement.SettleProofApplyRecordVO;
import com.welab.crm.operate.vo.settlement.SettlementProofReqDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 结清证明申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
public interface SettleProofApplyRecordMapper extends BaseMapper<SettleProofApplyRecord> {

    Page<SettleProofApplyRecordVO> query(Page<SettleProofApplyRecordVO> page, @Param("filter") SettlementProofReqDTO dto);

}
