package com.welab.crm.operate.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 选项卡配置服务
 * @date 2021/11/1
 */
public interface TabConfigService {

    /**
     * 获得不展示的列名
     * @param staffId
     * @param tabName
     * @return
     */
    List<String> getNotDisplayedColNames(String staffId, String tabName);

    /**
     * 更新不展示的列
     * @param staffId
     * @param tabName
     * @param colNames
     */
    void updateNotDisplayedColNames(String staffId, String tabName, List<String> colNames);
}
