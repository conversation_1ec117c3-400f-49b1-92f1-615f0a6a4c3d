package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.enums.LoanTypeEnum;
import com.welab.crm.interview.service.RepayUploadService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.operate.domain.CsRepayFile;
import com.welab.crm.operate.dto.repay.RepayImportDTO;
import com.welab.crm.operate.dto.repay.RepayQueryDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.CsRepayFileMapper;
import com.welab.crm.operate.service.RepayQueryService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.repay.RepayQueryVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-11-05
 */
@Service
@Slf4j
public class RepayQueryServiceImpl implements RepayQueryService {

    @Resource
    private RepayUploadService repayUploadService;

    @Resource
    private CsRepayFileMapper repayFileMapper;

    @Resource
    private UserInfoService userInfoService;

    @Override
    public Page<RepayQueryVO> getRepayQuery(RepayQueryDTO queryDTO) {
        Page<CsRepayFile> filePage = repayFileMapper.listRepayDetailPage(getPage(queryDTO), queryDTO);
        Page<RepayQueryVO> page = new Page<>();
        BeanUtils.copyProperties(filePage, page);
        if (!filePage.getRecords().isEmpty()) {
            List<RepayQueryVO> results = new ArrayList<>(filePage.getRecords().size());
            for (CsRepayFile record : filePage.getRecords()) {
                results.add(getRepayQueryVO(record));
            }
            page.setRecords(results);
        }
        return page;
    }


    @Override
    public String getFileUrl(Long id) {
        CsRepayFile csRepayFile = repayFileMapper.selectById(id);
        if (Objects.isNull(csRepayFile)){
            throw new FastRuntimeException("文件不存在");
        }
        if (csRepayFile.getTaskState() == 0){
            throw new FastRuntimeException("任务执行中");
        }
        
        return csRepayFile.getFileUrl();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importQueryUuidList(ExcelList<RepayImportDTO> uuidList, StaffVO staffVO) {
        List<String> us = checkUuidList(uuidList);
        // 1.先保存导入的数据
        CsRepayFile repay = new CsRepayFile();
        repay.setApproveState(1);
        repay.setApproveTime(new Date());
        repay.setAuditor(staffVO.getLoginName());
        repay.setCreateUser(staffVO.getLoginName());
        repay.setTaskState(0);
        repay.setUserCount(us.size());
        repay.setFileUrl("url");
        repay.setLoanType(uuidList.get(0).getLoanType());
        repayFileMapper.insert(repay);

        log.info("开始还款明细查询, 查询id: {}", repay.getId());
        // 2.异步(interview侧异步)查询和计算数据生成可下载url, 更新到主数据记录中
        repayUploadService.queryAndSaveFileUrl(repay.getId(), uuidList.get(0).getLoanType(), us);
        log.info("结束还款明细查询, 查询id: {}", repay.getId());
    }

    private List<String> checkUuidList(ExcelList<RepayImportDTO> uuidList) {
        if (CollectionUtils.isEmpty(uuidList)) {
            throw new CrmOperateException("导入文件的列表不能为空");
        }

        String loanType = uuidList.get(0).getLoanType();
        if (LoanTypeEnum.getLoanTypeByValue(loanType) == null) {
            throw new CrmOperateException("不支持的贷款类型,请仔细查看导入模板表头");
        }

        boolean isWallet = LoanTypeEnum.WALLET.getValue().equals(loanType);
        if (isWallet && uuidList.size() > 10) {
            throw new CrmOperateException("钱夹谷谷贷款类型目前一次最多只能查询10个用户");
        } else if (isWallet) {
            // 钱包类型的导入不能太频繁，限制正在导入中的钱包类型如果没有超过1分钟，则不能做导入钱包类型的操作
            CsRepayFile file = new CsRepayFile();
            file.setTaskState(0);
            CsRepayFile repayFile = repayFileMapper.selectWalletUnComplete(file);
            if (repayFile != null) {
                throw new CrmOperateException("钱夹谷谷类型查询不能过于频繁,请等待一分钟");
            }
        }

        List<String> us = new ArrayList<>(uuidList.size());
        for (RepayImportDTO r : uuidList) {
            if (StringUtils.isBlank(r.getUuid()) && StringUtils.isBlank(r.getUserId()) && StringUtils.isBlank(r.getApplicationId())) {
                throw new CrmOperateException("导入文件不能包含空格行");
            } else if (!StringUtils.isNumeric(r.getUuid()) && !StringUtils.isNumeric(r.getUserId()) && !StringUtils.isNumeric(r.getApplicationId())) {
                throw new CrmOperateException("导入文件中包含无效的用户数据");
            } else if (StringUtils.isBlank(r.getLoanType())) {
                throw new CrmOperateException("贷款类型列不能为空");
            } else if (!loanType.equals(r.getLoanType())) {
                throw new CrmOperateException("一次只能导入一种类型的贷款");
            }
            //对于上传了userid或者applicationId的数据转换为uuid
            if (StringUtils.isNotBlank(r.getUserId())) {
                r.setUuid(userInfoService.getUuidByUserId(Integer.valueOf(r.getUserId())));
            }
            if (StringUtils.isNotBlank(r.getApplicationId())) {
                r.setUuid(userInfoService.getUuidByApplicationId(r.getApplicationId()));
            }
            us.add(r.getUuid());
        }
        return us;
    }

    private Page<CsRepayFile> getPage(RepayQueryDTO queryDTO) {
        Page<CsRepayFile> page = new Page<>();
        page.setSize(queryDTO.getPageSize() == null ? 10 : queryDTO.getPageSize());
        page.setCurrent(queryDTO.getCurPage() == null ? 1 : queryDTO.getCurPage());
        return page;
    }

    private RepayQueryVO getRepayQueryVO(CsRepayFile record) {
        RepayQueryVO queryVO = new RepayQueryVO();
        BeanUtils.copyProperties(record, queryVO);
        queryVO.setGmtCreate(DateUtils.formatDate(record.getGmtCreate(), DateUtils.YYYYMMDD_HHMM));
        queryVO.setTaskState(record.getTaskState() == 0 ? "执行中" : "已完成");
        LoanTypeEnum loanTypeEnum = LoanTypeEnum.getLoanTypeByValue(record.getLoanType());
        queryVO.setLoanType(loanTypeEnum == null ? null : loanTypeEnum.getText());
        queryVO.setFileUrl(null);
        return queryVO;
    }
}
