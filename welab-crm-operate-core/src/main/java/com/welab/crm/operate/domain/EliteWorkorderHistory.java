package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 过河兵工单历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("elite_workorder_history")
public class EliteWorkorderHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 过河兵主键唯一Id
     */
    private String objectiveGuid;

    /**
     * 过河兵客户主键，用来跟附件表关联
     */
    private String customerGuid;

    /**
     * 工单号
     */
    private String workorderId;

    /**
     * 工单类型
     */
    private String orderType;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 工单大类
     */
    private String woType1;

    /**
     * 工单二类
     */
    private String woType2;

    /**
     * 工单三类
     */
    private String woType3;

    /**
     * 工单四类
     */
    private String woType4;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 工单状态
     */
    private String orderStatus;

    /**
     * 工单创建时间
     */
    private Date createTime;

    /**
     * 结案时间
     */
    private Date endTime;

    /**
     * 本步意见
     */
    private String comments;

    /**
     * 问题描述
     */
    private String problemDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前处理组
     */
    private String currGroup;

    /**
     * 当前处理人
     */
    private String currStaff;

    /**
     * 上次处理人
     */
    private String lastStaff;

    /**
     * 上次处理时间
     */
    private Date lastDealTime;


}
