package com.welab.crm.operate.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wo_rule_info")
public class WoRuleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 工单类型
     */
    private String workOrderType;

    /**
     * 子工单分类
     * 
     */
    private String orderCase;

    /**
     * 处理组
     */
    private String handleGroupId;

    /**
     * 处理人
     */
    private String handleStaffId;
	
	/**
     * 分配日期
     */
    private String assignDate;

    /**
     * 开始时间
     */
    private String startTime;
	
	/**
     * 结束时间
     */
    private String endTime;
	
	/**
     * 分单间隔
     */
    private Integer assignInterval;

	/**
     * 开关状态（开启，关闭）
     */
    private String status;
	
    /**
     * 优先级
     */
    private Integer level;
	
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
    
    /**
     * 规则最新执行时间
     */
    private Date gmtHandle;
	
	/**
     * 创建人
     */
    private String createStaffId;

    /**
     * 修改人
     */
    private String modifyStaffId;
    
    /**
     * 规则类型：order工单规则，telemarket电销规则
     */
    private String ruleType;

    /**
     * 分配类型：date按日期，num按数量
     */
    private String assignType;
	
	/**
     * 分配数量
     */
    private Integer assignNum;

    private String applyOrigin;

    /**
     * 上一次分配的staffId
     */
    private String lastAssignStaffId;
}
