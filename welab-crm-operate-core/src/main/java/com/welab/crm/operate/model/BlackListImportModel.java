package com.welab.crm.operate.model;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 黑名单导入模版
 * userId和uuid不能为空，如果是联系人黑名单userId、uuid、手机号不能为空
 *
 */
@Getter
@Setter
@Accessors(chain = true)
public class BlackListImportModel implements Serializable {
	private static final long serialVersionUID = 7882160426354911140L;
	/**
	 *合同号
	 */
	@ExcelProperty(value = "合同号")
	private String contractNo;
	
	/**  
	 * 身份证号
	 */
	@ExcelProperty(value="身份证号")
	private String idNo;
	/**  
	 * 手机号
	 */
	@ExcelProperty(value="手机号")
	private String mobile;

	/**
	 * 用户ID
	 */
	@ExcelProperty(value="用户ID")
	private String userId;

	/**
	 * 黑名单类型
	 */
	@ExcelProperty(value="黑名单类型(催收黑名单,联系人电话黑名单)")
	private String blackType;
	/**  
	 * 添加原因
	 */
	@ExcelProperty(value="添加原因")
	private String addReason;
	/**  
	 * 有效开始时间
	 */
	@ExcelProperty(value="有效开始时间(yyyy/MM/dd)*")
	private String validStartTime ;
	/**  
	 * 有效结束时间
	 */
	@ExcelProperty(value="有效结束时间(yyyy/MM/dd)*")
	private String validEndTime;

	/**  
	 * 备注
	 */
	@ExcelProperty(value="备注")
	private String remark;


	@ExcelProperty(value="uuid")
	private String uuid;



}