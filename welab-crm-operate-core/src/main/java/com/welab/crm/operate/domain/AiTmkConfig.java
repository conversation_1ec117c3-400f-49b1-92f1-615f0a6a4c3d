package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电销-ai推送配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_tmk_config")
public class AiTmkConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 产品名称(多个产品之间用,相隔)
     */
    private String productNames;

    /**
     * 进件渠道号(多个渠道号之间用,相隔)
     */
    private String loanChannels;
    
    /**
     * 标签编码(多个标签之间用,相隔)
     */
    private String labelCode;

    /**
     * 推送供应商代码
     */
    private String caCompany;

    /**
     * ai话术
     */
    private Integer speechId;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 推送方式(当前只有按时间一种,预留这个字段): 0-按时间
     */
    private Integer pushWay;

    /**
     * 推送方式对应的数量值
     */
    private Integer number;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 推送开始时间点: HH:mm
     */
    private Date startTime;

    /**
     * 推送结束时间点: HH:mm
     */
    private Date endTime;

    /**
     * 最大审批金额
     */
    private String maxAmount;

    /**
     * 最小审批金额
     */
    private String minAmount;

    /**
     * 审批时段
     */
    private String approvedAt;

    /**
     * 状态标识(0:未分配；1,2：已分配)
     */
    private String flag;

    /**
     * 状态: 0-不启用 1-启用
     */
    private Boolean state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 软删除标识: 0-未删除 1-已删除
     */
    private Boolean deleteFlag;


}
