package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工单组合表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_dict_info_conf")
public class OpDictInfoConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeId;

    /**
     * 工单大类描述
     */
    private String woTypeDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeFirId;

    /**
     * 工单一类描述
     */
    private String woTypeFirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeSecId;

    /**
     * 工单二类描述
     */
    private String woTypeSecDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeThirId;

    /**
     * 工单三类描述
     */
    private String woTypeThirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeChildId;

    /**
     * 子工单描述
     */
    private String woTypeChildDetail;

    /**
     * 工单模板描述
     */
    private String description;

    /**
     * 投诉级别: highRisk-高危投诉, complain-投诉
     */
    private String complainLevel;

    /**
     * 需要核实结果: 0-不需要核实,1-需要核实
     */
    private Boolean mustReview;

    /**
     * 需要处理结果: 0-不需要处理,1-需要处理
     */
    private Boolean mustApprove;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态；1-有效，2-无效
     */
    private Integer isStatus;

    /**
     * 快捷键描述
     */
    private String detail;

    /**
     * 快捷键标识 Y/N
     */
    private String fastStatus;

    /**
     * 所属组
     */
    private String groupCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 百融问题类型
     */
    private String bairongQuestionType;

    /**
     * 快捷工单使用对象。source-渠道；资金方-partner
     */
    private String fastTargetUser;


}
