package com.welab.crm.operate.anotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD) // 作用到方法上
@Retention(RetentionPolicy.RUNTIME) // 运行时有效
public @interface NoRepeatSubmit {
 
    String name() default "name:";


    /**
     * 防重复操作过期时间（借助redis实现限时控制）
     */
    int expireSeconds() default 5;


    /**
     * 单位时间内的操作次数上限
     */
    int times() default 2;
    
    
    String key() default "";
}