package com.welab.crm.operate.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-10-28 17:05:34
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class ColStaffModel implements Serializable {
	private static final long serialVersionUID = -1717534575550992970L;

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 催员编码
	 */
	private String staffId;

	/**
	 * 姓名
	 */
	private String loginName;

	/**
	 * 催员姓名
	 */
	private String staffName;

	/**
	 * 催员手机
	 */
	private String staffMobile;

	/**
	 * 所属阶段
	 */
	private String groupStage;

	/**
	 * 所属组code
	 */
	private String groupCode;

	/**
	 * 所属组名
	 */
	private String groupName;

	/**
	 * 记录状态，1：有效，2：无效
	 */
	private Integer isStatus;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改时间
	 */
	private Date updateTime;
}
