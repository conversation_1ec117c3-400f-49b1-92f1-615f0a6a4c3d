package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.SingleApprovalDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionAddReqDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionQueryReqDTO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportFailVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportTempVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionVO;

import java.util.List;

public interface BlackProductionService {

	/**
	 * 添加黑产信息
	 * @param reqDTO
	 */
	void addBlackProduction(BlackProductionAddReqDTO reqDTO);


	/**
	 * 查询黑产信息
	 */
	Page<BlackProductionVO> queryBlackProductionUserInfoPage(BlackProductionQueryReqDTO reqDTO);


	/**
	 * 导入黑产信息
	 * @param importList 导入列表
	 * @param staffId 操作人
	 */
	List<BlackProductionImportFailVO> importBlackProductionVo(List<BlackProductionImportTempVO> importList, Long staffId);


	/**
	 * 审批黑产记录
	 * @param dto
	 */
	void approvalBlackProductionRecord(SingleApprovalDTO dto);

	
	
	
	
	
	
	
	
}
