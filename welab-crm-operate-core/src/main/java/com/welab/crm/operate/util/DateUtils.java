package com.welab.crm.operate.util;

import ch.qos.logback.core.util.DatePatternToRegexUtil;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.exception.FastRuntimeException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日期工具类
 * <AUTHOR>
 * @version 2019-03-02
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
	private static final Logger LOGGER = LoggerFactory.getLogger(DateUtils.class);

	public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String  YYYY_MM_DD = "yyyy-MM-dd";

	public static final String  YYYYMMDD = "yyyyMMdd";

	public static final String  HH_MM_SS = "HH:mm:ss";

	public static final String  HH_MM = "HH:mm";

	public static final String  HHMMSS = "HHmmss";

	public static final String YYYY_MM_DD_HHMMSS_SSS="yyyy-MM-dd HH:mm:ss.SSS";

	public static final String  HH = "HH";

	public static final String  YYYY_MM = "yyyy-MM";
	
	public static final String  YYYYMMDD_FORMAT = "yyyy/MM/dd";

	public static final String  YYYYMMDD_HHMMSS = "yyyy/MM/dd HH:mm:ss";

	public static final String  YYYYMMDD_HHMM = "yyyy-MM-dd HH:mm";

	public static final String  YMD_DATE = "yyyy年MM月dd日";
	
	public static ThreadLocal<DateFormat> threadLocal  = new ThreadLocal<DateFormat>();

    public static DateFormat getDateFormat(){
        DateFormat dateFormat = threadLocal.get();
        if(dateFormat == null){
            dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            threadLocal.set(dateFormat);
        }
        return dateFormat;
    }

	/**
	 * 获取当前时间
	 * @return
	 */
	public static Date getCurrentDateTime()
	{
		return new Date();
	}

	/**
	 * 日期转换成字符串
	 * 
	 * @param date
	 * @param format
	 * @return
	 */
	public static String formatDate(Date date, String format)
	{
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		return dateFormat.format(date);
	}

	/**
	 * 字符串转成成日期
	 * @param stringDate
	 * @param format
	 * @return
	 */
	public static Date parseDate(String stringDate, String format)
	{
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		try
		{
			return dateFormat.parse(stringDate);
		}
		catch (ParseException e)
		{
			LOGGER.error("DateUtils.parseDate error", e);
		}

		return null;
	}

    /**
     * 获取制定时间日期的开始时间
     * @param date
     * @return
     */
	public static Date getStartOfDate(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.MILLISECOND,0);
        return calendar.getTime();
	}
	  /**
     * 获取指定日期的最后结束时间
     * @param date
     * @return
     */
    public static Date getEndOfDate(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }
	/**
	 * 昨天的开始时间
	 * 
	 * @return
	 */
	public static Date getStartOfYesterday() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.add(Calendar.DATE, -1);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static Date getEndOfYesterday() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.add(Calendar.DATE, -1);
		return calendar.getTime();
	}
	
	/**
	 * 今天的开始时间
	 * 
	 * @return
	 */
	public static Date getStartOfToday() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 今天的开始时间
	 *
	 * @return
	 */
	public static Date getEndOfToday() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		return calendar.getTime();
	}

	/**
	 * 获取指定日期的天,返回字符串
	 *
	 * @param date
	 * @return
	 */
	public static String getDayStr(Date date) {
		Calendar calendar = getGregorianCalendar(date);
		Integer day = calendar.get(Calendar.DAY_OF_MONTH);
		if (day < 10) {
			return "0" + day;
		}
		return day.toString();
	}

	/**
	 * 获得指定日期的月,返回字符串
	 *
	 * @param date
	 * @return
	 */
	public static String getMonthStr(Date date) {
		Calendar calendar = getGregorianCalendar(date);
		Integer month = calendar.get(Calendar.MONTH) + 1;
		if (month < 10) {
			return "0" + month;
		}
		return month.toString();
	}
	
	/**
	 * 初始化日期类，date为空，取当前日期
	 *
	 * @param date
	 * @return
	 */
	private static Calendar getGregorianCalendar(Date date)
	{
		Calendar calendar = GregorianCalendar.getInstance();
		if (null != date)
		{
			calendar.setTime(date);
		}
		return calendar;
	}
	/** 
	 * 获得指定日期的后一天 
	 *  
	 * @param specifiedDay 
	 * @return 
	 * @throws Exception 
	 */  
	public static String getSpecifiedDayBefore(String specifiedDay) {//可以用new Date().toLocalString()传递参数  
		Calendar c = Calendar.getInstance();  
		Date date = null;  
		try {  
			date = new SimpleDateFormat("yy-MM-dd").parse(specifiedDay);  
		} catch (ParseException e) {  
			e.printStackTrace();  
		}  
		c.setTime(date);  
		int day = c.get(Calendar.DATE);  
		c.set(Calendar.DATE, day +1);  
		String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c  
				.getTime());  
		return dayBefore;  
	} 
	/**
     * 计算两个日期之间相差的天数
     * 
     * @param smdate 较小的时间
     * @param bdate 较大的时间
     * @return 相差天数
     * @throws Exception
     */
    public static int daysBetween(Date smdate, Date bdate) throws Exception
    {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");

    	
        smdate = sdf1.parse(sdf1.format(smdate));
        bdate = sdf1.parse(sdf1.format(bdate));
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();

        return Integer.parseInt(String.valueOf((time2 - time1) / (1000 * 3600 * 24)));
    }
    public static String addMonth(String s, int n)
    {
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            
            Calendar cd = Calendar.getInstance();
            cd.setTime(sdf.parse(s));
            // cd.add(Calendar.MONTH, n);//增加一个月
            cd.add(Calendar.DAY_OF_YEAR, n);// 增加一天
            return sdf.format(cd.getTime());
            
        }
        catch (Exception e)
        {
            return null;
        }
    }
    
    /**
     * 过去i年的时间
     * @return
     */
    public static Date subYear(int i) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -i);
        return calendar.getTime();
    }
    
    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate() {
        return DateFormatUtils.format(new Date(), YYYY_MM_DD);
    }
    //获得当天24点时间
    public static int getTimesnight(){
    	Calendar cal = Calendar.getInstance();
    	cal.set(Calendar.HOUR_OF_DAY, 0);
    	cal.set(Calendar.SECOND, 0);
    	cal.set(Calendar.MINUTE, 0);
    	cal.set(Calendar.MILLISECOND, 0);
		cal.add(Calendar.DAY_OF_MONTH, 1);
    	return (int) (cal.getTimeInMillis()/1000);
    }

	public static Date[] getSpecifyDate(int specify) {
		Calendar calendarStart = new GregorianCalendar();
		calendarStart.setTime(new Date());
		calendarStart.add(Calendar.DATE, specify);
		String todayStart = new SimpleDateFormat("yyyy-MM-dd 00:00:00").format(calendarStart.getTime());

		Calendar calendarEnd = new GregorianCalendar();
		calendarEnd.setTime(new Date());
		calendarEnd.add(Calendar.DATE, specify);
		String todayEnd = new SimpleDateFormat("yyyy-MM-dd 23:59:59").format(calendarEnd.getTime());

		Date dateStart = null;
		Date dateEnd = null;
		try {
			dateStart = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(todayStart);
			dateEnd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(todayEnd);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return new Date[]{dateStart, dateEnd};
	}
	public static Date getLstMonthFristDay() {
		//30天内
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR,-30);
		return calendar.getTime();
	}
	public static String getTimeStr(Date date) {
		if(date==null) {
			return null;
		}
		return DateUtils.formatDate(date, DATE_FORMAT);
	}
	/**
     * 获取月初日期时间
     * @param date
     * @return
     */
    public static Date getBeginMonthDate(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

	/**
	 * 秒转换为时分秒的形式
	 *
	 * @param seconds 待转换的秒
	 * @return
	 */
	public static String secondsToTime(Integer seconds) {
		StringBuilder time = new StringBuilder();
		int hour = seconds / 3600;
		if (hour < 10) {
			time.append("0").append(hour);
		} else {
			time.append(hour);
		}
		time.append(":");
		int minute = (seconds % 3600) / 60;
		if (minute < 10) {
			time.append("0").append(minute);
		} else {
			time.append(minute);
		}
		time.append(":");
		int second = (seconds % 3600) % 60;
		if (second < 10) {
			time.append("0").append(second);
		} else {
			time.append(second);
		}
		return time.toString();
	}


	/**
	 * 秒转换为时分秒的形式
	 *
	 * @param seconds 待转换的秒
	 * @return
	 */
	public static String secondsToTimeByLong(Long seconds) {
		StringBuilder time = new StringBuilder();
		long hour = seconds / 3600;
		if (hour < 10) {
			time.append("0" + hour);
		} else {
			time.append(hour);
		}
		time.append(":");
		long minute = (seconds % 3600) / 60;
		if (minute < 10) {
			time.append("0" + minute);
		} else {
			time.append(minute);
		}
		time.append(":");
		long second = (seconds % 3600) % 60;
		if (second < 10) {
			time.append("0" + second);
		} else {
			time.append(second);
		}
		return time.toString();
	}

	/**
	 * 时分秒转换为秒
	 * @param time
	 * @return
	 */
	public static int getSeconds(String time) {
		String[] arr = time.split(":");
		int hour = Integer.parseInt(arr[0]);
		int min = Integer.parseInt(arr[1]);
		int sec = Integer.parseInt(arr[2]);
		return hour * 3600 + min * 60 + sec;
	}

	/**
	 * 获取指定日期所在周的周一
	 *
	 * @param date
	 * @return
	 */
	public static Date getFirstDayOfWeek(Date date) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 2;
		c.add(Calendar.DATE, -day_of_week);
		return c.getTime();
	}

	/**
	 * 获取指定日期所在周的周日
	 *
	 * @param date 日期
	 */
	public static Date getLastDayOfWeek(Date date) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		// 如果是周日直接返回, 默认以周日为一周的第一天
		int day = c.get(Calendar.DAY_OF_WEEK);
		if (day == 1) {
			return date;
		}
		c.add(Calendar.DATE, 8 - day);
		return c.getTime();
	}

	/**
	 * 判断 time 是否在 startTime 和 endTime 之间
	 * 是 返回 true
	 * 否 返回 false
	 * @param time
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static Boolean isBetween(Date time, Date startTime, Date endTime){
		if (Objects.isNull(time) || Objects.isNull(startTime) || Objects.isNull(endTime)){
			throw new CrmOperateException("比较时间不能为空");
		}
		long timeMS = time.getTime();
		long startTimeMS = startTime.getTime();
		long endTimeMS = endTime.getTime();
		if (timeMS >= startTimeMS && timeMS <= endTimeMS){
			return true;
		}
		return false;
	}


	public static boolean isInValidDate(String timeRange, String separator) {
		if (StringUtils.isBlank(timeRange)) {
			return true;
		}
		try {
			String[] split = timeRange.split(separator);
			if ("永久".equals(split[1])) {
				return true;
			}

			Date startDate = DateUtil.stringToDate(split[0]);
			Date endDate = DateUtil.stringToDate(split[1]);
			return isBetween(new Date(), startDate, endDate);
		} catch (Exception e) {
			log.error("时间格式错误", e);
		}
		return true;
	}

	/**
	 * 获取两个日期之间相隔的月份
	 * @param date1 更小的日期
	 * @param date2 更大的日期
	 * @return getMonthInterval(2023-04-22,2023-05-22) = 1
	 * getMonthInterval(2023-04-22,2023-05-23) = 1
	 * getMonthInterval(2023-04-22,2023-05-21) = 0
	 */
	public static int getMonthInterval(LocalDate date1, LocalDate date2) {
		Period period = Period.between(date1, date2);
		int months = period.getMonths();
		int years = period.getYears();
		return months + years * 12;
	}

	/**
	 * 获取两个时间之间的秒数
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static long getSecondsBetween(Date startTime, Date endTime) {
		return Seconds.secondsBetween(new DateTime(startTime), new DateTime(endTime))
				.getSeconds();
	}

	public static long getHoursBetween(Date startDate, Date endDate) {
		long difference = endDate.getTime() - startDate.getTime();
		return difference / (60 * 60 * 1000);
	}

	public static void main(String[] args) {
		LocalDate date1 = LocalDate.of(2023, 3, 22);
		LocalDate date2 = LocalDate.now();
		int monthInterval = getMonthInterval(date1, date2);
		System.out.println("两个日期间隔：" + monthInterval + "个月");
	}
}
