package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class InAuthCrmStaffServiceImpl implements InAuthCrmStaffService {

    @Resource
    private InAuthCrmStaffMapper staffMapper;

    @Override
    public Map<String, String> getStaffNameMap(Set<String> loginNames) {
        LambdaQueryWrapper<InAuthCrmStaff> wrapper = Wrappers.<InAuthCrmStaff>lambdaQuery()
                .in(InAuthCrmStaff::getLoginName, loginNames).eq(InAuthCrmStaff::getIsStatus, 1);
        List<InAuthCrmStaff> staffList = staffMapper.selectList(wrapper);
        if (staffList.isEmpty()) {
            return Collections.emptyMap();
        }
        return staffList.stream().collect(Collectors.toMap(InAuthCrmStaff::getLoginName,
                InAuthCrmStaff::getStaffName, (v1, v2) -> v2));
    }

    @Override
    public Map<String, String> getGroupNameMap() {
        List<InAuthCrmStaff> staffList = queryAllStaff();

        if (staffList.isEmpty()) {
            return Collections.emptyMap();
        }
        return staffList.stream().collect(Collectors.toMap(InAuthCrmStaff::getLoginName,
                InAuthCrmStaff::getGroupName, (v1, v2) -> v2));
    }

    private List<InAuthCrmStaff> queryAllStaff() {
        LambdaQueryWrapper<InAuthCrmStaff> wrapper = Wrappers.<InAuthCrmStaff>lambdaQuery().eq(InAuthCrmStaff::getIsStatus, 1);
        return staffMapper.selectList(wrapper);
    }

    @Override
    public Map<Long, InAuthCrmStaff> getStaffIdMap() {
        List<InAuthCrmStaff> staffList = staffMapper.selectList(null);

        if (staffList.isEmpty()) {
            return Collections.emptyMap();
        }

        return staffList.stream().collect(Collectors.toMap(InAuthCrmStaff::getId, inAuthCrmStaff -> inAuthCrmStaff));

    }


    @Override
    public InAuthCrmStaff getStaffByMobile(String mobile) {
        LambdaQueryWrapper<InAuthCrmStaff> wrapper = Wrappers.<InAuthCrmStaff>lambdaQuery()
                .eq(InAuthCrmStaff::getStaffMobile, mobile)
		        .orderByDesc(InAuthCrmStaff::getIsStatus, InAuthCrmStaff::getStaffStatus);
        List<InAuthCrmStaff> staffList = staffMapper.selectList(wrapper);
        if (staffList.isEmpty()) {
            return null;
        }
        
        return staffList.get(0);
    }
}
