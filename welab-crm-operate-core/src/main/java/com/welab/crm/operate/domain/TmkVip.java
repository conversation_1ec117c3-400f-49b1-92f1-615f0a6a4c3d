package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员电销表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_vip")
public class TmkVip implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电销唯一任务Id(vip**********)
     */
    private String tmkTaskId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 用户UUID
     */
    private String uuid;

    /**
     * 供应商订单号
     */
    private String supplierOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付时间
     */
    private Date paymentAt;

    /**
     * 来源
     */
    private String source;

    /**
     * 会员金额(328,199,188)
     */
    private String paymentAmount;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 初始工单状态
     */
    private String initOrderStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 未下单标识
     */
    private String noOrderFlag;

    /**
     * 状态标识(0:未分配；1：已分配；7:被拦截8：已废弃)
     */
    private String flag;

    /**
     * 员工Id
     */
    private Long staffId;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 订单更新时间
     */
    private Date orderUpdateTime;

    /**
     * 支付结果信息
     */
    private String repayMessage;

    /**
     * 支付结果码
     */
    private String repayCode;

    /**
     * 最新联系小结Id
     */
    private Long summaryId;

    /**
     * 分配时间
     */
    private Date assignDate;

    /**
     * 拨打次数
     */
    private Integer callNum;

    /**
     * 付费模式: pay_later是先享后付、pay_first是先付费
     */
    private String memberPayMode;
}
