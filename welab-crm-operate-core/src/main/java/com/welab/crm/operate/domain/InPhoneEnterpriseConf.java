package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 软电话呼叫中心配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_phone_enterprise_conf")
public class InPhoneEnterpriseConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 呼叫中心编码
     */
    private String code;

    /**
     * 呼叫中心名字
     */
    private String name;

    /**
     * 呼叫中心token
     */
    private String token;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 状态 0-有效 1-无效
     */
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
