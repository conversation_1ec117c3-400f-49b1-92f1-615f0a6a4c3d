package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.WoTaskSign;
import com.welab.crm.operate.dto.workorder.WorkOrderSignReqDTO;

/**
 * <p>
 * 工单标记表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface WoTaskSignMapper extends BaseMapper<WoTaskSign> {
	WoTaskSign queryWorkOrderSign(@Param("reqDTO") WorkOrderSignReqDTO reqDTO);
}
