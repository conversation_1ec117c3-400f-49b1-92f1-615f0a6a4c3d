package com.welab.crm.operate.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * AI 接口返回结果
 * 
 * <AUTHOR>
 */
@Data
public class AiRetModel {

    /**
     * 状态码 0-成功 1-失败
     */
    private Integer ret;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 描述信息
     */
    private String msg;

    /**
     * 匹配结果
     */
    private Result result;


    @Data
    public static class Result {
        private Integer match_index;

        private BigDecimal match_sim;

    }



}


