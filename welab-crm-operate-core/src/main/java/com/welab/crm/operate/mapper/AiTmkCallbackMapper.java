package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.AiTmkCallback;
import com.welab.crm.operate.vo.ai.AiTmkCallBackVO;
import com.welab.crm.operate.vo.telemarketing.AiTransformReportVO;
import java.util.Date;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-03-18
 */
public interface AiTmkCallbackMapper extends BaseMapper<AiTmkCallback> {

    /**
     * 根据用户id获取ai外呼情况
     * @param id
     * @return
     */
    List<AiTmkCallBackVO> queryCallBack(@Param("id") Long id);

    /**
     * 根据用户id获取ai外呼情况
     */
    List<AiTmkCallBackVO> queryUuidCallBack(@Param("id") Long id);

    /**
     * 查询
     * @param startTime
     * @param endTime
     * @param ruleId
     * @return
     */
    List<AiTransformReportVO> queryAiTransformData(@Param("starTime") Date startTime, @Param("endTime") Date endTime,
            @Param("ruleId") Long ruleId);
}
