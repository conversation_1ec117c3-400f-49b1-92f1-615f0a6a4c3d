package com.welab.crm.operate.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/10
 */
@Data
@ApiModel(value = "客服呼入工作状态报表实体类")
public class ReportCallInWorkStatusModel implements Serializable {

    private static final long serialVersionUID = 2898389753336121730L;

    @ApiModelProperty(value = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    private String cno;

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "团队代码")
    private String groupCode;

    @ApiModelProperty(value = "团队")
    private String groupName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "持续时间")
    private Integer duration;
}
