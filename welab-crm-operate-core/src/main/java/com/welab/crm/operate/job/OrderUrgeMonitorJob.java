package com.welab.crm.operate.job;

import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.message.SmsSendDTO;
import com.welab.crm.operate.enums.WebSocketMsgTypeEnum;
import com.welab.crm.operate.service.OrderMonitorService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO;
import com.welab.crm.operate.websocket.util.WebSocketUtil;
import com.welab.crm.operate.ws.SendMsgDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单催单监控任务
 * 
 * <AUTHOR>
 */
@Slf4j
public class OrderUrgeMonitorJob implements SimpleJob {

    private static final String NOTICE_MESSAGE = "当前有%d条重要催单数据，请尽快处理！";

    @Resource
    private OrderMonitorService orderMonitorService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long startTime = System.currentTimeMillis();
        log.info("OrderUrgeMonitorJob start");

        List<OrderUrgeMonitorVO> urgeOrderList = orderMonitorService.queryOrderUrgeList();
	    List<OrderUrgeMonitorVO> filterList = filterListByDict(urgeOrderList);
        if (CollectionUtils.isEmpty(filterList)){
            log.info("无需要提醒的催单记录");
            return;
        }
        Map<String, List<OrderUrgeMonitorVO>> staffMap =
            filterList.stream().collect(Collectors.groupingBy(OrderUrgeMonitorVO::getStaffMobile));
        staffMap.forEach((mobile, value) -> {
            JSONObject msgJson = new JSONObject();
            msgJson.put("content", String.format(NOTICE_MESSAGE, value.size()));
            msgJson.put("type", WebSocketMsgTypeEnum.URGE_ORDER.getCode());
            sendWebsocketMsg(mobile, msgJson);
        });
        log.info("OrderUrgeMonitorJob end use time:{} ms", System.currentTimeMillis() - startTime);
    }

    private void sendWebsocketMsg(String mobile, JSONObject msgJson) {
        SendMsgDTO dto = new SendMsgDTO();
        dto.setMobile(mobile);
        dto.setMessageJson(msgJson);
        WebSocketUtil.sendByMobile(dto);
    }

    private List<OrderUrgeMonitorVO> filterListByDict(List<OrderUrgeMonitorVO> urgeOrderList) {
        List<OpDictInfo> monitorOrderTypeList = CommonUtils.getDict("UrgeOrderType", null);
        if (CollectionUtils.isEmpty(monitorOrderTypeList)) {
            return Collections.emptyList();
        }
        List<String> orderTypeStrList =
            monitorOrderTypeList.stream().map(item -> item.getType() + item.getContent()).collect(Collectors.toList());
        return urgeOrderList.stream()
            .filter(item -> orderTypeStrList.contains(item.getOrderType() + item.getOrderThreeType()))
            .collect(Collectors.toList());
    }
}
