package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电销分单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_assign_history")
public class TmkAssignHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 分单规则id
     */
    private Long ruleId;
    
    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 用户UUID
     */
    private String uuid;
    
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 进件渠道
     */
    private String applyOrigin;
    
    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 合同号
     */
    private String applicationId;
    
    /**
     * 原来分单组
     */
    private String preGroupCode;

    /**
     * 原来分单人id
     */
    private String preStaffId;

    /**
     * 分单组
     */
    private String groupCode;

    /**
     * 分单人id
     */
    private String staffId;

    /**
     * 电销唯一ID
     */
    private String tmkTaskId;

    /**
     * 分单时间
     */
    private Date distributionTime;

    /**
     * 分单类型
     */
    private String distributionType;

    /**
     * 创建人
     */
    private String createStaffId;
    
    /**
     * 创建人组
     */
    private String createGroupCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
