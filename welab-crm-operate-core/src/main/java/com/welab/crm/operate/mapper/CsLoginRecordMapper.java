package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CsLoginRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.StaffQueryDTO;
import com.welab.crm.operate.vo.loginReport.LoginReportDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客服系统登录记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public interface CsLoginRecordMapper extends BaseMapper<CsLoginRecord> {

	/**
	 * 分页查询登录报表明细
	 */
	Page<LoginReportDetailVO> queryLoginReportDetailPage(Page<Object> objectPage, @Param("dto") StaffQueryDTO dto);

	/**
	 * 不分页查询登录报表明细
	 */
	List<LoginReportDetailVO> queryLoginReportDetailList(@Param("dto") StaffQueryDTO dto);
}
