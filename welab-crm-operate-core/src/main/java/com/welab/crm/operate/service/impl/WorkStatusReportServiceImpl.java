package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.constant.StaffWorkStatusConstant;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.enums.CallStatusEnum;
import com.welab.crm.operate.enums.CallTypeEnum;
import com.welab.crm.operate.enums.PeriodEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.model.ReportCallInWorkStatusModel;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.service.WorkStatusReportService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.phone.CallInfoStatsVO;
import com.welab.crm.operate.vo.woReport.*;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkStatusReportServiceImpl implements WorkStatusReportService {

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private OpStaffStatusHisMapper hisMapper;

    @Resource
    private ConPhoneCallInfoMapper callInfoMapper;

    @Resource
    private ConSatisfactionMapper conSatisfactionMapper;

    @Resource
    private ConRepeatCallMapper conRepeatCallMapper;
    
    
    @Resource
    private OpStaffExtraStatusLogMapper opStaffExtraStatusLogMapper;


    @Override
    public Page<ReportWorkStatusSummaryVO> getWorkStatusSummaryPage(ReportWorkStatusDTO dto) {
        if (StringUtils.isBlank(dto.getPeriod())){
            throw new FastRuntimeException("时间间隔不能为空");
        }
        long startTime = System.currentTimeMillis();
        // 查询每个员工所有工作状态
        Page<ReportWorkStatusSummaryVO> summaryPge = getWorkStatusSummaryBasic(dto);
        long middleTime = System.currentTimeMillis();
        log.info("getWorkStatusSummaryPage spend time: {}ms, s:{}, e:{}", middleTime - startTime, dto.getStartTime(),
                dto.getEndTime());
        if (CollectionUtils.isEmpty(summaryPge.getRecords())) {
            return summaryPge;
        }
        // 设置满意度数据
        fillSatisfyData(dto, summaryPge.getRecords());
        // 添加一条加总的总计数据放在当前分页的列表最后
        ReportWorkStatusSummaryVO total = getTotalWorkStatusSummary(dto, summaryPge);
        summaryPge.getRecords().add(total);
        log.info("getTotalWorkStatusSummary spend time: {}ms, s:{}, e:{}", System.currentTimeMillis() - middleTime,
                dto.getStartTime(), dto.getEndTime());
        return summaryPge;
    }


    @Override
    public List<ReportWorkStatusSummaryVO> listWorkStatusSummary(ReportWorkStatusDTO dto) {
        setMaxPageOnDto(dto);
        Page<ReportWorkStatusSummaryVO> pages = getWorkStatusSummaryPage(dto);
        return pages.getRecords();
    }

    @Override
    public Page<ReportCallInWorkStatusVO> getCallInWorkStatus(ReportWorkStatusDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ReportCallInWorkStatusModel> page = getPageParam(dto);
        Page<ReportCallInWorkStatusModel> modelPage = hisMapper.selectCallInWorkStatus(page, dto);
        if (CollectionUtils.isEmpty(modelPage.getRecords())) {
            return new Page<>();
        }
        List<ReportCallInWorkStatusVO> voList = convertToVoList(modelPage.getRecords());
        Page<ReportCallInWorkStatusVO> pageVo = new Page<>();
        pageVo.setCurrent(dto.getCurrentPage()).setSize(dto.getRowsPerPage()).setTotal(modelPage.getTotal());
        pageVo.setPages(modelPage.getPages()).setRecords(voList);
        return pageVo;
    }

    @Override
    public List<ReportCallInWorkStatusVO> getCallInWorkStatusList(ReportWorkStatusDTO dto) {
        setMaxPageOnDto(dto);
        Page<ReportCallInWorkStatusVO> voPage = getCallInWorkStatus(dto);
        return voPage.getRecords();
    }

    @Override
    public Page<ReportStaffEfficiencyVO> getStaffEfficiencyPage(ReportWorkStatusDTO dto) {
        long startTime = System.currentTimeMillis();
        Page<ReportStaffEfficiencyVO> basic = getStaffEfficiencyBasic(dto);
        long middleTime = System.currentTimeMillis();
        log.info("getStaffEfficiencyPage spend time: {}ms, s:{}, e:{}", middleTime - startTime
                , dto.getStartTime(), dto.getEndTime());
        if (CollectionUtils.isEmpty(basic.getRecords())) {
            return basic;
        }
        // 添加一条加总的总计数据放在当前分页的列表最后
        ReportStaffEfficiencyVO total = getTotalStaffEfficiency(dto, basic);
        basic.getRecords().add(total);
        log.info("getTotalStaffEfficiency spend time: {}ms, s:{}, e:{}", System.currentTimeMillis() - middleTime
                , dto.getStartTime(), dto.getEndTime());
        return basic;
    }

    @Override
    public List<ReportStaffEfficiencyVO> listStaffEfficiency(ReportWorkStatusDTO dto) {
        setMaxPageOnDto(dto);
        return getStaffEfficiencyPage(dto).getRecords();
    }

    private Page<ReportWorkStatusSummaryVO> getWorkStatusSummaryBasic(ReportWorkStatusDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ReportWorkStatusSummaryVO> pageParam = getPageParam(dto);
        // 查询每个员工所有工作状态
        Page<ReportWorkStatusSummaryVO> summaryPge = hisMapper.selectWorkStatusSummary(pageParam, dto);
        if (CollectionUtils.isEmpty(summaryPge.getRecords())) {
            return summaryPge;
        }
        // 按时间排序
        summaryPge.getRecords().sort(Comparator.comparing(ReportWorkStatusSummaryVO::getDate)
                .thenComparing(ReportWorkStatusSummaryVO::getCno));
        // 设置呼入呼出数据
        setWorkStatusSummaryCallInAndOutData(dto, summaryPge.getRecords());
        // 设置工单提交时间
        FullFillSubmitOrderTime(dto, summaryPge.getRecords());
        // 计算工时和通话利用率数据
        if ("day".equals(dto.getPeriod())) {
            computeUtilizationRate(summaryPge.getRecords());
        } else {
            computeUtilizationRateRange(dto,summaryPge.getRecords());
        }
        return summaryPge;
    }

    /**
     * 之前按每天计算的工作时长，是用最大登出时间 - 最小登陆时间，改成按照范围统计的话这种方法就不适用了
     * 需要先把每天的数据算出来，进行一个相加
     * @param dto
     * @param records
     */
    private void computeUtilizationRateRange(ReportWorkStatusDTO dto, List<ReportWorkStatusSummaryVO> records) {
        ReportWorkStatusDTO dayDto = new ReportWorkStatusDTO();
        BeanUtils.copyProperties(dto, dayDto);
        dayDto.setPeriod("day");
        Page<ReportWorkStatusSummaryVO> pageParam = getPageParam(dayDto);
        Page<ReportWorkStatusSummaryVO> dayPage = hisMapper.selectWorkStatusSummary(pageParam, dayDto);
        if (CollectionUtils.isEmpty(dayPage.getRecords())){
            return;
        }
        Map<String, Long> staffLoginDurationMap = new HashMap<>();
        for (ReportWorkStatusSummaryVO dayVO : dayPage.getRecords()) {
            Long loginDuration = staffLoginDurationMap.get(dayVO.getCno());
            if (Objects.isNull(loginDuration)){
                loginDuration = 0L;
            }
            if (dayVO.getLogoutTime() != null && dayVO.getLoginTime() != null) {
                // 工作总时长
                long dayLoginDuration = getWorkDuration(dayVO);
                loginDuration += dayLoginDuration;
            }

            staffLoginDurationMap.put(dayVO.getCno(), loginDuration);
            
            
        }

        for (ReportWorkStatusSummaryVO vo : records) {
            // 话后处理总时长
            int wrapUpDuration = 0;
            if (StringUtils.isNotBlank(vo.getWrapUpDuration())) {
                wrapUpDuration = DateUtils.getSeconds(vo.getWrapUpDuration());
            }
            if (Objects.isNull(vo.getSubmitOrderDuration())){
                vo.setSubmitOrderDuration(0);
            }
            //置闲时长
            int idleDuration = 0;
            if(StringUtils.isNotBlank(vo.getIdleDuration())){
                idleDuration = DateUtils.getSeconds(vo.getIdleDuration());
            }
            //午餐时长
            int eatingDuration = 0;
            if(StringUtils.isNotBlank(vo.getEatingDuration())){
                eatingDuration = DateUtils.getSeconds(vo.getEatingDuration());
            }
            //会议时长
            int meetingDuration = 0;
            if(StringUtils.isNotBlank(vo.getMeetingDuration())){
                meetingDuration = DateUtils.getSeconds(vo.getMeetingDuration());
            }
            //培训时长
            int trainingDuration = 0;
            if(StringUtils.isNotBlank(vo.getTrainingDuration())){
                trainingDuration = DateUtils.getSeconds(vo.getTrainingDuration());
            }
            // 总通话时长
            int totalDuration = getTotalDuration(vo);
            if (vo.getLogoutTime() != null && vo.getLoginTime() != null) {
                // 工作总时长
                Long loginDuration = staffLoginDurationMap.get(vo.getCno());
                if(Objects.isNull(loginDuration)){
                    loginDuration = 0L;
                }
                //超过8小时按8小时计算
                if (loginDuration > 28800L) {
                    loginDuration = 28800L;
                }
                vo.setLoginDuration(DateUtils.secondsToTimeByLong(loginDuration));
                //vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + vo.getSubmitOrderDuration()), loginDuration));
                vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + vo.getSubmitOrderDuration() + totalDuration + idleDuration + eatingDuration + meetingDuration + trainingDuration), loginDuration));
                vo.setCallUtilization(CommonUtil.calcPercentageByLong((long) totalDuration, loginDuration));
            }
        }
        
    }

    private Page<ReportStaffEfficiencyVO> getStaffEfficiencyBasic(ReportWorkStatusDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ReportWorkStatusSummaryVO> page = getPageParam(dto);
        // 查询员工给基本信息、满意度和首问解决率数据
        Page<ReportWorkStatusSummaryVO> staffListPage = hisMapper.selectStaffEfficiency(page, dto);
        if (CollectionUtils.isEmpty(staffListPage.getRecords())) {
            return getPageParam(dto);
        }

        if (PeriodEnum.RANGE.getValue().equals(dto.getPeriod())) {
            // 按时间区间汇总统计
            setEfficiencyRangeData(dto, staffListPage);
        } else {
            // 按照天统计(业务使用频率不高)
            setEfficiencyDayData(dto, staffListPage);
        }

        Page<ReportStaffEfficiencyVO> resultPage = computeAndGetEfficiencyVO(staffListPage);
        // 先按时间后按工号排序
        resultPage.getRecords().sort(Comparator.comparing(ReportStaffEfficiencyVO::getDate)
                .thenComparing(ReportStaffEfficiencyVO::getCno));
        return resultPage;
    }

    private ReportStaffEfficiencyVO getTotalStaffEfficiency(ReportWorkStatusDTO dto, Page<ReportStaffEfficiencyVO> basic) {
        List<ReportStaffEfficiencyVO> totalList;
        if (basic.getTotal() <= basic.getRecords().size()) {
            totalList = basic.getRecords();
        } else {
            setMaxPageOnDto(dto);
            Page<ReportWorkStatusSummaryVO> page = getPageParam(dto);
            // 查询员工给基本信息、满意度和首问解决率数据
            Page<ReportWorkStatusSummaryVO> staffListPage = hisMapper.selectStaffEfficiency(page, dto);
            // 直接按时间区间查询
            setEfficiencyRangeData(dto, staffListPage);
            totalList = convertToEfficiencyVO(staffListPage.getRecords());
        }

        // 加总所有数据计算总计值
        ReportStaffEfficiencyVO total = calcTotalWorkStatusVo(totalList, ReportStaffEfficiencyVO.class);
        total.setDate("总计");
        // 计算员工效能的工时利用率和通话利用率, 呼入呼出率
        computeEfficiencyTotalRate(total);
        // 查询总计维度的满意度数据
        ReportWorkStatusSummaryVO satisfiedVo = conSatisfactionMapper.selectSatisfactionTotalRate(dto);
        if (satisfiedVo != null) {
            total.setSatisfiedRate(satisfiedVo.getSatisfiedRate());
        }
        // 查询总计维度的首问解决率
        setResolveRateTotal(dto, total);
        return total;
    }

    private void setResolveRateTotal(ReportWorkStatusDTO dto, ReportStaffEfficiencyVO total) {
        int callInNumber = callInfoMapper.selectCallInNumber(dto);
        int repeatNumber = conRepeatCallMapper.selectRepeatNumber(dto);
        if (callInNumber == 0) {
            total.setResolvedRate("0.00%");
        } else {
            BigDecimal realUp = new BigDecimal(callInNumber - repeatNumber).multiply(new BigDecimal(100));
            BigDecimal successRate = realUp.divide(new BigDecimal(callInNumber), 2, RoundingMode.HALF_UP);
            total.setResolvedRate(successRate.doubleValue() + "%");
        }
    }

    private List<ReportStaffEfficiencyVO> convertToEfficiencyVO(List<ReportWorkStatusSummaryVO> summaryList) {
        List<ReportStaffEfficiencyVO> resultList = new ArrayList<>(summaryList.size());
        for (ReportWorkStatusSummaryVO record : summaryList) {
            ReportStaffEfficiencyVO vo = new ReportStaffEfficiencyVO();
            BeanUtils.copyProperties(record, vo);
            resultList.add(vo);
        }
        return resultList;
    }

    /**
     * 计算呼入接通率、呼入通话均长、呼出通话均长、呼出连通率
     */
    private Page<ReportStaffEfficiencyVO> computeAndGetEfficiencyVO(Page<ReportWorkStatusSummaryVO> summaryPage) {
        Page<ReportStaffEfficiencyVO> page = new Page<>();
        page.setCurrent(summaryPage.getCurrent()).setSize(summaryPage.getSize())
                .setTotal(summaryPage.getTotal()).setPages(summaryPage.getPages());
        List<ReportStaffEfficiencyVO> resultList = new ArrayList<>(summaryPage.getRecords().size());
        for (ReportWorkStatusSummaryVO record : summaryPage.getRecords()) {
            ReportStaffEfficiencyVO vo = new ReportStaffEfficiencyVO();
            BeanUtils.copyProperties(record, vo);
            setCallInAndOutConnectRate(vo);
            resultList.add(vo);
        }
        page.setRecords(resultList);
        return page;
    }

    private void setCallInAndOutConnectRate(ReportStaffEfficiencyVO vo) {
        vo.setCallInConnectedRate(CommonUtil.calcPercentage(vo.getCallInConnectedNumber(), vo.getCallInCount()));
        vo.setCallInAvgDuration(CommonUtil.divide(DateUtils.getSeconds(vo.getCallInDuration()), vo.getCallInConnectedNumber()));
        vo.setOutboundAvgDuration(CommonUtil.divide(DateUtils.getSeconds(vo.getOutboundDuration()), vo.getOutboundConnectedNumber()));
        vo.setOutboundConnectedRate(CommonUtil.calcPercentage(vo.getOutboundConnectedNumber(), vo.getOutboundCount()));
    }

    /**
     * 查询并设置员工时间范围内每一天的呼入数据、呼出数据、话后处理数据、通过每天登录登出计算工作总时长数据
     */
    private void setEfficiencyDayData(ReportWorkStatusDTO dto, Page<ReportWorkStatusSummaryVO> staffListPage) {
        // 1.查询并设置员工每一天的工作时长数据和话后处理数据
        List<ReportWorkStatusSummaryVO> loginList = hisMapper.selectLoginDurationByDay(dto);
        Map<String, List<ReportWorkStatusSummaryVO>> loginMap = new HashMap<>();
        if (!loginList.isEmpty()) {
            loginMap = loginList.stream().collect(Collectors.groupingBy(ReportWorkStatusSummaryVO::getCno));
        }
        // 构建一个员工号和天为key的map
        Map<String, Map<String, ReportWorkStatusSummaryVO>> dayKeyMap = new HashMap<>(loginMap.size(), 1);
        for (Map.Entry<String, List<ReportWorkStatusSummaryVO>> entry : loginMap.entrySet()) {
            Map<String, ReportWorkStatusSummaryVO> value = entry.getValue().stream()
                    .collect(Collectors.toMap(ReportWorkStatusSummaryVO::getDate, v -> v));
            dayKeyMap.put(entry.getKey(), value);
        }
        for (ReportWorkStatusSummaryVO vo : staffListPage.getRecords()) {
            Map<String, ReportWorkStatusSummaryVO> voMap = dayKeyMap.get(vo.getCno());
            if (voMap != null) {
                ReportWorkStatusSummaryVO loginVo = voMap.get(vo.getDate());
                if (loginVo != null) {
                    // 设置每一个工号对应的员工工作时长数据和话后处理数据
                    vo.setLoginTime(loginVo.getLoginTime());
                    vo.setLogoutTime(loginVo.getLogoutTime());
                    vo.setWrapUpDuration(loginVo.getWrapUpDuration());
                    vo.setIdleDuration(loginVo.getIdleDuration());
                    vo.setEatingDuration(loginVo.getEatingDuration());
                    vo.setMeetingDuration(loginVo.getMeetingDuration());
                    vo.setTrainingDuration(loginVo.getTrainingDuration());
                }
            }
        }
        // 2.设置呼入呼出数据
        setWorkStatusSummaryCallInAndOutData(dto, staffListPage.getRecords());
        // 3.设置提单时长
        FullFillSubmitOrderTime(dto, staffListPage.getRecords());
        // 4.计算工时利用率、通话利用率
        computeUtilizationRate(staffListPage.getRecords());

    }

    /**
     * 查询并设置员工所有符合时间区间的呼入数据、呼出数据、通过登录登出计算工作总时长数据
     */
    private void setEfficiencyRangeData(ReportWorkStatusDTO dto, Page<ReportWorkStatusSummaryVO> staffListPage) {
        List<ReportWorkTimeSummaryVO> callInList = callInfoMapper.selectCallInTotal(dto);
        List<ReportWorkTimeSummaryVO> outCallList = callInfoMapper.selectCallOutTotal(dto);
        List<ReportWorkStatusSummaryVO> loginList = hisMapper.selectLoginDurationByDay(dto);
        List<ReportWorkStatusSummaryVO> submitOrderList = opStaffExtraStatusLogMapper.selectSubmitOrderTimeGroupByStaff(dto);
        

        Map<String, ReportWorkTimeSummaryVO> inCallMap = new HashMap<>();
        Map<String, ReportWorkTimeSummaryVO> callOutMap = new HashMap<>();
        Map<String, ReportWorkWrapUpVO> loginMap = new HashMap<>();
        Map<String,Integer> submitOrderMap = new HashMap<>();
        // 组建员工坐席号为key，呼入数据、呼出数据、工作总时长、提单总时长为value的map
        if (!callInList.isEmpty()) {
            inCallMap = callInList.stream().collect(Collectors.toMap(ReportWorkTimeSummaryVO::getCno, v -> v));
        }
        if (!outCallList.isEmpty()) {
            callOutMap = outCallList.stream().collect(Collectors.toMap(ReportWorkTimeSummaryVO::getCno, v -> v));
        }

        if (!submitOrderList.isEmpty()) {
            submitOrderMap = submitOrderList.stream().collect(Collectors
                    .toMap(ReportWorkStatusSummaryVO::getStaffId, ReportWorkStatusSummaryVO::getSubmitOrderDuration));
        }

        if (!loginList.isEmpty()) {
            // 设置每一个工号的员工的工作时长数据和话后处理时长
            for (ReportWorkStatusSummaryVO vo : loginList) {
                ReportWorkWrapUpVO result = loginMap.get(vo.getCno());
                if (result == null) {
                    ReportWorkWrapUpVO wrap = new ReportWorkWrapUpVO();
                    wrap.setWrapUpDuration(getWrapUpDuration(vo));
                    wrap.setWorkDuration(getWorkDuration(vo));
                    loginMap.put(vo.getCno(), wrap);
                } else {
                    result.setWorkDuration(result.getWorkDuration() + getWorkDuration(vo));
                    result.setWrapUpDuration(result.getWrapUpDuration() + getWrapUpDuration(vo));
                }
            }
        }

        // 将数据拼装到vo上
        for (ReportWorkStatusSummaryVO vo : staffListPage.getRecords()) {
            // 1.设置呼入数据: 呼入总数、呼入接起数、呼入通话总时长
            ReportWorkTimeSummaryVO callInVo = inCallMap.get(vo.getCno());
            if (callInVo != null) {
                vo.setCallInCount(callInVo.getCallInCount());
                vo.setCallInConnectedNumber(callInVo.getCallInConnectedNumber());
                vo.setCallInDuration(DateUtils.secondsToTime(callInVo.getCallInDuration()));
            }
            // 2.设置呼出数据: 外呼总数、外呼成功数、外呼通话总时长
            ReportWorkTimeSummaryVO callOutVo = callOutMap.get(vo.getCno());
            if (callOutVo != null) {
                vo.setOutboundCount(callOutVo.getOutboundCount());
                vo.setOutboundConnectedNumber(callOutVo.getOutboundConnectedNumber());
                vo.setOutboundDuration(DateUtils.secondsToTime(callOutVo.getOutboundDuration()));
            }

            // 3.设置提单时间
            Integer submitOrderDuration = submitOrderMap.get(vo.getStaffId());
            if (Objects.nonNull(submitOrderDuration)) {
                vo.setSubmitOrderDuration(submitOrderDuration);
            }

            ReportWorkWrapUpVO loginVo = loginMap.get(vo.getCno());
            // 4.最后计算并设置工时利用率和通话利用率
            computeRateDataByRange(vo, loginVo);

        }
    }

    private int getWrapUpDuration(ReportWorkStatusSummaryVO vo) {
        return StringUtils.isNotBlank(vo.getWrapUpDuration()) ? DateUtils.getSeconds(vo.getWrapUpDuration()) : 0;
    }

    private int getIdleDuration(ReportWorkStatusSummaryVO vo) {
        return StringUtils.isNotBlank(vo.getIdleDuration()) ? DateUtils.getSeconds(vo.getIdleDuration()) : 0;
    }

    private int getEatingDuration(ReportWorkStatusSummaryVO vo) {
        return StringUtils.isNotBlank(vo.getEatingDuration()) ? DateUtils.getSeconds(vo.getEatingDuration()) : 0;
    }

    private int getMeetingDuration(ReportWorkStatusSummaryVO vo) {
        return StringUtils.isNotBlank(vo.getMeetingDuration()) ? DateUtils.getSeconds(vo.getMeetingDuration()) : 0;
    }

    private int getTrainingDuration(ReportWorkStatusSummaryVO vo) {
        return StringUtils.isNotBlank(vo.getTrainingDuration()) ? DateUtils.getSeconds(vo.getTrainingDuration()) : 0;
    }

    private int getWorkDuration(ReportWorkStatusSummaryVO vo) {
        if (vo.getLogoutTime() != null && vo.getLoginTime() != null) {
            // 工作总时长
            LocalDateTime logOutTime = LocalDateTime.parse(vo.getLogoutTime(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT));
            LocalDateTime loginTime = LocalDateTime.parse(vo.getLoginTime(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT));
            return (int) (Duration.between(loginTime, logOutTime).toMillis() / 1000);
        } else {
            return 0;
        }
    }

    private <T> Page<T> getPageParam(ReportWorkStatusDTO dto) {
        Page<T> page = new Page<>();
        page.setCurrent(dto.getCurrentPage()).setSize(dto.getRowsPerPage());
        return page;
    }

    private List<ReportCallInWorkStatusVO> convertToVoList(List<ReportCallInWorkStatusModel> modelList) {
        List<ReportCallInWorkStatusVO> voList = new ArrayList<>(modelList.size());
        for (ReportCallInWorkStatusModel model : modelList) {
            ReportCallInWorkStatusVO statusVO = buildWorkStatusVO(model);
            ReportWorkStatusDetailsVO detailVO = buildDetailsVO(model);
            switch (model.getStatus()) {
                case StaffWorkStatusConstant.BREAK:
                    statusVO.setRest(detailVO);
                    break;
                case StaffWorkStatusConstant.BUSY:
                    statusVO.setPause(detailVO);
                    break;
                case StaffWorkStatusConstant.TRAINING:
                    statusVO.setTraining(detailVO);
                    break;
                case StaffWorkStatusConstant.LUNCH:
                    statusVO.setEating(detailVO);
                    break;
                case StaffWorkStatusConstant.MEETING:
                    statusVO.setMeeting(detailVO);
                    break;
                case StaffWorkStatusConstant.SUBMIT_ORDER:
                    statusVO.setSubmitOrder(detailVO);
                    break;
                default:
                    throw new CrmOperateException("错误的工作状态");
            }
            voList.add(statusVO);
        }
        // 先按日期排序，再按工号排序
        voList.sort(Comparator.comparing(ReportCallInWorkStatusVO::getDate)
                .thenComparing(ReportCallInWorkStatusVO::getCno));
        return voList;
    }

    private ReportCallInWorkStatusVO buildWorkStatusVO(ReportCallInWorkStatusModel model) {
        ReportCallInWorkStatusVO vo = new ReportCallInWorkStatusVO();
        vo.setDate(model.getDate());
        vo.setCno(model.getCno());
        vo.setStaffName(model.getStaffName());
        vo.setGroupName(model.getGroupName());
        vo.setStaffId(model.getStaffId());
        vo.setGroupCode(model.getGroupCode());
        return vo;
    }

    private ReportWorkStatusDetailsVO buildDetailsVO(ReportCallInWorkStatusModel model) {
        ReportWorkStatusDetailsVO detailsVO = new ReportWorkStatusDetailsVO();
        detailsVO.setStartTime(model.getStartTime());
        detailsVO.setEndTime(model.getEndTime());
        detailsVO.setDuration(model.getDuration());
        return detailsVO;
    }

    private void setWorkStatusSummaryCallInAndOutData(ReportWorkStatusDTO dto, List<ReportWorkStatusSummaryVO> summaryVOList) {
        // 1.查询员工所有符合时间区间的呼入数据和呼出数据
        List<CallInfoStatsVO> calls = callInfoMapper.selectByPeriodTime(dto);
        // 2. 组建员工坐席号为key，员工所有天数的list为value的map
        Map<String, List<CallInfoStatsVO>> inCallMap = new HashMap<>();
        Map<String, List<CallInfoStatsVO>> callOutMap = new HashMap<>();
        constructCNoKeyMap(calls, inCallMap, callOutMap);
        // 3.将上述map再转换为员工坐席号为key，日期为key的map为value
        Map<String, Map<String, List<CallInfoStatsVO>>> dayKeyInMap = getDayKeyMap(inCallMap);
        Map<String, Map<String, List<CallInfoStatsVO>>> dayKeyOutMap = getDayKeyMap(callOutMap);
        // 4.将呼叫数据拼装到vo上返回
        for (ReportWorkStatusSummaryVO vo : summaryVOList) {
            // 设置呼入数据: 呼入总数、呼入接起数、呼入通话总时长
            setInCallData(vo, dayKeyInMap);
            // 设置呼出数据: 外呼总数、外呼成功数、外呼通话总时长
            setCallOutData(vo, dayKeyOutMap);
            // 设置通话总时长
            vo.setCallTotalDuration(DateUtils.secondsToTime(vo.getCallTotal()));
        }
    }

    /**
     * 计算并设置工时利用率和通话利用率
     */
    private void computeUtilizationRate(List<ReportWorkStatusSummaryVO> summaryVOList) {
        for (ReportWorkStatusSummaryVO vo : summaryVOList) {
            // 话后处理总时长
            int wrapUpDuration = 0;
            if (StringUtils.isNotBlank(vo.getWrapUpDuration())) {
                wrapUpDuration = DateUtils.getSeconds(vo.getWrapUpDuration());
            }
            if (Objects.isNull(vo.getSubmitOrderDuration())){
                vo.setSubmitOrderDuration(0);
            }
            //置闲时长
            int idleDuration = 0;
            if(StringUtils.isNotBlank(vo.getIdleDuration())){
                idleDuration = DateUtils.getSeconds(vo.getIdleDuration());
            }
            //午餐时长
            int eatingDuration = 0;
            if(StringUtils.isNotBlank(vo.getEatingDuration())){
                eatingDuration = DateUtils.getSeconds(vo.getEatingDuration());
            }
            //会议时长
            int meetingDuration = 0;
            if(StringUtils.isNotBlank(vo.getMeetingDuration())){
                meetingDuration = DateUtils.getSeconds(vo.getMeetingDuration());
            }
            //培训时长
            int trainingDuration = 0;
            if(StringUtils.isNotBlank(vo.getTrainingDuration())){
                trainingDuration = DateUtils.getSeconds(vo.getTrainingDuration());
            }
            // 总通话时长
            int totalDuration = getTotalDuration(vo);
            if (vo.getLogoutTime() != null && vo.getLoginTime() != null) {
                // 工作总时长
                long loginDuration = getWorkDuration(vo);
                //超过8小时按8小时计算
                if (loginDuration > 28800) {
                    loginDuration = 28800;
                }
                vo.setLoginDuration(DateUtils.secondsToTimeByLong(loginDuration));
                //vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + vo.getSubmitOrderDuration()), loginDuration));
                vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + vo.getSubmitOrderDuration() + eatingDuration + meetingDuration + trainingDuration + idleDuration), loginDuration));
                vo.setCallUtilization(CommonUtil.calcPercentageByLong((long) totalDuration, loginDuration));
            }
        }
    }

    /**
     * 通过呼入和呼出计算出通话总时长
     */
    private int getTotalDuration(ReportWorkStatusSummaryVO summaryVO) {
        // 呼入总时长
        int incomingCall = DateUtils.getSeconds(summaryVO.getCallInDuration());
        // 外呼总时长
        int outbound = DateUtils.getSeconds(summaryVO.getOutboundDuration());
        // 总通话时长
        return incomingCall + outbound;
    }


    /**
     * 计算员工效能的总计工时利用率和通话利用率，呼入呼出率
     */
    private void computeEfficiencyTotalRate(ReportStaffEfficiencyVO total) {
        // 1.计算工时利用率和通话利用率
        int incomingCall = DateUtils.getSeconds(total.getCallInDuration());// 呼入总时长
        int outbound = DateUtils.getSeconds(total.getOutboundDuration());// 外呼总时长
        long totalDuration = (long) incomingCall + outbound;// 通话总时长
        long wrapUpDuration = DateUtils.getSeconds(total.getWrapUpDuration());
        long workDuration = DateUtils.getSeconds(total.getLoginDuration());
        long idleDuration = DateUtils.getSeconds(total.getIdleDuration());
        long eatingDuration = DateUtils.getSeconds(total.getEatingDuration());
        long meetingDuration = DateUtils.getSeconds(total.getMeetingDuration());
        long trainingDuration = DateUtils.getSeconds(total.getTrainingDuration());
        // 工时利用率 = 通话总时长+话后处理时长/工作总时长
        //total.setWorkingHourUtilization(CommonUtil.calcPercentageByLong(wrapUpDuration + totalDuration + total.getSubmitOrderDuration(), workDuration));
        total.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + total.getSubmitOrderDuration() + idleDuration + eatingDuration + meetingDuration + trainingDuration), workDuration));
        // 通话利用率 = 通话总时长/工作总时长
        total.setCallUtilization(CommonUtil.calcPercentageByLong(totalDuration, workDuration));

        // 2.计算呼入呼出率
        setCallInAndOutConnectRate(total);
    }

    /**
     * 计算总计的工时利用率和通话利用率
     */
    private void computeUtilizationTotalRate(List<ReportWorkStatusSummaryVO> summaryVOList, ReportWorkStatusSummaryVO total) {
        ReportWorkWrapUpVO wrap = new ReportWorkWrapUpVO();
        wrap.setWrapUpDuration(0);
        wrap.setWorkDuration(0);
        for (ReportWorkStatusSummaryVO vo : summaryVOList) {
            wrap.setWorkDuration(wrap.getWorkDuration() + DateUtils.getSeconds(vo.getLoginDuration()));
            wrap.setWrapUpDuration(wrap.getWrapUpDuration() + getWrapUpDuration(vo));
            wrap.setIdleDuration(wrap.getIdleDuration() + getIdleDuration(vo));
            wrap.setEatingDuration(wrap.getEatingDuration() + getEatingDuration(vo));
            wrap.setMeetingDuration(wrap.getMeetingDuration() + getMeetingDuration(vo));
            wrap.setTrainingDuration(wrap.getTrainingDuration() + getTrainingDuration(vo));
        }
        int totalDuration = getTotalDuration(total);// 通话总时长
        long workDuration = wrap.getWorkDuration();// 工作总时长
        Integer submitOrderDuration = total.getSubmitOrderDuration();
        total.setLoginDuration(DateUtils.secondsToTimeByLong(workDuration));
        // 工时利用率 = 通话总时长+话后处理时长/工作总时长
        //total.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrap.getWrapUpDuration() + totalDuration + submitOrderDuration), workDuration));
        total.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrap.getWrapUpDuration() + totalDuration + submitOrderDuration + wrap.getIdleDuration() + wrap.getEatingDuration() + wrap.getMeetingDuration() + wrap.getTrainingDuration()), workDuration));
        // 通话利用率 = 通话总时长/工作总时长
        total.setCallUtilization(CommonUtil.calcPercentageByLong((long) totalDuration, workDuration));
    }

    private ReportWorkStatusSummaryVO getTotalWorkStatusSummary(ReportWorkStatusDTO dto,
                                                                Page<ReportWorkStatusSummaryVO> summaryPge) {
        List<ReportWorkStatusSummaryVO> totalList;
        if (summaryPge.getTotal() <= summaryPge.getRecords().size()) {
            totalList = summaryPge.getRecords();
        } else {
            setMaxPageOnDto(dto);
            Page<ReportWorkStatusSummaryVO> pageParam = getPageParam(dto);
            // 查询每个员工所有工作状态
            Page<ReportWorkStatusSummaryVO> totalPge = hisMapper.selectWorkStatusSummary(pageParam, dto);
            // 设置呼入呼出数据
            setWorkStatusSummaryCallInAndOutData(dto, totalPge.getRecords());
            FullFillSubmitOrderTime(dto, totalPge.getRecords());
            totalList = totalPge.getRecords();
        }

        // 加总所有数据计算总计值
        ReportWorkStatusSummaryVO total = calcTotalWorkStatusVo(totalList, ReportWorkStatusSummaryVO.class);
        total.setDate("总计");
        // 计算总量数据的工时利用率和通话利用率
        computeUtilizationTotalRate(totalList, total);
        // 查询总计维度的满意度数据
        ReportWorkStatusSummaryVO satisfiedVo = conSatisfactionMapper.selectSatisfactionTotalRate(dto);
        if (satisfiedVo != null) {
            total.setSatisfiedRate(satisfiedVo.getSatisfiedRate());
        }
        return total;
    }

    private <T> Method getSetMethod(Class<?> tClass, Field field, Class<T> type)
            throws NoSuchMethodException {
        return tClass.getMethod("set" + field.getName().substring(0, 1).toUpperCase()
                + field.getName().substring(1), type);
    }

    private <T> T calcTotalWorkStatusVo(List<T> totalList, Class<T> tClass) {
        // 加总所有数据计算总计值
        T total;
        try {
            total = tClass.newInstance();
        } catch (Exception e) {
            throw new CrmOperateException("实例化总计对象异常");
        }
        for (T vo : totalList) {
            for (Field field : tClass.getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    String typeName = field.getType().getName();
                    Object targetValue = field.get(vo);
                    if (targetValue != null && typeName.equals("java.lang.Integer")) {
                        int origin = field.get(total) == null ? 0 : (int) field.get(total);
                        Method setVal = getSetMethod(tClass, field, Integer.class);
                        setVal.invoke(total, origin + (int) targetValue);
                    } else if (targetValue != null && typeName.equals("java.lang.String")) {
                        String target = ((String) targetValue).trim();
                        if (target.contains(":") && !target.contains(" ")) {// 只有时间需要加总的时间字段才汇总(例00:00:00)
                            Method setVal = getSetMethod(tClass, field, String.class);
                            if (field.get(total) == null) {
                                setVal.invoke(total, target);
                            } else {
                                String origin = (String) field.get(total);
                                int seconds = DateUtils.getSeconds(target) + DateUtils.getSeconds(origin);
                                setVal.invoke(total, DateUtils.secondsToTime(seconds));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("计算员工工作状态或者效能总计数据异常:  {}", e.getMessage(), e);
                }
            }
        }
        return total;
    }

    private void setMaxPageOnDto(ReportWorkStatusDTO dto) {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(100000);
    }

    private void fillSatisfyData(ReportWorkStatusDTO dto, List<ReportWorkStatusSummaryVO> summaryVOList) {
        // 查询指定日期和工号的满意度数据
        List<ReportWorkStatusSummaryVO> satisList = conSatisfactionMapper.selectSatisfactionRate(dto);
        if (CollectionUtils.isEmpty(satisList)) {
            return;
        }
        Map<String, List<ReportWorkStatusSummaryVO>> cNoMap = satisList.stream().collect(
                Collectors.groupingBy(ReportWorkStatusSummaryVO::getDate));
        // 组装成先以天，后以工号为key的两层map
        Map<String, Map<String, ReportWorkStatusSummaryVO>> dayCNoKeyMap = new HashMap<>();
        for (Map.Entry<String, List<ReportWorkStatusSummaryVO>> entry : cNoMap.entrySet()) {
            Map<String, ReportWorkStatusSummaryVO> value = entry.getValue().stream()
                    .collect(Collectors.toMap(ReportWorkStatusSummaryVO::getCno, v -> v));
            dayCNoKeyMap.put(entry.getKey(), value);
        }
        // 填充满意度数据
        for (ReportWorkStatusSummaryVO vo : summaryVOList) {
            Map<String, ReportWorkStatusSummaryVO> dayMap = dayCNoKeyMap.get(vo.getDate());
            if (dayMap != null) {
                ReportWorkStatusSummaryVO summaryVO = dayMap.get(vo.getCno());
                vo.setSatisfiedRate(summaryVO == null ? null : summaryVO.getSatisfiedRate());
            }
        }
    }



    private void FullFillSubmitOrderTime(ReportWorkStatusDTO dto, List<ReportWorkStatusSummaryVO> records) {
        List<ReportWorkStatusSummaryVO> submitOrderTimeList = opStaffExtraStatusLogMapper.selectSubmitOrderTimeGroupByDayAndStaff(dto);
        if (CollectionUtils.isEmpty(submitOrderTimeList)){
            return;
        }

        Map<String, List<ReportWorkStatusSummaryVO>> dateMap = submitOrderTimeList.stream()
                .collect(Collectors.groupingBy(ReportWorkStatusSummaryVO::getDate));

        Map<String, Map<String, ReportWorkStatusSummaryVO>> dateStaffMap = new HashMap<>();
        for (Map.Entry<String, List<ReportWorkStatusSummaryVO>> entry : dateMap.entrySet()) {
            Map<String, ReportWorkStatusSummaryVO> value = entry.getValue().stream()
                    .collect(Collectors.toMap(ReportWorkStatusSummaryVO::getStaffId, v -> v));
            dateStaffMap.put(entry.getKey(), value);
        }

        // 填充工单提交时间
        for (ReportWorkStatusSummaryVO vo : records) {
            Map<String, ReportWorkStatusSummaryVO> staffMap = dateStaffMap.get(vo.getDate());
            if (staffMap != null) {
                ReportWorkStatusSummaryVO summaryVO = staffMap.get(vo.getStaffId());
                vo.setSubmitOrderDuration(summaryVO == null ? null : summaryVO.getSubmitOrderDuration());
            }
        }
        
    }

    /**
     * 计算一段时间内的工时利用率和通话利用率
     */
    private void computeRateDataByRange(ReportWorkStatusSummaryVO vo, ReportWorkWrapUpVO wrapVo) {
        if (wrapVo == null) {
            return;
        }
        // 话后处理总时长
        int wrapUpDuration = wrapVo.getWrapUpDuration();
        // 呼入总时长
        int incomingCall = DateUtils.getSeconds(vo.getCallInDuration());
        // 外呼总时长
        int outbound = DateUtils.getSeconds(vo.getOutboundDuration());
        // 总通话时长
        int totalDuration = incomingCall + outbound;
        //置闲时长
        int idleDuration = 0;
        if(StringUtils.isNotBlank(vo.getIdleDuration())){
            idleDuration = DateUtils.getSeconds(vo.getIdleDuration());
        }
        //午餐时长
        int eatingDuration = 0;
        if(StringUtils.isNotBlank(vo.getEatingDuration())){
            eatingDuration = DateUtils.getSeconds(vo.getEatingDuration());
        }
        //会议时长
        int meetingDuration = 0;
        if(StringUtils.isNotBlank(vo.getMeetingDuration())){
            meetingDuration = DateUtils.getSeconds(vo.getMeetingDuration());
        }
        //培训时长
        int trainingDuration = 0;
        if(StringUtils.isNotBlank(vo.getTrainingDuration())){
            trainingDuration = DateUtils.getSeconds(vo.getTrainingDuration());
        }
        // 工作总时长
        long loginDuration = wrapVo.getWorkDuration();
        //超过8小时按8小时计算
        if (loginDuration > 28800L) {
            loginDuration = 28800L;
        }
        vo.setLoginDuration(DateUtils.secondsToTimeByLong(loginDuration));
        vo.setWrapUpDuration(DateUtils.secondsToTimeByLong((long) wrapUpDuration));
        //vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + vo.getSubmitOrderDuration()), loginDuration));
        vo.setWorkingHourUtilization(CommonUtil.calcPercentageByLong((long) (wrapUpDuration + totalDuration + vo.getSubmitOrderDuration() + eatingDuration + meetingDuration + trainingDuration + idleDuration), loginDuration));
        vo.setCallUtilization(CommonUtil.calcPercentageByLong((long) totalDuration, loginDuration));
    }

    /**
     * 设置员工外呼数据
     */
    private void setCallOutData(ReportWorkStatusSummaryVO vo, Map<String, Map<String, List<CallInfoStatsVO>>> dayKeyOutMap) {
        Map<String, List<CallInfoStatsVO>> dayOutMap = dayKeyOutMap.get(vo.getCno());
        // 外呼总数
        int outboundCount = 0;
        // 外呼成功数
        int outboundConnectedNumber = 0;
        // 外呼通话总时长
        int outboundDuration = 0;
        if (Objects.nonNull(dayOutMap)) {
            List<CallInfoStatsVO> outVo = dayOutMap.get(vo.getDate());
            if (Objects.isNull(outVo)) {
                return;
            }
            //员工今天未签入过
            if (vo.getLoginTime() != null) {
                outboundCount = outVo.size();
            }

            for (CallInfoStatsVO statsVO : outVo) {
                if (CallStatusEnum.OUT_CONNECTED.getValue().equals(statsVO.getCdrStatus())) {
                    outboundConnectedNumber++;
                }
                if (StringUtils.isNotBlank(statsVO.getCdrEndBridgeTime()) && !statsVO.getCdrEndBridgeTime().equals("0")) {
                    outboundDuration += DateUtils.getSeconds(statsVO.getCdrEndBridgeTime());
                }
            }

        }
        vo.setOutboundCount(outboundCount);
        vo.setOutboundConnectedNumber(outboundConnectedNumber);
        vo.setOutboundDuration(DateUtils.secondsToTime(outboundDuration));
        vo.setCallTotal(vo.getCallTotal() + outboundDuration);
    }

    /**
     * 设置员工接收到的客户呼入数据
     */
    private void setInCallData(ReportWorkStatusSummaryVO vo, Map<String, Map<String, List<CallInfoStatsVO>>> dayKeyInMap) {
        // 获得该员工每一天的工作状态inMap
        Map<String, List<CallInfoStatsVO>> dayInMap = dayKeyInMap.get(vo.getCno());
        // 呼入总数
        int callInCount = 0;
        // 呼入接起数
        int callInConnectedNumber = 0;
        // 呼入通话总时长
        int callInDuration = 0;
        if (Objects.nonNull(dayInMap)) {
            List<CallInfoStatsVO> inVo = dayInMap.get(vo.getDate());
            if (Objects.isNull(inVo)) {
                return;
            }
            //为空表示员工今天未签入过
            if (vo.getLoginTime() != null) {
                callInCount = inVo.size();
            }
            for (CallInfoStatsVO statsVO : inVo) {
                if (CallStatusEnum.IN_CONNECTED.getValue().equals(statsVO.getCdrStatus())) {
                    callInConnectedNumber++;
                }
                if (StringUtils.isNotBlank(statsVO.getCdrEndBridgeTime()) && !statsVO.getCdrEndBridgeTime().equals("0")) {
                    callInDuration += DateUtils.getSeconds(statsVO.getCdrEndBridgeTime());
                }
            }
        }
        vo.setCallInCount(callInCount);
        vo.setCallInConnectedNumber(callInConnectedNumber);
        vo.setCallInDuration(DateUtils.secondsToTime(callInDuration));
        vo.setCallTotal(callInDuration);
    }

    /**
     * 组建员工坐席号为key，员工所有天数的list为value的map
     */
    private void constructCNoKeyMap(List<CallInfoStatsVO> calls, Map<String, List<CallInfoStatsVO>> inCallMap,
                                    Map<String, List<CallInfoStatsVO>> callOutMap) {
        for (CallInfoStatsVO call : calls) {
            // 组装呼入的map
            if (CallTypeEnum.IN_CALL.getValue().equals(call.getCdrCallType())) {
                List<CallInfoStatsVO> inCalls = inCallMap.get(call.getCdrCalleeCno());
                if (inCalls == null) {
                    List<CallInfoStatsVO> inList = new ArrayList<>();
                    inList.add(call);
                    inCallMap.put(call.getCdrCalleeCno(), inList);
                } else {
                    inCalls.add(call);
                }
                // 组装呼出的map
            } else if (CallTypeEnum.CALL_OUT.getValue().equals(call.getCdrCallType())) {
                List<CallInfoStatsVO> outCalls = callOutMap.get(call.getCdrCno());
                if (outCalls == null) {
                    List<CallInfoStatsVO> outList = new ArrayList<>();
                    outList.add(call);
                    callOutMap.put(call.getCdrCno(), outList);
                } else {
                    outCalls.add(call);
                }
            }
        }
    }

    /**
     * 把员工坐席号码为key，value为员工所有日期的list数据 转换为员工坐席号码为key,value为每一天的日期为key的map
     */
    private Map<String, Map<String, List<CallInfoStatsVO>>> getDayKeyMap(Map<String, List<CallInfoStatsVO>> cNoKeyMap) {
        if (cNoKeyMap.size() == 0) {
            return new HashMap<>();
        }
        Map<String, Map<String, List<CallInfoStatsVO>>> cNoDayKeyMap = new HashMap<>(cNoKeyMap.size(), 1);
        for (Map.Entry<String, List<CallInfoStatsVO>> entry : cNoKeyMap.entrySet()) {
            Map<String, List<CallInfoStatsVO>> value = entry.getValue().stream()
                    .collect(Collectors.groupingBy(CallInfoStatsVO::getDay));
            cNoDayKeyMap.put(entry.getKey(), value);
        }
        return cNoDayKeyMap;
    }
}
