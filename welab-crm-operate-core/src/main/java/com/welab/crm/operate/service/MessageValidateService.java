package com.welab.crm.operate.service;

import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.crm.operate.dto.message.SmsSendDTO;

import java.util.List;

/**
 * 用于人脸验证消息的查询和转换等服务
 */
public interface MessageValidateService {

    /**
     * 填充发送组, 转换数据类型等
     */
   void addGroupToResponse(ResponseVO<List<ResponseData>> response);


    /**
     * 发送短信
     * @param dto
     * @param staffVO
     */
   String sendMsg(SmsSendDTO dto, StaffVO staffVO);
}
