package com.welab.crm.operate.websocket.handler;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.WebSocketSession;

import com.welab.crm.operate.websocket.util.WebSocketUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: AuthHandler
 * @Description:
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2022/2/11 16:40
 */
@Component
@Slf4j
public class AuthHandler {


    public void execute(WebSocketSession session, String mobile) {
        // 如果未传递 accessToken
        if (StringUtils.isEmpty(mobile)) {
            log.info("手机号未传入");
            return;
        }
        // 添加到 WebSocketUtil 中
        WebSocketUtil.addSession(session, mobile);

    }
}
