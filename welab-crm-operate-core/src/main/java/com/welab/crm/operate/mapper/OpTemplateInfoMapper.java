package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpTemplateInfo;
import com.welab.crm.operate.dto.workorder.TemplateInfoReqDTO;
import com.welab.crm.operate.vo.workorder.TemplateInfoVO;

/**
 * <p>
 * 模板信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface OpTemplateInfoMapper extends BaseMapper<OpTemplateInfo> {
	Page<TemplateInfoVO> queryTemplateInfoByPage(Page<TemplateInfoVO> page, @Param("reqDTO") TemplateInfoReqDTO reqDTO);
}
