package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户还款明细查询表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_repay_file")
public class CsRepayFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 下载文件路径
     */
    private String fileUrl;

    /**
     * 贷款类型
     */
    private String loanType;

    /**
     * 本次查询的人数量
     */
    private Integer userCount;

    /**
     * 任务状态: 0-执行中,1-已完成
     */
    private Integer taskState;

    /**
     * 审批状态: 0-待审批,1-审批成功,2-审批拒绝
     */
    private Integer approveState;

    /**
     * 审批时间
     */
    private Date approveTime;

    /**
     * 审批说明
     */
    private String remark;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间 (请查询时间)
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
