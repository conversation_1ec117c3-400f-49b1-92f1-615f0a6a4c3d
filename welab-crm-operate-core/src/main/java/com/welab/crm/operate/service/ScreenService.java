package com.welab.crm.operate.service;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.operate.vo.personalPanel.PersonalPanelVO;
import com.welab.crm.operate.vo.phone.CallInfoTotalData;
import com.welab.crm.operate.vo.screen.AgentEfficientVO;
import com.welab.crm.operate.vo.screen.AgentOverview;
import com.welab.crm.operate.vo.screen.CountVO;

import java.util.List;
import java.util.Map;


/**
 * 大屏监控接口
 * <AUTHOR>
 */
public interface ScreenService {


    /**
     * 查询坐席工作概览
     * @return
     */
    AgentOverview queryAgentWorkOverview();

    /**
     * 查询top10热点问题
     * @return
     */
    List<CountVO> queryTop10Question();

    /**
     * 查询中控预览
     * @return
     */
    JSONObject queryZkOverview();


    /**
     * 查询呼叫地图
     * @return
     */
    List<CountVO> queryCallMap();


    /**
     * 分时统计呼入信息
     * @return
     */
    JSONObject queryHoursCallInfo();

    /**
     * 查询满意度信息
     * @return
     */
    JSONObject queryServiceLevel();


    /**
     * 查询坐席工作效率
     * @return
     */
    List<AgentEfficientVO> queryAgentEfficient();


    /**
     * 查询总转人工服务数、2小时内重复进线量，重复进线率
     * @return
     */
    JSONObject queryRepeatData();


    /**
     * 查询实时排队数
     * @param params 额外参数
     * @return 
     */
    Integer queryRealTimeQueues(Map<String,String> params);


    /**
     * 查询呼入数据
     * @param params 额外参数
     * @return
     */
    CallInfoTotalData queryCalInfoTotalData(Map<String,String> params);


    /**
     * 查询个人看板信息
     * @return
     */
    PersonalPanelVO queryPersonalPanelVO();
}
