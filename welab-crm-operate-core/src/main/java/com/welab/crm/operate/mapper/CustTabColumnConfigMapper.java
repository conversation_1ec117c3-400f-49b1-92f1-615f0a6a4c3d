package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.CustTabColumnConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 选项卡配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
public interface CustTabColumnConfigMapper extends BaseMapper<CustTabColumnConfig> {

    /**
     * 根据表名查询不显示的列
     * @param staffId
     * @param tabName
     * @return
     */
    List<CustTabColumnConfig> selectByStaffIdAndTabName(@Param("staffId") String staffId, @Param("tabName") String tabName);

    /**
     * @param staffId
     * @param tabName
     * @return
     */
    void deleteByStaffIdAndTabName(@Param("staffId") String staffId, @Param("tabName") String tabName);

    /**
     * @param configs
     * @return
     */
    void insertBatch(List<CustTabColumnConfig> configs);
}
