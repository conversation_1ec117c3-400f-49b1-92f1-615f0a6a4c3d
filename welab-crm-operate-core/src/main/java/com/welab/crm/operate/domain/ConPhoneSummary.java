package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 通话小结表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_phone_summary")
public class ConPhoneSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 用户Id
     */
    private Long customerId;


    /**
     * 电话小结内容
     */
    private String callSummary;

    /**
     * 电话小结code，不同级别用逗号隔开
     */
    private String callSummaryCode;

    /**
     * 保存小结时间
     */
    private Date saveSummaryTime;

    /**
     * 备注
     */
    private String callComment;

    /**
     * 名单类型，外呼时才有这个字段
     */
    private String mapName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


    /**
     * 员工号
     */
    private String staffId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 唯一通话id
     */
    private String cdrMainUniqueId;

    /**
     * 咨询状态
     * 1-已逾期；2-待还款
     */
    private Integer consultationStatus;

    /**
     * 逾期标签
     */
    private String overDueLabel;
}
