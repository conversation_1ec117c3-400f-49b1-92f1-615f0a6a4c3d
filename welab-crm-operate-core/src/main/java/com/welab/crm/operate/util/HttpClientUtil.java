package com.welab.crm.operate.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @Description Http工具类
 * @Date 2021/8/17 下午2:08
 */
@Slf4j
public class HttpClientUtil {

    private static final Logger LOG = LoggerFactory.getLogger(HttpClientUtil.class);

    public static final int TIMEOUT = 10000;

    public static final String ENCODING = "UTF-8";
    /**
     * 请求返回的headers的key
     */
    public static final String RESPONSE_HEADERS_KEY = "headers";
    /**
     * 请求返回结果的key,在需要返回headers时存在.
     */
    public static final String RESPONSE_RESULT_KEY = "result";

    /**
     * URL转码方法
     *
     * @param parameter
     * @return
     */
    public static String encodeUrlUTF8(String parameter) {
        if (isNullStr(parameter)) {
            return "";
        }
        try {
            parameter = URLEncoder.encode(parameter, "utf-8");
        } catch (Exception e) {
            log.error("encodeUrlUTF8 error: {}", e.getMessage());
        }
        return parameter;
    }

    /**
     * url解码方法
     *
     * @param parameter
     * @return
     */
    public static String decodeUrlUTF8(String parameter) {
        if (isNullStr(parameter)) {
            return "";
        }
        try {
            parameter = java.net.URLDecoder.decode(parameter, "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("decodeUrlUTF8 error: {}", e.getMessage());
        }
        return parameter;
    }

    private static boolean isNullStr(String value) {
        if (null == value || "".equals(value)) {
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * 发送POST请求,参数值以body形式发送
     *
     * @param url      请求的服务器地址,如: http://localhost/xmlTest.do
     * @param bodyData 需发送的Request Body数据,如: 我们要以流发送的数据...
     * @return 返回服务器的响应
     * @throws IOException
     */
    public static String send(String url, String bodyData) throws IOException {
        return send(url, null, null, bodyData);
    }

    /**
     * 发送请求[设置请求头],参数值以body形式发送
     *
     * @param url            请求服务器地址
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static String send(String url, Map<String, String> requestHeaders, String bodyData) {
        return send(url, null, requestHeaders, bodyData);
    }

    /**
     * 表单提交 type=application/x-www-form-urlencoded
     * @param url 地址
     * @param bodyParam 表单参数
     * @return
     */
    public static String sendForm(String url, Map<String, String> bodyParam){
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        RestTemplate restTemplate=new RestTemplate();
        MultiValueMap<String, String> forms = new LinkedMultiValueMap<>();
       // forms.put("sign", Collections.singletonList("123"));
        Set<String> entries = bodyParam.keySet();
        if (entries != null) {
            Iterator<String> iterator = entries.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = bodyParam.get(key);
                forms.put(key, Collections.singletonList(value));
            }
        }
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(forms, headers);
        //获取返回数据
        return restTemplate.postForObject(url, httpEntity, String.class);
    }


    /**
     * 表单提交 type=application/x-www-form-urlencoded
     * @param url 地址
     * @param requestHeaders 请求头
     * @param bodyParam 表单参数
     * @return
     */
    public static String sendForm(String url, Map<String, String> requestHeaders, Map<String, String> bodyParam){
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Set<String> headersMap = requestHeaders.keySet();
        if (headersMap != null) {
            Iterator<String> iterator = headersMap.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = requestHeaders.get(key);
                headers.set(key, value);
            }
        }
        RestTemplate restTemplate = new RestTemplate();
        MultiValueMap<String, String> forms = new LinkedMultiValueMap<>();
        Set<String> entries = bodyParam.keySet();
        if (entries != null) {
            Iterator<String> iterator = entries.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = bodyParam.get(key);
                forms.put(key, Collections.singletonList(value));
            }
        }
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(forms, headers);
        //获取返回数据
        return restTemplate.postForObject(url, httpEntity, String.class);
    }

    /**
     * 发送请求[设置请求方式GET POST PUT DELETE|设置请求头],参数值以body形式发送
     *
     * @param url            请求服务器地址
     * @param requestMethod  请求方式: GET POST PUT DELETE HEAD OPTIONSTRACE
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static String send(String url, String requestMethod, Map<String, String> requestHeaders, String bodyData) {
        try {
            log.debug("[发送请求]请求地址:" + url);
            long start = System.currentTimeMillis();
            if (null == url || "".equals(url)) {
                throw new NullPointerException("请求的地址不能为空!");
            }
            bodyData = (null == bodyData || "".equals(bodyData)) ? "0" : bodyData;
            HttpURLConnection httpURLConnection;
            // 建立链接
            URL gatewayUrl = new URL(url);
            httpURLConnection = (HttpURLConnection) gatewayUrl.openConnection();
            //true内部处理重定向，返回最终的响应结果，调用方得到最终的code为200
            // 设置连接属性
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setReadTimeout(TIMEOUT);
            httpURLConnection.setConnectTimeout(TIMEOUT);
            httpURLConnection.setUseCaches(false);
            requestMethod = isNullStr(requestMethod) ? "POST" : requestMethod;
            httpURLConnection.setRequestMethod(requestMethod);
            // 获得数据字节数据，请求数据流的编码，必须和下面服务器端处理请求流的编码一致
            byte[] requestStringBytes = bodyData.getBytes(ENCODING);

            // 设置请求属性
            httpURLConnection.setRequestProperty("Content-length", "" + requestStringBytes.length);
            if (null == requestHeaders || requestHeaders.size() < 1) {
                httpURLConnection.setRequestProperty("Content-Type", "application/json");
            } else {
                Set<String> entries = requestHeaders.keySet();
                if (entries != null) {
                    Iterator<String> iterator = entries.iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        String value = requestHeaders.get(key);
                        httpURLConnection.setRequestProperty(key, value);
                    }
                }
            }
            // 建立输出流，并写入数据
            OutputStream outputStream = httpURLConnection.getOutputStream();
            outputStream.write(requestStringBytes);
            outputStream.close();
            // 获取所有响应头字段
            Map<String, List<String>> map = httpURLConnection.getHeaderFields();
            // 遍历所有的响应头字段
            StringBuffer sb = new StringBuffer();
            for (String key : map.keySet()) {
                sb.append(key + "=>" + map.get(key) + "\r\t");
            }
            LOG.debug("[发送请求]响应头:" + sb);
            // 获得响应状态
            int responseCode = httpURLConnection.getResponseCode();
            StringBuffer responseBuffer = new StringBuffer();
            if (HttpURLConnection.HTTP_OK == responseCode) {
                String readLine;
                BufferedReader responseReader;
                // 处理响应流，必须与服务器响应流输出的编码一致
                responseReader = new BufferedReader(new InputStreamReader(
                        httpURLConnection.getInputStream(), ENCODING));
                while ((readLine = responseReader.readLine()) != null) {
                    responseBuffer.append(readLine).append("\n");
                }
                responseReader.close();
            } else {
                responseBuffer.append(responseCode);
            }
            LOG.debug("[发送请求]响应状态码:" + responseCode + " 响应内容:" + responseBuffer);
            LOG.debug("send url:{},spends:{}ms", url, System.currentTimeMillis() - start);
            return responseBuffer.toString();
        }catch (Exception e) {
            LOG.error("发送请求出错! 请求地址:" + url , e);
        }
        return null;
    }

    /**
     * 发送请求[设置请求方式GET POST PUT DELETE|设置请求头],参数值以body形式发送
     *
     * @param cookies        cookies值
     * @param url            请求服务器地址
     * @param requestMethod  请求方式: GET POST PUT DELETE HEAD OPTIONSTRACE
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static String send(String cookies, String url, String requestMethod, Map<String, String> requestHeaders, String bodyData) throws IOException {
        try {
           LOG.debug("[发送请求]请求地址:" + url);
           long start = System.currentTimeMillis();
           if (null == url || "".equals(url)) {
               throw new NullPointerException("请求的地址不能为空!");
           }
           bodyData = (null == bodyData || "".equals(bodyData)) ? "0" : bodyData;
           HttpURLConnection httpURLConnection;
           // 建立链接
           URL gatewayUrl = new URL(url);
           httpURLConnection = (HttpURLConnection) gatewayUrl.openConnection();
           httpURLConnection.setRequestProperty("Cookie", cookies);
           // 设置连接属性
           httpURLConnection.setDoOutput(true);
           httpURLConnection.setDoInput(true);
           httpURLConnection.setReadTimeout(TIMEOUT);
           httpURLConnection.setConnectTimeout(TIMEOUT);
           httpURLConnection.setUseCaches(false);
           requestMethod = isNullStr(requestMethod) ? "POST" : requestMethod;
           httpURLConnection.setRequestMethod(requestMethod);
           // 获得数据字节数据，请求数据流的编码，必须和下面服务器端处理请求流的编码一致
           byte[] requestStringBytes = bodyData.getBytes(ENCODING);

           // 设置请求属性
           httpURLConnection.setRequestProperty("Content-length", "" + requestStringBytes.length);
           if (null == requestHeaders || requestHeaders.size() < 1) {
               httpURLConnection.setRequestProperty("Content-Type", "application/json");
           } else {
               Set<String> entries = requestHeaders.keySet();
               if (entries != null) {
                   Iterator<String> iterator = entries.iterator();
                   while (iterator.hasNext()) {
                       String key = iterator.next();
                       String value = requestHeaders.get(key);
                       httpURLConnection.setRequestProperty(key, value);
                   }
               }
           }
           // 建立输出流，并写入数据
           OutputStream outputStream = httpURLConnection.getOutputStream();
           outputStream.write(requestStringBytes);
           outputStream.close();
           // 获取所有响应头字段
           Map<String, List<String>> map = httpURLConnection.getHeaderFields();
           // 遍历所有的响应头字段
           StringBuffer sb = new StringBuffer();
           for (String key : map.keySet()) {
               sb.append(key + "=>" + map.get(key) + "\r\t");
               //System.out.println("key:" + key + "=" + map.get(key));
           }
           log.debug("[发送请求]响应头:" + sb);
           // 获得响应状态
           int responseCode = httpURLConnection.getResponseCode();
           StringBuffer responseBuffer = new StringBuffer();
           if (HttpURLConnection.HTTP_OK == responseCode) {
               String readLine;
               BufferedReader responseReader;
               // 处理响应流，必须与服务器响应流输出的编码一致
               responseReader = new BufferedReader(new InputStreamReader(
                       httpURLConnection.getInputStream(), ENCODING));
               while ((readLine = responseReader.readLine()) != null) {
                   responseBuffer.append(readLine).append("\n");
               }
               responseReader.close();
           } else {
               responseBuffer.append(responseCode);
           }
           LOG.debug("[发送请求]响应状态码:" + responseCode + " 响应内容:" + responseBuffer);
           LOG.debug("send url:{},spends:{}ms", url, System.currentTimeMillis() - start);
           return responseBuffer.toString();
       }catch (Exception e){
            LOG.error("发送请求出错! 请求地址:" + url , e);
        }
        return null;
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param param 请求参数(如果有中文参数必须转码且转一次仍有乱码,可转2次))，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, param, false);
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url                 发送请求的URL
     * @param param               请求参数(如果有中文参数必须转码且转一次仍有乱码,可转2次))，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param needResponseHeaders 是否需要返回响应头 true需要 false不需要
     * @return "{headers:{},result:{}}"
     */
    public static String sendGet(String url, String param, boolean needResponseHeaders) {
        long start = System.currentTimeMillis();
        JSONObject resultObj = new JSONObject();
        StringBuffer result = new StringBuffer();
        BufferedReader in = null;
        try {
            String urlNameString = url;
            if (null != param && param.length() > 0) {
                urlNameString += "?" + param;
            }
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setReadTimeout(TIMEOUT);
            connection.setConnectTimeout(TIMEOUT);
            // 建立实际的连接
            connection.connect();
            if(needResponseHeaders) {
                // 获取所有响应头字段
                Map<String, List<String>> headers = connection.getHeaderFields();
                //for (String key : headers.keySet()) {
                //    System.out.println("key:" + key + "=" + headers.get(key));
                //}
                resultObj.put(RESPONSE_HEADERS_KEY, headers);
            }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line).append("\n");
            }
        } catch (IOException e) {
            LOG.error("发送GET请求出错! 请求地址:" + url + " 请求参数:" + param, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                LOG.error("发送GET请求出错! 请求地址:" + url + " 请求参数:" + param, ex);
            }
        }
        if(needResponseHeaders) {
            resultObj.put(RESPONSE_RESULT_KEY, result);
            LOG.info("sendGet url:{},spends:{}ms, response:{}", url, System.currentTimeMillis() - start, resultObj);
            return resultObj.toString();
        } else {
            LOG.info("sendGet url:{},spends:{}ms, response:{}", url, System.currentTimeMillis() - start, result);
            return result.toString();
        }
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数(中文参数可以不转码)，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        long start = System.currentTimeMillis();
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuffer result = new StringBuffer();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setReadTimeout(TIMEOUT);
            conn.setConnectTimeout(TIMEOUT);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            if(null != param) {
                out.print(param);
            }
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line).append("\n");
            }
            LOG.info("sendPost url:{},spends:{}ms",url,System.currentTimeMillis()-start);
        } catch (Exception e) {
            LOG.error("发送POST请求出错! 请求地址:" + url + " 请求参数:" + param, e);
        } finally {// 使用finally块来关闭输出流、输入流
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                LOG.error("发送POST请求出错! 请求地址:" + url + " 请求参数:" + param, ex);
            }
        }
        return result.toString();
    }



    public static HttpResponse returnWithHeader(String url, String method, String data, int timeOut, Header... headers) {
        LOG.info("post url: " + url + ", method: " + method);
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(data)) {
            return null;
        }
        Request request;
        if ("post".equalsIgnoreCase(method)) {
            request = Request.Post(url).bodyString(data, ContentType.APPLICATION_JSON).addHeader(new BasicHeader("Accept", ContentType.APPLICATION_JSON.toString()));
        } else {
            request = Request.Put(url).bodyString(data, ContentType.APPLICATION_JSON).addHeader(new BasicHeader("Accept", ContentType.APPLICATION_JSON.toString()));
        }
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }
        if (headers != null & headers.length > 0) {
            for (Header header : headers) {
                request.addHeader(header);
            }
        }
        try {
            HttpResponse returnResponse = request.execute().returnResponse();
            LOG.info("post successfully.");
            return returnResponse;
        } catch (Exception e) {
            LOG.error("url:" + url + ", " + e.getMessage(), e);
        }
        return null;
    }
}