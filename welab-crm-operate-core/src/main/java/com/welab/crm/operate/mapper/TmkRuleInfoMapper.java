package com.welab.crm.operate.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.WoRuleInfo;
import com.welab.crm.operate.dto.tmkManager.TmkAssignReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkManager.TotalTmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkInfoReqDTO;
import com.welab.crm.operate.vo.tmkManager.TmkAssignVO;
import com.welab.crm.operate.vo.tmkManager.TmkManagerVO;
import com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO;
import com.welab.crm.operate.vo.tmkRule.TmkInfoVO;
import com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO;

/**
 * <p>
 * 电销数据分配规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
public interface TmkRuleInfoMapper extends BaseMapper<WoRuleInfo> {
	Page<TmkInfoVO> queryTmkInfoByPage(Page<TmkInfoVO> page, @Param("reqDTO") TmkInfoReqDTO reqDTO);
	
	List<TypeTotalTmkInfoVO> typeTotalTmkInfo(@Param("reqDTO") TmkInfoReqDTO reqDTO);
	
	Page<TmkManagerVO> queryTmkManagerByPage(Page<TmkManagerVO> page, @Param("reqDTO") TmkManagerReqDTO reqDTO);
	
	Page<TotalTmkManagerVO> totalTmkManager(Page<TmkManagerVO> page, @Param("reqDTO") TmkManagerReqDTO reqDTO);
	
	Page<TmkAssignVO> queryTmkAssignByPage(Page<TmkAssignVO> page, @Param("reqDTO") TmkAssignReqDTO reqDTO);
}
