package com.welab.crm.operate.job;

import java.nio.channels.Pipe;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.mail.FetchProfile;

import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.vo.monitor.OrderCallbackMonitorVO;
import com.welab.crm.operate.vo.monitor.OrderFilterVO;
import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.enums.WebSocketMsgTypeEnum;
import com.welab.crm.operate.service.OrderMonitorService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO;
import com.welab.crm.operate.websocket.util.WebSocketUtil;
import com.welab.crm.operate.ws.SendMsgDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 工单回访监控任务
 * 
 * <AUTHOR>
 */
@Slf4j
public class OrderCallbackMonitorJob implements SimpleJob {

    /**
     * 通知内容
     */
    private static final String NOTICE_CONTENT = "亲亲，截止目前您已有{0}客户未再次跟进，请留意上次跟进结果及时与用户再次沟通，务必协商一致完成结案~" + "<div>资方用户:{1}位</div>"
        + "<div>监管用户:{2}位</div>" + "<div>舆情用户:{3}位</div>" + "<div>投诉用户:{4}位</div>" + "<div>普通用户:{5}位</div>";

    private static final String NOTICE_TITLE = "{0}位客户跟进提醒通知";

    @Resource
    private OrderMonitorService orderMonitorService;

    @Resource
    private NoticeMsgService noticeMsgService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long startTime = System.currentTimeMillis();
        log.info("OrderCallbackMonitorJob start");
        List<OrderCallbackMonitorVO> list = orderMonitorService.queryOrderCallbackList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<OrderCallbackMonitorVO> filteredList = filterListByDict(list);
        if (CollectionUtils.isEmpty(filteredList)) {
            return;
        }
        filteredList.stream().collect(Collectors.groupingBy(OrderCallbackMonitorVO::getLoginName))
            .forEach(this::sendNotice);
        log.info("OrderCallbackMonitorJob end. use time:{}", System.currentTimeMillis() - startTime);
    }

    private void sendNotice(String loginName, List<OrderCallbackMonitorVO> orderList) {
        Map<String, List<OrderCallbackMonitorVO>> orderTypeMap =
            orderList.stream().collect(Collectors.groupingBy(OrderFilterVO::getOrderType));
        NoticeMsgReqDTO dto = new NoticeMsgReqDTO();
        dto.setTitle(MessageFormat.format(NOTICE_TITLE, orderList.size()));
        dto.setType("system_msg");
        dto.setNoticeType("system");
        dto.setSender("system");
        dto.setIsBanner(true);
        dto.setReceiver(loginName);
        dto.setContent(MessageFormat.format(NOTICE_CONTENT, orderList.size(),
            orderTypeMap.containsKey("资方工单") ? orderTypeMap.get("资方工单").size() : 0,
            orderTypeMap.containsKey("监管工单") ? orderTypeMap.get("监管工单").size() : 0,
            orderTypeMap.containsKey("舆情工单") ? orderTypeMap.get("舆情工单").size() : 0,
            orderTypeMap.containsKey("投诉工单") ? orderTypeMap.get("投诉工单").size() : 0,
            orderTypeMap.containsKey("普通工单") ? orderTypeMap.get("普通工单").size() : 0));
        noticeMsgService.publishNotice(dto);

    }

    private List<OrderCallbackMonitorVO> filterListByDict(List<OrderCallbackMonitorVO> list) {
        List<OpDictInfo> orderTypeList = CommonUtils.getDict("OrderCallbackNotice", null);

        if (CollectionUtils.isEmpty(orderTypeList)) {
            return Collections.emptyList();
        }
        Map<String, Integer> matchMap = orderTypeList.stream().collect(
            Collectors.toMap(item -> item.getType() + item.getContent(), item2 -> Integer.parseInt(item2.getDetail())));

        return list.stream().filter(item -> matchMap.containsKey(item.getOrderType() + item.getOrderThreeType())
            && matchMap.get(item.getOrderType() + item.getOrderThreeType()) <= item.getHoursBetweenContactTimeAndNow())
            .collect(Collectors.toList());
    }

}
