package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.BlackProductionUserInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.blackProduction.BlackProductionQueryReqDTO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 黑产用户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface BlackProductionUserInfoMapper extends ExpandBaseMapper<BlackProductionUserInfo> {

    /**
     * 分页查询黑产信息
     * @param page
     * @param reqDTO
     * @return
     */
    Page<BlackProductionVO> queryBlackProductionPage(Page<Object> page,
        @Param("reqDTO") BlackProductionQueryReqDTO reqDTO);
}
