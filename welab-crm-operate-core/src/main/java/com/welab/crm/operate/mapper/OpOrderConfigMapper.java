package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.OpOrderConfig;
import com.welab.crm.operate.dto.workorder.OpOrderConfigDTO;
import com.welab.crm.operate.vo.workorder.OpOrderConfigVO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO;

/**
 * <p>
 * 手动领取工单配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
public interface OpOrderConfigMapper extends BaseMapper<OpOrderConfig> {
	OpOrderConfigVO queryOrderRuleConfig();
	
	void updateOrderRuleConfig(@Param("reqDTO") OpOrderConfigDTO reqDTO);
}