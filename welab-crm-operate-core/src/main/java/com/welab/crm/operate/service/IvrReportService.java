package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.vo.ivr.IvrKeyDetailVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsReportVO;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description: ivr报表接口
 * @date 2022/3/8 14:23
 */
public interface IvrReportService {


    /**
     * ivr按键详情接口
     * @param ivrReportReqDTO
     * @return
     */
    Page<IvrKeyDetailVO> ivrKeyDetailReport(IvrReportReqDTO ivrReportReqDTO);


    /**
     * ivr 分流统计报表
     * @param ivrReportReqDTO
     * @return
     */
    IvrStatisticsReportVO ivrShuntStatisticsReport(IvrReportReqDTO ivrReportReqDTO);

    /**
     * 导出ivr按键详情报表
     * @param reqDTO
     * @param response
     * @param fileName
     */
    void exportIvrKeyDetailReport(HttpServletResponse response, IvrReportReqDTO reqDTO, String fileName);


    /**
     * 导出ivr分流统计报表
     * @param response
     * @param reqDTO
     * @param fileName
     */
    void exportIvrStatisticsReport(HttpServletResponse response, IvrReportReqDTO reqDTO, String fileName);
}
