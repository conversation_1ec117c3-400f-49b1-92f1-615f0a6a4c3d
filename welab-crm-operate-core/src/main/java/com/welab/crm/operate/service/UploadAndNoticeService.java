package com.welab.crm.operate.service;

/**
 * 上传文件并通知服务类
 * <AUTHOR>
 */
public interface UploadAndNoticeService {

	/**
	 * 上传文件到oss，并且返回下载链接
	 * @param byteArray 上传文件字节数组
	 * @param fileName 文件名
	 * @return 文件下载路径
	 */
	String uploadFileAndReturnUrl(byte[] byteArray, String fileName);

	/**
	 * 通知文件下载链接
	 * @param fileName 文件名
	 * @param fileUrl 文件下载链接
	 * @param receiver 接收人   
	 */
	void noticeFileDownloadUrl(String fileName, String fileUrl, String receiver);


	/**
	 * 上传文件并通知下载链接
	 * @param byteArray 文件内容
	 * @param fileName 文件名
	 * @param receiver 接收人
	 * @param ext 文件后缀拓展名
	 */
	void uploadFileAndNotice(byte[] byteArray, String fileName, String receiver, String ext);

	/**
	 * 从缓存里去文件下载路径
	 * @param uniqueFileName
	 * @return 下载路径
	 */
	String getFileUrlFromCache(String uniqueFileName);
	
}
