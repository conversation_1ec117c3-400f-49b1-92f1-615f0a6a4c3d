package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.TmkLoanInvite;
import com.welab.crm.operate.dto.report.WoReportDTO;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 电销报表 Mapper 接口
 * @date 2022/3/22
 */
public interface TmkReportMapper extends BaseMapper<TmkLoanInvite> {

    /**
     * 查询中央监控再分配数据量
     */
    List<RedistributedOutboundEfficiencyVO> selectReassignData(@Param("filter") WoReportDTO dto
            , @Param("codeList") List<String> groupCodeList, @Param("typeList") List<String> businessTypeList);

    /**
     * 查询中央监控再分配数据外呼效能相关数据
     */
    List<RedistributedOutboundEfficiencyVO> selectReassignOutboundData(@Param("filter") WoReportDTO dto
            , @Param("codeList") List<String> groupCodeList, @Param("typeList") List<String> businessTypeList);
}
