package com.welab.crm.operate.service.impl;

import com.welab.crm.operate.service.OnlineSystemService;
import com.welab.security.util.MD5Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class OnlineSystemServiceImpl implements OnlineSystemService {

	@Value("${online.system.user.token}")
	private String onlineHistoryUserToken;

	@Value("${online.system.app.key}")
	private String onlineSystemAppKey;
	
	@Override
	public Map<String, Object> buildAuthenticationParams() {
		HashMap<String, Object> params = new HashMap<>();
		params.put("userToken", onlineHistoryUserToken);
		String flowCode = UUID.randomUUID().toString();
		params.put("flowCode", flowCode);
		String timestamp = String.valueOf(System.currentTimeMillis());
		params.put("timestamp", timestamp);
		params.put("sign", MD5Util.md5(onlineSystemAppKey + timestamp + flowCode));
		return params;
	}
}
