package com.welab.crm.operate.service;

import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.crm.operate.vo.telemarketing.AiTransformReportVO;
import com.welab.crm.operate.vo.tmkReport.StaffEfficiencyVO;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.telemarketing.TmkAppointReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkAssignDetailReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkAssignDetailReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReportBaseReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkSummaryReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.vo.telemarketing.AiPushReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkAssignDetailReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkCjhyReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkLoanReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkStaffWorkInfo;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkUuidReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkWalletReportVO;

/**
 * <AUTHOR>
 * @Description: 电销任务相关服务
 * @date 2022/2/22 11:19
 */
public interface TmkTaskService {


    /**
     * 查询电销任务列表
     * @param tmkReqDTO
     * @return
     */
    Page<TmkTaskDetailVO> queryTmkTask(TmkReqDTO tmkReqDTO);

    /**
     * 查询不同电销业务类型下的不同时间段的任务的数量
     * @param tmkReqDTO
     * @return
     */
    Map<String, Long> queryCountByType(TmkReqDTO tmkReqDTO);

    /**
     * 电销预约回电
     * @param tmkAppointReqDTO
     */
    void appoint(TmkAppointReqDTO tmkAppointReqDTO);

    /**
     * 保存电话小结
     * @param tmkSummaryReqDTO
     */
    void saveTmkSummary(TmkSummaryReqDTO tmkSummaryReqDTO);

    void updateLatestResultCode(String tmkType, String tmkTaskId, Long summaryId, Long staffId, String flag);

    /**
     * 查询电销外呼统计报表
     * @param dto
     * @return
     */
    List<TmkStaffWorkInfo> queryTmkCallInfo(TmkReportBaseReqDTO dto);

    /**
     * 查询电销转化数据
     * @param dto
     * @return
     */
    List<TmkTransformReportVO> queryTmkTransformData(TmkTransformReportReqDTO dto);

    /**
     * 查询ai外呼明细报表
     * @param dto
     * @return
     */
    Page<AiPushReportVO> queryAiPushDetailReport(TmkReportBaseReqDTO dto);

    /**
     * 查询电销放款邀约数据
     * @param dto
     * @return
     */
    Page<TmkLoanReportVO> queryTmkLoanData(TmkTransformReportReqDTO dto);
    
    /**
     * 查询电销超级会员数据
     * @param dto
     * @return
     */
    Page<TmkCjhyReportVO> queryTmkCjhyData(TmkTransformReportReqDTO dto);
    
    /**
     * 查询电销钱夹谷谷数据
     * @param dto
     * @return
     */
    Page<TmkWalletReportVO> queryTmkWalletData(TmkTransformReportReqDTO dto);
    
    /**
     * 查询电销UUID数据
     * @param dto
     * @return
     */
    Page<TmkUuidReportVO> queryTmkUuidData(TmkTransformReportReqDTO dto);
    
    /**
     * 查询电销名单分配明细数据
     * @param dto
     * @return
     */
    Page<TmkAssignDetailReportVO> queryTmkAssignDetailData(TmkAssignDetailReportReqDTO dto);

    /**
     * 根据电销的手机号查询 客服系统用户Id
     * @param mobile
     * @param tmkType
     * @param userId
     * @return
     */
    CashCustInfoVO queryCustomerIdByTmkMobile(String mobile, Integer userId, String tmkType);


    /**
     * 查询近1个月的uuid号码包名
     * @return
     */
    List<String> queryUuidPackageName();


    /**
     * 查询电销员工实时效能报表
     * @param dto
     * @return
     */
    List<StaffEfficiencyVO> queryStaffEfficiencyReportRealTime(TmkReportBaseReqDTO dto);


    List<AiTransformReportVO> queryAiTransformReport(TmkReportBaseReqDTO dto);

    /**
     * 查询全部AI推送配置，包括已删除的
     * @return
     */
    List<AiTmkConfigVO> queryAllAiConfig();

}
