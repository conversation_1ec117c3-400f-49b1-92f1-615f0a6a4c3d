package com.welab.crm.operate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.operate.domain.LoanTransferAttachment;
import com.welab.crm.operate.dto.loan.*;
import com.welab.crm.operate.vo.loan.*;

/**
 * 债转结清申请和审核服务类
 *
 * <AUTHOR>
 * @date 2022/2/9 14:30
 */
public interface LoanTransferService {

    /**
     * 保存债转结清主数据和合同号列表数据
     *
     * @param operator       操作者(当前登录者编码)
     * @param contractNoList 贷款号列表数据
     */
    Long saveContractNoList(String operator, ExcelList<LoanTransferExcelDTO> contractNoList);

    /**
     * 根据过滤条件筛选债转结清主页列表
     */
    Page<LoanTransferVO> listTransfer(LoanTransferDTO transferDTO);

    /**
     * 根据债转结清主数据Id查询对应的合同号列表
     */
    Page<LoanContactVO> listDetails(LoanTransferIdDTO contractDTO);

    /**
     * 保存债转结清主数据Id对应的用户上传附件
     *
     * @param operator    操作者(当前登录者编码)
     * @param attachments 附件对象列表
     */
    void saveAttachments(String operator, List<LoanTransferAttachment> attachments);

    /**
     * 根据债转结清主数据Id查询对应的附件列表
     */
    List<LoanTranAttachmentVO> listAttachments(Long transferId);

    /**
     * 更新审批状态
     *
     * @param staffCode 审批人编码(当前登录者)
     * @param stateDTO  审批状态
     */
    void updateApproveState(String staffCode, LoanTransferIdDTO stateDTO);

    /**
     * 推送债转结清贷款号到消金提供的接口
     */
    void pushLoanData(LoanTransferIdDTO stateDTO);

    /**
     * 更新已推送的合同状态
     *
     * @param operator 更新人
     * @param id       债转结清主键id
     */
    void updatePushState(String operator, Long id);


    /**
     * 异步推送数据
     * @param stateDTO
     */
    void pushLoanDataAsync(LoanTransferIdDTO stateDTO);
    
    /**
     * 根据过滤条件筛选委外/债转文件导入主页列表
     */
    Page<LoanImportVO> listImport(LoanImportDTO importDTO);
    
    /**
     * 保存委外列表数据
     *
     * @param operator       操作者(当前登录者编码)
     * @param outcaseList 委外列表数据
     */
    void saveOutcaseList(String operator, ExcelList<LoanOutcaseExcelDTO> outcaseList);
    
    /**
     * 保存债转列表数据
     *
     * @param operator       操作者(当前登录者编码)
     * @param deptList 债转列表数据
     */
    void saveDeptList(String operator, ExcelList<LoanDeptExcelDTO> deptList);
    
    /**
     * 根据过滤条件筛选委外/债转文件导入主页列表
     */
    LoanImportLabelVO getImportLabelInfo(String applicationId);


    /**
     * 工单债转明细查询
     */
    Page<OrderLoanTransferVO> listOrderLoanTransfer(OrderLoanTransferDTO dto);

    /**
     * 债转结清业务完成发送短信
     * @param id
     */
    void sendTransferSms(Long id);
}
