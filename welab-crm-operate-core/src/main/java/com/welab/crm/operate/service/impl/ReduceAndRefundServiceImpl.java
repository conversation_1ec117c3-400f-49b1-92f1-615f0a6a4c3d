package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.service.ILenderService;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.enums.ReduceAndRefundApprovalStatusEnum;
import com.welab.crm.interview.enums.ReduceAndRefundResultEnum;
import com.welab.crm.interview.enums.ReduceTaskStatusEnum;
import com.welab.crm.interview.enums.RepaymentPlanType;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.vo.loan.RepaymentPlanSettlementVO;
import com.welab.crm.interview.vo.loan.RepaymentPlanVO;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.reduce.*;
import com.welab.crm.operate.enums.DepartmentEnum;
import com.welab.crm.operate.mapper.OpReduceApplyRecordMapper;
import com.welab.crm.operate.mapper.OpReduceRefundApprovalLogMapper;
import com.welab.crm.operate.mapper.OpReduceRefundAttachmentMapper;
import com.welab.crm.operate.mapper.OpRefundApplyRecordMapper;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.service.ReduceAndRefundService;
import com.welab.crm.operate.service.UploadAndNoticeService;
import com.welab.crm.operate.util.CalculateUntil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SerialNoUtil;
import com.welab.crm.operate.vo.reduce.*;
import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import com.welab.exception.FastRuntimeException;
import com.welab.frs.account.dto.AllowanceCalculateDTO;
import com.welab.frs.account.dto.SubdidySaveDTO;
import com.welab.frs.account.dubbo.FinancialDubboService;
import com.welab.frs.account.enums.ReductionTypeEnum;
import com.welab.frs.account.vo.AllowanceCalculateVO;
import com.welab.frs.account.vo.AllowanceRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReduceAndRefundServiceImpl implements ReduceAndRefundService {

	@Resource
	private LoanApplicationService loanApplicationService;

	@Resource
	private OpReduceApplyRecordMapper opReduceApplyRecordMapper;

	@Resource
	private OpRefundApplyRecordMapper opRefundApplyRecordMapper;
	
	@Resource
	private OpReduceRefundApprovalLogMapper opReduceRefundApprovalLogMapper;
	
	@Resource
	private ILenderService lenderService;
	
	@Resource
	private InAuthCrmStaffService inAuthCrmStaffService;
	
	@Resource
	private FinancialDubboService financialDubboService;

	@Resource
	private NoticeMsgService noticeMsgService;
	
	@Resource
	private OpReduceRefundAttachmentMapper opReduceRefundAttachmentMapper;
	
	@Resource
	private UploadAndNoticeService uploadAndNoticeService;


	/**
	 * 模式类型字典
	 */
	private static final String LOAN_MODEL_TYPE = "loan_model_type";
	private static final String DEFAULT_MODEL_TYPE = "助贷模式";

	@Override
	public List<ReduceAndRefundSubmitVO> querySubmitData(List<String> applicationList) {
		List<ReduceAndRefundSubmitVO> submitVoList = new ArrayList<>();

		for (String applicationId : applicationList) {
			ReduceAndRefundSubmitVO submitVO = new ReduceAndRefundSubmitVO();
			submitVO.setApplicationId(applicationId);
			List<AllowanceRecordVO> allowanceRecordVOS = queryReducedRecord(applicationId);
			if (CollectionUtils.isNotEmpty(allowanceRecordVOS)){
				List<AllowanceRecordVO> successList = allowanceRecordVOS.stream()
						.filter(item -> ReduceAndRefundResultEnum.RECHARGE_SUCCESS.getStatus().equals(item.getRechargeState())).collect(Collectors.toList());
				submitVO.setHistoryReduceAmount(successList.stream().map(AllowanceRecordVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
				submitVO.setHistoryReduceDepartment(String.join(",", successList.stream().map(item -> translateDepartment(item.getApplySource())).collect(Collectors.toSet())));
			}

			// 查询还款计划
			List<RepaymentPlanVO> repaymentPlans = loanApplicationService.getRepaymentPlan(applicationId);
			for (RepaymentPlanVO repaymentPlan : repaymentPlans) {
				// 单期应还总额
				BigDecimal sumDueAmount = repaymentPlan.getSumDueAmount();
				// 单期已还金额金额
				BigDecimal sumStillAmount = repaymentPlan.getSumStillAmount();
				// 单期未还金额
				BigDecimal sumRemainAmount = sumDueAmount.subtract(sumStillAmount);

				// 已还总金额
				submitVO.setTotalFeeRepaid(submitVO.getTotalFeeRepaid().add(sumStillAmount));

				// 结清金额
				submitVO.setSettlementAmount(submitVO.getSettlementAmount().add(sumRemainAmount));

				// 已还
				if (sumRemainAmount.compareTo(BigDecimal.ZERO) == 0) {

					// 已还期数
					submitVO.setTenorRepaid(submitVO.getTenorRepaid() + 1);

					for (RepaymentPlanSettlementVO repayPlanDetail : repaymentPlan.getDetail()) {
						if (RepaymentPlanType.PRINCIPAL.getText().equals(repayPlanDetail.getDueType())) {
							// 已还本金
							submitVO.setPrincipalRepaid(submitVO.getPrincipalRepaid().add(repayPlanDetail.getSettledAmount()));
						} else {
							// 已还其他金额
							submitVO.setOtherFeeRepaid(submitVO.getOtherFeeRepaid().add(repayPlanDetail.getSettledAmount()));

							// 除本金外的其他金额合计(这里只加上了已还期数的其他金额，后面还要加上待还期数的)
							submitVO.setOtherFee(submitVO.getOtherFee().add(repayPlanDetail.getDueAmount()));
						}

						// 最近还款日期
						if (Objects.isNull(submitVO.getLastRepaidAt()) || repayPlanDetail.getSettledTime().after(submitVO.getLastRepaidAt())) {
							submitVO.setLastRepaidAt(repayPlanDetail.getSettledTime());
						}
					}
					// 待还
				} else {
					// 待还期数
					submitVO.setTenorRemain(submitVO.getTenorRemain() + 1);

					for (RepaymentPlanSettlementVO repayPlanDetail : repaymentPlan.getDetail()) {
						if (RepaymentPlanType.PRINCIPAL.getText().equals(repayPlanDetail.getDueType())) {
							// 未还金额
							BigDecimal remainAmount = repayPlanDetail.getDueAmount().subtract(repayPlanDetail.getSettledAmount());
							// 未还本金
							submitVO.setPrincipalRemain(submitVO.getPrincipalRemain().add(remainAmount));
						} else {
							// 除本金外的其他金额合计
							submitVO.setOtherFee(submitVO.getOtherFee().add(repayPlanDetail.getDueAmount()));
						}
					}
				}
			}

			submitVoList.add(submitVO);

		}
		return submitVoList;
	}

	private String translateDepartment(String department) {
		return DepartmentEnum.getTextByValue(department);
	}

	@Override
	public BigDecimal queryReducibleAmount(BigDecimal principal, Integer tenor, BigDecimal otherFee) {
		try {
			// 计算IRR24费率下的本息合计
			BigDecimal irr24TotalAmount = CalculateUntil.calculateIRR24Amount(tenor, principal, new BigDecimal("0.24"));
			// 本来的本息合计 - IRR24 费率下的本息合计，计算出可减免金额
			return otherFee.add(principal).subtract(irr24TotalAmount);
		} catch (Exception e) {
			log.warn("计算可减免金额异常", e);
			throw new FastRuntimeException("计算可减免金额异常");
		}
	}

	
	@Override
	public void reduceApply(ReduceApplyDTO dto, String requestNo, Date currentTime) {
		OpReduceApplyRecord reduceApplyRecord = convertReduceApplyDtoToDomain(dto);
		reduceApplyRecord.setRequestNo(requestNo);
		reduceApplyRecord.setGmtCreate(currentTime);
		opReduceApplyRecordMapper.insert(reduceApplyRecord);

	}

	private OpReduceApplyRecord convertReduceApplyDtoToDomain(ReduceApplyDTO dto) {
		OpReduceApplyRecord reduceRecord = new OpReduceApplyRecord();
		BeanUtils.copyProperties(dto, reduceRecord);
		reduceRecord.setCustomerName(dto.getName());
		reduceRecord.setUserId(Integer.valueOf(dto.getUserId()));
		reduceRecord.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.APPLY.getCode());
		reduceRecord.setStaffId(CommonUtils.getCurrentloggedStaffId());

		return reduceRecord;
	}

	@Override
	public void refundApply(RefundApplyDTO dto, String requestNo, Date currentTime) {
		OpRefundApplyRecord refundApplyRecord = convertRefundApplyDtoToDomain(dto);
		refundApplyRecord.setRequestNo(requestNo);
		refundApplyRecord.setGmtCreate(currentTime);
		opRefundApplyRecordMapper.insert(refundApplyRecord);
	}

	private OpRefundApplyRecord convertRefundApplyDtoToDomain(RefundApplyDTO dto) {
		OpRefundApplyRecord refundApplyRecord = new OpRefundApplyRecord();
		BeanUtils.copyProperties(dto, refundApplyRecord);
		refundApplyRecord.setCustomerName(dto.getName());
		refundApplyRecord.setUserId(Integer.valueOf(dto.getUserId()));
		refundApplyRecord.setRefundStartTime(DateUtil.stringToDate(dto.getRefundStartTime()));
		refundApplyRecord.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.APPLY.getCode());
		refundApplyRecord.setStaffId(CommonUtils.getCurrentloggedStaffId());
		return refundApplyRecord;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void submitReduceAndRefundApply(ReduceAndRefundApplySubmitDTO dto) {
		if (Objects.isNull(dto)) {
			throw new FastRuntimeException("提交参数不能为空");
		}
		List<ReduceApplyDTO> reduceApplyList = dto.getReduceApplyList();
		List<RefundApplyDTO> refundApplyList = dto.getRefundApplyList();

		if (CollectionUtils.isEmpty(reduceApplyList) && CollectionUtils.isEmpty(refundApplyList)) {
			throw new FastRuntimeException("无提交数据");
		}
		Date currentTime = new Date();
		// 序列号，一个序列号对应一次提交
		String requestNo = SerialNoUtil.serialNo("KF");
		if (CollectionUtils.isNotEmpty(reduceApplyList)) {
			for (ReduceApplyDTO reduceApplyDTO : reduceApplyList) {
				reduceApplyDTO.setComplaintChannel(dto.getComplaintChannel());
				reduceApplyDTO.setReductionReason(dto.getReduceReason());
				reduceApply(reduceApplyDTO, requestNo, currentTime);
			}
		}

		if (CollectionUtils.isNotEmpty(refundApplyList)) {
			for (RefundApplyDTO refundApplyDTO : refundApplyList) {
				refundApplyDTO.setComplaintChannel(dto.getComplaintChannel());
				refundApply(refundApplyDTO, requestNo, currentTime);
			}
		}

		for (WoAttachmentVO attachmentVO : dto.getAttachmentList()) {
			ReduceAttachmentDTO attachmentDTO = new ReduceAttachmentDTO();
			BeanUtils.copyProperties(attachmentVO,attachmentDTO);
			attachmentDTO.setFileName(attachmentVO.getFilename());
			attachmentDTO.setUniqueFileName(attachmentVO.getUniqueFilename());
			attachmentDTO.setRequestNo(requestNo);
			attachmentDTO.setGmtCreate(attachmentVO.getUploadTime());
			saveAttachment(attachmentDTO);
		}		
		// 保存记录
		saveProcessLog(requestNo, dto.getOpinion(), ReduceAndRefundApprovalStatusEnum.APPLY.getCode(), dto.getReduceReason(), dto.getComplaintChannel());

		//发送待审批到审批人员
		/*List<OpDictInfo> dictList = CommonUtils.getDict("reduce_approval_staff", null);
		if (CollectionUtils.isEmpty(dictList)) {
			return;
		}
		Map<String, List<String>> approverMap = new HashMap<>();
		for (OpDictInfo info : dictList) {
			approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
		}
		List<String> firstApprover = approverMap.get("1");
		NoticeMsgReqDTO req = new NoticeMsgReqDTO();
		req.setTitle("减免申请审批通知");
		req.setContent("【减免申请审批】:减免申请审批" + "\t" + "【数量】:" + reduceApplyList.size() + refundApplyList.size());
		noticeMsgService.publishBannerNotice(firstApprover, req);*/
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void approvalRecord(ReduceApprovalDTO dto) {
		if (dto.getCurrentStatus().equals(dto.getApprovalStatus())){
			throw new FastRuntimeException("记录已审批");
		}
		
		checkApprovalAuth(dto.getCurrentStatus());
		// 更新记录状态
		updateRecordApprovalStatus(dto.getApprovalStatus(), dto.getRequestNo());
		
		// 添加审批记录
		saveProcessLog(dto.getRequestNo(), dto.getOpinion(), dto.getApprovalStatus(), null , null);

		// 如果复审通过，则推送减免记录到财务
		if (ReduceAndRefundApprovalStatusEnum.SECOND_APPROVAL.getCode().equals(dto.getApprovalStatus())) {
			String loginName = CommonUtils.getCurrentlogged();
			 pushReduceRecordToLender(dto.getRequestNo(), loginName);
		}
	}

	/**
	 * 检查审批权限
	 * @param currentStatus 当前状态
	 */
	private void checkApprovalAuth(String currentStatus) {
		List<OpDictInfo> dictList = CommonUtils.getDict("reduce_approval_staff", null);
		if (CollectionUtils.isEmpty(dictList)){
			return;
		}
		Map<String, List<String>> approverMap = new HashMap<>();
		for (OpDictInfo info : dictList) {
			approverMap.put(info.getType(),Arrays.asList(info.getContent().split(",")));
		}
		
		if (ReduceAndRefundApprovalStatusEnum.APPLY.getCode().equals(currentStatus)){
			// 一审人员
			List<String> firstApprover = approverMap.get("1");
			if (!firstApprover.contains(CommonUtils.getCurrentlogged())){
				throw new FastRuntimeException("无一审权限");
			}
		} else if (ReduceAndRefundApprovalStatusEnum.FIRST_APPROVAL.getCode().equals(currentStatus)){
			List<String> secondApprover = approverMap.get("2");
			if (!secondApprover.contains(CommonUtils.getCurrentlogged())){
				throw new FastRuntimeException("无二审权限");
			}
		}
		
	}

	private void updateRecordApprovalStatus(String approvalStatus, String requestNo) {
		OpReduceApplyRecord reduceRecord = new OpReduceApplyRecord();
		reduceRecord.setApprovalStatus(approvalStatus);
		reduceRecord.setGmtModify(new Date());
		opReduceApplyRecordMapper.update(reduceRecord, Wrappers.lambdaUpdate(OpReduceApplyRecord.class).eq(OpReduceApplyRecord::getRequestNo, requestNo));


		OpRefundApplyRecord refundRecord = new OpRefundApplyRecord();
		refundRecord.setApprovalStatus(approvalStatus);
		refundRecord.setGmtModify(new Date());
		opRefundApplyRecordMapper.update(refundRecord, Wrappers.lambdaUpdate(OpRefundApplyRecord.class).eq(OpRefundApplyRecord::getRequestNo, requestNo));
		

	}

	private void saveProcessLog(String requestNo, String opinion, String approvalStatus, String reduceReason, String complaintChannel) {
		OpReduceRefundApprovalLog log = new OpReduceRefundApprovalLog();
		log.setRequestNo(requestNo);
		log.setOpinion(opinion);
		log.setApprovalStatus(approvalStatus);
		log.setStaffId(CommonUtils.getCurrentloggedStaffId());
		log.setReductionReason(reduceReason);
		log.setComplaintChannel(complaintChannel);
		opReduceRefundApprovalLogMapper.insert(log);
	}

	@Override
	public Page<ReduceAndRefundVO> queryApplyRecord(RecordQueryDTO dto) {
		Page<ReduceAndRefundVO> page = opReduceApplyRecordMapper.selectApplyRecordPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
		List<ReduceAndRefundVO> list = page.getRecords()
				.stream().map(this::explainCodeToCh).collect(Collectors.toList());
		page.setRecords(list);
		return page;
	}

	private ReduceAndRefundVO explainCodeToCh(ReduceAndRefundVO vo) {
		vo.setApprovalStatusCode(vo.getApprovalStatus());
		vo.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.getDescByCode(vo.getApprovalStatus()));
		vo.setTaskStatus(ReduceTaskStatusEnum.getDescByCode(vo.getTaskStatus()));
		return vo;
	}

	@Override
	public void pushReduceRecordToLender(String requestNo, String approvalStaff) {
		// 查询该序列号下，未推送，并且审批状态为复审通过的减免记录
		List<OpReduceApplyRecord> reduceList = getUnPushedApprovedReduceRecord(requestNo);


		if (CollectionUtils.isEmpty(reduceList)){
			log.warn("{},无需要推送的减免记录", requestNo);
			return;
		}
		
		//查询员工信息map，用于后面填充
		Map<Long, InAuthCrmStaff> staffIdMap = inAuthCrmStaffService.getStaffIdMap();

		// 查询
		int index = 1;
		for (OpReduceApplyRecord reduceRecord : reduceList) {
			SubdidySaveDTO subdidySaveDTO = convertReduceRecordToSubdidySaveDTO(reduceRecord, staffIdMap, approvalStaff, index);
			log.info("客服推送减免记录,requestNo:{},参数:{}", subdidySaveDTO.getServiceNo(), JSON.toJSONString(subdidySaveDTO));
			Response response = lenderService.pushReductionDataToLender(subdidySaveDTO);
			log.info("客服推送减免记录结果,requestNo:{}, res:{}", subdidySaveDTO.getServiceNo(), JSON.toJSON(response));
			if (Response.isSuccess(response)){
				updateReduceRecordPushFlag(reduceRecord);
			} else {
				reduceRecord.setResult(ReduceAndRefundResultEnum.PUSH_FAILED.getStatus());
				reduceRecord.setFailReason(response.getMessage());
				updateReduceRecordPushFlag(reduceRecord);
				log.warn("客服推送减免记录失败,requestNo:{}", subdidySaveDTO.getServiceNo());
			}
			index++;
		}
		
		
	}


	@Override
	public ReduceAndRefundApplySubmitDTO queryApprovalRecord(String requestNo) {
		ReduceAndRefundApplySubmitDTO submitDTO = new ReduceAndRefundApplySubmitDTO();

		Map<String, String> loanModelTypeMap = CommonUtils.getDictTypeContentMap(LOAN_MODEL_TYPE, "");
		// 查询减免记录
		List<ReduceApplyDTO> reduceList = queryReduceList(requestNo);
		reduceList.forEach(item -> item.setLoanModelType(loanModelTypeMap.getOrDefault(item.getPartnerCodeNew(), DEFAULT_MODEL_TYPE)));
		submitDTO.setReduceApplyList(reduceList);
		
		// 查询退款记录
		List<RefundApplyDTO> refundList = queryRefundList(requestNo);
		refundList.forEach(item -> item.setLoanModelType(loanModelTypeMap.getOrDefault(item.getPartnerCodeNew(), DEFAULT_MODEL_TYPE)));
		submitDTO.setRefundApplyList(refundList);


		Map<Long, InAuthCrmStaff> staffIdMap = inAuthCrmStaffService.getStaffIdMap();
		// 查询日志
		List<ReduceApprovalLog> logs = queryReduceLogs(requestNo, staffIdMap);
		submitDTO.setLogs(logs);
		
		// 查询附件
		submitDTO.setAttachmentList(queryAttachment(requestNo));
		return submitDTO;
	}

	private List<WoAttachmentVO> queryAttachment(String requestNo) {
        List<OpReduceRefundAttachment> attachmentList = opReduceRefundAttachmentMapper.selectList(
            Wrappers.lambdaQuery(OpReduceRefundAttachment.class).eq(OpReduceRefundAttachment::getRequestNo, requestNo));
		return attachmentList.stream().map(this::convertToAttachmentVo).collect(Collectors.toList());
	}

	private WoAttachmentVO convertToAttachmentVo(OpReduceRefundAttachment attachment) {
		WoAttachmentVO vo = new WoAttachmentVO();
        BeanUtils.copyProperties(attachment, vo);
		vo.setFilename(attachment.getFileName());
		vo.setUniqueFilename(attachment.getUniqueFileName());
		vo.setUploadTime(attachment.getGmtCreate());
        vo.setPath(uploadAndNoticeService.getFileUrlFromCache(attachment.getUniqueFileName()));
		return vo;
	}


	@Override
	public Page<ReduceDetailReportVO> reduceAndRefundDetailReport(ReduceReportDTO dto) {
		if (CollectionUtils.isEmpty(dto.getApplyTypeList())){
			throw new FastRuntimeException("申请类型不能为空");
		}
		Page<ReduceDetailReportVO> page = opReduceApplyRecordMapper
				.selectReduceRefundDetailPage(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
		page.getRecords().forEach(this::explainDetailVO);
		
		return page;
	}

    private void explainDetailVO(ReduceDetailReportVO reduceDetailReportVO) {
        reduceDetailReportVO.setReduceType(ReductionTypeEnum.getDescByType(reduceDetailReportVO.getReduceType()));
        reduceDetailReportVO.setRefundType(ReductionTypeEnum.getDescByType(reduceDetailReportVO.getRefundType()));
        String processStatus = reduceDetailReportVO.getProcessStatus();
        if (StringUtils.isNotBlank(processStatus)) {
            String[] split = processStatus.split(",");
            if (split.length > 1) {
                reduceDetailReportVO.setProcessStatus(ReduceAndRefundResultEnum.getDescByStatus(split[0]) + ","
                    + ReduceAndRefundResultEnum.getDescByStatus(split[1]));
            } else {
	            reduceDetailReportVO.setProcessStatus(ReduceAndRefundResultEnum.getDescByStatus(split[0]));
            }
			
        }
		
    }


	@Override
	public Page<ReduceStatisticsReportVO> reduceStatisticReport(ReduceReportDTO dto) {
		Page<ReduceStatisticsReportVO> page = opReduceApplyRecordMapper.queryStatisticData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
		for (ReduceStatisticsReportVO reportVO : page.getRecords()) {
			reportVO.setReduceType(ReductionTypeEnum.getDescByType(reportVO.getReduceType()));
			reportVO.setRefundType(ReductionTypeEnum.getDescByType(reportVO.getRefundType()));
		}
		return page;
	}

	@Override
	public List<AllowanceRecordVO> queryReducedRecord(String appNo) {
		return financialDubboService.querySubsidyRecordByAppid(appNo).getResult();
	}

	@Override
	public Long saveAttachment(ReduceAttachmentDTO dto) {
		OpReduceRefundAttachment attachment = new OpReduceRefundAttachment();
		BeanUtils.copyProperties(dto, attachment);
		opReduceRefundAttachmentMapper.insert(attachment);
		return attachment.getId();
	}


	@Override
    public String getAttachmentUrl(Long id) {
        OpReduceRefundAttachment attachment = opReduceRefundAttachmentMapper.selectById(id);
        if (Objects.isNull(attachment)) {
            throw new FastRuntimeException(id + "附件不存在");
        }
        return uploadAndNoticeService.getFileUrlFromCache(attachment.getUniqueFileName());
    }

	@Override
	public void deleteAttachment(Long id) {
		opReduceRefundAttachmentMapper.deleteById(id);
	}

	@Override
	public List<AllowanceCalculateVO> checkReduce(List<String> appNoList) {
		AllowanceCalculateDTO dto = new AllowanceCalculateDTO();
		dto.setApplicationIdList(appNoList);
		log.info("allowanceCalculate dto: {}", dto);
		Response<List<AllowanceCalculateVO>> res = financialDubboService.allowanceCalculate(dto);
		log.info("allowanceCalculate res: {}", res);
		return res.getResult();
	}

	/**
	 * 根据流水号查询减免审批日志
	 */
	private List<ReduceApprovalLog> queryReduceLogs(String requestNo, Map<Long, InAuthCrmStaff> staffIdMap) {
		List<OpReduceRefundApprovalLog> logs = opReduceRefundApprovalLogMapper
				.selectList(Wrappers.lambdaQuery(OpReduceRefundApprovalLog.class).eq(OpReduceRefundApprovalLog::getRequestNo, requestNo));

		return logs.stream().map(item -> convertDomainToLogDTO(item ,staffIdMap)).collect(Collectors.toList());
	}

	private ReduceApprovalLog convertDomainToLogDTO(OpReduceRefundApprovalLog opReduceRefundApprovalLog, Map<Long, InAuthCrmStaff> staffIdMap) {
		ReduceApprovalLog approvalLog = new ReduceApprovalLog();
		approvalLog.setStaffName(staffIdMap.get(opReduceRefundApprovalLog.getStaffId()).getLoginName());
		approvalLog.setProcessTime(opReduceRefundApprovalLog.getGmtCreate());
		approvalLog.setOpinion(opReduceRefundApprovalLog.getOpinion());
		approvalLog.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.getDescByCode(opReduceRefundApprovalLog.getApprovalStatus()));
		return approvalLog;
	}

	/**
	 * 根据流水号查询退款记录
	 */
	private List<RefundApplyDTO> queryRefundList(String requestNo) {
		List<OpRefundApplyRecord> refundList = opRefundApplyRecordMapper
				.selectList(Wrappers.lambdaQuery(OpRefundApplyRecord.class).eq(OpRefundApplyRecord::getRequestNo, requestNo));
		return refundList.stream().map(this::convertDomainToRefundDTO).collect(Collectors.toList());
	}

	private RefundApplyDTO convertDomainToRefundDTO(OpRefundApplyRecord refundApplyRecord) {
		RefundApplyDTO refundDTO = new RefundApplyDTO();
		BeanUtils.copyProperties(refundApplyRecord, refundDTO);
		refundDTO.setName(refundApplyRecord.getCustomerName());
		refundDTO.setUserId(String.valueOf(refundApplyRecord.getUserId()));
		refundDTO.setRefundStartTime(DateUtil.dateToString(refundApplyRecord.getRefundStartTime()));
		refundDTO.setRefundType(ReductionTypeEnum.getDescByType(refundDTO.getRefundType()));
		return refundDTO;
	}

	/**
	 * 根据流水号查询减免记录
	 */
	private List<ReduceApplyDTO> queryReduceList(String requestNo) {
		List<OpReduceApplyRecord> list = opReduceApplyRecordMapper.selectList(Wrappers.lambdaQuery(OpReduceApplyRecord.class).eq(OpReduceApplyRecord::getRequestNo, requestNo));
		return list.stream().map(this::convertDomainToReduceDTO).collect(Collectors.toList());
	}

	private ReduceApplyDTO convertDomainToReduceDTO(OpReduceApplyRecord reduceApplyRecord) {
		ReduceApplyDTO applyDTO = new ReduceApplyDTO();
		BeanUtils.copyProperties(reduceApplyRecord, applyDTO);
		applyDTO.setName(reduceApplyRecord.getCustomerName());
		applyDTO.setUserId(String.valueOf(reduceApplyRecord.getUserId()));
		applyDTO.setReductionType(ReductionTypeEnum.getDescByType(reduceApplyRecord.getReductionType()));
		return applyDTO;
	}

	private void updateReduceRecordPushFlag(OpReduceApplyRecord reduceRecord) {
		reduceRecord.setPushFlag("Y");
		reduceRecord.setGmtModify(new Date());
		opReduceApplyRecordMapper.updateById(reduceRecord);
	}

	private SubdidySaveDTO convertReduceRecordToSubdidySaveDTO(OpReduceApplyRecord reduceRecord, Map<Long, InAuthCrmStaff> staffIdMap, String approvalStaff, int index) {
		SubdidySaveDTO subdidySaveDTO = new SubdidySaveDTO();
		subdidySaveDTO.setApplicationId(reduceRecord.getApplicationId());
		subdidySaveDTO.setUserId(Long.valueOf(reduceRecord.getUserId()));
		subdidySaveDTO.setAmount(reduceRecord.getAmount());
		subdidySaveDTO.setReductionType(reduceRecord.getReductionType());
		subdidySaveDTO.setReductionReason(reduceRecord.getReductionReason());
		subdidySaveDTO.setApplyAt(DateUtil.dateToString(reduceRecord.getGmtCreate()));
		subdidySaveDTO.setApplicants(staffIdMap.get(reduceRecord.getStaffId()).getLoginName());
		subdidySaveDTO.setFinalist(approvalStaff);
		subdidySaveDTO.setFinaAt(new Date());
		subdidySaveDTO.setApplySource("kefu");
		subdidySaveDTO.setServiceNo(reduceRecord.getRequestNo() + "_" + index);
		subdidySaveDTO.setUserName(reduceRecord.getCustomerName());
		subdidySaveDTO.setReducibleAmount(reduceRecord.getReducibleAmount());
		subdidySaveDTO.setInterestRate(reduceRecord.getTotalRate());
		if (reduceRecord.getRateAfterReduce().compareTo(BigDecimal.ONE) > 0 ){
			subdidySaveDTO
					.setFinanlRate(reduceRecord.getRateAfterReduce().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
		} else {
			subdidySaveDTO
					.setFinanlRate(reduceRecord.getRateAfterReduce());
		}

		return subdidySaveDTO;
	}

	public List<OpReduceApplyRecord> getUnPushedApprovedReduceRecord(String requestNo) {
		return opReduceApplyRecordMapper.selectList(Wrappers.lambdaQuery(OpReduceApplyRecord.class)
				.eq(OpReduceApplyRecord::getRequestNo, requestNo)
				.eq(OpReduceApplyRecord::getApprovalStatus, ReduceAndRefundApprovalStatusEnum.SECOND_APPROVAL.getCode())
				.eq(OpReduceApplyRecord::getPushFlag, "N"));
		
	}
}
