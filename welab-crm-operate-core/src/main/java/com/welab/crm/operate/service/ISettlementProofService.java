package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.vo.settlement.SettleProofApplyRecordVO;
import com.welab.crm.operate.vo.settlement.SettlementProofReqDTO;
import com.welab.crm.operate.vo.settlement.SettlementProofVO;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/22
 */
public interface ISettlementProofService {

    /**
     * 存储结清记录
     *
     * @param dto
     * @param applicationId
     */
    public void addProofRecord(SettlementProofVO dto, String applicationId);

    /**
     * 更新结清记录
     *
     * @param dto
     * @param applicationId
     */
    public void updateProofRecord(SettlementProofVO dto, String applicationId);

    /**
     * 获取结清记录
     *
     * @param dto
     * @return
     */
    public Page<SettleProofApplyRecordVO> queryProofRecord(SettlementProofReqDTO dto);


    /**
     * 获取下载链接
     * @param id
     * @return
     */
    String queryDownloadUrl(Long id);


    /**
     * 获取 oppo 结清证明
     *
     * @param applicationId 贷款号
     */
    SettlementProofVO getOppoSettlement(String applicationId);


    /**
     * 获取 山东信托 结清证明（当前只用于获取山东信托结清证明，后续可能还能获取其他资金方的结清证明）
     *
     * @param applicationId       贷款号
     * @param partnerCode         实际资金方编码 eg: sdtrust1, sdtrust2, sdtrust3
     * @param standardPartnerCode 标准化资金方编码 eg:sdtrust
     */
    SettlementProofVO getSettlement(String applicationId, String partnerCode, String standardPartnerCode);


    /**
     * 是否重复申请，如果是重复申请，则更新链接，如果原链接为空，则不管
     *
     * @param applicationId
     * @return
     */
    boolean queryDupApply(String applicationId);


}
