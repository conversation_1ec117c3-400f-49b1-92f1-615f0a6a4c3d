package com.welab.crm.operate.service.impl;

import com.welab.crm.operate.mapper.WoTaskMapper;
import com.welab.crm.operate.service.OrderMonitorService;
import com.welab.crm.operate.vo.monitor.OrderCallbackMonitorVO;
import com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OrderMonitorServiceImpl implements OrderMonitorService {
	@Resource
	private WoTaskMapper woTaskMapper;
	@Override
	public List<OrderUrgeMonitorVO> queryOrderUrgeList() {
		return woTaskMapper.queryOrderUrgeList();
	}

	@Override
	public List<OrderCallbackMonitorVO> queryOrderCallbackList() {
		return woTaskMapper.queryOrderCallbackList();
	}
}
