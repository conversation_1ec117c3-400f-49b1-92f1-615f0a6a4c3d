package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.StopCollectionList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.blacklist.BlackDetailReqDTO;
import com.welab.crm.operate.vo.blacklist.BlackDetailReportVO;
import com.welab.crm.operate.vo.blacklist.BlackListSummaryReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 停催名单列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface StopCollectionListMapper extends ExpandBaseMapper<StopCollectionList> {

    /**
     * 根据userID列表查询 列表
     * @param userIdList uuid列表
     * @return userId:uuid Map
     */
    List<StopCollectionList> queryUuidByUserIdList(@Param("list") Set<String> userIdList);

    /**
     * 分页查询停催明细数据
     * @param page 分页参数
     * @param dto 请求参数
     * @return {@link BlackDetailReportVO}
     */
    Page<BlackDetailReportVO> selectDetailReportPage(Page<Object> page, @Param("dto") BlackDetailReqDTO dto);

    /**
     * 查询加黑明细
     * @param dto
     * @return
     */
    List<StopCollectionList> queryDetail(@Param("dto") BlackDetailReqDTO dto);


}
