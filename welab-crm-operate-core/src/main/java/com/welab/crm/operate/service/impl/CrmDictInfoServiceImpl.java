/**
 * @Title: ICrmDictInfoServiceImpl.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.operate.domain.InCallSummary;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.dict.CallSummaryReqDTO;
import com.welab.crm.operate.dto.dict.CallSummaryRespDTO;
import com.welab.crm.operate.dto.dict.DictInfoReqDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.InCallSummaryMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.ICrmDictInfoService;
import com.welab.crm.operate.vo.dict.DictInfoResVO;
import com.welab.security.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 字典服务
 * <AUTHOR>
 * @date 2021-10-15 10:11:44
 * @version v1.0
 */
@Slf4j
@Service
public class CrmDictInfoServiceImpl implements ICrmDictInfoService {
    
    @Autowired
    private OpDictInfoMapper opDictInfoMapper;

    @Autowired
    private InCallSummaryMapper inCallSummaryMapper;

    @Value("${online.system.user.token}")
    private String onlineHistoryUserToken;

    @Value("${online.system.app.key}")
    private String onlineSystemAppKey;

    @Value("${online.system.http.url.pre}")
    private String onlineSystemUrlPre;

    @Override
    public Page<DictInfoResVO> getDictInfos(DictInfoReqDTO req) {
        try {
            Page<OpDictInfo> pageResult = opDictInfoMapper.selectPage(new Page<>(req.getCurPage(), req.getPageSize()), buildDictSelectWrapper(req));
            List<DictInfoResVO> reslist = new ArrayList<DictInfoResVO>();
            Page<DictInfoResVO> page = new Page<DictInfoResVO>();
            BeanUtils.copyProperties(pageResult, page, "list");
            if (null != pageResult && null != pageResult.getRecords()) {
                reslist = pageResult.getRecords().stream().map(item->{
                    DictInfoResVO res = new DictInfoResVO();
                    BeanUtils.copyProperties(item, res, "status");
                    res.setStatus(item.getStatus() ? 1 : 0);
                    return res;
                }).collect(Collectors.toList());
            }
            page.setRecords(reslist);
            return page;
        } catch (Exception e) {
            log.error("getDictInfos:{}", e.getMessage());
            throw new RuntimeException("系统查询错误");
        }
    }
    
    private QueryWrapper<OpDictInfo> buildDictSelectWrapper(DictInfoReqDTO req){
        QueryWrapper<OpDictInfo> wrapper = new QueryWrapper<OpDictInfo>();
        wrapper.eq(StringUtils.isNotBlank(req.getCategory()), "category", req.getCategory());
        wrapper.likeRight(StringUtils.isNotBlank(req.getContent()), "content", req.getContent());
        wrapper.likeRight(StringUtils.isNotBlank(req.getType()), "type", req.getType());
        wrapper.eq(StringUtils.isNotBlank(req.getDetail()), "detail", req.getDetail());
        wrapper.eq(null != req.getStatus(), "status", req.getStatus());
        wrapper.orderByDesc("sort").orderByAsc("gmt_create");
        return wrapper;
    }

    @Override
    public boolean addDictInfo(DictInfoReqDTO reqDTO) {
        queryDupDict(reqDTO);
        try {
            OpDictInfo dict = new OpDictInfo();
            BeanUtils.copyProperties(reqDTO, dict, "status");
            dict.setStatus(null != reqDTO.getStatus() && 0 != reqDTO.getStatus());
            int saveCount = opDictInfoMapper.insert(dict);
            if (reqDTO.getCategory().contains("phoneSummary")) {
                sendDictByOnlineCrm();
            }
            return 0 >= saveCount ? false : true;
        } catch (Exception e) {
            log.error("addDictInfo:{}", e.getMessage());
            throw new RuntimeException("系统插入错误");
        }
    }

    private void sendDictByOnlineCrm() {
        try {
            log.info("sendDictByOnlineCrm star");
            LambdaQueryWrapper<OpDictInfo> wrapper = Wrappers.<OpDictInfo>lambdaQuery()
                    .eq(OpDictInfo::getCategory, "phoneSummary").eq(OpDictInfo::getStatus, 1);
            List<OpDictInfo> opDictInfos = opDictInfoMapper.selectList(wrapper);
            // 推送字典变动给在线客服系统
            Map<String, String> params = new HashMap<>();
            params.put("userToken", onlineHistoryUserToken);
            String flowCode = UUID.randomUUID().toString();
            params.put("flowCode", flowCode);
            String timestamp = String.valueOf(System.currentTimeMillis());
            params.put("timestamp", timestamp);
            params.put("sign", MD5Util.md5(onlineSystemAppKey + timestamp + flowCode));
            List<String> dict = opDictInfos.stream().map(OpDictInfo::getContent).collect(Collectors.toList());
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("data", dict);
            String resStr = HttpClientUtil.post(HttpConfig.custom().url(assembleUrlParamsToUrl(onlineSystemUrlPre + "/api/summary/sensitive", params))
                    .headers(HttpHeader.custom().contentType("application/json").build())
                    .json(JSON.toJSONString(requestBody)));
            log.info("sendDictByOnlineCrm end, result:{}", resStr);
            if (StringUtils.isNotBlank(resStr)) {
                JSONObject jsonObject = JSON.parseObject(resStr);
                if (jsonObject.getIntValue("ret") != 0) {
                    log.warn("推送在线系统失败,res:{}", resStr);
                }
            }
        } catch (Exception e) {
            log.warn("sendDictByOnlineCrm error", e);
        }
    }

    private String assembleUrlParamsToUrl(String url, Map<String, String> urlParamMap) {
        if (urlParamMap.isEmpty()) {
            return url;
        }
        if (!url.contains("?")) {
            url += "?";
        } else {
            url += "&";
        }
        List<NameValuePair> params = new ArrayList<>();
        urlParamMap.forEach((k, v) -> {
            params.add(new BasicNameValuePair(k, v));
        });
        String paramString = URLEncodedUtils.format(params, "UTF-8");
        return url.concat(paramString);
    }

    @Override
    public boolean updateDictInfo(DictInfoReqDTO reqDTO) {
        OpDictInfo dictInfo = opDictInfoMapper.selectById(reqDTO.getId());
        if (null == dictInfo) {
            throw new RuntimeException("不存在此条信息");
        }
        queryDupDict(reqDTO);
        try {
            BeanUtils.copyProperties(reqDTO, dictInfo);
            dictInfo.setStatus(null != reqDTO.getStatus() && 0 != reqDTO.getStatus());
            int updateCount = opDictInfoMapper.updateById(dictInfo);
            if (dictInfo.getCategory().contains("phoneSummary")) {
                sendDictByOnlineCrm();
            }
            return 0 >= updateCount ? false : true;
        } catch (Exception e) {
            log.error("updateDictInfo:{}", e.getMessage());
            throw new RuntimeException("系统更新出错");
        }
    }

    @Override
    public boolean deleteDictInfo(DictInfoReqDTO reqDTO) {
        OpDictInfo dictInfo = opDictInfoMapper.selectById(reqDTO.getId());
        if(null == dictInfo) {
            throw new RuntimeException("不存在此条信息");
        }
        int delCount = opDictInfoMapper.deleteById(reqDTO.getId());
        if (dictInfo.getCategory().contains("phoneSummary")) {
            sendDictByOnlineCrm();
        }
        return 0 >= delCount ? false : true;
    }

    @Override
    public Page<CallSummaryRespDTO> getCallSummarys(CallSummaryReqDTO reqDTO) {
        try {
            Page<InCallSummary> pageResult = inCallSummaryMapper.selectPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), buildCallSummaryWrapper(reqDTO));
            List<CallSummaryRespDTO> reslist = new ArrayList<CallSummaryRespDTO>();
            Page<CallSummaryRespDTO> page = new Page<CallSummaryRespDTO>();
            BeanUtils.copyProperties(pageResult, page, "list");
            if (Objects.nonNull(pageResult) && Objects.nonNull(pageResult.getRecords())) {
                reslist = pageResult.getRecords().stream().map(t -> {
                    CallSummaryRespDTO res = new CallSummaryRespDTO();
                    BeanUtils.copyProperties(t, res);
                    return res;
                }).collect(Collectors.toList());
            }
            page.setRecords(reslist);
            return page;
        } catch (Exception e) {
            log.error("getCallSummarys:{}", e.getMessage());
            throw new RuntimeException("系统查询错误");
        }
    }

    @Override
    public boolean addCallSummary(CallSummaryReqDTO reqDTO) {
        try {
            InCallSummary dto = new InCallSummary();
            BeanUtils.copyProperties(reqDTO, dto);
            int saveCount = inCallSummaryMapper.insert(dto);
            return 0 >= saveCount ? false : true;
        } catch (Exception e) {
            log.error("addCallSummary:{}", e.getMessage());
            throw new RuntimeException("系统插入错误");
        }
    }

    @Override
    public boolean deleteCallSummary(CallSummaryReqDTO reqDTO) {
        InCallSummary inCallSummary = inCallSummaryMapper.selectById(reqDTO.getId());
        if (null == inCallSummary) {
            throw new RuntimeException("不存在此条信息");
        }
        int delCount = inCallSummaryMapper.deleteById(reqDTO.getId());
        return 0 >= delCount ? false : true;
    }

    @Override
    public boolean topCallSummary(CallSummaryReqDTO reqDTO) {
        InCallSummary inCallSummary = inCallSummaryMapper.selectById(reqDTO.getId());
        if (null == inCallSummary) {
            throw new RuntimeException("不存在此条信息");
        }
        try {
            BeanUtils.copyProperties(reqDTO, inCallSummary);
            inCallSummary.setTopTime(new Date());
            int updateCount = inCallSummaryMapper.updateById(inCallSummary);
            return 0 >= updateCount ? false : true;
        } catch (Exception e) {
            log.error("topCallSummary:{}", e.getMessage());
            throw new RuntimeException("系统更新出错");
        }
    }

    private QueryWrapper<InCallSummary> buildCallSummaryWrapper(CallSummaryReqDTO reqDTO) {
        QueryWrapper<InCallSummary> wrapper = new QueryWrapper<InCallSummary>();
        wrapper.eq(Objects.nonNull(reqDTO.getId()), "id", reqDTO.getId());
        wrapper.eq(StringUtils.isNotBlank(reqDTO.getStaffMobile()), "staff_mobile", reqDTO.getStaffMobile());
        wrapper.orderByDesc("top_status", "top_time", "gmt_create");
        return wrapper;
    }


    private void queryDupDict(DictInfoReqDTO reqDTO) {
        if (!"speechAI".equals(reqDTO.getCategory())){
            return;
        }
        List<OpDictInfo> list = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class)
                .eq(OpDictInfo::getCategory, reqDTO.getCategory()).eq(OpDictInfo::getType, reqDTO.getType()));
        if (CollectionUtils.isNotEmpty(list)){
            if (Objects.isNull(reqDTO.getId())) {
                throw new CrmOperateException("不能添加相同英文名的字典");
            } else if (!reqDTO.getId().equals(list.get(0).getId())){
                throw new CrmOperateException("存在同英文名字典,更新失败");
            }
        }

    }
}
