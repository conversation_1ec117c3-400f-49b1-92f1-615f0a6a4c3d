package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.SensitiveWorkorderConfig;
import com.welab.crm.operate.dto.SensitiveWorkOrderDTO;
import com.welab.crm.operate.mapper.SensitiveWorkorderConfigMapper;
import com.welab.crm.operate.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
public class SensitiveWorkOrderServiceImpl implements SensitiveWorkOrderService {

	@Resource
	private SensitiveWorkorderConfigMapper sensitiveWorkorderConfigMapper;

	@Override
	public void add(SensitiveWorkOrderDTO dto) {
		SensitiveWorkorderConfig config = buildAddDomain(dto);
		sensitiveWorkorderConfigMapper.insert(config);

	}

	private SensitiveWorkorderConfig buildAddDomain(SensitiveWorkOrderDTO dto) {
		SensitiveWorkorderConfig config = buildUpdateDomain(dto);
		config.setGmtCreate(new Date());
		return config;
	}

	private SensitiveWorkorderConfig buildUpdateDomain(SensitiveWorkOrderDTO dto) {
		SensitiveWorkorderConfig config = new SensitiveWorkorderConfig();
		config.setWoTypeId(dto.getWoTypeId());
		config.setWoTypeDetail(dto.getWoTypeDetail());
		config.setWoTypeFirId(dto.getWoTypeFirId());
		config.setWoTypeFirDetail(dto.getWoTypeFirDetail());
		config.setWoTypeSecId(dto.getWoTypeSecId());
		config.setWoTypeSecDetail(dto.getWoTypeSecDetail());
		config.setWoTypeThirId(dto.getWoTypeThirId());
		config.setWoTypeThirDetail(dto.getWoTypeThirDetail());
		config.setGmtModify(new Date());
		config.setCreateStaff(CommonUtils.getCurrentlogged());
		config.setPartnerNames(dto.getPartnerNames());
		config.setRemark(dto.getRemark());
		config.setFollowUpTime(dto.getFollowUpTime());
		config.setWarnType(dto.getWarnType());
		return config;
	}


	@Override
	public Page<SensitiveWorkOrderDTO> query(SensitiveWorkOrderDTO dto) {
		Page<SensitiveWorkorderConfig> page = sensitiveWorkorderConfigMapper.selectPage(new Page<>(dto.getCurPage(), dto.getPageSize()),
				Wrappers.lambdaQuery(SensitiveWorkorderConfig.class)
						.eq(Objects.nonNull(dto.getWoTypeId()), SensitiveWorkorderConfig::getWoTypeId, dto.getWoTypeId())
						.eq(Objects.nonNull(dto.getWoTypeFirId()), SensitiveWorkorderConfig::getWoTypeFirId, dto.getWoTypeFirId())
						.eq(Objects.nonNull(dto.getWoTypeSecId()), SensitiveWorkorderConfig::getWoTypeSecId, dto.getWoTypeSecId())
						.eq(Objects.nonNull(dto.getWoTypeThirId()), SensitiveWorkorderConfig::getWoTypeThirId, dto.getWoTypeThirId())
						.like(StringUtils.isNotBlank(dto.getPartnerNames()), SensitiveWorkorderConfig::getPartnerNames, dto.getPartnerNames())
						.like(StringUtils.isNotBlank(dto.getWarnType()), SensitiveWorkorderConfig::getWarnType, dto.getWarnType())
						.eq(SensitiveWorkorderConfig::getIsStatus, 1)
						.orderByDesc(SensitiveWorkorderConfig::getGmtCreate));

		Page<SensitiveWorkOrderDTO> resPage = new Page<>();
		resPage.setTotal(page.getTotal());
		resPage.setRecords(copyList(page.getRecords()));
		resPage.setCurrent(page.getCurrent());
		resPage.setSize(page.getSize());
		return resPage;

	}

	private List<SensitiveWorkOrderDTO> copyList(List<SensitiveWorkorderConfig> records) {
		if (records == null || records.isEmpty()) {
			return Collections.emptyList();
		}
		List<SensitiveWorkOrderDTO> dtoList = new ArrayList<>();

		for (SensitiveWorkorderConfig config : records) {
			dtoList.add(buildDTO(config));
		}

		return dtoList;
	}

	private SensitiveWorkOrderDTO buildDTO(SensitiveWorkorderConfig config) {
		SensitiveWorkOrderDTO dto = new SensitiveWorkOrderDTO();
		dto.setId(config.getId());
		dto.setWoTypeId(config.getWoTypeId());
		dto.setWoTypeDetail(config.getWoTypeDetail());
		dto.setWoTypeFirId(config.getWoTypeFirId());
		dto.setWoTypeFirDetail(config.getWoTypeFirDetail());
		dto.setWoTypeSecId(config.getWoTypeSecId());
		dto.setWoTypeSecDetail(config.getWoTypeSecDetail());
		dto.setWoTypeThirId(config.getWoTypeThirId());
		dto.setWoTypeThirDetail(config.getWoTypeThirDetail());
		dto.setPartnerNames(config.getPartnerNames());
		dto.setGmtCreate(config.getGmtCreate());
		dto.setRemark(config.getRemark());
		dto.setCreateStaff(config.getCreateStaff());
		dto.setFollowUpTime(config.getFollowUpTime());
		dto.setWarnType(config.getWarnType());
		return dto;
	}

	@Override
	public void delete(List<Long> ids) {

		if (ids == null || ids.isEmpty()) {
			return;
		}
		for (Long id : ids) {
			SensitiveWorkorderConfig con = new SensitiveWorkorderConfig();
			con.setIsStatus(2);
			sensitiveWorkorderConfigMapper.update(con, Wrappers.lambdaUpdate(SensitiveWorkorderConfig.class).eq(SensitiveWorkorderConfig::getId, id));
		}

	}

	@Override
	public void update(SensitiveWorkOrderDTO dto) {

		if (dto == null || dto.getId() == null) {
			return;
		}
		SensitiveWorkorderConfig config = buildUpdateDomain(dto);
		config.setId(dto.getId());
		sensitiveWorkorderConfigMapper.updateById(config);

	}
}
