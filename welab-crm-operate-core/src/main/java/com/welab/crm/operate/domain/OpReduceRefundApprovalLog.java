package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.welab.crm.interview.enums.ReduceAndRefundApprovalStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 减免、退款审批日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_reduce_refund_approval_log")
public class OpReduceRefundApprovalLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客服id
     */
    private Long staffId;

    /**
     * 处理意见
     */
    private String opinion;

    /**
     * 关联对应的减免记录和退款记录
     */
    private String requestNo;


    /**
     * 审批状态
     * @see ReduceAndRefundApprovalStatusEnum
     */
    private String approvalStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;



    /**
     * 减免原因
     */
    private String reductionReason;


    /**
     * 投诉渠道
     */
    private String complaintChannel;


}
