package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 过河兵工单流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("elite_workorder_logs")
public class EliteWorkorderLogs implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联工单历史表
     */
    private String objectiveGuid;

    /**
     * 提单时间
     */
    private Date submitTime;

    /**
     * 提单组
     */
    private String submitGroup;

    /**
     * 提单人
     */
    private String submitStaff;

    /**
     * 处理时间
     */
    private Date dealTime;

    /**
     * 从步骤
     */
    private String fromStep;

    /**
     * 到步骤
     */
    private String toStep;

    /**
     * 处理组
     */
    private String dealGroup;

    /**
     * 处理人
     */
    private String dealStaff;

    /**
     * 操作
     */
    private String operateType;

    /**
     * 工单状态
     */
    private String orderStatus;

    /**
     * 处理意见
     */
    private String comments;


}
