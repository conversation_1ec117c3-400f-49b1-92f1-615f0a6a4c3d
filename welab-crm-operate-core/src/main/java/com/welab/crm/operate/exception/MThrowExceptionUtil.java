/**
 * Copyright 2021 Welab, Inc. All rights reserved.
 * WELAB PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.welab.crm.operate.exception;

import com.welab.common.exception.WeLabException;
import com.welab.crm.operate.enums.MErrorCodeEnum;

/**
 * <AUTHOR>
 * @date 2021-11-08 18:01:29
 * @version v1.0
 */
public final class MThrowExceptionUtil {

    public static void throwWelabException(MErrorCodeEnum errorCodeEnum) {
        throw new WeLabException(errorCodeEnum.getCode(), errorCodeEnum.getMessage());
    }

    public static void throwWelabException(MErrorCodeEnum errorCodeEnum, Object... args) {
        throw new WeLabException(errorCodeEnum.getCode(),
                String.format(errorCodeEnum.getMessage(), args));
    }

}
