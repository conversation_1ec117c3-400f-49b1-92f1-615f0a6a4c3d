/**
 * @Title: CrmColOrgServiceImpl.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.operate.domain.InAuthCrmOrg;
import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColOrgReqDTO;
import com.welab.crm.operate.mapper.InAuthCrmOrgMapper;
import com.welab.crm.operate.service.ICrmOrgService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.staff.ColOrgInfoResVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description TODO
 * <AUTHOR>
 * @date 2021-11-04 10:10:46
 * @version v1.0
 */
@Service
@Slf4j
public class CrmOrgServiceImpl implements ICrmOrgService {
    
    @Autowired
    private InAuthCrmOrgMapper inAuthCrmOrgMapper;
    
    
    @Value("${exam.system.url.pre}")
    private String examUrlPre;
    
    
    private static final String ADD = "add";
    private static final String MODIFY = "modify";
    private static final String DELETE = "delete";

    @Override
    public void addColOrgInfo(ColOrgReqDTO dto) {
        dto.setCode(dto.getCode().trim());
        if (CollectionUtils.isNotEmpty(getColOrgInfoByCode(dto.getCode()))) {
            throw new FastRuntimeException("operate.colorg.recard.duplicate", "组织已存在");
        }
        try {
            inAuthCrmOrgMapper.insert(getInAuthCollectionOrg(dto));
        } catch (Exception e) {
            throw new FastRuntimeException("system.insert.error.default", "添加组织异常");
        }
        // 同步变更到考试系统
        syncDeptToExaminationSystem(CommonUtils.getXUserId(), CommonUtils.getXUserToken(), ADD, dto);
    }

    private void syncDeptToExaminationSystem(String xUserId, String xUserToken, String operation, ColOrgReqDTO dto) {
        try {

            JSONObject params = new JSONObject();
            params.put("extraId", dto.getCode());
            params.put("parentId", dto.getPcode());
            params.put("source", "crm");
            params.put("name", dto.getName());
            params.put("operation", operation);

            String result = HttpClientUtil.post(HttpConfig
                .custom().url(examUrlPre + "/dept/syncDept").headers(HttpHeader.custom().contentType("application/json")
                    .other("x-user-id", xUserId).other("x-user-token", xUserToken).build())
                .json(params.toJSONString()));
            log.info("syncDeptToExaminationSystem ,params:{},res:{},x-user-id:{}", params.toJSONString(), result, xUserId);

        } catch (Exception e) {
            log.warn("syncDeptToExaminationSystem exception,params:{},x-user-id:{}", JSON.toJSONString(dto), xUserId,
                e);
        }
    }


    
    /*private InAuthCrmOrg getInAuthCollectionOrg(Long id) {
        InAuthCrmOrg org = getColOrgInfoById(id);
        if(null==org) {
            throw new FastRuntimeException("operate.colorg.recard.null","数据不存在");
        }
        return org.setGmtModify(new Date()).setLstUpdUser(null);// null=CommonUtils.getCurrentlogged()
    }*/
    
    private InAuthCrmOrg getColOrgInfoById(Long id) {
        if(null==id) {
            throw new FastRuntimeException("id不能为空");
        }
        return inAuthCrmOrgMapper.selectById(id);
    }
    
    private List<InAuthCrmOrg> getColOrgInfoByCode(String code) {
        QueryWrapper<InAuthCrmOrg> queryWrapper = new QueryWrapper<InAuthCrmOrg>();
        queryWrapper.eq("code", code);
        return inAuthCrmOrgMapper.selectList(queryWrapper);
    }
    
    private InAuthCrmOrg getInAuthCollectionOrg(ColOrgReqDTO reqDTO) {
        InAuthCrmOrg colOrg = new InAuthCrmOrg();
        BeanUtils.copyProperties(reqDTO, colOrg);
        /*if(StringUtils.isEmpty(colOrg.getIsReassign())) {
            colOrg.setIsReassign("Y");
        }*/
        return colOrg.setGmtCreate(new Date()).setCreateUser(CommonUtils.getCurrentlogged()).setGmtModify(new Date());
    }

    @Override
    public void deleteColOrgInfo(BatchDictInfoReqDTO reqDTO) {
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();
        for (Long id : reqDTO.getNo()) {
            //updateById(getInAuthCollectionOrg(id));
            InAuthCrmOrg org = getColOrgInfoById(id);
            if (Objects.isNull(org)) {
                throw new FastRuntimeException("operate.colorg.recard.null", "数据不存在");
            }
            inAuthCrmOrgMapper.deleteById(id);

            ColOrgReqDTO colOrgReqDTO = new ColOrgReqDTO();
            BeanUtils.copyProperties(org, colOrgReqDTO);
            syncDeptToExaminationSystem(xUserId,xUserToken,DELETE, colOrgReqDTO);
        }
    }
    
    private void updateById(InAuthCrmOrg colOrg) {
        inAuthCrmOrgMapper.updateById(colOrg);
    }

    @Override
    public List<ColOrgInfoResVO> updateColOrgInfoList(ColOrgReqDTO reqDTO) {
        try {
            List<InAuthCrmOrg> result = getColOrgInfosList(reqDTO);
            List<ColOrgInfoResVO> datas = null;
            if (null != result) {
                datas = result.parallelStream().map(item -> {
                    try {
                        ColOrgInfoResVO res = new ColOrgInfoResVO();
                        BeanUtils.copyProperties(item, res);
                        //res.setIsStatus(item.getIsStatus() ? 1 : 0);
                        //res.setOrgOrder(Integer.parseInt(item.getOrgOrder()));
                        res.setLevel(Integer.parseInt(item.getLevel()));
                        return res;
                    } catch (BeansException e) {
                        log.error("getColOrgInfosList error :{}", e.getMessage());
                    }
                    return null;
                }).filter(item -> null != item).collect(Collectors.toList());

            }
            List<ColOrgInfoResVO> resultList = findTree(datas, reqDTO);
            /*int size = getColOrgInfosCount();
            if (size == datas.size()) {
                for (ColOrgInfoResVO colOrgInfoResVO : datas) {
                    for (InAuthCrmOrg info : result) {
                        if (colOrgInfoResVO.getId() == info.ge.getId()) {
                            info.setLevel(colOrgInfoResVO.getLevel() + "");
                            updateById(info);
                        }
                    }
                }
            }*/
            return resultList;
        } catch (Exception e) {
            log.error("The method getColOrgInfosList error :exception{}", e);
            throw new FastRuntimeException("system.query.error.default", e);
        }
    }
    
    public List<InAuthCrmOrg> getColOrgInfosList(ColOrgReqDTO filter) {
        return inAuthCrmOrgMapper.selectList(buildConditionByQueryFilter(filter));
    }
    
    /*private int getColOrgInfosCount() {
        return inAuthCrmOrgMapper.selectCount(new QueryWrapper<InAuthCrmOrg>().eq("is_status", true));
    }*/
    
    private QueryWrapper<InAuthCrmOrg> buildConditionByQueryFilter(ColOrgReqDTO filter) {
        return new QueryWrapper<InAuthCrmOrg>()
                .like(StringUtils.isNotEmpty(filter.getCode()),"code", filter.getCode())
                .eq(StringUtils.isNotEmpty(filter.getName()),"name", filter.getName())
                .eq(StringUtils.isNotEmpty(filter.getPcode()),"pcode", filter.getPcode())
                // 综合跟踪管理控制数据权限
                //.in(CommonUtils.isManager(),"code", getManagerOrgs(CommonUtils.getCurrentloggedOrg()))
                //.eq(!CommonUtils.isManager(),"code", CommonUtils.getCurrentloggedOrg())
                .orderByAsc("level");
    }
    
    public List<ColOrgInfoResVO> findTree(List<ColOrgInfoResVO> datas, ColOrgReqDTO reqDTO) {
        List<ColOrgInfoResVO> allMenu = datas;
        // 根节点
        List<ColOrgInfoResVO> rootMenu = new ArrayList<ColOrgInfoResVO>();
        for (ColOrgInfoResVO nav : allMenu) {
            // 父节点是0的，为根节点。
            if ((nav.getPcode() == null || nav.getPcode().isEmpty())) {
                rootMenu.add(nav);
            }
        }
        if (rootMenu.isEmpty()) {
            for (ColOrgInfoResVO nav : allMenu) {
                if (allMenu.get(0).getLevel() == nav.getLevel()) {
                    rootMenu.add(nav);
                }
            }
        }
        // 为根菜单设置子菜单，getClild是递归调用的
        for (ColOrgInfoResVO nav : rootMenu) {
            //nav.setLevel(1);
            /* 获取根节点下的所有子节点 使用getChild方法 */
            List<ColOrgInfoResVO> childList = getChild(nav, allMenu);
            // 给根节点设置子节点
            nav.setChildren(childList);
        }
        return rootMenu;
    }
    
    /**
     * 获取子节点
     * @param string 父节点id
     * @param allMenu 所有菜单列表
     * @return 每个根节点下，所有子菜单列表
     */
    public List<ColOrgInfoResVO> getChild(ColOrgInfoResVO vo, List<ColOrgInfoResVO> allMenu) {
        // 子菜单
        List<ColOrgInfoResVO> childList = new ArrayList<ColOrgInfoResVO>();
        for (ColOrgInfoResVO nav : allMenu) {
            // 遍历所有节点，将所有菜单的父id与传过来的根节点的id比较
            // 相等说明：为该根节点的子节点。
            if (vo.getCode().equals(nav.getPcode())) {
                //nav.setLevel(vo.getLevel() + 1);
                childList.add(nav);
            }
        }
        // 递归
        for (ColOrgInfoResVO nav : childList) {
            nav.setChildren(getChild(nav, allMenu));
        }
        //排序
        Collections.sort(childList, order());
        // 如果节点下没有子节点，返回一个空List（递归退出）
        if (childList.size() == 0) {
            return new ArrayList<ColOrgInfoResVO>();
        }
        return childList;
    }
    
    /**
     * 根据order排序
    * @return
    */
    public Comparator<ColOrgInfoResVO> order() {
        Comparator<ColOrgInfoResVO> comparator = new Comparator<ColOrgInfoResVO>() {
            @Override
            public int compare(ColOrgInfoResVO o1, ColOrgInfoResVO o2) {
                if (o1.getLevel() != o2.getLevel()) {
                    return o1.getLevel() - o2.getLevel();
                }
                return 0;
            }
        };
        return comparator;
    }
    
    @Override
    public void updateColOrg(ColOrgReqDTO reqDTO) {
        updateById(getOrgModel(reqDTO));

        syncDeptToExaminationSystem(CommonUtils.getXUserId(), CommonUtils.getXUserToken(), MODIFY, reqDTO);
    }
    
    private InAuthCrmOrg getOrgModel(ColOrgReqDTO dto) {
        InAuthCrmOrg colorg = getColOrgInfoById(dto.getId());
        return colorg.setName(dto.getName()).setPcode(dto.getPcode()).setGmtModify(DateUtil.getCurrentDateTime()).setLstUpdUser(CommonUtils.getCurrentlogged()).setLevel(dto.getLevel() + "");
    }

    @Override
    public String getManagerOrgList(String code) {
        List<String> codeStrList = getManagerOrgs(code);
        return Arrays.toString(codeStrList.toArray(new String[codeStrList.size()]));
    }

    @Override
    public String getOrgNameByCode(String code) {
        List<InAuthCrmOrg> existList = getColOrgInfoByCode(code);
        if(null == existList ||  existList.isEmpty()) {
            return code;
        }
        return existList.get(0).getName();
    }

    @Override
    public List<ColOrgInfoResVO> getOrgSelectList(String code) {
        if (null == code) {
            throw new FastRuntimeException("非法请求，请先登录");
        }
        List<InAuthCrmOrg> codeList = new ArrayList<InAuthCrmOrg>();
        List<InAuthCrmOrg> result = getColOrgInfosByfilter(new ColOrgReqDTO());
        if (null == result || result.isEmpty()) {
            throw new FastRuntimeException("无有效组织");
        }
        codeList = getChildCodeList(code, result, codeList);
        for (InAuthCrmOrg inAuthCollectionOrg : result) {
            if (code.equals(inAuthCollectionOrg.getCode())) {
                codeList.add(inAuthCollectionOrg);
                break;
            }
        }
        List<ColOrgInfoResVO> datas = null;
        datas = codeList.parallelStream().map(item -> {
            try {
                ColOrgInfoResVO res = new ColOrgInfoResVO();
                BeanUtils.copyProperties(item, res, "");
                return res;
            } catch (BeansException e) {
                // ignore exception
            }
            return null;
        }).filter(item -> null != item).collect(Collectors.toList());

        return datas;
    }
    
    private List<InAuthCrmOrg> getColOrgInfosByfilter(ColOrgReqDTO filter) {
        return inAuthCrmOrgMapper.selectList(getQueryWrapper(filter).orderByAsc("level"));
    }
    
    private QueryWrapper<InAuthCrmOrg> getQueryWrapper(ColOrgReqDTO filter) {
        return new QueryWrapper<InAuthCrmOrg>().eq(StringUtils.isNotEmpty(filter.getCode()),"code", filter.getCode());
    }
    
    private List<InAuthCrmOrg> getChildCodeList(String code, List<InAuthCrmOrg> result, List<InAuthCrmOrg> codeList) {
        for (InAuthCrmOrg inAuthCollectionOrg : result) {
            if (code.equals(inAuthCollectionOrg.getPcode())) {
                codeList.add(inAuthCollectionOrg);
                getChildCodeList(inAuthCollectionOrg.getCode(), result, codeList);
            }
        }
        return codeList;
    }

    @Override
    public List<String> getManagerOrgs(String code) {
        List<InAuthCrmOrg> codeList = new ArrayList<InAuthCrmOrg>();
        List<InAuthCrmOrg> result = getColOrgInfosByfilter(new ColOrgReqDTO());
        codeList = getChildCodeList(code, result, codeList);
        List<String> codeStrList = new ArrayList<String>();
        codeStrList.add(code);
        for (InAuthCrmOrg info : codeList) {
            codeStrList.add(info.getCode());
        }
        return codeStrList;
    }

    @Override
    public List<String> getManagerOaOrgs(String code) {
        List<InAuthCrmOrg> codeList = new ArrayList<InAuthCrmOrg>();
        List<InAuthCrmOrg> result = getColOrgInfosByfilter(new ColOrgReqDTO());
        codeList=getChildCodeList(code, result, codeList);
        List<String> codeStrList =  new ArrayList<String>();
        //codeStrList.add(queryOrgByGroupCode(code).getOaOrgCd());
        /*for (InAuthCrmOrg info : codeList) {
            if(StringUtils.isNotEmpty(info.getOaOrgCd())) {
            codeStrList.add(info.getOaOrgCd());
            }
        }*/
        if(CollectionUtils.isEmpty(codeStrList)) {
            throw new FastRuntimeException("无数据查询权限");
        }
        return codeStrList;
    }

    @Override
    public boolean isManaGroup(String code) {
        InAuthCrmOrg org = inAuthCrmOrgMapper
            .selectOne(Wrappers.lambdaQuery(InAuthCrmOrg.class).eq(InAuthCrmOrg::getCode, code));
        return "1".equals(org.getLevel());
    }

    @Override
    public Map<String, String> getGroupCodeNameMap() {
        return inAuthCrmOrgMapper.selectList(null).stream()
            .collect(Collectors.toMap(InAuthCrmOrg::getCode, InAuthCrmOrg::getName, (s, s2) -> s2));
    }

    @Override
    public void syncAllOrgToExamSystem() {
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();

        List<InAuthCrmOrg> orgList =
            inAuthCrmOrgMapper.selectList(Wrappers.lambdaQuery(InAuthCrmOrg.class).orderByAsc(InAuthCrmOrg::getId));

        orgList.stream().map(item -> {
            ColOrgReqDTO dto = new ColOrgReqDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).forEach(dto -> syncDeptToExaminationSystem(xUserId, xUserToken, ADD, dto));
    }

    @Override
    public ColOrgInfoResVO getOrgInfoByCode(String code) {
        InAuthCrmOrg inAuthCrmOrg = inAuthCrmOrgMapper.selectOne(Wrappers.lambdaQuery(InAuthCrmOrg.class).eq(InAuthCrmOrg::getCode, code));
        if (Objects.isNull(inAuthCrmOrg)) {
            return null;
        }
        return buildOrgVO(inAuthCrmOrg);
    }

    private ColOrgInfoResVO buildOrgVO(InAuthCrmOrg inAuthCrmOrg) {
        ColOrgInfoResVO res = new ColOrgInfoResVO();
        BeanUtils.copyProperties(inAuthCrmOrg, res);
        return res;

    }
}
