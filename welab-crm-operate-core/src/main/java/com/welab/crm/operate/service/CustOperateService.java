package com.welab.crm.operate.service;

import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import com.welab.crm.operate.dto.operate.ApplicationOperateReqDTO;
import com.welab.crm.operate.dto.operate.OperateHistoryQueryReqDTO;
import com.welab.crm.operate.dto.operate.UrgentApprovalReqDTO;
import com.welab.crm.operate.dto.operate.UserInfoModifyReqDTO;
import com.welab.crm.operate.vo.operate.CustHisOperateVO;
import java.util.List;

/**
 * 操作历史服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface CustOperateService {


    /**
     * 保存贷款申请操作历史记录
     * @param operateReqDTO
     */
    void saveApplicationOperateHistory(ApplicationOperateReqDTO operateReqDTO);


    /**
     * 保存修改用户操作记录
     * @param reqDTO
     */
    void saveUpdateUserInfoOperateHistory(UserInfoModifyReqDTO reqDTO);

    /**
     * 保存用户操作历史
     * @param custHisOperate
     */
    void saveOperateHistory(CustHisOperate custHisOperate);


    /**
     * 批量插入
     * @param operates
     */
    void saveOperateHistoryBatch(List<CustHisOperate> operates);


    /**
     * 查询操作历史
     * @param reqDTO
     * @return
     */
    List<CustHisOperateVO> queryOperateHistory(OperateHistoryQueryReqDTO reqDTO);


    /**
     * 加急审批
     * @param dto
     */
    void saveUrgentApprovalHistory(UrgentApprovalReqDTO dto);

    /**
     * 保存用户操作历史
     * @param dto
     * @param staffVO
     * @param OperateType
     */
    void saveOperationHistory(HistoryOperationDTO dto, StaffVO staffVO, String OperateType);
}
