package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkSummaryDict;
import com.welab.crm.operate.dto.dict.tmk.TmkSumDictQueryDTO;
import com.welab.crm.operate.dto.dict.tmk.TmkSummaryDictDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.TmkSummaryDictMapper;
import com.welab.crm.operate.service.TmkSummaryDictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TmkSummaryDictServiceImpl implements TmkSummaryDictService {

    @Resource
    private TmkSummaryDictMapper summaryMapper;

    @Override
    public Page<TmkSummaryDict> listTmkSummaryDict(TmkSumDictQueryDTO qDTO) {
        Page<TmkSummaryDict> page = new Page<TmkSummaryDict>().setCurrent(qDTO.getCurPage()).setSize(qDTO.getPageSize());
        LambdaQueryWrapper<TmkSummaryDict> wrapper = Wrappers.<TmkSummaryDict>lambdaQuery()
                .select(TmkSummaryDict::getSummaryCode, TmkSummaryDict::getBusinessType, TmkSummaryDict::getResultCode,
                        TmkSummaryDict::getSummaryContent, TmkSummaryDict::getSort,
                        TmkSummaryDict::getState);
        if (StringUtils.isNotBlank(qDTO.getBusinessType())) {
            wrapper.in(StringUtils.isNotBlank(qDTO.getBusinessType()), TmkSummaryDict::getBusinessType,
                    Arrays.asList(qDTO.getBusinessType().split(",")));
        }
        wrapper.eq(StringUtils.isNotBlank(qDTO.getResultCode()), TmkSummaryDict::getResultCode, qDTO.getResultCode())
                .like(StringUtils.isNotBlank(qDTO.getSummaryContent()), TmkSummaryDict::getSummaryContent, qDTO.getSummaryContent())
                .eq(qDTO.getSort() != null, TmkSummaryDict::getSort, qDTO.getSort())
                // 先按照联系结果升序排列再按照营销小结优先级升序排列
                .orderByAsc(TmkSummaryDict::getResultCode).orderByAsc(TmkSummaryDict::getSort);
        return summaryMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTmkSummaryDict(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        // 1.判断指定业务类型和联系结果下的小结名称是否已存在,已存在则直接提示用户相应错误
        checkAddSummaries(tmkSummaryDictDTO);
        // 2.判断指定业务类型和联系结果下的优先级是否已存在,已存在需先把冲突的记录及其后的记录优先级都加1保存，然后再做插入操作
        processInsertSortData(staffCode, tmkSummaryDictDTO);
        // 3.保存用户新增的数据
        insertSummaries(staffCode, tmkSummaryDictDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTmkSummaryDict(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        // 1.判断指定业务类型和联系结果下的小结名称是否已存在,已存在则直接提示用户相应错误
        checkUpdateSummary(tmkSummaryDictDTO);
        // 2.判断指定业务类型和联系结果下的优先级是否已存在,已存在需先把冲突的记录及其后的记录优先级都加1保存，然后再做插入操作
        processUpdateSortData(staffCode, tmkSummaryDictDTO);
        // 3.更新用户提交的数据
        updateSummary(staffCode, tmkSummaryDictDTO);
    }

    /**
     * 处理插入操作小结优先级(说明: 相当于做队列处理)
     */
    private void processInsertSortData(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        // 1.判断业务类型、联系结果下此优先级是否已存在
        List<TmkSummaryDict> dictList = getSortSummaries(tmkSummaryDictDTO);
        // 2.如果优先级已存在,则更新其他记录的优先级
        if (!dictList.isEmpty()) {
            batchUpdateSort(staffCode, dictList);
        }
    }

    /**
     * 处理更新操作小结优先级(说明: 相当于做队列处理)
     */
    private void processUpdateSortData(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        // 1.判断业务类型、联系结果下此优先级是否已存在
        TmkSummaryDict sortContent = getSortSummary(tmkSummaryDictDTO);
        if (sortContent == null || tmkSummaryDictDTO.getSummaryCode().equals(sortContent.getSummaryCode())) {
            // 如果更新的值在数据库中还不存在或者就是更新值本身那就直接返回
            return;
        }
        // 2.更新其他记录的优先级
        updateSort(staffCode, sortContent);
    }

    /**
     * 更新记录的优先级
     *
     * @param staffCode 更新人编号
     * @param dict      具体某一个业务类型和联系结果对应的优先级记录
     */
    private void updateSort(String staffCode, TmkSummaryDict dict) {
        // 更新冲突的优先级数据及其后的数据
        LambdaQueryWrapper<TmkSummaryDict> lWrapper = Wrappers.<TmkSummaryDict>lambdaQuery()
                .eq(TmkSummaryDict::getBusinessType, dict.getBusinessType())
                .eq(TmkSummaryDict::getResultCode, dict.getResultCode())
                .ge(TmkSummaryDict::getSort, dict.getSort());
        List<TmkSummaryDict> updateList = summaryMapper.selectList(lWrapper);
        for (TmkSummaryDict summary : updateList) {
            summary.setSort(summary.getSort() + 1);
            summary.setLstUpdUser(staffCode);
            summaryMapper.updateById(summary);
        }
    }

    /**
     * 批量更新记录的优先级
     *
     * @param staffCode 更新人编号
     * @param dictList  多个业务类型下联系结果对应的优先级记录
     */
    private void batchUpdateSort(String staffCode, List<TmkSummaryDict> dictList) {
        for (TmkSummaryDict dict : dictList) {
            // 更新冲突的优先级数据及其后的数据
            updateSort(staffCode, dict);
        }
    }

    /**
     * 查询多个业务类型下联系结果对应的优先级数据
     */
    private List<TmkSummaryDict> getSortSummaries(TmkSummaryDictDTO tmkSummaryDictDTO) {
        LambdaQueryWrapper<TmkSummaryDict> sWrapper = getSortQueryWrapper(tmkSummaryDictDTO);
        sWrapper.in(TmkSummaryDict::getBusinessType, getBusinessTypeList(tmkSummaryDictDTO.getBusinessType()));
        return summaryMapper.selectList(sWrapper);
    }

    /**
     * 查询单个业务类型下联系结果对应的优先级数据
     */
    private TmkSummaryDict getSortSummary(TmkSummaryDictDTO dictDTO) {
        LambdaQueryWrapper<TmkSummaryDict> sWrapper = getSortQueryWrapper(dictDTO);
        sWrapper.eq(TmkSummaryDict::getBusinessType, dictDTO.getBusinessType());
        return summaryMapper.selectOne(sWrapper);
    }

    private LambdaQueryWrapper<TmkSummaryDict> getSortQueryWrapper(TmkSummaryDictDTO dictDTO) {
        return Wrappers.<TmkSummaryDict>lambdaQuery()
                .eq(TmkSummaryDict::getResultCode, dictDTO.getResultCode())
                .eq(TmkSummaryDict::getSort, dictDTO.getSort());
    }

    private List<String> getBusinessTypeList(String businessTypeString) {
        return Arrays.asList(businessTypeString.split(","));
    }

    private void checkAddSummaries(TmkSummaryDictDTO tmkSummaryDictDTO) {
        List<TmkSummaryDict> summaryDict = getSummaries(tmkSummaryDictDTO);
        if (!summaryDict.isEmpty()) {
            throw new CrmOperateException("小结名称已存在请修改!");
        }
    }

    /**
     * 通过多个业务类型、单个联系结果编码、单个小结内容 查询记录列表
     */
    private List<TmkSummaryDict> getSummaries(TmkSummaryDictDTO dictDTO) {
        LambdaQueryWrapper<TmkSummaryDict> cWrapper = getQueryWrapper(dictDTO);
        cWrapper.in(TmkSummaryDict::getBusinessType, getBusinessTypeList(dictDTO.getBusinessType()));
        return summaryMapper.selectList(cWrapper);
    }

    /**
     * 通过单业务类型、联系结果编码、小结内容 查询记录
     */
    private TmkSummaryDict getSummary(TmkSummaryDictDTO dictDTO) {
        LambdaQueryWrapper<TmkSummaryDict> cWrapper = getQueryWrapper(dictDTO);
        cWrapper.eq(TmkSummaryDict::getBusinessType, dictDTO.getBusinessType());
        return summaryMapper.selectOne(cWrapper);
    }

    private LambdaQueryWrapper<TmkSummaryDict> getQueryWrapper(TmkSummaryDictDTO dictDTO) {
        return Wrappers.<TmkSummaryDict>lambdaQuery()
                .eq(TmkSummaryDict::getResultCode, dictDTO.getResultCode())
                .eq(TmkSummaryDict::getSummaryContent, dictDTO.getSummaryContent());
    }

    /**
     * 检查更新时新修改的小结名称是否已经存在
     */
    private void checkUpdateSummary(TmkSummaryDictDTO tmkSummaryDictDTO) {
        TmkSummaryDict content = getSummary(tmkSummaryDictDTO);
        if (content != null && !Objects.equals(tmkSummaryDictDTO.getSummaryCode(), content.getSummaryCode())) {
            throw new CrmOperateException("小结名称已存在请修改!");
        }
    }

    private void insertSummaries(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        List<String> typeList = getBusinessTypeList(tmkSummaryDictDTO.getBusinessType());
        for (String businessType : typeList) {
            TmkSummaryDict summary = new TmkSummaryDict();
            BeanUtils.copyProperties(tmkSummaryDictDTO, summary);
            summary.setBusinessType(businessType);
            summary.setCreateUser(staffCode);
            summary.setSummaryCode(genSummaryCode());
            summary.setState(Boolean.TRUE);
            summaryMapper.insert(summary);
        }
    }

    /**
     * 产生一个营销小结对应的唯一编码
     */
    private String genSummaryCode() {
        LambdaQueryWrapper<TmkSummaryDict> wrapper = Wrappers.<TmkSummaryDict>lambdaQuery()
                .orderByDesc(TmkSummaryDict::getSummaryCode).last("LIMIT 1");
        TmkSummaryDict latestSummary = summaryMapper.selectOne(wrapper);
        if (latestSummary == null) {
            // 初始化手动设置一个编码，同时这也代表营销小结编号的样式
            return "su0001";
        } else {
            String[] codes = latestSummary.getSummaryCode().split("su");
            int num = Integer.parseInt(codes[1]) + 1;
            DecimalFormat f = new DecimalFormat("0000");
            String resultNum = f.format(num);
            return "su" + resultNum;
        }
    }

    private void updateSummary(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO) {
        TmkSummaryDict summary = new TmkSummaryDict();
        BeanUtils.copyProperties(tmkSummaryDictDTO, summary);
        summary.setLstUpdUser(staffCode);
        LambdaUpdateWrapper<TmkSummaryDict> wrapper = Wrappers.<TmkSummaryDict>lambdaUpdate()
                .eq(TmkSummaryDict::getSummaryCode, tmkSummaryDictDTO.getSummaryCode());
        summaryMapper.update(summary, wrapper);
    }
}
