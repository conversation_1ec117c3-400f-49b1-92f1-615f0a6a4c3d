package com.welab.crm.operate.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.constant.TmkTaskTypeConstant;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.excel.ExcelMergeStrategy;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.ICrmOrgService;
import com.welab.crm.operate.service.ICrmOrgStaffService;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.exception.FastRuntimeException;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import redis.clients.jedis.JedisCommands;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2021/11/9 16:59
 */
@Slf4j
@Component
public class CommonUtils {

    @Autowired
    private JedisCommands jedisCommands;
    @Autowired
    private ICrmOrgService crmOrgService;
    @Autowired
    private ICrmOrgStaffService crmOrgStaffService;
    @Autowired
    private OpDictInfoMapper opDictInfoMapper;

    private static CommonUtils util;

    private static String CODE_KEY = "crm_staffCode_";
    private static String GROUP_CODE_KEY = "crm_staffGroupCode_";

    private static String GROUP_NAME_KEY = "crm_staffGroupName_";
    private static String NAME_KEY = "crm_staffName_";
    private static String IS_MANAGER = "crm_staffPosition_";
    private static String MANAGER_CODES = "crm_managerOrgCodes_";
    private static String STAFF_ID_KEY = "crm_staffId_";

    private static final int MONTH_DAY = 31;
    private static final String TIME_EXCEPTION_MESSAGE = "温馨提示:最大查询时间跨度为31天哦~";

    public static String getStaffIdByMobile(String mobile) {
        return util.jedisCommands.get(CODE_KEY + mobile);
    }


    @PostConstruct
    public void init() {
        util = this;
        util.jedisCommands = this.jedisCommands;
        util.crmOrgService = this.crmOrgService;
        util.crmOrgStaffService = this.crmOrgStaffService;
        util.opDictInfoMapper = this.opDictInfoMapper;
    }

    /**
     * 特别说明：由于网关鉴权，只有经过fat的网关，网关才会将手机号set进去，因此如果本地测试是获取不到手机号的
     * @param
     * <AUTHOR>
     * @Return {@link String}
     * @DATE 2020/11/6
     */
    public static String getStaffMobile(){
        String staffMobile = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
                .getHeader("X-Mobile");
        if (StringUtils.isEmpty(staffMobile)){
            log.error("当前手机号为空!");
            throw new FastRuntimeException("当前手机号获取异常");
        }
        return staffMobile;
    }


    /**
     * 从请求头中获取x-user-id
     * @return
     */
    public static String getXUserId(){
        return getHttpHeader("x-user-id");
    }

    /**
     * 从请求头中获取x-user-token
     * @return
     */
    public static String getXUserToken(){
        return getHttpHeader("x-user-token");
    }

    /**
     * 根据字段名，获取请求头中的字段
     * @return
     */
    public static String getHttpHeader(String headerName){
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
                .getHeader(headerName);
    }

    /**
     * 从redis获取当前登录用户员工的loginName
     *
     * @return
     */
    public static String getCurrentlogged() {
        String staffMobile = getStaffMobile();
        String staffCode = util.jedisCommands.get(CODE_KEY + staffMobile);
        log.info("staffMobile:{},staffCode:{}",SecurityUtil.maskMobile(staffMobile),staffCode);
        if (StringUtils.isEmpty(staffCode)) {
            log.warn("员工为空！");
            throw new FastRuntimeException("获取当前员工异常，系统无此员工信息！");
        }
        return staffCode;
    }

    /**
     * 从redis获取当前登录用户员工staffId
     *
     * @return
     */
    public static Long getCurrentloggedStaffId() {
        String staffMobile = getStaffMobile();
        String staffId = util.jedisCommands.get(STAFF_ID_KEY + staffMobile);
        log.info("staffMobile:{},staffId:{}",SecurityUtil.maskMobile(staffMobile),staffId);
        if (StringUtils.isEmpty(staffId)) {
            log.warn("员工为空！");
            throw new FastRuntimeException("获取当前员工异常，系统无此员工信息！");
        }
        return Long.valueOf(staffId);
    }

    /**
     * 从redis获取当前登录用户员工组code
     *
     * @return
     */
    public static String getCurrentloggedOrg() {
        String staffMobile = getStaffMobile();
        String staffOrg = util.jedisCommands.get(GROUP_CODE_KEY + staffMobile);
        log.info("staffMobile:{},staffOrg:{}",SecurityUtil.maskMobile(staffMobile),staffOrg);
        if (StringUtils.isEmpty(staffOrg)) {
            log.error("员工组别为空！");
            throw new FastRuntimeException("获取当前员工异常，系统无此员工信息！");
        }
        return util.jedisCommands.get(GROUP_CODE_KEY + staffMobile);
    }

    /**
     * 从redis获取当前登录用户员工组名称
     *
     * @return
     */
    public static String getCurrentloggedOrgName() {
        String staffMobile = getStaffMobile();
        String staffOrg = util.jedisCommands.get(GROUP_NAME_KEY + staffMobile);
        log.info("staffMobile:{},staffOrg:{}",SecurityUtil.maskMobile(staffMobile),staffOrg);
        if (StringUtils.isEmpty(staffOrg)) {
            log.error("员工组别为空！");
            throw new FastRuntimeException("获取当前员工异常，系统无此员工信息！");
        }
        return util.jedisCommands.get(GROUP_NAME_KEY + staffMobile);
    }


    /**
     * 获取当前登录用户管辖组list（string格式）
     *
     * @return
     */
    public static String getManagerOrgCodeList() {
        String staffMobile = getStaffMobile();
        return util.jedisCommands.get(MANAGER_CODES + staffMobile);
    }


    /**
     * 获取当前登录用户管辖组string[]
     *
     * @return
     */
    public static String[] getManagerOrgList() {
        if (getManagerOrgCodeList() != null) {
            String[] codes = getManagerOrgCodeList().replace("[", "").replace("]", "").replace(" ", "").split(",");
            return codes;
        }
        return null;
    }

    /**
     * 获取当前登录员工名字staffName
     *
     * @return
     */
    public static String getCurrentloggedName() {
        String staffMobile = getStaffMobile();
        String staffName = util.jedisCommands.get(NAME_KEY + staffMobile);
        log.info("staffMobile:{},staffName:{}",SecurityUtil.maskMobile(staffMobile),staffName);
        if (StringUtils.isEmpty(staffName)) {
            log.error("员工姓名为空！");
            throw new FastRuntimeException("获取当前员工异常，系统无此员工信息！");
        }
        return staffName;
    }


    /**
     * 保存当前用户信息到redis
     */
    public static void setLogInfo() {
        String staffMobile = getStaffMobile();
        int expireTime = 43200;
        log.info("CommonUtils setLogInfo staffMobile:{}",SecurityUtil.maskMobile(staffMobile));
        ColStaffResVO currentUser = util.crmOrgStaffService.getColStaff(staffMobile);
        util.jedisCommands.set(CODE_KEY + staffMobile, currentUser.getLoginName());
        util.jedisCommands.expire(CODE_KEY + staffMobile,expireTime);
        util.jedisCommands.set(GROUP_CODE_KEY + staffMobile, currentUser.getGroupCode());
        util.jedisCommands.expire(GROUP_CODE_KEY + staffMobile,expireTime);
        util.jedisCommands.set(NAME_KEY + staffMobile, currentUser.getStaffName());
        util.jedisCommands.expire(NAME_KEY + staffMobile,expireTime);
        util.jedisCommands.set(STAFF_ID_KEY + staffMobile, currentUser.getId().toString());
        util.jedisCommands.expire(STAFF_ID_KEY + staffMobile, expireTime);
        util.jedisCommands.set(GROUP_NAME_KEY + staffMobile, currentUser.getGroupName());
        util.jedisCommands.expire(GROUP_NAME_KEY + staffMobile, expireTime);

//        util.jedisCommands.set(IS_MANAGER + staffMobile, currentUser.getIsManager());
//        util.jedisCommands.expire(IS_MANAGER + staffMobile,expireTime);
//        // staff配置为管理员才获取管辖组
//        if (isManager()) {
//            util.jedisCommands.set(MANAGER_CODES + staffMobile,
//                    util.crmOrgService.getManagerOrgList(currentUser.getGroupCode().toString()));
//            util.jedisCommands.expire(MANAGER_CODES + staffMobile,expireTime);
//        }
    }

    /**
     * 从redis获取当前登录用户是否组长
     *
     * @return
     */
    public static boolean isManager() {
        String staffMobile = getStaffMobile();
        String result = util.jedisCommands.get(IS_MANAGER + staffMobile);
        return "true".equals(result);
    }

    /**
     * 正则表达式去处字符串中的所有空格
     *
     * @param s
     * @return
     */
    public static String replaceBlank(String s) {
        String result = null;
        if (s == null) {
            return result;
        } else {
            Pattern p = Pattern.compile("\\s+");
            Matcher m = p.matcher(s);
            result = m.replaceAll("");
            return result;
        }
    }

    public static void main(String[] args) {
        String s = replaceBlank("a     a     b" +
                "fdsfdsfd");
        System.out.println("s = " + s);
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }


    public static List<String> getUrl(String urls) {
        if (StringUtils.isNotEmpty(urls)) {
            String[] codes = urls.replace("[", "").replace("]", "").replace(" ", "").split(",");
            List<String> voList = new ArrayList<>();
            for (String name : codes) {
                voList.add(name);
            }
            return voList;
        }
        return null;
    }

    public static void checkTimeThan31(String startTime,String endTime){
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            throw new CrmOperateException("查询时间不能为空");
        }
        if (DateUtil.getDaysBetween(startTime, endTime) >= MONTH_DAY) {
            throw new CrmOperateException(TIME_EXCEPTION_MESSAGE);
        }
    }

    public static String getMapNameByTaskId(String taskId){
        if (StringUtils.isBlank(taskId)){
            return null;
        }

        if (taskId.startsWith(TmkTaskTypeConstant.LOAN)){
            return "进件模式";
        } else if (taskId.startsWith(TmkTaskTypeConstant.CREDIT)){
            return "额度模式";
        } else if (taskId.startsWith(TmkTaskTypeConstant.CJHY)){
            return "超级会员";
        } else if (taskId.startsWith(TmkTaskTypeConstant.WALLET)){
            return "钱包";
        } else if (taskId.startsWith(TmkTaskTypeConstant.UUID)){
            return "uuid";
        }
        return "";

    }

    /**
     * 根据 category 和 type 查询字典
     * category 不能为空，type 可以为空
     * @param category
     * @param type
     * @return
     */
    public static List<OpDictInfo> getDict(String category, String type) {
        if (StringUtils.isBlank(category)){
            return null;
        }
        return util.opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category)
                .eq(StringUtils.isNotBlank(type), OpDictInfo::getType, type).eq(OpDictInfo::getStatus, 1));
    }

    public static List<OpDictInfo> getAllDict(String category, String type) {
        if (StringUtils.isBlank(category)){
            return null;
        }
        return util.opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category)
                .eq(StringUtils.isNotBlank(type), OpDictInfo::getType, type));
    }

    /**
     * 获取联系结果map，key 为type(英文名，success,failed,uncontact,noNeedContact)
     * value 为中文名
     * @return
     */
    public static Map<String,String> getContactResultMap(){
        List<OpDictInfo> resultList = getDict("contactResult", null);
        Map<String, String> map = new HashMap<>();
        for (OpDictInfo opDictInfo : resultList) {
            map.put(opDictInfo.getType(),opDictInfo.getContent());
        }
        return map;
    }

    /**
     * 使用easyExcel导出文件的合并做法
     */
    public static <T> void mergeExport(HttpServletResponse response, String fileName, Class<T> t, List<T> dataList,
                                List<CellRangeAddress> mergeList) throws IOException {
        CommonUtil.setResponse(response, fileName);
        // 需要做表格的合并操作
        EasyExcel.write(new BufferedOutputStream(response.getOutputStream()), t)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.TRUE)
                .registerWriteHandler(new ExcelMergeStrategy(mergeList))
                .sheet(fileName)
                .doWrite(dataList);
    }

    /**
     * 使用easyExcel导出文件的通用做法
     */
    public static <T> void commonExport(HttpServletResponse response, String fileName, Class<T> t,
                                 List<T> dataList) throws IOException {
        CommonUtil.setResponse(response, fileName);
        // 直接导出的按如下操作
        EasyExcel.write(new BufferedOutputStream(response.getOutputStream()), t)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.TRUE)
                .sheet(fileName)
                .doWrite(dataList);
    }


    /**
     * 获取字典map
     * @param category 不能为空
     * @param type 可以为空
     * @return
     */
    public static Map<String,String> getDictTypeContentMap(String category,String type){
        if (StringUtils.isBlank(category)){
            throw new FastRuntimeException("category 不能为空");
        }
        List<OpDictInfo> resultList = getDict(category, type);
        if (CollectionUtils.isEmpty(resultList)){
            return Collections.emptyMap();
        }
        Map<String, String> map = new HashMap<>();
        
        for (OpDictInfo opDictInfo : resultList) {
            map.put(opDictInfo.getType(),opDictInfo.getContent());
        }
        return map;
    }
}
