package com.welab.crm.operate.service;

import com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO;
import com.welab.crm.operate.dto.report.WoReportDTO;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;

import java.util.List;

/**
 * 电销报表服务
 * <AUTHOR>
 * @version v1.0
 * @date 2022/03/07
 */
public interface TmkReportService {

    /**
     * 查询中央监控再分配数据外呼效能
     */
    List<RedistributedOutboundEfficiencyVO> getReassignOutboundEfficiency(ReassignOutboundEfficiencyDTO dto);

    /**
     * 公共校验时间方法,主要是查询或者导入的日期不能超过3个月
     */
    void validTime(String startTime, String endTime);
}
