package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.constant.BusiConstant;
import com.welab.crm.interview.dto.repay.RepaymentDTO;
import com.welab.crm.interview.dto.wechat.WalletDueDTO;
import com.welab.crm.interview.dto.wechat.WalletLoanDTO;
import com.welab.crm.interview.enums.PayCodeEnum;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.WalletService;
import com.welab.crm.interview.vo.loan.RepaymentPlanVO;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.InLenderWithholdRecord;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.withhold.RepaymentExtDTO;
import com.welab.crm.operate.dto.withhold.WithholdReportVO;
import com.welab.crm.operate.dto.withhold.WithholdReqDTO;
import com.welab.crm.operate.enums.WithholdResultEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.ConPhoneSummaryMapper;
import com.welab.crm.operate.mapper.InLenderWithholdRecordMapper;
import com.welab.crm.operate.service.WithholdService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.withhold.WithholdVO;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.repayment.dto.RepayChannelReq;
import com.welab.finance.repayment.enums.ChannelTypeEnum;
import com.welab.finance.repayment.enums.RepaymentModeEnum;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.welab.finance.repayment.vo.RepayChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description: 代扣服务实现类
 * @date 2022/4/19 17:51
 */
@Service
@Slf4j
public class WithholdServiceImpl implements WithholdService {

    @Resource
    private FinanceService financeService;

    @Resource
    private InLenderWithholdRecordMapper inLenderWithholdRecordMapper;

    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource
    private ConPhoneSummaryMapper conPhoneSummaryMapper;

    @Resource
    private WalletService walletService;
    
    
    private static final String REPAY_ORIGIN = "kefu";


    @Override
    public Page<WithholdVO> queryWithholdRecord(WithholdReqDTO dto) {
        if (StringUtils.isNotBlank(dto.getGroupCode())) {
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getStaffId())) {
            String[] staffIds = dto.getStaffId().split(",");
            List<Long> staffIdList = new ArrayList<>();
            for (String staffId : staffIds) {
                staffIdList.add(Long.valueOf(staffId));
            }
            dto.setStaffIdList(staffIdList);
        }
        Page<WithholdVO> page = new Page<>(dto.getCurPage(), dto.getPageSize());

        Page<WithholdVO> voPage = inLenderWithholdRecordMapper.queryWithholdRecordByCondition(page, dto);

        // 查询离代扣申请时间最近的一通相同客服相同员工的通话时间
        if (Objects.nonNull(voPage)) {
            List<WithholdVO> records = voPage.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                for (WithholdVO withholdVO : records) {
                    List<PhoneSummaryVO> phoneSummaryVOList = conPhoneSummaryMapper
                            .selectCallInSummaryByCustomerIdAndStaffIdAndTime(withholdVO.getCustomerId(),
                                    withholdVO.getStaffId(), withholdVO.getApplyTime());
                    if (CollectionUtils.isNotEmpty(phoneSummaryVOList)) {
                        withholdVO.setCallTime(phoneSummaryVOList.get(0).getCallTime());
                    }

                    // 翻译部分字段
                    withholdVO.setRepaymentMode(RepaymentModeEnum.getDisplayByCode(withholdVO.getRepaymentMode()));
                    withholdVO.setResult(WithholdResultEnum.getValueByCode(withholdVO.getResult()));
                    withholdVO.setRepayOrigin(PayCodeEnum.getNameByChannel(withholdVO.getRepayOrigin()));
                }
            }
        }

        return voPage;
    }

    @Override
    public Response<RepaymentVO> repayment(RepaymentExtDTO repaymentDTO) {
        Response<RepaymentVO> response = new Response<>();

        try {
            repaymentDTO.setRepayOrigin(REPAY_ORIGIN);
            repaymentDTO.setServiceNo(CommonUtil.getRandomFileName("KF", 11));
            RepaymentDTO dto = new RepaymentDTO();
            BeanUtils.copyProperties(repaymentDTO, dto);
            if (repaymentDTO.getApplicationId().startsWith(BusiConstant.WL)){
                response = walletService.repayment(dto);
            } else {
                response = financeService.repayment(dto);
            }
        } catch (Exception e) {
            log.error("repayment 代扣异常", e);
            response.setCode(ResponsCodeTypeEnum.FAILURE.getCode());
            response.setMessage("代扣异常");
            return response;
        }

        // 保存代扣记录
        InLenderWithholdRecord record = new InLenderWithholdRecord();
        record.setServiceNo(repaymentDTO.getServiceNo());
        record.setApplicationId(repaymentDTO.getApplicationId());
        record.setUserId(repaymentDTO.getCustomerId());
        record.setStaffId(CommonUtils.getCurrentloggedStaffId());
        record.setRepayType(repaymentDTO.getRepaymentMode());
        record.setAmount(new BigDecimal(repaymentDTO.getAmount()));
        record.setConsultationStatus(repaymentDTO.getConsultationStatus());
        if (Response.isSuccess(response)) {
            record.setWithholdResult("success");
            record.setCallbackResult("processing");
        } else {
            record.setWithholdResult("fail");
            record.setCallbackResult("fail");
            record.setFailReason(response.getMessage());
        }

        inLenderWithholdRecordMapper.insert(record);
        return response;
    }


    @Override
    public BigDecimal getRepayAmountByMode(WithholdReqDTO dto) {
        //获取还款计划
        List<RepaymentPlanVO> repayPlan = new ArrayList<>();
        if (dto.getApplicationId().startsWith(BusiConstant.WL)){
            // 钱包分期还款计划
            WalletLoanDTO walletLoan = walletService.getWalletRepayPlan(dto.getApplicationId());
            if (Objects.nonNull(walletLoan)) {
                List<WalletDueDTO> walletRepayPlan = walletLoan.getDues();
                for (WalletDueDTO walletDueDTO : walletRepayPlan) {
                    RepaymentPlanVO planVO = new RepaymentPlanVO();
                    BeanUtils.copyProperties(walletDueDTO, planVO);
                    planVO.setDueDate(DateUtils.parseDate(walletDueDTO.getDueDate(), DateUtils.DATE_FORMAT));
                    repayPlan.add(planVO);
                }
            }
        } else {
            // 现金贷还款计划
            repayPlan = loanApplicationService.getRepaymentPlan(dto.getApplicationId());
        }
        Date now = new Date();
        // 按期还款则查询出最早一期逾期金额
        if ("YD".equals(dto.getRepaymentMode())) {
            Optional<RepaymentPlanVO> voOptional = repayPlan.stream()
                    .filter(item -> !item.getSumDueAmount().equals(item.getSumStillAmount())).min(Comparator.comparingInt(RepaymentPlanVO::getIndex));
            if (voOptional.isPresent()) {
                RepaymentPlanVO vo = voOptional.get();
                return vo.getSumDueAmount().subtract(vo.getSumStillAmount());
            } else {
                throw new CrmOperateException(dto.getApplicationId() + " 无还款数据");
            }
            // YH 则查询全部逾期数据
        } else if ("YH".equals(dto.getRepaymentMode())) {
            List<RepaymentPlanVO> overDueVO = repayPlan.stream()
                    .filter(item -> !item.getSumDueAmount().equals(item.getSumStillAmount()) && item.getDueDate()
                            .before(now)).collect(
                            Collectors.toList());
            BigDecimal totalDueAmount = new BigDecimal("0");
            BigDecimal totalStillAmount = new BigDecimal("0");
            for (RepaymentPlanVO repaymentPlanVO : overDueVO) {
                totalDueAmount = totalDueAmount.add(repaymentPlanVO.getSumDueAmount());
                totalStillAmount = totalStillAmount.add(repaymentPlanVO.getSumStillAmount());
            }
            return totalDueAmount.subtract(totalStillAmount);
        }
        throw new FastRuntimeException("还款模式异常，暂时只支持 按期还款:YD;还完全部逾期:YH");
    }

    @Override
    public List<String> queryRepaymentModeByAppNo(String appNo) {
        Response<List<String>> repayMode = financeService.getRepayMode(appNo, REPAY_ORIGIN);
        if (Response.isSuccess(repayMode)){
            return repayMode.getResult();
        } else {
            throw new FastRuntimeException(repayMode.getMessage());
        }
    }

    @Override
    public RepayChannelVO getRepayChannel(RepayChannelReq repayChannelReq) {
        repayChannelReq.setRepayOrigin(REPAY_ORIGIN);
        Response<RepayChannelVO> repayChannel = financeService.getRepayChannel(repayChannelReq);
        if (Response.isSuccess(repayChannel)){
            return repayChannel.getResult();
        } else {
            throw new FastRuntimeException(repayChannel.getMessage());
        }
    }

    @Override
    public List<WithholdReportVO> queryWithholdReport(ReportBaseDTO dto) {

        switch (dto.getPeriod()) {
            case Constant.DAY:
                dto.setPeriod("date_format(t0.gmt_create ,'%Y-%m-%d')");
                break;
            case Constant.WEEK:
                dto.setPeriod(
                        "CONCAT(DATE_SUB(date_format(t0.gmt_create,'%Y-%m-%d'),INTERVAL WEEKDAY(date_format(t0.gmt_create,'%Y-%m-%d')) DAY) ,'-' ,DATE_SUB(date_format(t0.gmt_create,'%Y-%m-%d'),INTERVAL WEEKDAY(date_format(t0.gmt_create,'%Y-%m-%d'))-6 DAY))");
                break;
            case Constant.MONTH:
                dto.setPeriod("date_format(t0.gmt_create ,'%Y-%m')");
                break;
            default:
                dto.setPeriod("");
                break;
        }
        
        
        

        List<WithholdReportVO> voList = inLenderWithholdRecordMapper.queryWithholdReport(dto);
        for (WithholdReportVO vo : voList) {
            vo.setSuccessRate(CommonUtil.calcPercentage(vo.getSuccessCount(), vo.getOrderCount()));
            vo.setFailRate(CommonUtil.calcPercentage(vo.getFailCount(), vo.getOrderCount()));
        }

        return voList;
    }
}
