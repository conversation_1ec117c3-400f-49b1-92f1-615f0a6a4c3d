package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.domain.OpStaffStatusHis;
import com.welab.crm.operate.domain.StaffStatusStartTime;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.dto.staff.ReportStaffStatusDTO;
import com.welab.crm.operate.mapper.OpStaffStatusHisMapper;
import com.welab.crm.operate.mapper.StaffStatusStartTimeMapper;
import com.welab.crm.operate.service.StaffStatusService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2021/11/20 11:37
 */
@Service
public class StaffStatusServiceImpl implements StaffStatusService {

    @Resource
    private OpStaffStatusHisMapper staffStatusHisMapper;

    @Resource
    private StaffStatusStartTimeMapper staffStatusStartTimeMapper;

    @Resource
    private StaffService staffService;

    @Resource
    private CrmOrgStaffServiceImpl crmOrgStaffService;

    @Override
    public void reportStatusMsg(ReportStaffStatusDTO dto) {
        OpStaffStatusHis staffStatusHis = new OpStaffStatusHis();
        staffStatusHis.setStatus(dto.getStatus());
        ColStaffResVO staff = null;
        if (Objects.nonNull(dto.getMobile())){
            ColStaffReqDTO staffReqDTO = new ColStaffReqDTO();
            staffReqDTO.setStaffMobile(dto.getMobile());
            List<ColStaffResVO> colStaffList = crmOrgStaffService.getColStaffList(staffReqDTO);
            if (CollectionUtils.isNotEmpty(colStaffList)){
                staff = colStaffList.get(0);
            }
        }
        if (Objects.nonNull(staff)) {
            staffStatusHis.setStaffId(staff.getLoginName());
        } else {
            staffStatusHis.setStaffId(CommonUtils.getCurrentlogged());
        }
        if (StringUtils.isNotBlank(dto.getStartTime())) {
            staffStatusHis.setStartTime(DateUtil.stringToDate(dto.getStartTime()));
        }
        if (StringUtils.isNotBlank(dto.getEndTime())) {
            staffStatusHis.setEndTime(DateUtil.stringToDate(dto.getEndTime()));
        }
        staffStatusHis.setMobile(dto.getMobile());
        staffStatusHisMapper.insert(staffStatusHis);

    }

    @Override
    public void submitStatusStartTime(String status, String mobile) {
        StaffStatusStartTime staffStatusStartTime = new StaffStatusStartTime();
        staffStatusStartTime.setStatus(status);
        StaffVO staff = null;
        if (Objects.nonNull(mobile)) {
            staff = staffService.getStaffByMobile(mobile);
        }
        if (Objects.nonNull(staff)) {
            staffStatusStartTime.setStaffId(staff.getId());
        }
        staffStatusStartTime.setStartTime(new Date());
        staffStatusStartTimeMapper.insert(staffStatusStartTime);

    }

    @Override
    public OpStaffStatusHis getLastStatusRecord(String mobile, String status) {
        List<OpStaffStatusHis> opStaffStatusHis = getNotEndRecord(mobile, status);
        if (CollectionUtils.isNotEmpty(opStaffStatusHis)) {
            return opStaffStatusHis.get(0);
        } else {
            return null;
        }
    }


    @Override
    public List<OpStaffStatusHis> getNotEndRecord(String mobile, String status) {
        return staffStatusHisMapper.selectList(
                Wrappers.lambdaQuery(OpStaffStatusHis.class)
                        .eq(StringUtils.isNotBlank(mobile), OpStaffStatusHis::getMobile, mobile)
                        .eq(StringUtils.isNotBlank(status), OpStaffStatusHis::getStatus, status)
                        .isNull(OpStaffStatusHis::getEndTime).lt(OpStaffStatusHis::getStartTime, new Date())
                        .orderByDesc(OpStaffStatusHis::getStartTime));
    }

    @Override
    public void updateStaffStatus(OpStaffStatusHis opStaffStatusHis) {
        staffStatusHisMapper.updateById(opStaffStatusHis);
    }


    @Override
    public void updateAllStatus(String mobile) {
        List<OpStaffStatusHis> notEndRecordList = getNotEndRecord(mobile, null);
        for (OpStaffStatusHis statusHis : notEndRecordList) {
            Date now = new Date();
            statusHis.setEndTime(now);
            statusHis.setGmtModify(now);
            statusHis.setDuration(
                    (long) Seconds.secondsBetween(new DateTime(statusHis.getStartTime()), new DateTime(now))
                            .getSeconds());
            updateStaffStatus(statusHis);
        }
    }
}
