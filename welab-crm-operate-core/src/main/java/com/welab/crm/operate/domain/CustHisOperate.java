package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 操作历史表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2021-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cust_his_operate")
public class CustHisOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 操作人
     */
    private String staffId;

    /**
     * 操作人所在组
     */
    private String groupCode;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 贷款号(部分针对贷款的操作才有，比如结清)
     */
    private String loanId;

    /**
     * 修改手机号前的手机号
     */
    private String oldMobile;


    /**
     * 客服操作时所填的备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 客服系统客户Id
     */
    private Long customerId;




}
