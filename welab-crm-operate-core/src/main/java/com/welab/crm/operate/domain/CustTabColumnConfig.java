package com.welab.crm.operate.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 选项卡配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cust_tab_column_config")
public class CustTabColumnConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 表格名称
     */
    private String tabName;

    /**
     * 表格不展示的列名
     */
    private String tabColumn;

    /**
     * 员工id，关联sys_auth_crm_staff表
     */
    private String staffId;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
