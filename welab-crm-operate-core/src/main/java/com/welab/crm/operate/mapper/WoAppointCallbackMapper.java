package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.WoAppointCallback;

/**
 * <p>
 * 工单预约回电表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
public interface WoAppointCallbackMapper extends BaseMapper<WoAppointCallback> {
	WoAppointCallback queryAppointCallback(@Param("reqDTO") WoAppointCallback reqDTO);
}
