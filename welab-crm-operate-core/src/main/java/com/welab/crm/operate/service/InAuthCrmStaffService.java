package com.welab.crm.operate.service;

import com.welab.crm.operate.domain.InAuthCrmStaff;

import java.util.Map;
import java.util.Set;

/**
 * 员工查询基础服务
 *
 * <AUTHOR>
 * @date 2022/2/17 14:30
 */
public interface InAuthCrmStaffService {

    /**
     * 获取员工login_name为key值，staff name为value的map
     */
    Map<String, String> getStaffNameMap(Set<String> loginNames);


    /**
     * 获取全部员工login_name为key值，groupName为value
     */
    Map<String, String> getGroupNameMap();


    /**
     * 获取 id:staff信息 map
     */
    Map<Long, InAuthCrmStaff> getStaffIdMap();

    /**
     * 根据手机号查询员工信息
     * @param mobile
     * @return
     */
    InAuthCrmStaff getStaffByMobile(String mobile);
}
