package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 敏感工单配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sensitive_workorder_config")
public class SensitiveWorkorderConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeId;

    /**
     * 工单大类描述
     */
    private String woTypeDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeFirId;

    /**
     * 工单一类描述
     */
    private String woTypeFirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeSecId;

    /**
     * 工单二类描述
     */
    private String woTypeSecDetail;

    /**
     * 字典id，即dict_info表主键
     */
    private Long woTypeThirId;

    /**
     * 工单三类描述
     */
    private String woTypeThirDetail;

    /**
     * 状态；1-有效，2-无效
     */
    private Integer isStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 资金方，多个用逗号隔开
     */
    private String partnerNames;

    /**
     * 创建人
     */
    private String createStaff;

    /**
     * 备注
     */
    private String remark;


    /**
     * 跟进时效，单位：小时
     */
    private Integer followUpTime;

    /**
     * 提醒方式，robot:机器人，system:系统，多个用逗号隔开
     */
    private String warnType;


}
