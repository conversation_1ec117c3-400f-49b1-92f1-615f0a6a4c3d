package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.interview.enums.ReduceAndRefundApprovalStatusEnum;
import com.welab.crm.operate.constant.ParamConstant;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.notice.ToolsNoticeDTO;
import com.welab.crm.operate.dto.reduce.RecordQueryDTO;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.service.ToolsService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/12/27
 */
@Service
@Slf4j
public class ToolsServiceImpl implements ToolsService {

    @Resource
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private BlackProductionUserInfoMapper blackProductionUserInfoMapper;

    @Resource
    private CsCallOutBlackListMapper csCallOutBlackListMapper;

    @Resource
    private StopCollectionListMapper stopCollectionListMapper;

    @Resource
    private OpReduceApplyRecordMapper opReduceApplyRecordMapper;

    @Override
    public List<ToolsNoticeDTO> menuTips() {
        List<ToolsNoticeDTO> result = new ArrayList<>();
        //黑产名单审批人
        List<OpDictInfo> blackDictList = CommonUtils.getDict("black-production", null);
        if (CollectionUtils.isEmpty(blackDictList)) {
            return null;
        }
        Map<String, List<String>> blackMap = new HashMap<>();
        for (OpDictInfo info : blackDictList) {
            blackMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> blackProduction = blackMap.get("1");
        ToolsNoticeDTO black = new ToolsNoticeDTO();
        black.setTipsType("black-production");
        black.setCnt(blackProductionUserInfoMapper.selectCount(Wrappers.lambdaQuery(BlackProductionUserInfo.class).eq(BlackProductionUserInfo::getApprovalStatus, CallOutBlackListServiceImpl.ApprovalStatusEnum.APPLY.getCode())));
        black.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, blackProduction)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(black);
        //外呼黑名单审批人
        List<OpDictInfo> callOutDictList = CommonUtils.getDict("callout-blacklist", null);
        if (CollectionUtils.isEmpty(callOutDictList)) {
            return null;
        }
        Map<String, List<String>> callOutMap = new HashMap<>();
        for (OpDictInfo info : callOutDictList) {
            callOutMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> callOutApproval = callOutMap.get("1");
        ToolsNoticeDTO callOut = new ToolsNoticeDTO();
        callOut.setTipsType("callout-blacklist");
        callOut.setCnt(csCallOutBlackListMapper.selectCount(Wrappers.lambdaQuery(CsCallOutBlackList.class).eq(CsCallOutBlackList::getApprovalStatus, CallOutBlackListServiceImpl.ApprovalStatusEnum.APPLY.getCode())));
        callOut.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, callOutApproval)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(callOut);
        //黑名单审批人
        ToolsNoticeDTO blackList = new ToolsNoticeDTO();
        String[] blackApproval = opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "approval_staff").eq(OpDictInfo::getType, "approval_staff_one").eq(OpDictInfo::getStatus, true)).getDetail().split(",");
        blackList.setTipsType("black-list_1");
        blackList.setCnt(stopCollectionListMapper.selectCount(Wrappers.lambdaQuery(StopCollectionList.class).eq(StopCollectionList::getOperateState, ParamConstant.ZERO)));
        blackList.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, blackApproval)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(blackList);
        //黑名单二审
        ToolsNoticeDTO blackList2 = new ToolsNoticeDTO();
        String[] blackApproval2 = opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "approval_staff").eq(OpDictInfo::getType, "approval_staff_two").eq(OpDictInfo::getStatus, true)).getDetail().split(",");
        blackList2.setTipsType("black-list_2");
        blackList2.setCnt(stopCollectionListMapper.selectCount(Wrappers.lambdaQuery(StopCollectionList.class).eq(StopCollectionList::getOperateState, ParamConstant.FIRST)));
        blackList2.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, blackApproval2)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(blackList2);
        //减免审批人
        List<OpDictInfo> dictList = CommonUtils.getDict("reduce_approval_staff", null);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dictList)) {
            return null;
        }
        Map<String, List<String>> approverMap = new HashMap<>();
        for (OpDictInfo info : dictList) {
            approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> reduceApproval1 = approverMap.get("1");
        ToolsNoticeDTO reduce = new ToolsNoticeDTO();
        reduce.setTipsType("reduce_1");
        RecordQueryDTO query = new RecordQueryDTO();
        query.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.APPLY.getCode());
        List<ReduceAndRefundVO> reduceAndRefundVOS = opReduceApplyRecordMapper.selectApplyRecordPage(query);
        reduce.setCnt(CollectionUtils.isNotEmpty(reduceAndRefundVOS) ? reduceAndRefundVOS.size() : 0);
        reduce.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, reduceApproval1)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(reduce);
        //减免二审
        List<String> secondApproval2 = approverMap.get("2");
        ToolsNoticeDTO reduce2 = new ToolsNoticeDTO();
        reduce2.setTipsType("reduce_2");
        RecordQueryDTO query2 = new RecordQueryDTO();
        query2.setApprovalStatus(ReduceAndRefundApprovalStatusEnum.FIRST_APPROVAL.getCode());
        List<ReduceAndRefundVO> reduceAndRefundVOS2 = opReduceApplyRecordMapper.selectApplyRecordPage(query2);
        reduce2.setCnt(CollectionUtils.isNotEmpty(reduceAndRefundVOS2) ? reduceAndRefundVOS2.size() : 0);
        reduce2.setMobile(inAuthCrmStaffMapper.selectList(Wrappers.lambdaQuery(InAuthCrmStaff.class).in(InAuthCrmStaff::getLoginName, secondApproval2)).stream().map(InAuthCrmStaff::getStaffMobile).collect(Collectors.toList()));
        result.add(reduce2);
        return result;
    }
}
