package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.enums.PeriodEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.operate.mapper.ConRepeatCallMapper;
import com.welab.crm.operate.service.StatsDupCallService;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.woReport.ReportDupCallDetailVO;
import com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StatsDupCallServiceImpl implements StatsDupCallService {

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private ConPhoneCallInfoMapper phoneMapper;

    @Resource
    private ConRepeatCallMapper repeatCallMapper;

    @Override
    public Page<ReportDupCallTotalVO> listTotalByPage(ReportDupCallDTO callDTO) {
        checkPeriod(callDTO);
        checkTime(callDTO);
        if (PeriodEnum.DAY.getValue().equals(callDTO.getPeriod())) {
            // 统计按照天的维度数据
            return getDayTotalData(callDTO);
        } else {
            // 统计周期内(指的是一个时间段)的数据
            return getPeriodTotalData(callDTO);
        }
    }

    @Override
    public List<ReportDupCallTotalVO> listTotal(ReportDupCallDTO callDTO) {
        callDTO.setCurrentPage(1);
        callDTO.setRowsPerPage(1000000);
        Page<ReportDupCallTotalVO> totalByPage = listTotalByPage(callDTO);
        return totalByPage.getRecords();
    }

    @Override
    public Page<ReportDupCallDetailVO> listDetailByPage(ReportDupCallDTO callDTO) {
        checkTime(callDTO);
        Date start = parseToDate(callDTO.getStartTime());
        Date end = parseToDate(callDTO.getEndTime());
        // 按照时间范围和热线号码查询
        Page<ReportDupCallDetailVO> page = new Page<ReportDupCallDetailVO>().setCurrent(callDTO.getCurrentPage())
                .setSize(callDTO.getRowsPerPage());
        Page<ReportDupCallDetailVO> detailPage = phoneMapper.selectDetailByPage(page, start, end, callDTO.getHotline(),
                callDTO.getStaffId());
        convertToResultPage(detailPage.getRecords());
        return detailPage;
    }

    @Override
    public List<ReportDupCallDetailVO> listDetail(ReportDupCallDTO callDTO) {
        callDTO.setCurrentPage(1);
        callDTO.setRowsPerPage(1000000);
        Page<ReportDupCallDetailVO> detailByPage = listDetailByPage(callDTO);
        return detailByPage.getRecords();
    }

    /**
     * 按每一天统计: 形如2022-05-30, 先获取每条热线每一天的数据再拼装2小时重复进线量数据
     */
    private Page<ReportDupCallTotalVO> getDayTotalData(ReportDupCallDTO callDTO) {
        /*
         * 1.查询每条热线每一天的总来电量、真实客户数(总来电量基础上去重)、总转人工服务数(总来电量基础上选出转人工的数据)
         * 转工人真实客户数(总转人工服务数基础上去重)
         */
        Page<ReportDupCallTotalVO> page = new Page<>();
        page.setCurrent(callDTO.getCurrentPage()).setSize(callDTO.getRowsPerPage());
        Page<ReportDupCallTotalVO> dayPage = phoneMapper.countCustomerCallByDay(page, callDTO);
        List<ReportDupCallTotalVO> days = dayPage.getRecords();
        if (days.isEmpty()) {
            return dayPage;
        }

        // 2.查询并计算2小时重复进线量
        List<ReportDupCallTotalVO> dupCalls = repeatCallMapper.selectManDupCalls(callDTO);
        // 将list转换为hotline、日期为key的map便于设置
        Map<String, Map<String, ReportDupCallTotalVO>> dupCallMap = new HashMap<>();
        if (!dupCalls.isEmpty()) {
            Map<String, List<ReportDupCallTotalVO>> tempMap = dupCalls.stream().collect(
                    Collectors.groupingBy(ReportDupCallTotalVO::getHotline));
            for (Map.Entry<String, List<ReportDupCallTotalVO>> entry : tempMap.entrySet()) {
                Map<String, ReportDupCallTotalVO> dayMap = entry.getValue().stream().collect(
                        Collectors.toMap(ReportDupCallTotalVO::getCallDay, v -> v));
                dupCallMap.put(entry.getKey(), dayMap);
            }
        }

        for (ReportDupCallTotalVO day : days) {
            if (dupCallMap.get(day.getHotline()) != null) {
                ReportDupCallTotalVO vo = dupCallMap.get(day.getHotline()).get(day.getCallDay());
                if (vo != null) {
                    day.setDuplicateCalls(vo.getDuplicateCalls());
                } else {
                    day.setDuplicateCalls(0);
                }
            } else {
                day.setDuplicateCalls(0);
            }
        }

        return dayPage;
    }

    /**
     * 按时间范围汇总统计: 时间范围形如2022-01-30~2022-04-30, 查询汇总出范围数据再拼装2小时重复进线量数据
     */
    private Page<ReportDupCallTotalVO> getPeriodTotalData(ReportDupCallDTO callDTO) {
        /*
         * 1.查询每条热线所有天数的总来电量、真实客户数(总来电量基础上去重)、总转人工服务数(总来电量基础上选出转人工的数据)
         * 转工人真实客户数(总转人工服务数基础上去重)
         */
        Page<ReportDupCallTotalVO> page = new Page<>();
        page.setCurrent(callDTO.getCurrentPage()).setSize(callDTO.getRowsPerPage());
        Page<ReportDupCallTotalVO> totalPage = phoneMapper.countCustomerCall(page, callDTO);
        List<ReportDupCallTotalVO> totals = totalPage.getRecords();
        if (totals.isEmpty()) {
            return totalPage;
        }

        // 2.查询并计算2小时重复进线量
        List<ReportDupCallTotalVO> dupCalls = repeatCallMapper.selectManDupCalls(callDTO);
        // 将list转换为hotline为key的map
        Map<String, ReportDupCallTotalVO> dupCallMap;
        if (!dupCalls.isEmpty()) {
            dupCallMap = dupCalls.stream().collect(Collectors.toMap(ReportDupCallTotalVO::getHotline, v -> v));
        } else {
            dupCallMap = new HashMap<>();
        }

        for (ReportDupCallTotalVO total : totals) {
            String hotline = total.getHotline();
            // 生成时间段的字符串,例如2022-04-28~2022-05-28, 如果是天day的展示样式则是2022-05-28
            total.setCallDay(callDTO.getStartTime().split(" ")[0] + "~" + callDTO.getEndTime().split(" ")[0]);
            if (dupCallMap.get(hotline) != null) {
                total.setDuplicateCalls(dupCallMap.get(hotline).getDuplicateCalls());
            } else {
                total.setDuplicateCalls(0);
            }
        }

        return totalPage;
    }

    private void convertToResultPage(List<ReportDupCallDetailVO> records) {
        if (!records.isEmpty()) {
            records.forEach(record -> {
                record.setMobile(SecurityUtil.maskMobile(record.getMobile()));
                record.setCallDay(getDayFromDate(record.getCdrStartTime()));
                // hotLine,uuid,userId,cdrEndBridgeTime,cdrCno,staffName,callComment无需处理
                record.setEnterTime(getTimeFromDate(record.getCdrStartTime()));
                record.setAnswerTime(getTimeFromDate(record.getCdrAnswerTime()));
                record.setJoinQueueTime(getTimeFromDate(record.getCdrJoinQueueTime()));
                record.setEndTime(getTimeFromDate(record.getCdrEndTime()));
                record.setCdrStatus("2".equals(record.getCdrStatus()) ? "座席未接听" : "座席接听");
                record.setVip(record.getIVip() != null && record.getIVip() ? "是" : "否");
                // 设置各个层级的来电小结分类
                if (StringUtils.isNoneBlank(record.getCallSummary())) {
                    String[] levelArray = record.getCallSummary().split("#");
                    record.setSummary1Code(levelArray[0]);
                    if (levelArray.length >= 2) {
                        record.setSummary2Code(levelArray[1]);
                    }
                    if (levelArray.length >= 3) {
                        record.setSummary3Code(levelArray[2]);
                    }
                }
            });
        }
        setSortToRecordVo(records);
    }

    private void setSortToRecordVo(List<ReportDupCallDetailVO> records) {
        if (!records.isEmpty()) {
            Map<String, List<ReportDupCallDetailVO>> rMap = records.stream().collect(Collectors.groupingBy(item ->
                    item.getCallDay() + item.getHotline() + item.getCdrCustomerNumber()));
            for (Map.Entry<String, List<ReportDupCallDetailVO>> entry : rMap.entrySet()) {
                List<ReportDupCallDetailVO> eachCustomerVal = entry.getValue();
                if (StringUtils.isNotBlank(eachCustomerVal.get(0).getCdrCustomerNumber())) {
                    // 按照客户进入系统的时间升序排列，以方便设置客户同一天同一条热线的来电次数
                    eachCustomerVal.sort(Comparator.comparing(ReportDupCallDetailVO::getCdrStartTime));
                    for (int i = 0; i < eachCustomerVal.size(); i++) {
                        eachCustomerVal.get(i).setCounts(i + 1);
                        // 保密数据，统计完来电次数后置空
                        eachCustomerVal.get(i).setCdrCustomerNumber(null);
                    }
                }
            }
        }
    }

    private String getDayFromDate(Date date) {
        if (date != null) {
            ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
            LocalDate localDate = LocalDate.from(zonedDateTime);
            return DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD).format(localDate);
        } else {
            return null;
        }
    }

    private String getTimeFromDate(Date date) {
        if (date != null) {
            ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
            LocalTime localTime = LocalTime.from(zonedDateTime);
            return DateTimeFormatter.ofPattern(DateUtils.HH_MM_SS).format(localTime);
        } else {
            return null;
        }
    }

    private Date parseToDate(String dateParam) {
        LocalDateTime parse = LocalDateTime.parse(dateParam, DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT));
        return Date.from(parse.atZone(ZoneId.systemDefault()).toInstant());
    }

    private void checkPeriod(ReportDupCallDTO callDTO) {
        // 系统支持的时间间隔是天、周、月
        String period = callDTO.getPeriod();
        if (StringUtils.isBlank(period)) {
            // 设置默认查询时间间隔参数为周期
            callDTO.setPeriod(PeriodEnum.RANGE.getValue());
        }
        PeriodEnum periodEnum = PeriodEnum.getValue(period);
        if (periodEnum == null) {
            throw new CrmOperateException("时间间隔参数无法识别");
        }
        // 当时间间隔不是天时，一共返回会是很少的数据，这里直接设置为固定值不采用前端传过来的值
        if (periodEnum != PeriodEnum.DAY) {
            callDTO.setCurrentPage(1);
            callDTO.setRowsPerPage(20);
        }
    }

    private void checkTime(ReportDupCallDTO callDTO) {
        // 查询结束时间和开始时间的最大间隔是3个月
        tmkReportService.validTime(callDTO.getStartTime(), callDTO.getEndTime());
    }
}
