package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.LoanTransferContract;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.vo.loan.LoanContactVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 债转结清关联合同号表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-09
 */
public interface LoanTransferContractMapper extends ExpandBaseMapper<LoanTransferContract> {


    /**
     * 根据transferID分页查询
     * @param page
     * @param transferId
     * @return
     */
    Page<LoanContactVO> selectPageByTransferId(Page<LoanTransferContract> page,@Param("transferId") Long transferId);

	/**
	 * 更新短信状态
	 * @param id transferId
	 * @param applicationIdList 贷款号列表
	 * @param state 状态
	 */
	void updateSmsState(@Param("id") Long id, @Param("list") List<String> applicationIdList, @Param("state") boolean state);
}
