package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.blacklist.BatchDictInfoReqDTO;
import com.welab.collection.interview.dto.blacklist.BlackRecordReqDTO;
import com.welab.collection.interview.dto.blacklist.StopReasonDictDTO;
import com.welab.collection.interview.vo.blacklist.BlackListVO;
import com.welab.collection.interview.vo.blacklist.BlackRecordVO;
import com.welab.crm.interview.vo.LiaisonVo;
import com.welab.crm.operate.dto.blacklist.BlackDetailReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListAddDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryApprovalReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryReqDTO;
import com.welab.crm.operate.vo.blacklist.BlackDetailReportVO;
import com.welab.crm.operate.vo.blacklist.BlackListSummaryReportStrVO;
import com.welab.crm.operate.vo.blacklist.KfBlackListApprovalVO;
import com.welab.crm.operate.vo.blacklist.KfBlackListVO;

import java.util.List;

/**
 * 黑名单服务接口
 */
public interface BlackListService {

    /**
     * 查询黑名单
     * @param dto 请求参数
     * @return {@link BlackListVO}
     */
    Page<KfBlackListVO> queryBlackListPage(BlackListQueryReqDTO dto);

    /**
     * 查询待审批黑名单
     * @param dto
     * @return
     */
    Page<KfBlackListApprovalVO> queryBlackListApprovalPage(BlackListQueryApprovalReqDTO dto);


    /**
     * 查询黑名单明细
     * @param Id
     * @return
     */
    KfBlackListVO queryBlackDetail(Long id);


    /**
     * 新增黑名单
     * @param blackListAddDTO 请求参数
     * @return
     */
    void addBlackList(BlackListAddDTO blackListAddDTO);


    /**
     * 修改黑名单
     * @param blackListAddDTO
     * @return
     */
    void updateBlackList(BlackListAddDTO blackListAddDTO);


    /**
     * 删除黑名单
     * @param reqDTO
     * @return
     */
    boolean deleteBlackList(BatchDictInfoReqDTO reqDTO);


    /**
     * 导入黑名单
     * @param arrCells Excel解析的列表
     */
    void importBlackList(List<List<String>> arrCells);


    /**
     * 根据userId获取联系人信息
     * @param userId userID
     * @return {@link LiaisonVo}
     */
    List<LiaisonVo> getLiaisonByUserId(Integer userId);


    /**
     * 获取操作记录
     * @param dto
     * @return
     */
    Page<BlackRecordVO> getRecord(BlackRecordReqDTO dto);

    /**
     * 同步停催原因字典
     * @param list
     */
    void syncDict(List<StopReasonDictDTO> list);


    /**
     * 黑名单明细报表
     * @param dto
     * @return
     */
    Page<BlackDetailReportVO> queryBlackDetailReport(BlackDetailReqDTO dto);


    /**
     * 查询黑名单统计报表
     * @param dto
     * @return
     */
    List<BlackListSummaryReportStrVO> queryBlackListSummary(BlackDetailReqDTO dto);





}
