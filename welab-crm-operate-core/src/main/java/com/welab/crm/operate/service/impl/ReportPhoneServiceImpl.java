package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.dto.report.ReportPhoneSummaryDTO;
import com.welab.crm.operate.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.operate.service.IReportPhoneService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.phone.ReportPhoneSummaryItem;
import com.welab.crm.operate.vo.phone.ReportPhoneSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/21
 */
@Service
@Slf4j
public class ReportPhoneServiceImpl implements IReportPhoneService {

    @Autowired
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Autowired
    private TrUtil trUtil;

    @Override
    public Page<ReportPhoneVO> queryDetai(ReportPhoneResultDTO dto) {
        try {
            covert(dto);
            Page<ReportPhoneVO> result = conPhoneCallInfoMapper.queryReportDetail(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
            if (Objects.nonNull(result)){
                result.getRecords().forEach(reportPhoneVO -> reportPhoneVO
                    .setCdrCustomerNumber(SecurityUtil.maskMobile(reportPhoneVO.getCdrCustomerNumber())));
            }
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public List<ReportPhoneVO> queryDetailExcel(ReportPhoneResultDTO dto) {
        try {
            covert(dto);
            List<ReportPhoneVO> result = conPhoneCallInfoMapper.queryReportDetail(dto);
            result.forEach(reportPhoneVO -> reportPhoneVO
                    .setCdrCustomerNumber(SecurityUtil.maskMobile(reportPhoneVO.getCdrCustomerNumber())));
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.excel.error.default", e.getMessage());
        }
    }

    @Override
    public ReportPhoneSummaryVO querySummary(ReportPhoneSummaryDTO dto) {
        try {
            String jsonString = trUtil.queryTrunkReportIb(dto);
            ReportPhoneSummaryVO result = JSONObject.parseObject(jsonString, ReportPhoneSummaryVO.class);
            // 重新计算接通率,天润接口返回的这个接通率是用 客服接听数/总来电数，要改为 客服接听数/转人工数
            calcuAnswerRate(result);
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    private void calcuAnswerRate(ReportPhoneSummaryVO result) {
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getList())){
            return;
        }

        for (ReportPhoneSummaryItem item : result.getList()) {
            try {
                item.setIbAnsweredRate(CommonUtil.calcPercentage(Integer.parseInt(item.getIbAnsweredCount()),
                        Integer.parseInt(item.getIbQueueCount())));
            } catch (Exception e){
                log.warn("calcuAnswerRate error", e);
            }

        }

    }

    // 转换dto
    private ReportPhoneResultDTO covert(ReportPhoneResultDTO dto) {
        if (StringUtils.isNotBlank(dto.getCdrHotline())) {
            dto.setCdrHotlines(Arrays.asList(dto.getCdrHotline().split(",")));
        }
        return dto;
    }
}
