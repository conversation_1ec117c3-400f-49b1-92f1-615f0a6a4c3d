package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.dto.report.ReportPhoneSummaryDTO;
import com.welab.crm.operate.vo.phone.ReportPhoneSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/21
 */
public interface IReportPhoneService {

    /**
     * 通话明细报表
     * @param dto
     * @return
     */
    public Page<ReportPhoneVO> queryDetai(ReportPhoneResultDTO dto);

    /**
     * 导出通话明细报表
     * @param dto
     * @return
     */
    public List<ReportPhoneVO> queryDetailExcel(ReportPhoneResultDTO dto);

    /**
     * 通话统计报表
     * @param dto
     * @return
     */
    public ReportPhoneSummaryVO querySummary(ReportPhoneSummaryDTO dto);

}
