package com.welab.crm.operate.util;
 
 
import com.welab.crm.operate.constant.Constant;
import com.welab.privacy.util.CryptoUtil;

/**
 * 使用rsa和aes加密算法对数据进行加解密
 * 加密得数据逗号分割，
 *      前部分为用rsa加密得aes密钥，后部分为aes用前面密钥加密得字符串
 */
public class CryptUtil {
    private static String split = ",";
    private static Integer aesKeySize = 128;
 
 
    /*public static String encryptData(String data) throws Exception{
        return encryptData(data, Constant.RSA_PUB_KEY);
    }
    public static String decryptData(String data) throws Exception{
        return decryptData(data,Constant.RSA_PRI_KEY);
    }*/
    /**
     * 公钥加密
     * @param data
     * @param publicInfoStr
     * @return 使用RSA加密AES的key,使用AES加密数据
     */
    /*private static String encryptData(String data, String publicInfoStr) throws Exception{
        // 随机生成AES key
        String aesKey = AesUtils.generateSecret(aesKeySize);
        // 使用AES加密数据
        String enData = AesUtils.encrypt(data, aesKey);
        // 使用RSA加密AES key
        String enAesKey = RSAEncryptUtil.encrypt(aesKey, publicInfoStr);
        return enAesKey + split + enData;
    }*/
 
    /**
     * 私钥解密
     * @param data 逗号分割:'使用RSA加密AES的key,使用AES加密数据'
     * @param privateInfoStr
     * @return 解密后的数据
     */
    /*private static String decryptData(String data, String privateInfoStr) throws Exception{
        // 拆解数据
        String[] dataArr = data.split(split);
        if(dataArr.length != 2){
            throw new IllegalArgumentException("data必须逗号分割:'使用RSA加密AES的key,使用AES加密数据'");
        }
        // 使用RSA解密AES key
        String aesKey = RSAEncryptUtil.decrypt(dataArr[0], privateInfoStr);
        // 使用AES解密数据
        return AesUtils.decrypt(dataArr[1], aesKey);
    }*/


    /*public static void main(String[] args) throws Exception{
//         生成公钥和私钥
//        List<String> keys = RSAUtils.genKeyPair();
//        //加密字符串
//        System.out.println("公钥:" + keys.get(0));
//        System.out.println("私钥:" + keys.get(1));
        long temp;
        temp = System.currentTimeMillis();
        String enStr = encryptData("你好测试测试测试测试你好测试你好测试测试");
        System.out.println(enStr);
        System.out.println("加密消耗时间:" + (System.currentTimeMillis() - temp) / 1000.0 + "秒");
        temp = System.currentTimeMillis();
        String deStr = decryptData("1123,123");
        System.out.println(deStr);
        System.out.println("解密消耗时间:" + (System.currentTimeMillis() - temp) / 1000.0 + "秒");

    }*/
 
}