package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.dto.ai.AiTmkConfigDTO;
import com.welab.crm.interview.service.AiPushJobService;
import com.welab.crm.operate.domain.AiTmkConfig;
import com.welab.crm.operate.domain.AiTmkPush;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.ai.AIPushConfigDTO;
import com.welab.crm.operate.dto.ai.AIPushStateDTO;
import com.welab.crm.operate.dto.ai.AiPushHistoryDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.AiTmkConfigMapper;
import com.welab.crm.operate.mapper.AiTmkPushMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.AiPushService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.ai.AiTmkPushVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiPushServiceImpl implements AiPushService {

    @Resource
    private AiPushJobService aiPushJobService;

    @Resource
    private AiTmkConfigMapper configMapper;

    @Resource
    private AiTmkPushMapper pushMapper;

    @Resource
    private OpDictInfoMapper dictInfoMapper;

    @Override
    public List<AiTmkConfigVO> getAIPushConfig() {
        LambdaQueryWrapper<AiTmkConfig> wrapper = Wrappers.<AiTmkConfig>lambdaQuery()
                .eq(AiTmkConfig::getDeleteFlag, Boolean.FALSE)
                .orderByDesc(AiTmkConfig::getState).orderByDesc(AiTmkConfig::getId);
        List<AiTmkConfig> configs = configMapper.selectList(wrapper);
        List<AiTmkConfigVO> results = new ArrayList<>();
        Map<String, String> speechMap = getSpeechMap();
        for (AiTmkConfig c : configs) {
            AiTmkConfigVO vo = new AiTmkConfigVO();
            BeanUtils.copyProperties(c, vo);
            vo.setGmtCreate(DateUtils.formatDate(c.getGmtCreate(), DateUtils.DATE_FORMAT));
            // 将话术id转换为话术名称返回
            vo.setSpeechName(speechMap.get(c.getSpeechId() + "-" + c.getTaskId()));
            setConfigVoTime(c, vo);
            results.add(vo);
        }
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAIPushConfig(String staffCode, AIPushConfigDTO addDTO) {
        log.info("addAIPushConfig start.");
        // 1.检查用户设置的参数合法性
        checkProductAndChannel(addDTO);

        // 2.将用户数据保存到数据库
        addDTO.setId(null);
        AiTmkConfig config = new AiTmkConfig();
        BeanUtils.copyProperties(addDTO, config);
        config.setCreateUser(staffCode);
        config.setState(Boolean.TRUE);

        AiTmkConfigDTO taskDTO = getAiTmkConfigDTO(config);
        setConfigTime(addDTO, config, taskDTO);
        if (config.getPushWay() == null) {
            config.setPushWay(0);
        }
        configMapper.insert(config);

        // 3.设置此任务的相应定时器(只有状态是开启的时候才去创建)
        try {
            if (config.getState()) {
                // 注意这里实体对象的id只有做了插入操作之后才存在,所以需要赋值
                taskDTO.setId(config.getId());
                aiPushJobService.createPushJob(taskDTO);
            }
        } catch (Exception e) {
            log.warn("createPushJob exception: {}", e.getMessage());
            throw new CrmOperateException("创建ai外呼定时推送任务发生错误");
        }
        log.info("addAIPushConfig success");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAIPushConfig(String staffCode, AIPushConfigDTO updateDTO) {
        log.info("updateAIPushConfig start.");
        // 1.校验用户更新参数是否正确
        checkProductAndChannel(updateDTO);

        AiTmkConfig updateConfig = configMapper.selectById(updateDTO.getId());
        if (updateConfig == null) {
            throw new CrmOperateException("更新ai外呼传入的id错误");
        }

        // 2.更新用户数据到数据库
        AiTmkConfig config = new AiTmkConfig();
        BeanUtils.copyProperties(updateDTO, config);
        config.setLstUpdUser(staffCode);
        AiTmkConfigDTO taskDTO = getAiTmkConfigDTO(config);
        setConfigTime(updateDTO, config, taskDTO);
        configMapper.updateById(config);

        // 3.更新此任务的相应定时器
        try {
            if (updateConfig.getState()) {
                aiPushJobService.updatePushJob(taskDTO);
            }
        } catch (Exception e) {
            log.warn("updateAIPushConfig exception: {}", e.getMessage());
            throw new CrmOperateException("更新ai外呼定时推送任务发生错误");
        }
        log.info("updateAIPushConfig success");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePushState(String staffCode, AIPushStateDTO stateDTO) {
        if (stateDTO.getId() != null) {
            // 单条更新任务状态
            updatePushSingleState(staffCode, stateDTO);
        } else {
            // 批量更新任务状态
            updatePushAllState(staffCode, stateDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAIPushConfig(String staffCode, Long id) {
        log.info("deleteAIPushConfig start.");
        // 1.删除任务对应的定时器
        try {
            aiPushJobService.deletePushJob(id);
        } catch (Exception e) {
            log.warn("deleteAIPushConfig exception: {}", e.getMessage());
            throw new CrmOperateException("删除ai外呼定时推送任务发生未知错误");
        }
        // 2.删除数据库中配置记录,是软删除操作
        AiTmkConfig config = new AiTmkConfig();
        config.setId(id);
        config.setState(Boolean.FALSE);
        config.setDeleteFlag(Boolean.TRUE);
        config.setLstUpdUser(staffCode);
        configMapper.updateById(config);
        log.info("deleteAIPushConfig success");
    }

    @Override
    public Page<AiTmkPushVO> getAiConfigPushHistory(AiPushHistoryDTO hisDTO) {
        Page<AiTmkPush> page = new Page<AiTmkPush>().setCurrent(hisDTO.getCurPage()).setSize(hisDTO.getPageSize());
        LambdaQueryWrapper<AiTmkPush> wrapper = Wrappers.<AiTmkPush>lambdaQuery()
                .eq(AiTmkPush::getConfigId, hisDTO.getConfigId())
                .orderByDesc(AiTmkPush::getId);
        Page<AiTmkPush> pushPage = pushMapper.selectPage(page, wrapper);
        return convertToVOPage(pushPage);
    }

    private void updatePushSingleState(String staffCode, AIPushStateDTO stateDTO) {
        log.info("updatePushSingleState start.");
        // 1.先更新数据库所有记录的状态
        AiTmkConfig config = new AiTmkConfig();
        config.setId(stateDTO.getId());
        config.setState(stateDTO.getState());
        config.setLstUpdUser(staffCode);
        configMapper.updateById(config);
        // 2.单条删除或者启用定时器
        try {
            LambdaQueryWrapper<AiTmkConfig> wrapper = Wrappers.<AiTmkConfig>lambdaQuery()
                    .eq(AiTmkConfig::getId, stateDTO.getId());
            AiTmkConfig task = configMapper.selectOne(wrapper);
            if (task == null) {
                return;
            }
            if (stateDTO.getState()) {
                // 创建单个任务的定时器
                AiTmkConfigDTO configDTO = getAiTmkConfigDTOWithTime(task);
                aiPushJobService.createPushJob(configDTO);
            } else {
                // 单条删除定时器
                aiPushJobService.deletePushJob(stateDTO.getId());
            }
        } catch (Exception e) {
            log.warn("updatePushSingleState exception: {}", e.getMessage());
            throw new CrmOperateException("开启或关闭单条ai外呼定时推送状态发生错误");
        }
        log.info("updatePushSingleState success");
    }

    private void updatePushAllState(String staffCode, AIPushStateDTO stateDTO) {
        log.info("updateAllPushState start.");
        // 1.先更新数据库所有记录的状态
        LambdaUpdateWrapper<AiTmkConfig> wrapper = Wrappers.<AiTmkConfig>lambdaUpdate()
                .eq(AiTmkConfig::getDeleteFlag, Boolean.FALSE);
        AiTmkConfig config = new AiTmkConfig();
        config.setState(stateDTO.getState());
        config.setLstUpdUser(staffCode);
        configMapper.update(config, wrapper);
        // 2.批量删除或者启用定时器
        try {
            LambdaQueryWrapper<AiTmkConfig> sWrapper = Wrappers.<AiTmkConfig>lambdaQuery()
                    .eq(AiTmkConfig::getDeleteFlag, Boolean.FALSE);
            List<AiTmkConfig> configs = configMapper.selectList(sWrapper);
            if (configs.isEmpty()) {
                return;
            }
            if (stateDTO.getState()) {
                // 批量创建定时器
                List<AiTmkConfigDTO> dtoList = getAiTmkConfigDTOList(configs);
                aiPushJobService.batchCreatePushJob(dtoList);
            } else {
                // 批量删除定时器
                List<Long> idList = configs.stream().map(AiTmkConfig::getId).collect(Collectors.toList());
                aiPushJobService.batchDeletePushJob(idList);
            }
        } catch (Exception e) {
            log.warn("updateAllPushState exception: {}", e.getMessage());
            throw new CrmOperateException("开启或关闭全部ai外呼定时推送状态发生错误");
        }
        log.info("updateAllPushState success");
    }

    /**
     * 将domain对象列表转换为调用创建定时任务的参数对象列表
     *
     * @param configs 从数据库查出的ai外呼实体对象列表
     */
    private List<AiTmkConfigDTO> getAiTmkConfigDTOList(List<AiTmkConfig> configs) {
        List<AiTmkConfigDTO> dtoList = new ArrayList<>(configs.size());
        for (AiTmkConfig c : configs) {
            AiTmkConfigDTO dto = getAiTmkConfigDTOWithTime(c);
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 构建创建定时任务的参数对象,携带定时器需要的时间参数
     *
     * @param config ai外呼配置实体对象
     */
    private AiTmkConfigDTO getAiTmkConfigDTOWithTime(AiTmkConfig config) {
        AiTmkConfigDTO dto = getAiTmkConfigDTO(config);
        LocalTime start = config.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        LocalTime end = config.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        dto.setStartHour(start.getHour());
        dto.setEndHour(end.getHour());
        return dto;
    }

    /**
     * 构建创建定时任务的参数对象
     *
     * @param config ai外呼配置实体对象
     */
    private AiTmkConfigDTO getAiTmkConfigDTO(AiTmkConfig config) {
        AiTmkConfigDTO taskDTO = new AiTmkConfigDTO();
        taskDTO.setId(config.getId());
        taskDTO.setNumber(config.getNumber());
        return taskDTO;
    }

    /**
     * 将时间转换为字符串以便展示
     */
    private void setConfigVoTime(AiTmkConfig config, AiTmkConfigVO configVO) {
        LocalTime localStartTime = config.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        String startTimeChars = getDateTimeFormatter().format(localStartTime);
        configVO.setStartTime(startTimeChars);
        LocalTime localEndTime = config.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        String endTimeChars = getDateTimeFormatter().format(localEndTime);
        configVO.setEndTime(endTimeChars);
    }

    private void checkProductAndChannel(AIPushConfigDTO configDTO) {
        if (StringUtils.isBlank(configDTO.getProductNames()) && StringUtils.isBlank(configDTO.getLoanChannels())) {
            throw new CrmOperateException("产品名称和进件渠道号不能同时为空");
        }
        if (!"ZY".equals(configDTO.getCaCompany())) {
            throw new CrmOperateException("目前系统只支持自研推送渠道");
        }
    }

    private DateTimeFormatter getDateTimeFormatter() {
        return DateTimeFormatter.ofPattern(DateUtils.HH_MM);
    }

    /**
     * 将时间点字符串转换为时间函数或数组以便进行存储或创建定时任务
     *
     * @param configDTO 用户传过来的参数值
     * @param config    用于存储到数据库的实体对象
     * @param taskDTO   用于创建定时任务的参数对象
     */
    private void setConfigTime(AIPushConfigDTO configDTO, AiTmkConfig config, AiTmkConfigDTO taskDTO) {
        LocalTime startTime = LocalTime.parse(configDTO.getStartTime(), getDateTimeFormatter());
        LocalTime endTime = LocalTime.parse(configDTO.getEndTime(), getDateTimeFormatter());
        int hourPeriod = endTime.getHour() - startTime.getHour();
        if (startTime.isAfter(endTime) || endTime.equals(startTime) || hourPeriod == 0) {
            throw new CrmOperateException("开始时间和结束时间节点设置不正确,开始时间必须小于结束时间,且时间间隔不能超过24小时");
        }
        if (configDTO.getNumber() <= 0 || configDTO.getNumber() > 24) {
            throw new CrmOperateException("推送时间间隔设置的数值不正确,必须是大于0且小于等于24的整数值");
        }

        Date start = Date.from(startTime.atDate(LocalDate.now()).atZone(ZoneId.systemDefault()).toInstant());
        config.setStartTime(start);
        taskDTO.setStartHour(startTime.getHour());
        Date end = Date.from(endTime.atDate(LocalDate.now()).atZone(ZoneId.systemDefault()).toInstant());
        config.setEndTime(end);
        taskDTO.setEndHour(endTime.getHour());
    }

    private Page<AiTmkPushVO> convertToVOPage(Page<AiTmkPush> pushPage) {
        Page<AiTmkPushVO> page = new Page<>();
        BeanUtils.copyProperties(pushPage, page);
        List<AiTmkPush> records = pushPage.getRecords();
        List<AiTmkPushVO> results = new ArrayList<>(records.size());
        for (AiTmkPush record : records) {
            AiTmkPushVO vo = new AiTmkPushVO();
            vo.setQuantities(record.getQuantities());
            vo.setPushDay(DateUtils.formatDate(record.getPushDay(), DateUtils.YYYY_MM_DD));
            results.add(vo);
        }
        page.setRecords(results);
        return page;
    }

    private Map<String, String> getSpeechMap() {
        LambdaQueryWrapper<OpDictInfo> wrapper = Wrappers.<OpDictInfo>lambdaQuery()
            .eq(OpDictInfo::getCategory, "speechAI").eq(OpDictInfo::getStatus, 1);
        List<OpDictInfo> dictInfo = dictInfoMapper.selectList(wrapper);
        return dictInfo.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent));
    }
}

