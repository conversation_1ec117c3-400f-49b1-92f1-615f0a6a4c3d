package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.domain.DataCustomer;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.loan.OutstandingEarlySettleDTO;
import com.welab.crm.operate.dto.operate.*;
import com.welab.crm.operate.dto.wallet.WalletEarlySettleDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.mapper.CustHisOperateMapper;
import com.welab.crm.operate.mapper.DataCustomerMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.StringUtil;
import com.welab.crm.operate.vo.operate.CustHisOperateVO;
import com.welab.user.interfaces.dto.NotesDTO;
import com.welab.user.interfaces.facade.NotesServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
@Service
@Slf4j
public class CustOperateServiceImpl extends ServiceImpl<CustHisOperateMapper, CustHisOperate> implements
        CustOperateService {

    @Resource
    private CustHisOperateMapper custHisOperateMapper;

    @Resource
    private DataCustomerMapper customerMapper;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private NotesServiceFacade notesServiceFacade;

    public static final String SEPARATOR = "#";

    @Override
    public void saveApplicationOperateHistory(ApplicationOperateReqDTO operateReqDTO) {
        if (Objects.nonNull(operateReqDTO)) {
            operateReqDTO.setStaffId(CommonUtils.getCurrentlogged());
            operateReqDTO.setGroupCode(CommonUtils.getCurrentloggedOrg());
            log.info("saveApplicationOperateHistory, params:{}", JSON.toJSONString(operateReqDTO));
            CustHisOperate operate = new CustHisOperate();
            BeanUtils.copyProperties(operateReqDTO, operate);
            operate.setOperateType(operateReqDTO.getType());
            operate.setLoanId(operateReqDTO.getApplicationId());
            operate.setOperateTime(new Date());
            custHisOperateMapper.insert(operate);
        }
    }

    @Override
    public void saveUpdateUserInfoOperateHistory(UserInfoModifyReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO)) {
            reqDTO.setStaffId(CommonUtils.getCurrentlogged());
            reqDTO.setGroupCode(CommonUtils.getCurrentloggedOrg());
            log.info("saveUpdateUserInfoOperateHistory,params:{}", JSON.toJSONString(reqDTO));
            CustHisOperate operate = new CustHisOperate();
            BeanUtils.copyProperties(reqDTO, operate);
            operate.setOperateType(OperateTypeEnum.USER_INFO_MODIFY.getCode());
            operate.setOperateTime(new Date());
            StringBuilder newValue = new StringBuilder("新手机号码为:");
            StringBuilder oldValue = new StringBuilder("客服手动修改手机号码,旧手机号码为:");
            DataCustomer dataCustomer = new DataCustomer();
            if (StringUtils.isNotBlank(reqDTO.getName())) {
                dataCustomer.setCustomerName(reqDTO.getName());
                newValue.append(reqDTO.getName()).append(SEPARATOR);
                oldValue.append(reqDTO.getOldName()).append(SEPARATOR);
            }
            if (StringUtils.isNotBlank(reqDTO.getCnid()) && !reqDTO.getCnid().contains("*")) {
                dataCustomer.setCnid(reqDTO.getCnid());
                newValue.append(reqDTO.getCnid()).append(SEPARATOR);
                oldValue.append(reqDTO.getOldCnid()).append(SEPARATOR);
            }
            if (StringUtils.isNotBlank(reqDTO.getMobile()) && !reqDTO.getMobile().contains("*")) {
                operate.setOldMobile(reqDTO.getOldMobile());
                dataCustomer.setMobile(reqDTO.getMobile());
                newValue.append(reqDTO.getMobile()).append(SEPARATOR);
                oldValue.append(reqDTO.getOldMobile()).append(SEPARATOR);
            }
            if (oldValue.indexOf(SEPARATOR) >= 0 && newValue.indexOf(SEPARATOR) >= 0) {
                oldValue.deleteCharAt(oldValue.lastIndexOf(SEPARATOR));
                newValue.deleteCharAt(newValue.lastIndexOf(SEPARATOR));
            }
            operate.setComment(oldValue.toString() + "\n" + newValue.toString());
            custHisOperateMapper.insert(operate);
            customerMapper.update(dataCustomer,
                    Wrappers.lambdaUpdate(DataCustomer.class).eq(DataCustomer::getUserId, reqDTO.getUserId()));

        }
    }

    @Override
    public void saveOperateHistory(CustHisOperate custHisOperate) {
        if (Objects.nonNull(custHisOperate)) {
            custHisOperateMapper.insert(custHisOperate);
        }
    }

    @Override
    public void saveOperateHistoryBatch(List<CustHisOperate> operates) {
        if (CollectionUtils.isNotEmpty(operates)) {
            custHisOperateMapper.insertBatchSomeColumn(operates);
        }
    }

    @Override
    public List<CustHisOperateVO> queryOperateHistory(OperateHistoryQueryReqDTO reqDTO) {
        log.info("queryOperateHistory, params:{}", JSON.toJSONString(reqDTO));
        List<CustHisOperateVO> list = custHisOperateMapper
                .selectHis(reqDTO);
        Map<String, String> operateMap = opDictInfoMapper.selectList(getDictWrapper("loanOperateType")).stream()
                .collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent));
        List<NotesDTO> notesUser = notesServiceFacade.selectBySubjectIdAndSubjectType(Arrays.asList(reqDTO.getUserId()), "User");
        if (CollectionUtils.isNotEmpty(notesUser)) {
            //添加用户自客户端发起的注销记录
            list.addAll(notesUser.stream().filter(t -> "client_block_user".equals(t.getNoteType())).map(t -> {
                CustHisOperateVO vo = new CustHisOperateVO();
                vo.setUserId(t.getSubjectId());
                vo.setOperateType(t.getNoteType());
                vo.setOperateTime(t.getCreatedAt());
                return vo;
            }).collect(Collectors.toList()));
        }
        List<NotesDTO> notesProfile = notesServiceFacade.selectBySubjectIdAndSubjectType(Arrays.asList(reqDTO.getUserId()), "Profile");
        if (CollectionUtils.isNotEmpty(notesProfile)) {
            //添加姓名修改记录
            list.addAll(notesProfile.stream().filter(s -> "kefu_modify_name".equals(s.getNoteType())).map(s -> {
                CustHisOperateVO vo = new CustHisOperateVO();
                vo.setUserId(s.getSubjectId());
                vo.setStaffName("用户自助");
                vo.setOperateType(s.getNoteType());
                vo.setOperateTime(s.getCreatedAt());
                vo.setComment(s.getContent());
                return vo;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().peek(custHisOperateVO -> {
                        if (custHisOperateVO.getOperateType().equals(OperateTypeEnum.BANK_CARD_OPEN_UNPIN.getCode())) {
                            custHisOperateVO.setLoanId(StringUtil.hideBankCard(custHisOperateVO.getLoanId()));
                        }
                        custHisOperateVO
                                .setOperateType(operateMap.get(custHisOperateVO.getOperateType()));
                    })
                    .collect(Collectors.toList());
        }
        return list;
    }

    private Wrapper<OpDictInfo> getDictWrapper(String category) {
        QueryWrapper<OpDictInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("category", category);
//        wrapper.eq("status", true);
        return wrapper;
    }

    @Override
    public void saveUrgentApprovalHistory(UrgentApprovalReqDTO dto) {
        log.info("saveUrgentApprovalHistory, req:{}", dto);
        CustHisOperate operate = new CustHisOperate();
        BeanUtils.copyProperties(dto, operate);
        operate.setStaffId(CommonUtils.getCurrentlogged());
        operate.setGroupCode(CommonUtils.getCurrentloggedOrg());
        operate.setOperateType(OperateTypeEnum.APPROVAL_URGENT.getCode());
        operate.setLoanId(dto.getApplicationId());
        operate.setOperateTime(new Date());
        custHisOperateMapper.insert(operate);

    }

    @Override
    public void saveOperationHistory(HistoryOperationDTO dto, StaffVO staffVO, String operateType) {
        CustHisOperate custHisOperate = new CustHisOperate();
        custHisOperate.setCustomerId(dto.getCustomerId());
        if (operateType.equals(OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING_ALLOW.getCode()) && dto instanceof OutstandingEarlySettleDTO) {
            custHisOperate.setComment(((OutstandingEarlySettleDTO) dto).getReason() + "#" +  dto.getComment());
        } else if (operateType.equals(OperateTypeEnum.WALLET_EARLY_SETTLE_ALLOW.getCode()) && dto instanceof WalletEarlySettleDTO){
            custHisOperate.setComment(((WalletEarlySettleDTO) dto).getReason() + "#" +  dto.getComment());
        } else {
            custHisOperate.setComment(dto.getComment());
        }
        custHisOperate.setUserId(dto.getUserId());
        custHisOperate.setLoanId(dto.getApplicationId());
        custHisOperate.setStaffId(staffVO.getLoginName());
        custHisOperate.setGroupCode(staffVO.getGroupCode());
        custHisOperate.setOperateType(operateType);
        custHisOperate.setOperateTime(new Date());
        custHisOperateMapper.insert(custHisOperate);
    }
}
