package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CsLoginRecord;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.dto.StaffQueryDTO;
import com.welab.crm.operate.dto.login.LoginTypeDTO;
import com.welab.crm.operate.enums.LoginTypeEnum;
import com.welab.crm.operate.mapper.CsLoginRecordMapper;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import com.welab.crm.operate.service.LoginService;
import com.welab.crm.operate.vo.loginReport.LoginReportDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class LoginServiceImpl implements LoginService {

	@Resource
	private InAuthCrmStaffService inAuthCrmStaffService;

	@Resource
	private CsLoginRecordMapper csLoginRecordMapper;

	@Override
	public void submitLoginRecord(LoginTypeDTO dto) {
		Long staffId = null;
		if (StringUtils.isNotBlank(dto.getMobile())) {
			InAuthCrmStaff staff = inAuthCrmStaffService.getStaffByMobile(dto.getMobile());
			if (Objects.nonNull(staff)) {
				staffId = staff.getId();
			}
		}

		csLoginRecordMapper.insert(buildLoginRecord(dto, staffId));

	}

	@Override
	public Page<LoginReportDetailVO> queryLoginReportDetailPage(StaffQueryDTO dto) {
		Page<LoginReportDetailVO> page = csLoginRecordMapper.queryLoginReportDetailPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
		page.getRecords().forEach(item -> {
			item.translateLoginResult();
			item.setLoginType(LoginTypeEnum.getNameByCode(item.getLoginType()));
		});

		return page;
	}

	@Override
	public List<LoginReportDetailVO> queryLoginReportDetailList(StaffQueryDTO dto) {
		List<LoginReportDetailVO> list = csLoginRecordMapper.queryLoginReportDetailList(dto);
		list.forEach(item -> {
			item.translateLoginResult();
			item.setLoginType(LoginTypeEnum.getNameByCode(item.getLoginType()));
		});
		return list;
	}

	private CsLoginRecord buildLoginRecord(LoginTypeDTO dto, Long staffId) {
		CsLoginRecord loginRecord = new CsLoginRecord();
		loginRecord.setStaffId(staffId);
		loginRecord.setResult(dto.getResult());
		loginRecord.setLoginType(dto.getLoginType());
		loginRecord.setGmtCreate(new Date());
		loginRecord.setGmtModify(new Date());
		return loginRecord;


	}
}
