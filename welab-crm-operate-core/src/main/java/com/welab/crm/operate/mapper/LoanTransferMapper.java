package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.LoanTransfer;
import com.welab.crm.operate.dto.loan.LoanTransferDTO;
import com.welab.crm.operate.dto.loan.OrderLoanTransferDTO;
import com.welab.crm.operate.vo.loan.OrderLoanTransferVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 债转结清主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface LoanTransferMapper extends BaseMapper<LoanTransfer> {


    /**
     * 分页查询债转结清记录
     *
     * @param page
     * @param tranDTO
     * @return
     */
    Page<LoanTransfer> selectPageByCondition(Page<LoanTransfer> page, @Param("dto") LoanTransferDTO tranDTO);


    /**
     * 分页查询工单债转明细
     */
    Page<OrderLoanTransferVO> selectOrderLoanTransferList(Page<OrderLoanTransferVO> page, @Param("dto") OrderLoanTransferDTO dto);
}
