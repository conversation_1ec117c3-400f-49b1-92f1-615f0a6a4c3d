package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.blacklist.BatchDictInfoReqDTO;
import com.welab.collection.interview.dto.blacklist.BlackListReqDTO;
import com.welab.collection.interview.dto.blacklist.BlackRecordReqDTO;
import com.welab.collection.interview.dto.blacklist.StopReasonDictDTO;
import com.welab.collection.interview.enums.BlackTypeEnum;
import com.welab.collection.interview.utils.DateUtils;
import com.welab.collection.interview.vo.blacklist.BlackListVO;
import com.welab.collection.interview.vo.blacklist.BlackRecordVO;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.LiaisonVo;
import com.welab.crm.interview.vo.UserBlackInfoVO;
import com.welab.crm.operate.constant.ParamConstant;
import com.welab.crm.operate.domain.DataCustomer;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.domain.StopCollectionList;
import com.welab.crm.operate.dto.blacklist.BlackDetailReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListAddDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryApprovalReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryReqDTO;
import com.welab.crm.operate.mapper.DataCustomerMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.mapper.StopCollectionListMapper;
import com.welab.crm.operate.service.BlackListService;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.blacklist.*;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.loanprocedure.vo.LoanVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BlackListServiceImpl implements BlackListService {

    @Resource
    private com.welab.collection.interview.service.BlackListService collectionBlackListService;

    @Resource
    private StopCollectionListMapper stopCollectionListMapper;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private InAuthCrmStaffService inAuthCrmStaffService;

    @Resource
    private LoansApplicationService loansApplicationService;

    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private NoticeMsgService noticeMsgService;
    
    @Resource
    private DataCustomerMapper dataCustomerMapper;

    public SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD);

    @Override
    public Page<KfBlackListVO> queryBlackListPage(BlackListQueryReqDTO dto) {
        try {
            // 如果只用uuid查询，将uuid的查询条件转化为userId
            if (StringUtils.isNotBlank(dto.getUuid()) && StringUtils.isBlank(dto.getUserId())
                && StringUtils.isBlank(dto.getIdNo())) {
                UserBlackInfoVO userInfo = userInfoService.getUserIdByUuid(dto.getUuid());
                if (Objects.nonNull(userInfo)) {
                    dto.setUserId(String.valueOf(userInfo.getUserId()));
                }
            }
            BlackListReqDTO reqDTO = new BlackListReqDTO();
            BeanUtils.copyProperties(dto, reqDTO);
            reqDTO.setRmtUserId(dto.getUserId());
            Page<BlackListVO> result = collectionBlackListService.getBlackListByPage(reqDTO);

            List<BlackListVO> list = result.getRecords();
            // 获取全部的userID
            List<Integer> userIdList = list.stream().filter(item -> StringUtils.isNotBlank(item.getUserId())).
                    map(item -> Integer.parseInt(item.getUserId())).collect(Collectors.toList());
            List<KfBlackListVO> kfBlackListVOS = new ArrayList<>();
            List<DataCustomer> dataCustomerList = null;
            Map<Long, List<DataCustomer>> userIdDataCustomerMap = null;
            Map<String, String> staffGroupNameMap = null;
            if (CollectionUtils.isNotEmpty(userIdList)) {
                dataCustomerList = dataCustomerMapper.queryByUserIdList(userIdList);
                // 转化为map
                userIdDataCustomerMap = dataCustomerList.stream()
                    .collect(Collectors.groupingBy(DataCustomer::getUserId));
                // 查询loginName对应groupName的map
                staffGroupNameMap = inAuthCrmStaffService.getGroupNameMap();
            }
            for (BlackListVO vo : list) {
                KfBlackListVO kfBlackListVO = new KfBlackListVO();
                BeanUtils.copyProperties(vo, kfBlackListVO);
                kfBlackListVO.setBlackTypeCode(BlackTypeEnum.getValue(kfBlackListVO.getBlackType()));
                if (Objects.nonNull(userIdDataCustomerMap) && StringUtils.isNotBlank(vo.getUserId())) {
                    List<DataCustomer> dataCustomers = userIdDataCustomerMap.get(Long.valueOf(vo.getUserId()));
                    if (CollectionUtils.isNotEmpty(dataCustomers)) {
                        if (Objects.nonNull(dataCustomers.get(0))) {
                            kfBlackListVO.setUuid(dataCustomers.get(0).getUuid());
                            kfBlackListVO.setName(dataCustomers.get(0).getCustomerName());
                        }
                    }
                }
                
                // 客服添加的黑名单，到客服自己的库里面查询uuid,添加客服组别
                if ("客服".equals(vo.getDepartment())) {

                    if (StringUtils.isBlank(kfBlackListVO.getUpdateGroup()) && Objects.nonNull(staffGroupNameMap)) {
                        kfBlackListVO.setCreateGroup(staffGroupNameMap.get(vo.getCreateUser()));
                        kfBlackListVO.setUpdateGroup(staffGroupNameMap.get(vo.getLstUpdUser()));
                    }
                }
                kfBlackListVOS.add(kfBlackListVO);
            }
            Page<KfBlackListVO> kfPage = new Page<>();
            BeanUtils.copyProperties(result, kfPage);
            kfPage.setRecords(kfBlackListVOS);

            return kfPage;
        } catch (Exception e) {
            log.error("queryBlackListPage error", e);
            throw new FastRuntimeException(e.getMessage());
        }

    }

    /**
     * 获取停催原因code:中文 map
     * @return
     */
    public Map<String, String> getStopReasonDict() {
        List<OpDictInfo> list = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "stop_collection_reason").eq(OpDictInfo::getStatus, true));
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent, (s, s2) -> s2));
        }
        return new HashMap<>();
    }

    @Override
    public Page<KfBlackListApprovalVO> queryBlackListApprovalPage(BlackListQueryApprovalReqDTO dto) {
        QueryWrapper<StopCollectionList> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(dto.getMobile()), "mobile", dto.getMobile());
        wrapper.eq(StringUtils.isNotBlank(dto.getUuid()), "uuid", dto.getUuid());
        wrapper.eq(Objects.nonNull(dto.getBlackDay()), "black_day", dto.getBlackDay());
        wrapper.eq(StringUtils.isNotBlank(dto.getBlackType()), "black_type", dto.getBlackType());
        wrapper.eq(StringUtils.isNotBlank(dto.getAddReason()), "add_reason", dto.getAddReason());
        wrapper.eq(StringUtils.isNotBlank(dto.getStaffName()), "staff_name", dto.getStaffName());
        wrapper.between(StringUtils.isNotBlank(dto.getGmtStartCreate()) && StringUtils.isNotBlank(dto.getGmtEndCreate()), "gmt_create", dto.getGmtStartCreate(), dto.getGmtEndCreate());
        if (Objects.nonNull(dto.getOperateState())) {
            wrapper.in("operate_state", dto.getOperateState()).orderByDesc("gmt_create");
        } else {
            //查询出初审/复审数据
            wrapper.in("operate_state", ParamConstant.ZERO, ParamConstant.FIRST, ParamConstant.THIRD).orderByDesc("gmt_create");
        }
        Page<StopCollectionList> page = stopCollectionListMapper.selectPage(new Page<>(dto.getCurPage(), dto.getPageSize()), wrapper);
        Page<KfBlackListApprovalVO> result = new Page<KfBlackListApprovalVO>();
        BeanUtils.copyProperties(page, result, "records");
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            Map<String, String> stopReasonMap = getStopReasonDict();
            result.setRecords(page.getRecords().stream().map(t -> {
                KfBlackListApprovalVO vo = new KfBlackListApprovalVO();
                BeanUtils.copyProperties(t, vo);
                vo.setAddReason(stopReasonMap.get(t.getAddReason()));
                vo.setBlackType(BlackTypeEnum.getText(t.getBlackType()));
                vo.setIdNo(SecurityUtil.maskCnid(t.getIdNo()));
                vo.setMobile(SecurityUtil.maskMobile2(t.getMobile()));
                vo.setBlackTypeCode(BlackTypeEnum.getValue(t.getBlackType()));
                vo.setRemark(t.getComment());
                vo.setCreateUser(t.getStaffName());
                vo.setCreateGroup(t.getGroupName());
                vo.setCreateTime(t.getGmtCreate());
                vo.setUserId("" + t.getUserId());
                vo.setId("" + t.getId());
                vo.setUpdateTime(t.getGmtModify());
                return vo;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public KfBlackListVO queryBlackDetail(Long id) {
        BlackListVO blackListVO = collectionBlackListService.queryDetail(id);
        KfBlackListVO kfBlackListVO = new KfBlackListVO();
        BeanUtils.copyProperties(blackListVO,kfBlackListVO);
        kfBlackListVO.setIdNo(AesUtils.encrypt(kfBlackListVO.getIdNo()));
        kfBlackListVO.setMobile(AesUtils.encrypt(kfBlackListVO.getMobile()));
        return kfBlackListVO;
    }

    @Override
    public void addBlackList(BlackListAddDTO blackListAddDTO) {
        if (BlackTypeEnum.CONTACT.getValue().equals(blackListAddDTO.getBlackType())) {
            String mobiles = blackListAddDTO.getMobiles();
            // 校验手机号
            if (StringUtils.isBlank(mobiles)) {
                throw new FastRuntimeException("联系人黑名单手机不能为空");
            }
            String[] split = mobiles.split(",");
            for (String mobile : split) {
                if (mobile.length() != 11){
                    throw new FastRuntimeException("联系人黑名单存在不合法的手机号");
                }
            }

        }
        // 根据uuid查询客户信息
        UserBlackInfoVO userInfo = userInfoService.getUserIdByUuid(blackListAddDTO.getUuid());
        if (Objects.nonNull(userInfo)) {
            // 联系人黑名单手机号前端会传，催收黑名单保存本人的手机号
            if (BlackTypeEnum.COLL.getValue().equals(blackListAddDTO.getBlackType())) {
                blackListAddDTO.setMobiles(userInfo.getMobile());
            }
            blackListAddDTO.setUserId(String.valueOf(userInfo.getUserId()));
            blackListAddDTO.setIdNo(userInfo.getCnid());
            blackListAddDTO.setCustName(userInfo.getName());
        }
        String mobiles = blackListAddDTO.getMobiles();
        String[] mobileArr = mobiles.split(",");
        for (String mobile : mobileArr) {
            // 新增黑名单添加到催收系统库->2023.10.16 新增黑名单数据需审批之后才能推送催收
            /*BlackListReqDTO dto = new BlackListReqDTO();
            BeanUtils.copyProperties(blackListAddDTO, dto);
            dto.setDepartment("kf");
            dto.setCreateUser(CommonUtils.getCurrentlogged());
            dto.setRmtUserId(blackListAddDTO.getUserId());
            dto.setMobile(mobile);
            collectionBlackListService.addBlackList(dto);*/

            // 客服系统内也需要保存一份
            blackListAddDTO.setMobile(mobile);
            saveToStopCollectionList(blackListAddDTO);
        }
        /*List<String> approval = Arrays.asList(getApprovalAllInfo("approval_staff", "approval_staff_one").split(","));
        NoticeMsgReqDTO dto = new NoticeMsgReqDTO();
        dto.setTitle("停催黑名单审批通知");
        dto.setContent("【停催黑名单审批】:停催黑名单审批" + "\t" + "【数量】:" + mobileArr.length);
        noticeMsgService.publishBannerNotice(approval, dto);*/
    }

    private void saveToStopCollectionList(BlackListAddDTO blackListAddDTO) {
        StopCollectionList stopCollectionList = new StopCollectionList();
        BeanUtils.copyProperties(blackListAddDTO, stopCollectionList);
        stopCollectionList.setGroupName(CommonUtils.getCurrentloggedOrgName());
        stopCollectionList.setStaffName(CommonUtils.getCurrentlogged());
        stopCollectionList.setUserId(Integer.valueOf(blackListAddDTO.getUserId()));
        stopCollectionList.setComment(blackListAddDTO.getRemark());
        stopCollectionList.setValidStartTime(DateUtil.stringToDate(blackListAddDTO.getValidStartTime()));
        stopCollectionList.setValidEndTime(DateUtil.stringToDate(blackListAddDTO.getValidEndTime()));

        if (BlackTypeEnum.COLL.getValue().equals(blackListAddDTO.getBlackType())) {
            // 查询当前时间该客户在途贷款的数量
            List<LoanVO> loanVOList =
                loanApplicationService.getLoanVOList(Integer.valueOf(blackListAddDTO.getUserId()));
            if (CollectionUtils.isNotEmpty(loanVOList)) {
                stopCollectionList.setCurrentLoanCount(loanVOList.size());
            }
        }

        // 查询表中是否已存在日志
        if (queryIsExist(stopCollectionList)) {
            stopCollectionList.setOperateType("update");
        } else {
            stopCollectionList.setOperateType("insert");
        }
        stopCollectionListMapper.insert(stopCollectionList);
    }

    private boolean queryIsExist(StopCollectionList stopCollectionList) {
        QueryWrapper<StopCollectionList> wrapper = new QueryWrapper<>();
        if (BlackTypeEnum.CONTACT.getValue().equals(stopCollectionList.getBlackType())) {
            wrapper.eq("mobile", stopCollectionList.getMobile());
            wrapper.eq("id_no", stopCollectionList.getIdNo());
        } else if (BlackTypeEnum.COLL.getValue().equals(stopCollectionList.getBlackType())) {
            wrapper.eq("id_no", stopCollectionList.getIdNo());
        } else {
            throw new FastRuntimeException("不合法的加黑类型:" + stopCollectionList.getBlackType());
        }

        List<StopCollectionList> list = stopCollectionListMapper.selectList(wrapper);
        return !list.isEmpty();
    }

    private String getAllInfo(String category) {
        return opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category).eq(OpDictInfo::getStatus, true)).getDetail();
    }

    private String getApprovalAllInfo(String category, String type) {
        return opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category).eq(OpDictInfo::getType, type).eq(OpDictInfo::getStatus, true)).getDetail();
    }

    @Override
    public void updateBlackList(BlackListAddDTO blackListAddDTO) {
        if (Objects.isNull(blackListAddDTO.getOperateState())) {
            BlackListReqDTO dto = new BlackListReqDTO();
            BeanUtils.copyProperties(blackListAddDTO, dto);
            dto.setId(String.valueOf(blackListAddDTO.getId()));
            dto.setDepartment("kf");
            dto.setCreateUser(CommonUtils.getCurrentlogged());
            dto.setRmtUserId(blackListAddDTO.getUserId());
            dto.setRequestUser(CommonUtils.getCurrentlogged());
            dto.setStopType(0);
            collectionBlackListService.updateBlackList(dto);
            saveToStopCollectionList(blackListAddDTO);
        } else {
            //批量审批
            if (CollectionUtils.isNotEmpty(blackListAddDTO.getIds())) {
                for (Integer id : blackListAddDTO.getIds()) {
                    StopCollectionList detail = stopCollectionListMapper.selectById(id);
                    if (ParamConstant.ZERO.equals(detail.getOperateState())) {
                        List<String> approval = Arrays.asList(getApprovalAllInfo("approval_staff", "approval_staff_one").split(","));
                        if (!approval.contains(CommonUtils.getCurrentlogged())) {
                            throw new FastRuntimeException(CommonUtils.getCurrentlogged() + "无初审权限!");
                        }
                    } else if (ParamConstant.FIRST.equals(detail.getOperateState())) {
                        List<String> approval = Arrays.asList(getApprovalAllInfo("approval_staff", "approval_staff_two").split(","));
                        if (!approval.contains(CommonUtils.getCurrentlogged())) {
                            throw new FastRuntimeException(CommonUtils.getCurrentlogged() + "无复审权限!");
                        }
                    }
                    //拒绝
                    if (ParamConstant.THIRD.equals(blackListAddDTO.getOperateState())) {
                        detail.setOperateState(blackListAddDTO.getOperateState());
                    } else {
                        //初审
                        if (ParamConstant.ZERO.equals(detail.getOperateState())) {
                            String approvalDay = getAllInfo("approval_day");
                            //小于需复审的拉黑天数直接审批完成,否则需要复审
                            if (detail.getBlackDay() <= Integer.parseInt(approvalDay)) {
                                detail.setOperateState(ParamConstant.SECOND);
                            } else {
                                detail.setOperateState(ParamConstant.FIRST);
                            }
                        } else if (ParamConstant.FIRST.equals(detail.getOperateState())) {
                            detail.setOperateState(ParamConstant.SECOND);
                        }
                    }
                    detail.setOperateUser(CommonUtils.getCurrentlogged());
                    detail.setApprovalComment(blackListAddDTO.getApprovalComment());
                    int update = stopCollectionListMapper.updateById(detail);
                    //审批完成的推送到催收
                    if (update > 0 && ParamConstant.SECOND.equals(detail.getOperateState())) {
                        BlackListReqDTO dto = new BlackListReqDTO();
                        BeanUtils.copyProperties(detail, dto);
                        dto.setValidStartTime(DateUtil.dateToString(detail.getValidStartTime(), DateUtil.TimeFormatter.YYYY_MM_DD_HH_MM_SS));
                        dto.setValidEndTime(DateUtil.dateToString(detail.getValidEndTime(), DateUtil.TimeFormatter.YYYY_MM_DD_HH_MM_SS));
                        dto.setDepartment("kf");
                        dto.setCreateUser(detail.getStaffName());
                        dto.setRmtUserId("" + detail.getUserId());
                        dto.setMobile(detail.getMobile());
                        dto.setRemark(detail.getComment());
                        dto.setIsInform(detail.getIsInform());
                        dto.setStopType(0);
                        collectionBlackListService.addBlackList(dto);
                    }
                }
            }
        }
    }

    @Override
    public boolean deleteBlackList(BatchDictInfoReqDTO reqDTO) {
        return collectionBlackListService.deleteBlackList(reqDTO);
    }

    @Override
    public void importBlackList(List<List<String>> arrCells) {
        if (CollectionUtils.isEmpty(arrCells)) {
            log.warn("importBlackList 导入文件为空");
            throw new FastRuntimeException("导入文件为空");
        }
        collectionBlackListService.importBlackList(arrCells, CommonUtils.getCurrentlogged(), "kf");

        // 批量保存到客服库
        batchSaveToKfStopList(arrCells);
    }

    @Override
    public List<LiaisonVo> getLiaisonByUserId(Integer userId) {
        return loansApplicationService.getLiaisonByUserId(userId);
    }

    @Override
    public Page<BlackRecordVO> getRecord(BlackRecordReqDTO dto) {
        return collectionBlackListService.getBlackRecordByPage(dto);
    }

    @Override
    public void syncDict(List<StopReasonDictDTO> list) {
        if (CollectionUtils.isEmpty(list)){
            log.warn("syncDict 停催原因字典列表为空");
            return;
        }
        List<OpDictInfo> opDictInfos = CommonUtils.getAllDict(list.get(0).getCategory(), null);
        if (CollectionUtils.isEmpty(opDictInfos)){
            log.warn("syncDict 停催原因字典不存在");
            return;
        }
        collectionBlackListService.saveStopCollectionDict(opDictInfos.stream().map(this::buildDictDTO).collect(Collectors.toList()));
    }

    private StopReasonDictDTO buildDictDTO(OpDictInfo dict) {
        StopReasonDictDTO stopReasonDictDTO = new StopReasonDictDTO();
        stopReasonDictDTO.setCategory(dict.getCategory());
        stopReasonDictDTO.setType(dict.getType());
        stopReasonDictDTO.setContent(dict.getContent());
        return stopReasonDictDTO;
    }

    @Override
    public Page<BlackDetailReportVO> queryBlackDetailReport(BlackDetailReqDTO dto) {
        Page<BlackDetailReportVO> page =
            stopCollectionListMapper.selectDetailReportPage(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            Map<String, String> reasonMap = getStopReasonCodeNameMap();
            for (BlackDetailReportVO reportVo : page.getRecords()) {
                reportVo.setBlackType(BlackTypeEnum.getText(reportVo.getBlackType()));
                reportVo.setReason(reasonMap.get(reportVo.getReason()));
                if ("insert".equals(reportVo.getOperateType())) {
                    reportVo.setOperateType("新增");
                } else if ("update".equals(reportVo.getOperateType())) {
                    reportVo.setOperateType("更新");
                }
                reportVo.setMobile(SecurityUtil.maskMobile(reportVo.getMobile()));
                reportVo.setIdNo(SecurityUtil.maskCnid(reportVo.getIdNo()));
            }
        }
        return page;
    }

    @Override
    public List<BlackListSummaryReportStrVO> queryBlackListSummary(BlackDetailReqDTO dto) {

        List<BlackListSummaryReportVO> resultList = new ArrayList<>();
        // 查询指定条件内的黑名单数据
        List<StopCollectionList> totalList = stopCollectionListMapper.queryDetail(dto);

        long startTime = System.currentTimeMillis();

        // 计算拉黑本人量和合同量
        calSelfAndOrderCount(resultList, totalList);

        // 计算拉黑联系人量
        calContactCount(resultList, totalList);

        // 计算拉黑实际客户量
        calUserCount(resultList, totalList);

        // 计算环比数据行
        BlackListSummaryReportVO roundVO = calRoundRow(dto);

        // 计算失效客户量
        calUnBlockUserCount(resultList, dto);

        // 数据合并，将同一个客服的多条数据合并成一条
        List<BlackListSummaryReportVO> list = convertResultList(resultList);

        // 计算合计行
        BlackListSummaryReportVO totalVO = calTotalRow(list);

        list.add(totalVO);
        list.add(roundVO);
        // 计算环比
        BlackListSummaryReportStrVO roundCompareVO = calRoundCompare(totalVO, roundVO);


        // 数据转化，将整数转化为字符串，因为环比数据是百分比
        List<BlackListSummaryReportStrVO> strVOList = new ArrayList<>();
        for (BlackListSummaryReportVO reportVO : list) {
            BlackListSummaryReportStrVO strVO = new BlackListSummaryReportStrVO();
            BeanUtils.copyProperties(reportVO, strVO);
            strVO.setBlockOrderCount(String.valueOf(reportVO.getBlockOrderCount()));
            strVO.setBlockSelfCount(String.valueOf(reportVO.getBlockSelfCount()));
            strVO.setBlockUserCount(String.valueOf(reportVO.getBlockUserCount()));
            strVO.setBlockContactCount(String.valueOf(reportVO.getBlockContactCount()));
            strVO.setUnBlockUserCount(String.valueOf(reportVO.getUnBlockUserCount()));
            strVOList.add(strVO);
        }
        strVOList.add(roundCompareVO);

        System.out.println("use time" + (System.currentTimeMillis() - startTime));

        return strVOList;
    }

    private BlackListSummaryReportStrVO calRoundCompare(BlackListSummaryReportVO totalVO, BlackListSummaryReportVO roundVO) {
        BlackListSummaryReportStrVO roundCompareVO = new BlackListSummaryReportStrVO();
        roundCompareVO.setGroupName("环比");
        roundCompareVO.setBlockSelfCount(CommonUtil
                .calcPercentage(totalVO.getBlockSelfCount() - roundVO.getBlockSelfCount(), roundVO.getBlockSelfCount()));
        roundCompareVO.setBlockContactCount(CommonUtil.calcPercentage(
                totalVO.getBlockContactCount() - roundVO.getBlockContactCount(), roundVO.getBlockContactCount()));
        roundCompareVO.setBlockUserCount(CommonUtil
                .calcPercentage(totalVO.getBlockUserCount() - roundVO.getBlockUserCount(), roundVO.getBlockUserCount()));
        roundCompareVO.setBlockOrderCount(CommonUtil
                .calcPercentage(totalVO.getBlockOrderCount() - roundVO.getBlockOrderCount(), roundVO.getBlockOrderCount()));

        return roundCompareVO;
    }

    private BlackListSummaryReportVO calTotalRow(List<BlackListSummaryReportVO> list) {

        int totalSelfCount = 0;
        int totalContactCount = 0;
        int totalUserCount = 0;
        int totalOrderCount = 0;
        int totalUnBlockCount = 0;
        // 计算合计行
        for (BlackListSummaryReportVO row : list) {
            totalSelfCount += row.getBlockSelfCount();
            totalContactCount += row.getBlockContactCount();
            totalUserCount += row.getBlockUserCount();
            totalOrderCount += row.getBlockOrderCount();
            totalUnBlockCount += row.getUnBlockUserCount();
        }

        return new BlackListSummaryReportVO("", "合计", totalSelfCount, totalContactCount, totalUserCount,
            totalOrderCount, totalUnBlockCount);
    }

    private BlackListSummaryReportVO calRoundRow(BlackDetailReqDTO dto) {
        // 计算环比行数据
        convertDTO(dto);
        List<StopCollectionList> roundList = stopCollectionListMapper.queryDetail(dto);
        BlackListSummaryReportVO roundVO = new BlackListSummaryReportVO();
        roundVO.setGroupName(dto.getStartTime() + "~" + dto.getEndTime());
        roundVO.setBlockSelfCount(
            Math.toIntExact(roundList.stream().filter(item -> BlackTypeEnum.COLL.getValue().equals(item.getBlackType()))
                .map(StopCollectionList::getUserId).distinct().count()));
        roundVO.setBlockContactCount(Math
            .toIntExact(roundList.stream().filter(item -> BlackTypeEnum.CONTACT.getValue().equals(item.getBlackType()))
                .map(item -> item.getMobile() + item.getUserId()).distinct().count()));
        roundVO.setBlockUserCount(
            Math.toIntExact(roundList.stream().map(StopCollectionList::getUserId).distinct().count()));
        roundVO.setBlockOrderCount(
            roundList.stream().map(StopCollectionList::getCurrentLoanCount).reduce(0, Integer::sum));

        return roundVO;
    }

    private void calUserCount(List<BlackListSummaryReportVO> resultList, List<StopCollectionList> totalList) {
        Map<String, List<StopCollectionList>> totalGroupMap =
            totalList.stream().collect(Collectors.groupingBy(item -> item.getStaffName() + "," + item.getGroupName()));
        totalGroupMap.forEach((staffGroupName, blackList) -> {
            BlackListSummaryReportVO reportVO = new BlackListSummaryReportVO();
            reportVO.setStaffName(staffGroupName.split(",")[0]);
            reportVO.setGroupName(staffGroupName.split(",")[1]);
            reportVO.setBlockUserCount(
                Math.toIntExact(blackList.stream().map(StopCollectionList::getUserId).distinct().count()));
            resultList.add(reportVO);
        });
    }

    private void calContactCount(List<BlackListSummaryReportVO> resultList, List<StopCollectionList> totalList) {
        // 联系人黑名单数据
        List<StopCollectionList> contactBlackList = totalList.stream()
            .filter(item -> BlackTypeEnum.CONTACT.getValue().equals(item.getBlackType())).collect(Collectors.toList());
        // 拉黑联系人量
        Map<String, List<StopCollectionList>> contactStaffGroupMap = contactBlackList.stream()
            .collect(Collectors.groupingBy(item -> item.getStaffName() + "," + item.getGroupName()));
        contactStaffGroupMap.forEach((staffGroupName, staffContactBlackList) -> {
            BlackListSummaryReportVO reportVO = new BlackListSummaryReportVO();
            reportVO.setStaffName(staffGroupName.split(",")[0]);
            reportVO.setGroupName(staffGroupName.split(",")[1]);
            reportVO.setBlockContactCount(Math.toIntExact(
                staffContactBlackList.stream().map(item -> item.getMobile() + item.getUserId()).distinct().count()));
            resultList.add(reportVO);
        });
    }

    /**
     * 计算拉黑本人量和来黑合同量
     * 
     * @param resultList
     * @param totalList
     */
    private void calSelfAndOrderCount(List<BlackListSummaryReportVO> resultList, List<StopCollectionList> totalList) {
        // 催收黑名单的数据
        List<StopCollectionList> collBlackList = totalList.stream()
            .filter(item -> BlackTypeEnum.COLL.getValue().equals(item.getBlackType())).collect(Collectors.toList());
        Map<String, List<StopCollectionList>> collStaffGroupMap = collBlackList.stream()
            .collect(Collectors.groupingBy(item -> item.getStaffName() + "," + item.getGroupName()));
        collStaffGroupMap.forEach((staffGroupName, staffCollBlackList) -> {
            BlackListSummaryReportVO reportVO = new BlackListSummaryReportVO();
            reportVO.setStaffName(staffGroupName.split(",")[0]);
            reportVO.setGroupName(staffGroupName.split(",")[1]);
            reportVO.setBlockSelfCount(
                Math.toIntExact(staffCollBlackList.stream().map(StopCollectionList::getUserId).distinct().count()));
            reportVO.setBlockOrderCount(
                staffCollBlackList.stream().map(StopCollectionList::getCurrentLoanCount).reduce(0, Integer::sum));
            resultList.add(reportVO);
        });
    }

    private void convertDTO(BlackDetailReqDTO dto) {
        if (StringUtils.isNotBlank(dto.getRoundStartTime())) {
            dto.setStartTime(dto.getRoundStartTime());
        } else {
            dto.setStartTime(DateUtil.plusMonths(DateUtil.stringToDate(dto.getStartTime()), -1,
                com.welab.crm.operate.util.DateUtils.DATE_FORMAT));
        }

        if (StringUtils.isNotBlank(dto.getRoundEndTime())) {
            dto.setEndTime(dto.getRoundEndTime());
        } else {
            dto.setEndTime(DateUtil.plusMonths(DateUtil.stringToDate(dto.getEndTime()), -1,
                com.welab.crm.operate.util.DateUtils.DATE_FORMAT));
        }
    }

    private void calUnBlockUserCount(List<BlackListSummaryReportVO> resultList, BlackDetailReqDTO dto) {
        dto.setStartTime(DateUtil.dateToString(com.welab.crm.operate.util.DateUtils.getStartOfYesterday()));
        dto.setEndTime(DateUtil.dateToString(com.welab.crm.operate.util.DateUtils.getEndOfYesterday()));
        List<StopCollectionList> list = stopCollectionListMapper.queryDetail(dto);
        Map<String, List<StopCollectionList>> map =
            list.stream().collect(Collectors.groupingBy(item -> item.getStaffName() + "," + item.getGroupName()));
        map.forEach((staffGroupName, blackList) -> {
            BlackListSummaryReportVO reportVO = new BlackListSummaryReportVO();
            reportVO.setStaffName(staffGroupName.split(",")[0]);
            reportVO.setGroupName(staffGroupName.split(",")[1]);
            reportVO.setUnBlockUserCount(
                Math.toIntExact(blackList.stream().map(StopCollectionList::getUserId).distinct().count()));
            resultList.add(reportVO);
        });
    }

    private List<BlackListSummaryReportVO> convertResultList(List<BlackListSummaryReportVO> tempList) {
        Map<String, int[]> map = new HashMap<>();
        for (BlackListSummaryReportVO reportVO : tempList) {
            String staffName = reportVO.getStaffName();
            String groupName = reportVO.getGroupName();
            String key = staffName + "," + groupName;
            int[] countArr = map.get(key);
            if (countArr == null) {
                countArr = new int[] {reportVO.getBlockSelfCount(), reportVO.getBlockContactCount(),
                    reportVO.getBlockUserCount(), reportVO.getBlockOrderCount(), reportVO.getUnBlockUserCount()};
                map.put(key, countArr);
            } else {
                countArr[0] += reportVO.getBlockSelfCount();
                countArr[1] += reportVO.getBlockContactCount();
                countArr[2] += reportVO.getBlockUserCount();
                countArr[3] += reportVO.getBlockOrderCount();
                countArr[4] += reportVO.getUnBlockUserCount();
            }
        }

        // 将map中的数据转化为list
        List<BlackListSummaryReportVO> resultList = new ArrayList<>();
        for (Map.Entry<String, int[]> entry : map.entrySet()) {
            String name = entry.getKey();
            int[] count = entry.getValue();
            BlackListSummaryReportVO s = new BlackListSummaryReportVO(name.split(",")[0], name.split(",")[1], count[0],
                count[1], count[2], count[3], count[4]);
            resultList.add(s);
        }

        return resultList;
    }

    private void batchSaveToKfStopList(List<List<String>> arrCells) {

        List<StopCollectionList> batchInsertList = new ArrayList<>();

        int i = 1;
        for (List<String> row : arrCells) {
            i++;
            String contractNo = row.get(0);
            String idNo = row.get(1);
            String mobile = row.get(2);
            String userId = row.get(3);
            if (StringUtils.isBlank(contractNo) && StringUtils.isBlank(idNo) && StringUtils.isBlank(mobile)
                && StringUtils.isBlank(userId)) {
                throw new FastRuntimeException("第" + i + "行[合同号]、[身份证号]、[手机号]、[用户ID] 不能同时为空!");
            }

            String blackType = row.get(4); // 黑名单类型
            if (StringUtils.isNotBlank(blackType) && BlackTypeEnum.getValue(blackType) == null) {
                throw new FastRuntimeException("第" + i + "行[黑名单类型]不存在的字典编码");
            }

            if (StringUtils.isBlank(blackType)) {
                blackType = BlackTypeEnum.COLL.getValue();
            } else {
                blackType = BlackTypeEnum.getValue(blackType);
            }

            String addReason = row.get(5);
            Map<String, String> stopReasonMap = getStopReasonMap();
            if (StringUtils.isNotBlank(addReason) && stopReasonMap.get(addReason) == null) {
                throw new FastRuntimeException("第" + i + "行[添加原因]不存在的字典编码");
            }
            addReason = stopReasonMap.get(addReason);
            String validStartTime = row.get(6);
            if (StringUtils.isBlank(validStartTime)) {
                throw new FastRuntimeException("第" + i + "行[有效期起始日期]为空!");
            }
            Date start;
            try {
                start = DateUtils.parseDate(validStartTime, DateUtils.YYYYMMDD_FORMAT);
                if (null == start) {
                    start = dateFormat.parse(validStartTime);
                }
            } catch (Exception e) {
                throw new FastRuntimeException("第" + i + "行[有效期起始日期]格式错误");
            }
            String validEndTime = row.get(7);
            if (StringUtils.isBlank(validEndTime)) {
                throw new FastRuntimeException("第" + i + "行[有效期结束日期]为空!");
            }
            Date end;
            try {
                end = DateUtils.parseDate(validEndTime, DateUtils.YYYYMMDD_FORMAT);
                if (null == end) {
                    end = dateFormat.parse(validEndTime);
                }
            } catch (Exception e) {
                throw new FastRuntimeException("第" + i + "行[有效期结束日期]格式错误");
            }
            if (start != null && end != null && start.getTime() > end.getTime()) {
                throw new FastRuntimeException("第" + i + "行[有效期起始日期]必须小于[有效期结束日期]");
            }

            String remark = row.get(8); // 备注

            String uuid = row.get(9);

            if (StringUtils.isBlank(uuid)) {
                throw new FastRuntimeException("第" + i + "行uuid不能为空");
            }
            if (StringUtils.isBlank(mobile) && BlackTypeEnum.CONTACT.getValue().equals(blackType)) {
                throw new FastRuntimeException("第" + i + "行联系人黑名单手机号不能为空");
            }
            StopCollectionList stopCollectionList = new StopCollectionList();
            UserBlackInfoVO userInfo = userInfoService.getUserIdByUuid(uuid);
            stopCollectionList.setUuid(uuid);
            if (StringUtils.isBlank(userId)) {
                stopCollectionList.setUserId(userInfo.getUserId());
            } else {
                stopCollectionList.setUserId(Integer.valueOf(userId));
            }

            // 查询当前时间该客户在途贷款的数量
            if (BlackTypeEnum.COLL.getValue().equals(blackType)) {
                List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(stopCollectionList.getUserId());
                if (CollectionUtils.isNotEmpty(loanVOList)) {
                    stopCollectionList.setCurrentLoanCount(loanVOList.size());
                }
            }

            if (StringUtils.isBlank(mobile)) {
                stopCollectionList.setMobile(userInfo.getMobile());
            } else {
                stopCollectionList.setMobile(mobile);
            }

            if (StringUtils.isBlank(idNo)) {
                stopCollectionList.setIdNo(userInfo.getCnid());
            } else {
                stopCollectionList.setIdNo(idNo);
            }

            stopCollectionList.setAddReason(addReason);
            stopCollectionList.setBlackType(blackType);
            stopCollectionList.setComment(remark);
            stopCollectionList.setValidStartTime(start);
            stopCollectionList.setValidEndTime(end);
            stopCollectionList.setCustName(userInfo.getName());
            stopCollectionList.setGroupName(CommonUtils.getCurrentloggedOrgName());
            stopCollectionList.setStaffName(CommonUtils.getCurrentlogged());
            if (queryIsExist(stopCollectionList)) {
                stopCollectionList.setOperateType("update");
            } else {
                stopCollectionList.setOperateType("insert");
            }

            batchInsertList.add(stopCollectionList);
        }

        stopCollectionListMapper.insertBatchSomeColumn(batchInsertList);
    }

    /**
     * 获取停催原因 中文:code map
     *
     * @return
     */
    private Map<String, String> getStopReasonMap() {
        List<OpDictInfo> list = CommonUtils.getDict("stop_collection_reason", null);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(OpDictInfo::getContent, OpDictInfo::getType, (s, s2) -> s2));
        }
        return new HashMap<>();
    }

    /**
     * 获取停催原因 code:中文 map
     *
     * @return
     */
    private Map<String, String> getStopReasonCodeNameMap() {
        List<OpDictInfo> list = CommonUtils.getDict("stop_collection_reason", null);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(OpDictInfo::getType, OpDictInfo::getContent, (s, s2) -> s2));
        }
        return new HashMap<>();
    }
}
