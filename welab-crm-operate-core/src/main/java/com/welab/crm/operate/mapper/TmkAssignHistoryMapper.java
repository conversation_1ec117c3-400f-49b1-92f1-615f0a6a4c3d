package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.TmkAssignHistory;
import com.welab.crm.operate.dto.report.WoReportDTO;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电销分单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-03-07
 */
public interface TmkAssignHistoryMapper extends BaseMapper<TmkAssignHistory> {

    /**
     * 查询所有员工指定时间内被回收的数据量
     */
    List<RedistributedOutboundEfficiencyVO> selectRecycleByTime(@Param("filter") WoReportDTO reportDTO,
                                                                @Param("codeList") List<String> groupCodeList,
                                                                @Param("typeList") List<String> businessTypeList);
}
