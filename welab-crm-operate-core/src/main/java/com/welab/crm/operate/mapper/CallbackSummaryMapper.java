package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CallbackSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummaryReportQueryDTO;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryReportVO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.workorder.WorkOrderLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 回电小结表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
public interface CallbackSummaryMapper extends BaseMapper<CallbackSummary> {

    /**
     * 根据uuid查询回电小结
     * 
     * @param uuid
     * @return
     */
    List<PhoneSummaryVO> querySummaryByUuid(String uuid);

    /**
     * 分页查询报表数据
     * 
     * @param page
     * @param dto
     * @return
     */
    Page<CallbackSummaryReportVO> queryReportPage(Page<CallbackSummaryReportVO> page,
        @Param("dto") CallbackSummaryReportQueryDTO dto);

    /**
     * 查询回电小结日志
     * @param orderNo
     * @return
     */
    List<WorkOrderLogVO> querySummaryLogByOrderNo(String orderNo);
}
