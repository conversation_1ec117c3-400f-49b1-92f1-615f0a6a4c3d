/**
 * @Title: ICrmDictInfoServiceImpl.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpDictInfoConf;
import com.welab.crm.operate.dto.dict.DictInfoConfReqDTO;
import com.welab.crm.operate.mapper.OpDictInfoConfMapper;
import com.welab.crm.operate.service.ICrmDictInfoConfService;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 字典服务
 * <AUTHOR>
 * @date 2021-10-15 10:11:44
 * @version v1.0
 */
@Slf4j
@Service
public class CrmDictInfoConfServiceImpl implements ICrmDictInfoConfService {
    
    @Autowired
    private OpDictInfoConfMapper opDictInfoConfMapper;
    
    @Override
    public Page<DictInfoConfResVO> getDictInfosConf(DictInfoConfReqDTO req) {
        try {
            Page<OpDictInfoConf> pageResult = opDictInfoConfMapper.selectPage(new Page<>(req.getCurPage(), req.getPageSize()), buildDictSelectWrapper(req));
            List<DictInfoConfResVO> reslist = new ArrayList<DictInfoConfResVO>();
            Page<DictInfoConfResVO> page = new Page<DictInfoConfResVO>();
            BeanUtils.copyProperties(pageResult, page, "list");
            if (null != pageResult && null != pageResult.getRecords()) {
                reslist = pageResult.getRecords().stream().map(item -> {
                    DictInfoConfResVO res = new DictInfoConfResVO();
                    BeanUtils.copyProperties(item, res, "status");
                    // res.setIsStatus(item.getIsStatus() ? 1 : 0);
                    return res;
                }).collect(Collectors.toList());
            }
            page.setRecords(reslist);
            return page;
        } catch (Exception e) {
            log.error("getDictInfosConf:{}", e.getMessage());
            throw new RuntimeException("系统查询错误");
        }
    }

    private QueryWrapper<OpDictInfoConf> buildDictSelectWrapper(DictInfoConfReqDTO req) {
        QueryWrapper<OpDictInfoConf> wrapper = new QueryWrapper<OpDictInfoConf>();
        wrapper.eq(Objects.nonNull(req.getWoTypeId()), "wo_type_id", req.getWoTypeId());
        wrapper.eq(Objects.nonNull(req.getWoTypeFirId()), "wo_type_fir_id", req.getWoTypeFirId());
        wrapper.eq(Objects.nonNull(req.getWoTypeSecId()), "wo_type_sec_id", req.getWoTypeSecId());
        wrapper.eq(Objects.nonNull(req.getWoTypeThirId()), "wo_type_thir_id", req.getWoTypeThirId());
        wrapper.eq(Objects.nonNull(req.getWoTypeChildId()), "wo_type_child_id", req.getWoTypeChildId());
        wrapper.eq(Objects.nonNull(req.getIsStatus()), "is_status", req.getIsStatus());
        wrapper.eq(Objects.nonNull(req.getSort()), "sort", req.getSort());
        wrapper.eq(Objects.nonNull(req.getComplainLevel()), "complain_level", req.getComplainLevel());
        wrapper.eq(Objects.nonNull(req.getMustApprove()), "must_approve", req.getMustApprove());
        wrapper.eq(Objects.nonNull(req.getMustReview()), "must_review", req.getMustReview());
        wrapper.eq(StringUtils.isNotBlank(req.getFastStatus()), "fast_status", req.getFastStatus());
        wrapper.like(StringUtils.isNotBlank(req.getGroupCode()), "group_code", req.getGroupCode());
        wrapper.eq(StringUtils.isNotBlank(req.getFastTargetUser()), "fast_target_user", req.getFastTargetUser());
        wrapper.orderByAsc("sort");
        return wrapper;
    }

    @Override
    public boolean addDictInfoConf(DictInfoConfReqDTO reqDTO) {
        try {
            OpDictInfoConf dict = new OpDictInfoConf();
            BeanUtils.copyProperties(reqDTO, dict, "status");
            // dict.setIsStatus(null != reqDTO.getIsStatus() && 0 != reqDTO.getIsStatus());
            int saveCount = opDictInfoConfMapper.insert(dict);
            return 0 < saveCount;
        } catch (Exception e) {
            log.error("addDictInfo:{}", e.getMessage());
            throw new RuntimeException("系统插入错误");
        }
    }

    @Override
    public boolean updateDictInfoConf(DictInfoConfReqDTO reqDTO) {
        OpDictInfoConf dictInfoConf = opDictInfoConfMapper.selectById(reqDTO.getId());
        if (null == dictInfoConf) {
            throw new RuntimeException("不存在此条信息");
        }
        try {
            BeanUtils.copyProperties(reqDTO, dictInfoConf);
            //dictInfo.setStatus(null != reqDTO.getStatus() && 0 != reqDTO.getStatus());
            int updateCount = opDictInfoConfMapper.updateById(dictInfoConf);
            return 0 < updateCount;
        } catch (Exception e) {
            log.error("updateDictInfoConf:{}", e.getMessage());
            throw new RuntimeException("系统更新出错");
        }
    }

    @Override
    public boolean deleteDictInfoConf(DictInfoConfReqDTO reqDTO) {
        OpDictInfoConf dictInfoConf = opDictInfoConfMapper.selectById(reqDTO.getId());
        if(null == dictInfoConf) {
            throw new RuntimeException("不存在此条信息");
        }
        int delCount = opDictInfoConfMapper.deleteById(reqDTO.getId());
        return 0 < delCount;
    }

}
