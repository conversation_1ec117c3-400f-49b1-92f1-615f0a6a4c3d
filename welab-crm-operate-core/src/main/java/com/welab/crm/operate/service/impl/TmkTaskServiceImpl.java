package com.welab.crm.operate.service.impl;

import com.welab.crm.base.enums.MemberPayModeEnum;
import com.welab.crm.base.enums.VipOrderStatusEnum;
import com.welab.crm.interview.enums.QuotaStateEnum;
import com.welab.crm.operate.domain.AiTmkConfig;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.enums.TmkDistributionTypeEnum;
import com.welab.crm.operate.mapper.AiTmkCallbackMapper;
import com.welab.crm.operate.mapper.AiTmkConfigMapper;
import com.welab.crm.operate.mapper.AiTmkPushMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.telemarketing.AiTransformReportVO;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.tmkReport.StaffEfficiencyVO;
import com.welab.exception.FastRuntimeException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.operate.constant.TmkTaskTypeConstant;
import com.welab.crm.operate.domain.TmkAppoint;
import com.welab.crm.operate.domain.TmkLoanInvite;
import com.welab.crm.operate.domain.TmkSummary;
import com.welab.crm.operate.domain.TmkUuid;
import com.welab.crm.operate.domain.TmkVip;
import com.welab.crm.operate.domain.TmkWallet;
import com.welab.crm.operate.dto.telemarketing.TmkAppointReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkAssignDetailReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReportBaseReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkSummaryReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.InPhoneLoginInfoMapper;
import com.welab.crm.operate.mapper.TmkAppointMapper;
import com.welab.crm.operate.mapper.TmkLoanInviteMapper;
import com.welab.crm.operate.mapper.TmkSummaryMapper;
import com.welab.crm.operate.mapper.TmkUuidMapper;
import com.welab.crm.operate.mapper.TmkVipMapper;
import com.welab.crm.operate.mapper.TmkWalletMapper;
import com.welab.crm.operate.service.TmkTaskService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.crm.operate.vo.telemarketing.AiPushReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkAssignDetailReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkCjhyReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkLoanReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkStaffWorkInfo;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkUuidReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkWalletReportVO;
import com.welab.privacy.util.http.HttpClients;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description: 电销任务接口实现类
 * @date 2022/2/22 14:46
 */
@Service
@Slf4j
public class TmkTaskServiceImpl implements TmkTaskService {

    @Resource
    private TmkLoanInviteMapper tmkLoanInviteMapper;
    @Resource
    private TmkVipMapper tmkVipMapper;
    @Resource
    private TmkWalletMapper tmkWalletMapper;
    @Resource
    private TmkAppointMapper tmkAppointMapper;
    @Resource
    private TmkSummaryMapper tmkSummaryMapper;
    @Resource
    private TmkUuidMapper tmkUuidMapper;
    @Resource
    private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;
    @Resource
    private CustomerServiceImpl customerService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private OpDictInfoMapper opDictInfoMapper;
    @Resource
    private NoticeMsgServiceImpl noticeMsgService;
    @Resource
    private AiTmkCallbackMapper aiTmkCallbackMapper;
    @Resource
    private AiTmkConfigMapper aiTmkConfigMapper;
    @Resource
    private AiTmkPushMapper aiTmkPushMapper;
    @Resource
    private TrUtil trUtil;

    @Value("${tr.tmk.enterpriseId}")
    private String enterpriseId;

    @Value("${tr.call.base.url}")
    private String trBaseUrl;


    private static final String TIME_EXCEPTION_MESSAGE = "温馨提示:最大查询时间跨度为31天哦~";

    @Override
    public Page<TmkTaskDetailVO> queryTmkTask(TmkReqDTO tmkReqDTO) {
        log.info("queryTmkTask start");
        long startTime = System.currentTimeMillis();
        if (Objects.isNull(tmkReqDTO) || StringUtils.isBlank(tmkReqDTO.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }
        if ("L".equals(tmkReqDTO.getQueryType())) {
            checkTime(tmkReqDTO);
        }

        if (StringUtils.isNotBlank(tmkReqDTO.getContactResult())){
            tmkReqDTO.setContactResultList(Arrays.asList(tmkReqDTO.getContactResult().split(",")));
        }

        // 根据电销任务类型选择不同的查询方法
        Page<TmkTaskDetailVO> tmkTaskList;
        tmkReqDTO.setStaffId(String.valueOf(CommonUtils.getCurrentloggedStaffId()));
        Page<TmkTaskDetailVO> pageParams = new Page<>(tmkReqDTO.getCurPage(), tmkReqDTO.getPageSize());
        String tmkType = tmkReqDTO.getTmkType();
        switch (tmkType) {
            case TmkTaskTypeConstant.CJHY:
                // 先享后付模式的成功订单不需要查询出来
                if (MemberPayModeEnum.PAY_LATER.getValue().equals(tmkReqDTO.getMemberPayMode())
                        && StringUtils.isBlank(tmkReqDTO.getOrderStatus())) {
                    tmkReqDTO.setOrderStatus(VipOrderStatusEnum.FAIL.getStatusCode());
                }
                tmkTaskList = tmkVipMapper.queryTmkVipPage(pageParams, tmkReqDTO);
                break;
            case TmkTaskTypeConstant.WALLET:
                tmkTaskList = tmkWalletMapper.queryWalletTaskListPage(pageParams, tmkReqDTO);
                break;
            case TmkTaskTypeConstant.UUID:
                pageParams.setSearchCount(false);
                tmkTaskList = tmkUuidMapper.queryUuidTaskListPage(pageParams, tmkReqDTO);
                Long total = tmkUuidMapper.queryTaskCount(tmkReqDTO);
                tmkTaskList.setTotal(total);
                tmkTaskList.setPages(getTotalPages(total, tmkTaskList.getSize()));
                break;
            default:
                tmkTaskList = tmkLoanInviteMapper.queryLoanInviteTaskPage(pageParams, tmkReqDTO);
        }
        if (Objects.isNull(tmkTaskList) || CollectionUtils.isEmpty(tmkTaskList.getRecords())){
            return tmkTaskList;
        }

        if ("L".equals(tmkReqDTO.getQueryType())) {
            for (TmkTaskDetailVO vo : tmkTaskList.getRecords()) {
                vo.setIsLock(noticeMsgService.queryTmkUnReadMsg(vo.getTmkTaskId()));
                vo.setMobile(SecurityUtil.maskMobile(vo.getMobile()));
                vo.setMemberPayMode(convertToPayModeDesc(vo.getMemberPayMode()));
                if (Objects.nonNull(vo.getAiPushTime()) && DateUtil.isToday(vo.getAiPushTime().getTime())) {
                    vo.setAiPushFlag(true);
                }
            }
        }

        log.info("queryTmkTask db query end useTime:{}", System.currentTimeMillis() - startTime);


        /*
         * 详情查询需要做特殊处理
         */
        if ("D".equals(tmkReqDTO.getQueryType())) {
            TmkTaskDetailVO vo = tmkTaskList.getRecords().get(0);

            // 设置其他个人信息
            CashCustInfoVO custInfoVO = queryCustomerIdByTmkMobile(vo.getMobile(), vo.getUserId(),
                    tmkReqDTO.getTmkType());
            vo.setId(custInfoVO.getId());
            vo.setCnid(custInfoVO.getCnid());
            vo.setGender(custInfoVO.getGender());
            vo.setAge(custInfoVO.getAge());
            vo.setMemberPayMode(convertToPayModeDesc(vo.getMemberPayMode()));
            if (TmkTaskTypeConstant.UUID.equals(tmkReqDTO.getTmkType()) || TmkTaskTypeConstant.WALLET
                    .equals(tmkReqDTO.getTmkType())) {
                vo.setAvlCredit(custInfoVO.getAvlCreditline());
                vo.setCreditLine(custInfoVO.getCreditline());
                vo.setCreditStatus(custInfoVO.getCreditState());
            }

            vo.setCreditStatus(QuotaStateEnum.getDesc(vo.getCreditStatus()));

            // 预约信息
            List<TmkAppoint> appointList = tmkAppointMapper.selectList(
                    Wrappers.lambdaQuery(TmkAppoint.class).eq(TmkAppoint::getTmkTaskId, vo.getTmkTaskId())
                            .eq(TmkAppoint::getStatus,false)
                            .orderByDesc(TmkAppoint::getAppointTime));
            if (CollectionUtils.isNotEmpty(appointList)){
                vo.setAppointTime(appointList.get(0).getAppointTime());
                vo.setAppointComment(appointList.get(0).getComment());
            }

            // 申请与审批金额之差
            if (TmkTaskTypeConstant.LOAN.equals(tmkReqDTO.getTmkType())){
                String appliedAmount = vo.getAppliedAmount();
                String amount = vo.getAmount();
                if (StringUtils.isNotBlank(appliedAmount) && StringUtils.isNotBlank(amount)){
                    BigDecimal diffAmount = new BigDecimal(appliedAmount).subtract(new BigDecimal(amount));
                    vo.setAmountDiff(diffAmount.toPlainString());
                }
            }

            // 是否注销
            if (Objects.isNull(vo.getBlocked())){
                vo.setBlocked(false);
            }
            log.info("queryTmkTask user query end useTime:{}", System.currentTimeMillis() - startTime);
        }
        return tmkTaskList;
    }

    private long getTotalPages(Long total, Long size) {
        if (total % size == 0) {
            return total / size;
        } else {
            return total / size + 1;
        }
    }

    private void checkTime(TmkReqDTO tmkReqDTO) {

        if (StringUtils.isNotBlank(tmkReqDTO.getDistributedAtStart()) && StringUtils.isNotBlank(tmkReqDTO.getDistributedAtEnd())){
            if (DateUtil.getDaysBetween(tmkReqDTO.getDistributedAtStart(), tmkReqDTO.getDistributedAtEnd()) >= MONTH_DAY) {
                throw new CrmOperateException(TIME_EXCEPTION_MESSAGE);
            }
        } else {
            throw new CrmOperateException("分配时间不能为空");
        }

    }

    /**
     * 转换付费模式的英文编码为中文描述
     *
     * @param memberPayMode 英文编码
     */
    private String convertToPayModeDesc(String memberPayMode) {
        if (StringUtils.isBlank(memberPayMode)) {
            return MemberPayModeEnum.PAY_FIRST.getDesc();
        } else {
            return MemberPayModeEnum.getDesc(memberPayMode);
        }
    }

    @Override
    public Map<String, Long> queryCountByType(TmkReqDTO tmkReqDTO) {
        // 新单数量
        long newOrderCount = 0;
        // 过期单数量
        long expireCount = 0;
        fillCountReqDTO(tmkReqDTO);
        Page<TmkTaskDetailVO> tmkTaskDetailVOPage = queryTmkTask(tmkReqDTO);
        if (Objects.nonNull(tmkTaskDetailVOPage)){
            List<TmkTaskDetailVO> records = tmkTaskDetailVOPage.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                newOrderCount = records.parallelStream()
                        .filter(item -> Objects.nonNull(item.getDistributedAt())
                                && org.apache.commons.lang3.time.DateUtils
                                .isSameDay(new Date(), item.getDistributedAt())).count();
                expireCount = records.parallelStream()
                        .filter(item -> Objects.nonNull(item.getDistributedAt())
                                && org.apache.commons.lang3.time.DateUtils
                                .isSameDay(DateUtil.plusDays(new Date(), -14), item.getDistributedAt())).count();
            }
        }
        HashMap<String, Long> map = new HashMap<>();
        map.put("newOrderCount", newOrderCount);
        map.put("expireCount", expireCount);
        return map;
    }

    private void fillCountReqDTO(TmkReqDTO tmkReqDTO) {
        tmkReqDTO.setCurPage(1);
        tmkReqDTO.setPageSize(100000);
        tmkReqDTO.setIsAge(Boolean.FALSE);
        tmkReqDTO.setQueryType("C");
        tmkReqDTO.setDistributedAtEnd(DateUtil.currentDateStr());
        tmkReqDTO.setDistributedAtStart(DateUtil.dateToString(DateUtil.plusDays(new Date(), -16)));
    }


    @Override
    public void appoint(TmkAppointReqDTO tmkAppointReqDTO) {
        // 查询该任务是否存在还未推送预约记录,有则更新，无则新增
        List<TmkAppoint> appointList = tmkAppointMapper.selectList(
                Wrappers.lambdaQuery(TmkAppoint.class).eq(TmkAppoint::getTmkTaskId, tmkAppointReqDTO.getTmkTaskId())
                        .eq(TmkAppoint::getStatus, false));
        if (CollectionUtils.isNotEmpty(appointList)){
            TmkAppoint appoint = appointList.get(0);
            if (StringUtils.isBlank(tmkAppointReqDTO.getAppointTime())){
                tmkAppointMapper.deleteById(appoint.getId());
                return;
            }
            appoint.setAppointTime(DateUtil.stringToDate(tmkAppointReqDTO.getAppointTime()));
            appoint.setComment(tmkAppointReqDTO.getComment());
            appoint.setStaffId(CommonUtils.getCurrentloggedStaffId());
            appoint.setGmtModify(new Date());
            tmkAppointMapper.updateById(appoint);
        } else {
            TmkAppoint tmkAppoint = new TmkAppoint();
            BeanUtils.copyProperties(tmkAppointReqDTO, tmkAppoint);
            tmkAppoint.setStaffId(CommonUtils.getCurrentloggedStaffId());
            tmkAppoint.setAppointTime(DateUtil.stringToDate(tmkAppointReqDTO.getAppointTime()));
            tmkAppointMapper.insert(tmkAppoint);
        }

    }

    @Override
    public void saveTmkSummary(TmkSummaryReqDTO tmkSummaryReqDTO) {
        TmkSummary tmkSummary = new TmkSummary();
        BeanUtils.copyProperties(tmkSummaryReqDTO, tmkSummary);
        tmkSummary.setStaffId(CommonUtils.getCurrentloggedStaffId());
        tmkSummaryMapper.insert(tmkSummary);

        // 更新最新电话小结Id
        updateLatestResultCode(tmkSummaryReqDTO.getTmkType(), tmkSummaryReqDTO.getTmkTaskId(), tmkSummary.getId(), null, null);

    }

    @Override
    public void updateLatestResultCode(String tmkType, String tmkTaskId, Long summaryId, Long staffId, String flag) {
    	Date currDate = new Date();
        switch (tmkType) {
            case TmkTaskTypeConstant.CJHY:
                TmkVip vip = tmkVipMapper
                        .selectOne(Wrappers.lambdaQuery(TmkVip.class).eq(TmkVip::getTmkTaskId, tmkTaskId));
                if (Objects.isNull(vip)){
                    log.warn("任务【" + tmkTaskId + "】不存在");
                    return;
                }
                if(summaryId != null) {
                	vip.setSummaryId(summaryId);
                	if (Objects.isNull(vip.getCallNum())){
                	    vip.setCallNum(0);
                    }
                	vip.setCallNum(vip.getCallNum() + 1);
                }
                if(staffId != null) {
                	vip.setStaffId(staffId);
                	vip.setFlag(flag);
                	vip.setAssignDate(currDate);
                }
                tmkVipMapper.updateById(vip);
                break;
            case TmkTaskTypeConstant.WALLET:
                TmkWallet wallet = tmkWalletMapper
                        .selectOne(Wrappers.lambdaQuery(TmkWallet.class).eq(TmkWallet::getTmkTaskId, tmkTaskId));
                if (Objects.isNull(wallet)){
                    log.warn("任务【" + tmkTaskId + "】不存在");
                    return;
                }
                if(summaryId != null) {
                	wallet.setSummaryId(summaryId);
                    if (Objects.isNull(wallet.getCallNum())){
                        wallet.setCallNum(0);
                    }
                    wallet.setCallNum(wallet.getCallNum() + 1);
                }
                if(staffId != null) {
                	wallet.setStaffId(staffId);
                	wallet.setFlag(flag);
                	wallet.setAssignDate(currDate);
                }
                tmkWalletMapper.updateById(wallet);
                break;
            case TmkTaskTypeConstant.UUID:
                TmkUuid tmkUuid = tmkUuidMapper
                        .selectOne(Wrappers.lambdaQuery(TmkUuid.class).eq(TmkUuid::getTmkTaskId, tmkTaskId));
                if (Objects.isNull(tmkUuid)){
                    log.warn("任务【" + tmkTaskId + "】不存在");
                    return;
                }
                if(summaryId != null) {
                	tmkUuid.setSummaryId(summaryId);
                    if (Objects.isNull(tmkUuid.getCallNum())){
                        tmkUuid.setCallNum(0);
                    }
                    tmkUuid.setCallNum(tmkUuid.getCallNum() + 1);
                }
                if(staffId != null) {
                	tmkUuid.setStaffId(staffId);
                	tmkUuid.setFlag(flag);
                	tmkUuid.setAssignDate(currDate);
                }
                tmkUuidMapper.updateById(tmkUuid);
                break;
            default:
                TmkLoanInvite tmkLoanInvite = tmkLoanInviteMapper.selectOne(
                        Wrappers.lambdaQuery(TmkLoanInvite.class).eq(TmkLoanInvite::getTmkTaskId, tmkTaskId));
                if (Objects.isNull(tmkLoanInvite)){
                    log.warn("任务【" + tmkTaskId + "】不存在");
                    return;
                }
                if(summaryId != null) {
                	tmkLoanInvite.setSummaryId(summaryId);
                    if (Objects.isNull(tmkLoanInvite.getCallNum())){
                        tmkLoanInvite.setCallNum(0);
                    }
                    tmkLoanInvite.setCallNum(tmkLoanInvite.getCallNum() + 1);
                }
                if(staffId != null) {
                	tmkLoanInvite.setStaffId(staffId);
                	tmkLoanInvite.setFlag(flag);
                	tmkLoanInvite.setAssignDate(currDate);
                }
                tmkLoanInviteMapper.updateById(tmkLoanInvite);
        }

    }

    @Override
    public List<TmkStaffWorkInfo> queryTmkCallInfo(TmkReportBaseReqDTO dto) {
        List<TmkStaffWorkInfo> resultList = new ArrayList<>();
        CommonUtils.checkTimeThan31(dto.getStartTime(),dto.getEndTime());

        Map<String, String> params = TrUtil.getTrBaseParams(enterpriseId, trUtil.getTokenByEnterpriseId(enterpriseId));
        if (StringUtils.isNotBlank(dto.getStaffId())){
            List<String> staffIdList = Arrays.asList(dto.getStaffId().split(","));
            List<String> cnoList = inPhoneLoginInfoMapper.queryCnoListByStaffId(staffIdList);
            if (CollectionUtils.isNotEmpty(cnoList)){
                dto.setCnos(Joiner.on(",").join(cnoList));
            } else {
                return new ArrayList<>();
            }
        } else if (StringUtils.isNotBlank(dto.getGroupCode()) && StringUtils.isBlank(dto.getStaffId())){
            List<String> groupList = Arrays.asList(dto.getGroupCode().split(","));
            List<String> cnoList = inPhoneLoginInfoMapper.queryCnoListByGroupCode(groupList);
            if (CollectionUtils.isNotEmpty(cnoList)){
                dto.setCnos(Joiner.on(",").join(cnoList));
            } else {
                return new ArrayList<>();
            }
        }
        fillOtherParams(params, dto);

        log.info("queryTmkCallInfo start 调用天润接口获取话务员工作信息");
        String result = HttpClients.create().setUrl(trBaseUrl + "/agentReport/agentWorkload").addURLParams(params).doGet();
        log.info("queryTmkCallInfo end 调用天润接口获取话务员工作信息");
        JSONObject resultJson = JSON.parseObject(result);
        if (resultJson.containsKey("result")&& "0".equals(resultJson.getString("result"))){
            List<TmkStaffWorkInfo>  tempList = JSON.parseObject(resultJson.getJSONObject("data").getString("list"), new TypeReference<List<TmkStaffWorkInfo>>() {
            });

            tempList.forEach(item ->{
                item.setObBridgeCount(item.getPreviewObAnsweredCount());
                List<InAuthCrmStaff> groupList = inPhoneLoginInfoMapper.queryGroupCodeByCno(item.getCno());
                if (CollectionUtils.isNotEmpty(groupList)){
                    item.setGroupCode(groupList.get(0).getGroupName());
                    item.setAgentName(groupList.get(0).getStaffName());
                } else {
                    item.setGroupCode("未知");
                }
            });
            // 计算团队平均
            Map<String, List<TmkStaffWorkInfo>> group = tempList.stream()
                    .collect(Collectors.groupingBy(TmkStaffWorkInfo::getGroupCode));
            for (Entry<String, List<TmkStaffWorkInfo>> entry : group.entrySet()) {
                TmkStaffWorkInfo workInfo = new TmkStaffWorkInfo();
                int loginTime = 0;
                int obCallingCount = 0;
                int obBridgeCount = 0;
                int obBridgeTime = 0;
                int obAvgBridgeTime = 0;
                int obWrapupTime = 0;
                int pauseTime = 0;
                int restTime = 0;
                workInfo.setGroupCode(entry.getKey() + "团队平均");
                for (TmkStaffWorkInfo info : entry.getValue()) {
                    loginTime += DateUtils.getSeconds(info.getLoginTime());
                    obCallingCount += Integer.parseInt(info.getObCallingCount());
                    obBridgeCount += Integer.parseInt(info.getObBridgeCount());
                    obBridgeTime += DateUtils.getSeconds(info.getObBridgeTime());
                    obAvgBridgeTime += DateUtils.getSeconds(info.getObAvgBridgeTime());
                    obWrapupTime += DateUtils.getSeconds(info.getObWrapupTime());
                    pauseTime += DateUtils.getSeconds(info.getPauseTime());
                    restTime += DateUtils.getSeconds(info.getRestTime());
                }
                workInfo.setLoginTime(DateUtils.secondsToTime(loginTime / entry.getValue().size()));
                workInfo.setObCallingCount(String.valueOf(obCallingCount / entry.getValue().size()));
                workInfo.setObBridgeCount(String.valueOf(obBridgeCount / entry.getValue().size()));
                workInfo.setObBridgeTime(DateUtils.secondsToTime(obBridgeTime / entry.getValue().size()));
                workInfo.setObAvgBridgeTime(DateUtils.secondsToTime(obAvgBridgeTime / entry.getValue().size()));
                workInfo.setObWrapupTime(DateUtils.secondsToTime(obWrapupTime / entry.getValue().size()));
                workInfo.setPauseTime(DateUtils.secondsToTime(pauseTime / entry.getValue().size()));
                workInfo.setRestTime(DateUtils.secondsToTime(restTime / entry.getValue().size()));
                tempList.add(workInfo);
            }

            resultList = tempList.parallelStream().map(this::fillWorkInfo)
                    .sorted(Comparator.comparing(TmkStaffWorkInfo::getGroupCode)).collect(Collectors.toList());
        }
        return resultList;
    }

    private TmkStaffWorkInfo fillWorkInfo(TmkStaffWorkInfo item) {

        // 接通率
        item.setAgentRate(TrUtil.calcPercentage(Integer.valueOf(item.getObBridgeCount()),Integer.valueOf(item.getObCallingCount())));
        // 总登录时长
        int loginTime = DateUtils.getSeconds(item.getLoginTime());
        // 话后处理时长
        int wrapupTIme = DateUtils.getSeconds(item.getObWrapupTime());
        // 总通话时长
        int bridgeTime = DateUtils.getSeconds(item.getObBridgeTime());
        // 工时利用率
        item.setOperationRate(TrUtil.calcPercentage(wrapupTIme + bridgeTime, loginTime));
        // 通话利用率
        item.setCallRate(TrUtil.calcPercentage(bridgeTime, loginTime));

        return item;


    }

    private void fillOtherParams(Map<String, String> params, TmkReportBaseReqDTO dto) {
        params.put("statisticMethod", "2");
        params.put("timeRangeType", "4");
        params.put("startTime", dto.getStartTime().substring(0,10));
        params.put("endTime", dto.getEndTime().substring(0,10));
        params.put("limit", "300");
        if (StringUtils.isNotBlank(dto.getCnos())){
            params.put("cnos",dto.getCnos());
        }
    }

    @Override
    public List<TmkTransformReportVO> queryTmkTransformData(TmkTransformReportReqDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }
        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        setStaffIdAndGroupCodeParams(dto);
        List<TmkTransformReportVO> tmkTransformReport = new ArrayList<>();
        switch (dto.getTmkType()) {
            case TmkTaskTypeConstant.CJHY:
                tmkTransformReport = tmkVipMapper.queryVipTransformData(dto);
                break;
            case TmkTaskTypeConstant.WALLET:
                tmkTransformReport = tmkWalletMapper.queryWalletTransformData(dto);
                break;
            case TmkTaskTypeConstant.UUID:
                tmkTransformReport = tmkUuidMapper.queryUuidTransformData(dto);
                break;
            default:
                tmkTransformReport = tmkLoanInviteMapper.queryLoanInviteTransformData(dto);
        }

       return tmkTransformReport.stream().map(this::calculateRate).collect(Collectors.toList());
    }

    private void setStaffIdAndGroupCodeParams(TmkTransformReportReqDTO dto) {
        if (StringUtils.isNotBlank(dto.getGroupCode())){
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getStaffId())){
            dto.setStaffIdList(Arrays.asList(dto.getStaffId().split(",")));
        }
    }

    private TmkTransformReportVO calculateRate(TmkTransformReportVO vo) {
        vo.setAnswerRate(TrUtil.calcPercentage(vo.getAnswerCount(), vo.getCallOutCount()));
        vo.setTransformRate(TrUtil.calcPercentage(vo.getTransformCount(), vo.getAnswerCount()));
        vo.setHisAnswerRate(TrUtil.calcPercentage(vo.getHisAnswerCount(), vo.getHisCallOutCount()));
        vo.setHisTransformRate(TrUtil.calcPercentage(vo.getHisTransformCount(), vo.getHisAnswerCount()));
        return vo;
    }



    private static final int MONTH_DAY = 31;

    @Override
    public Page<AiPushReportVO> queryAiPushDetailReport(TmkReportBaseReqDTO dto) {
        CommonUtils.checkTimeThan31(dto.getStartTime(),dto.getEndTime());
        return tmkLoanInviteMapper.queryAiPushDetail(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
    }


    @Override
    public CashCustInfoVO queryCustomerIdByTmkMobile(String mobile,Integer userId, String tmkType) {
        String type = "cash";
        if (TmkTaskTypeConstant.WALLET.equals(tmkType)){
            type = "wallet";
        }
//        CashCustInfoVO cashCustInfoVO = customerService.queryUserInfoByMobileAndType(mobile, type);
//        if (Objects.nonNull(cashCustInfoVO) && Objects.nonNull(cashCustInfoVO.getId())){
//            return cashCustInfoVO;
//        } else {
        UserDetailQueryDTO dto = new UserDetailQueryDTO();
        dto.setUserId(userId);
        if ("wallet".equals(type)) {
            WalletUserDTO walletUserDTO = userInfoService.queryWalletUser(dto);
            if (walletUserDTO != null) {
                customerService.saveUserInfoWallet(walletUserDTO);
            }
        } else {
            PersonalDetailsVoExpand result = userInfoService.queryUserInfo(dto);
            customerService.saveUserInfoCash(result);
        }
        return customerService.queryUserInfoByMobileAndType(mobile, type);
//        }
    }

    @Override
    public Page<TmkLoanReportVO> queryTmkLoanData(TmkTransformReportReqDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }

        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());

        setStaffIdAndGroupCodeParams(dto);

        Page<TmkLoanReportVO> tmkLoanReport = tmkLoanInviteMapper
                .queryTmkLoanData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
        Map<String, String> contactResultMap = CommonUtils.getContactResultMap();
        for (TmkLoanReportVO vo : tmkLoanReport.getRecords()) {
            vo.setResultCode(contactResultMap.get(vo.getResultCode()));
            if (Objects.nonNull(vo.getTimeDif())) {
                if (vo.getTimeDif() >= 0) {
                    vo.setIntervals(DateUtils.secondsToTime(vo.getTimeDif()));
                } else {
                    vo.setIntervals("-" + DateUtils.secondsToTime(Math.abs(vo.getTimeDif())));
                }
            }
        }

        return tmkLoanReport;
    }

    @Override
    public Page<TmkCjhyReportVO> queryTmkCjhyData(TmkTransformReportReqDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }
        
        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        setStaffIdAndGroupCodeParams(dto);

        Page<TmkCjhyReportVO> tmkCjhyReport = tmkVipMapper
                .queryTmkCjhyData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
        Map<String, String> contactResultMap = CommonUtils.getContactResultMap();
        for (TmkCjhyReportVO vo : tmkCjhyReport.getRecords()) {
            vo.setResultCode(contactResultMap.get(vo.getResultCode()));
            vo.setOrderdStatus(VipOrderStatusEnum.getStatusDescByCode(vo.getOrderdStatus()));
            vo.setPushStatus(VipOrderStatusEnum.getStatusDescByCode(vo.getPushStatus()));
            vo.setMemberPayMode(convertToPayModeDesc(vo.getMemberPayMode()));
        }

        return tmkCjhyReport;
    }

    @Override
    public Page<TmkWalletReportVO> queryTmkWalletData(TmkTransformReportReqDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }
        
        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        setStaffIdAndGroupCodeParams(dto);

        Page<TmkWalletReportVO> tmkWalletReport = tmkWalletMapper
                .queryTmkWalletData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);

        Map<String, String> contactResultMap = CommonUtils.getContactResultMap();
        for (TmkWalletReportVO vo : tmkWalletReport.getRecords()) {
            vo.setResultCode(contactResultMap.get(vo.getResultCode()));
        }

       return tmkWalletReport;
    }

    @Override
    public Page<TmkUuidReportVO> queryTmkUuidData(TmkTransformReportReqDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getTmkType())) {
            throw new CrmOperateException("查询参数不能为空并且电销类型不能为空");
        }
        
        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        setStaffIdAndGroupCodeParams(dto);

        Page<TmkUuidReportVO> tmkUuidReport = tmkUuidMapper
                .queryTmkUuidData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);

        Map<String, String> contactResultMap = CommonUtils.getContactResultMap();
        for (TmkUuidReportVO vo : tmkUuidReport.getRecords()) {
            vo.setResultCode(contactResultMap.get(vo.getResultCode()));
        }

        return tmkUuidReport;
    }

    @Override
    public Page<TmkAssignDetailReportVO> queryTmkAssignDetailData(TmkAssignDetailReportReqDTO dto) {

    	CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());

        if (StringUtils.isNotBlank(dto.getTmkType())){
            dto.setTmkTypeList(Arrays.asList(dto.getTmkType().split(",")));
        }

        if (StringUtils.isNotBlank(dto.getGroupCode())){
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getStaffId())){
            dto.setStaffIdList(Arrays.asList(dto.getStaffId().split(",")));
        }


        Page<TmkAssignDetailReportVO> tmkAssignDetailReport = tmkLoanInviteMapper
                .queryTmkAssignDetailData(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);
        if (Objects.nonNull(tmkAssignDetailReport)){
            List<TmkAssignDetailReportVO> records = tmkAssignDetailReport.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                for (TmkAssignDetailReportVO vo : records) {
                    vo.setDistributionType(TmkDistributionTypeEnum.getState(vo.getDistributionType()));
                }
            }
        }



       return tmkAssignDetailReport;
    }

    @Override
    public List<String> queryUuidPackageName() {
        return tmkUuidMapper.selectPackageDefine(DateUtil.plusMonths(new Date(), -1));
    }

    @Override
    public List<StaffEfficiencyVO> queryStaffEfficiencyReportRealTime(TmkReportBaseReqDTO dto) {
        if (StringUtils.isBlank(dto.getGroupCode())) {
            throw new FastRuntimeException("组别不能为空");
        }
        List<String> groupList = Arrays.asList(dto.getGroupCode().split(","));

        List<StaffEfficiencyVO> staffEfficiencyVOS = inPhoneLoginInfoMapper
                .queryStaffEfficiency(dto.getStartTime(), dto.getEndTime(), groupList);
        staffEfficiencyVOS.forEach(item -> {
            item.setAvgCalloutTime(
                    new BigDecimal(item.getAvgCalloutTime()).setScale(2, RoundingMode.HALF_UP).toPlainString());
            item.setCallTimeRate(new BigDecimal(item.getCallTimeRate()).multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
            item.setWorkTimeRate(new BigDecimal(item.getWorkTimeRate()).multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
            item.setBridgeRate(new BigDecimal(item.getBridgeRate()).multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP) + "%");
        });
        Map<String, List<StaffEfficiencyVO>> groupMap = staffEfficiencyVOS.stream()
                .collect(Collectors.groupingBy(StaffEfficiencyVO::getGroupName));
        for (Entry<String, List<StaffEfficiencyVO>> entry : groupMap.entrySet()) {
            StaffEfficiencyVO summaryVo = new StaffEfficiencyVO();
            summaryVo.setGroupName(entry.getKey() + " 团队平均");

            int acwTime = 0;
            int busyTime = 0;
            int breakTime = 0;
            int callTime = 0;
            int avgCalloutCount = 0;
            int avgBridgeCount = 0;
            int loginTime = 0;
            int ibTime = 0;
            BigDecimal avgCalloutTime = BigDecimal.ZERO;
            for (StaffEfficiencyVO vo : entry.getValue()) {
                if (StringUtils.isNotBlank(vo.getAvgCalloutTime())) {
                    avgCalloutTime = avgCalloutTime.add(new BigDecimal(vo.getAvgCalloutTime()));
                }
                acwTime += Integer.parseInt(vo.getAcwTime());
                busyTime += Integer.parseInt(vo.getBusyTime());
                breakTime += Integer.parseInt(vo.getBreakTime());
                callTime += Integer.parseInt(vo.getCallTime());
                avgCalloutCount += Integer.parseInt(vo.getAvgCalloutCount());
                avgBridgeCount += Integer.parseInt(vo.getAvgBridgeCount());
                loginTime += Integer.parseInt(vo.getLoginTime());
                ibTime += Integer.parseInt(vo.getIbTime());
            }
            int size = entry.getValue().size();
            summaryVo.setAvgCalloutTime(
                    avgCalloutTime.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP).toPlainString());
            summaryVo.setAcwTime(CommonUtil.divide(acwTime, size) + "");
            summaryVo.setBusyTime(CommonUtil.divide(busyTime, size) + "");
            summaryVo.setBreakTime(CommonUtil.divide(breakTime, size) + "");
            summaryVo.setCallTime(CommonUtil.divide(callTime, size) + "");
            summaryVo.setAvgCalloutCount(CommonUtil.divide(avgCalloutCount, size) + "");
            summaryVo.setAvgBridgeCount(CommonUtil.divide(avgBridgeCount, size) + "");
            summaryVo.setLoginTime(CommonUtil.divide(loginTime, size) + "");
            summaryVo.setBridgeRate(CommonUtil.calcPercentage(avgBridgeCount, avgCalloutCount));
            summaryVo.setWorkTimeRate(CommonUtil.calcPercentage(ibTime + callTime + acwTime, loginTime));
            summaryVo.setCallTimeRate(CommonUtil.calcPercentage(ibTime + callTime, loginTime));

            staffEfficiencyVOS.add(summaryVo);
        }
        return staffEfficiencyVOS.stream().sorted(Comparator.comparing(StaffEfficiencyVO::getGroupName))
                .collect(Collectors.toList());

    }

    @Override
    public List<AiTransformReportVO> queryAiTransformReport(TmkReportBaseReqDTO dto) {
        List<AiTransformReportVO> resultList = aiTmkCallbackMapper
                .queryAiTransformData(DateUtil.stringToDate(dto.getStartTime()),
                        DateUtil.stringToDate(dto.getEndTime()), dto.getRuleId());
        // 查询推送总数
        Integer callOutNum = aiTmkPushMapper.queryTotalPushCount(dto.getStartTime(), dto.getEndTime(), dto.getRuleId());
        for (AiTransformReportVO vo : resultList) {
            vo.setCallOutNum(callOutNum);
            vo.setConnectedRate(CommonUtil.calcPercentage(vo.getConnectedNum(),vo.getCallOutNum()));
            vo.setTransformRate(CommonUtil.calcPercentage(vo.getTransformNum(),vo.getCallOutNum()));
        }

        return resultList;
    }

    @Override
    public List<AiTmkConfigVO> queryAllAiConfig() {
        List<AiTmkConfig> aiTmkConfigs = aiTmkConfigMapper
                .selectList(Wrappers.<AiTmkConfig>lambdaQuery().select(AiTmkConfig::getId, AiTmkConfig::getRuleName));
        return aiTmkConfigs.stream().map(item -> {
            AiTmkConfigVO vo = new AiTmkConfigVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
    }
}

