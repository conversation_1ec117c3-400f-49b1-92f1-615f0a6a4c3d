package com.welab.crm.operate.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.WoRuleInfo;
import com.welab.crm.operate.domain.WoTask;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleAdjustReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleReqDTO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleVO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO;

/**
 * <p>
 * 工单规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface WoRuleInfoMapper extends BaseMapper<WoRuleInfo> {
	Page<WorkOrderRuleVO> queryWorkOrderRuleByPage(Page<WorkOrderRuleVO> page, @Param("reqDTO") WorkOrderRuleReqDTO reqDTO);
	
	Page<WorkOrderRuleAdjustVO> queryWorkOrderAdjustCountByPage(Page<WorkOrderRuleAdjustVO> page, @Param("reqDTO") WorkOrderRuleAdjustReqDTO reqDTO);
	
	Page<WorkOrderRuleAdjustVO> queryWorkOrderAdjustListByPage(Page<WorkOrderRuleAdjustVO> page, @Param("reqDTO") WorkOrderRuleAdjustReqDTO reqDTO);
	
	List<WorkOrderRuleAdjustVO> queryWorkOrderAdjustList(@Param("reqDTO") WorkOrderRuleAdjustReqDTO reqDTO);
	
	Integer updateWorkOrderRuleAll(@Param("status") String status, @Param("ruleType") String ruleType);
	
	List<WorkOrderTypeTotalVO> typeTotalWorkOrder();
	
	Integer updateWorkOrderInfo(@Param("orderNo") String orderNo);
	
	String queryWorkOrderNoByTaskId(@Param("taskId") String taskId);

	/**
	 * 根据工单号查询流程ID
	 * @param orderNo
	 * @return
	 */
	String queryExeIdByOrderNo(@Param("orderNo") String orderNo);
}
