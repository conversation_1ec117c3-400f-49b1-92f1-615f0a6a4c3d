package com.welab.crm.operate.service;

import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.operate.dto.customer.AddCustReqDTO;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;

import java.util.List;

/**
 *
 * 客服客户数据服务类
 * <AUTHOR>
 * @date 2021/11/5 15:59
 */
public interface CustomerService {


    /**
     * 查询用户是否在客服库中存在
     * @param mobile 手机号
     * @param type 客户类型，钱包为 wallet，现金贷：cash
     * @return
     */
    boolean isUserExist(String mobile,String type);


    /**
     * 保存现金贷用户信息
     * @param userInfo
     * @return 客户表主键Id
     */
    Long saveUserInfoCash(PersonalDetailsVoExpand userInfo);


    /**
     * 保存钱包用户信息
     * @param walletUserInfo
     * @return
     */
    Long saveUserInfoWallet(WalletUserDTO walletUserInfo);


    /**
     * 新增或者保存用户信息
     * @param addCustReqDTO
     * @return
     */
    Boolean saveUserInfo(AddCustReqDTO addCustReqDTO);


    /**
     * 查询客服数据库客户信息
     * @param mobile
     * @return
     */
    CashCustInfoVO queryUserInfoCash(String mobile);


    /**
     * 根据手机号和类型查询用户
     * @param mobile
     * @param type
     * @return
     */
    CashCustInfoVO queryUserInfoByMobileAndType(String mobile, String type);


    /**
     * 查询用户的uuid
     * @param mobile 用户手机号码
     */
    Long queryUserUuidByMobile(String mobile);

    /**
     * 查询用户的所有手机号
     *
     * @param uuid 用户uuid
     * @param mobile 未注册用户手机号码
     */
    List<CashCustInfoVO> queryMobileByUuid(String uuid, String mobile);
}
