package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpCustomerComplain;
import com.welab.crm.operate.dto.workorder.WorkOrderComplainDTO;
import com.welab.crm.operate.vo.workorder.WorkOrderComplainVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户投诉反馈表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-06-02
 */
public interface OpCustomerComplainMapper extends BaseMapper<OpCustomerComplain> {

    Page<WorkOrderComplainVO> selectComplainsList(Page<OpCustomerComplain> page, @Param("filter") WorkOrderComplainDTO reqDTO);
}
