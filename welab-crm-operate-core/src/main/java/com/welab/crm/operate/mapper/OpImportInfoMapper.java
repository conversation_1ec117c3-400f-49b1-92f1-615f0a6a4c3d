package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpImportInfo;
import com.welab.crm.operate.dto.loan.LoanImportDTO;
import com.welab.crm.operate.vo.loan.LoanImportLabelVO;
import com.welab.crm.operate.vo.loan.LoanImportVO;

/**
 * <p>
 * 导入文件信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
public interface OpImportInfoMapper extends BaseMapper<OpImportInfo> {
	Page<LoanImportVO> selectImportByPage(Page<LoanImportVO> objectPage, @Param("filter") LoanImportDTO dto);
	
	LoanImportLabelVO getImportLabelInfo(@Param("applicationId") String applicationId);
}
