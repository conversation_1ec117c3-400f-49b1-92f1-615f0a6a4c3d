package com.welab.crm.operate.service.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Stopwatch;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import com.welab.crm.operate.domain.IvrKeyDict;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.mapper.IvrKeyDictMapper;
import com.welab.crm.operate.mapper.IvrSummaryMapper;
import com.welab.crm.operate.service.IvrReportService;
import com.welab.crm.operate.service.UploadAndNoticeService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.ivr.*;
import com.welab.exception.FastRuntimeException;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description: ivr报表接口实现类
 * @date 2022/3/8 16:52
 */
@Service
@Slf4j
public class IvrReportServiceImpl implements IvrReportService {

    @Resource
    private IvrSummaryMapper ivrSummaryMapper;
    @Resource
    private IvrKeyDictMapper ivrKeyDictMapper;
    
    @Resource
    private UploadAndNoticeService uploadAndNoticeService;

    private static final List<String> IVR_TYPE_LIST = new ArrayList<>(
        Arrays.asList("registered", "overdue", "ds", "zl", "wallet", "jiafen"));

    private static final String GXD_HOTLINE = "10100518";
    private static final String DS_HOTLINE = "4006000799";
    private static final String WALLET_HOTLINE = "4006040888";
    private static final String JIAFEN_HOTLINE = "4000666730";

    private static Integer COMMON_FIELD_COUNT = 11;
    private static Integer REGISTERED_COUNT;
    private static Integer OVERDUE_COUNT;
    private static Integer TRANSFER_COUNT;
    private static Integer NOT_REGISTER_COUNT;
    private static Integer VIP_FIELD_COUNT;
    
    private static Integer DS_FIELD_COUNT;
    private static Integer ZL_FIELD_COUNT;
    private static Integer WALLET_FIELD_COUNT;
    private static Integer JIAFEN_FIELD_COUNT;

    private static final List<String> TITLE_LIST =
        Arrays.asList("服务热线", "用户类型", "业务一类", "总按键合计数", "转人工数", "分流率", "业务二类", "总按键数", "再次转人工数", "分流率");

    private static final List<String> GXD_TITLE_1 = Arrays
            .asList("贷款申请", "放款问题咨询" , "还款相关问题", "个人信息修改");

    private static final List<String> GXD_TITLE_2 = Arrays
            .asList("无法提交申请",
                    "额度咨询",
                    "额度冻结",
                    "提现火爆",
                    "放款时效",
                    "提现页面异常",
                    "订单取消",
                    "解绑或换卡",
                    "延期还款咨询",
                    "还款失败原因",
                    "还款页面问题",
                    "开具结清证明",
                    "全额结清",
                    "修改手机号",
                    "注销账号",
                    "其他信息修改",
                    "协商还款",
                    "银行卡冻结或卡受限",
                    "还款方案核实",
                    "投诉催收",
                    "债转公司咨询",
                    "订单状态更新",
                    "还款方案核实",
                    "/",
                    "/"
                    );
    private static final List<String> DS_TITLE = Arrays
            .asList("产品介绍", "物流及订单状态", "商家入驻", "产品介绍", "租赁计费问题", "绑卡及发货问题", "还款问题", "订单状态查询");
    private static final List<String> WALLET_TITLE = Arrays
            .asList("产品介绍", "额度申请", "审核周期", "分期咨询", "绑卡咨询", "还款咨询", "账单查询");
    private static final List<String> JIAFEN_TITLE = Arrays
            .asList("开户问题", "更新风险测评", "银行卡支付额度问题", "跨组合支付失败", "基金确认时间", "基金赎回到账时间", "愿望功能", "新手福利", "愿望功能", "现金钱包", "目标盈");


    /**
     * 转人工键作为叶子节点的按键类型
     */
    private static final List<String> artificalAsEndType = Arrays.asList("overdue", "transfer","vip","notRegistered");

    /**
     * 初始化各个业务字段的数量
     */
    @PostConstruct
    public void initFieldCount(){
        List<IvrKeyDict> ivrKeyDicts = ivrKeyDictMapper.selectList(new QueryWrapper<>());
        Map<String, List<IvrKeyDict>> map = ivrKeyDicts.stream()
                .collect(Collectors.groupingBy(IvrKeyDict::getType));
        REGISTERED_COUNT = Math.toIntExact(map.get("registered").stream()
            .filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).count());
        OVERDUE_COUNT = Math.toIntExact(map.get("overdue").stream()
                .filter(item -> Boolean.TRUE.equals(item.getIsEndNode())).count());
        TRANSFER_COUNT = Math.toIntExact(map.get("transfer").stream()
                .filter(item -> Boolean.TRUE.equals(item.getIsEndNode())).count());
        VIP_FIELD_COUNT = Math
                .toIntExact(map.get("vip").stream().filter(item -> Boolean.TRUE.equals(item.getIsEndNode())).count());
        NOT_REGISTER_COUNT = Math
                .toIntExact(map.get("notRegistered").stream().filter(item -> Boolean.TRUE.equals(item.getIsEndNode())).count());
        
        

        DS_FIELD_COUNT = Math.toIntExact(map.get("ds").stream().filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).count());
        ZL_FIELD_COUNT = Math.toIntExact(map.get("zl").stream().filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).count());
        WALLET_FIELD_COUNT = Math.toIntExact(map.get("wallet").stream().filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).count());
        // 嘉纷工作日和休息日按键不一样，所以真实数量要除以2
        JIAFEN_FIELD_COUNT = Math.toIntExact(map.get("jiafen").stream().filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).count() / 2);
    }

    @Override
    public Page<IvrKeyDetailVO> ivrKeyDetailReport(IvrReportReqDTO ivrReportReqDTO) {
        log.info("ivrKeyDetailReport params:{}", JSON.toJSONString(ivrReportReqDTO));
        List<IvrKeyDict> ivrKeyDict = getIvrKeyDictByHotline(ivrReportReqDTO.getHotline(), true);
        if (CollectionUtils.isEmpty(ivrKeyDict)) {
            throw new FastRuntimeException("该热线号码无按键数据");
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        Page<IvrKeyDetailVOPlus> ivrDetailPlus = ivrSummaryMapper.getPhoneInfo(
            new Page<>(ivrReportReqDTO.getCurrentPage(), ivrReportReqDTO.getRowsPerPage()), ivrReportReqDTO);
        if (CollectionUtils.isEmpty(ivrDetailPlus.getRecords())){
            return new Page<>();
        }
        log.info("ivrKeyDetailReport getPhoneInfo use time:{} s", stopwatch.elapsed(TimeUnit.SECONDS));
        List<IvrKeyDetailVOPlus> ivrList = new ArrayList<>();
        if (ivrReportReqDTO.getExport()){
            ivrList = ivrSummaryMapper.queryIvrDetailByCondition(ivrKeyDict, ivrReportReqDTO);
        } else {
            List<String> idList = ivrDetailPlus.getRecords().stream().map(IvrKeyDetailVOPlus::getRecordId).collect(Collectors.toList());
            ivrList = ivrSummaryMapper.queryIvrDetailByMainId(ivrKeyDict, idList);
        }
        log.info("ivrKeyDetailReport queryIvr use time:{} s", stopwatch.elapsed(TimeUnit.SECONDS));
        mergeData(ivrDetailPlus, ivrList);
        log.info("ivrKeyDetailReport mergeData use time:{} s", stopwatch.elapsed(TimeUnit.SECONDS));
        Page<IvrKeyDetailVO> ivrDetail = new Page<>();
        if (CollectionUtils.isNotEmpty(ivrDetailPlus.getRecords())) {
            BeanUtils.copyProperties(ivrDetailPlus, ivrDetail, "records");
            List<IvrKeyDetailVO> records = ivrDetailPlus.getRecords().stream()
                .map(item -> buildIvrDetail(item, ivrReportReqDTO.getHotline())).collect(Collectors.toList());
            ivrDetail.setRecords(records);
        }
        // 获取合计数据
        IvrKeyDetailVOPlus total = ivrSummaryMapper.countIvrDetailByDict(ivrKeyDict, ivrReportReqDTO);
        if (CollectionUtils.isNotEmpty(ivrDetail.getRecords())) {
            ivrDetail.getRecords().add(buildIvrDetail(total, ivrReportReqDTO.getHotline()));
        }
        log.info("ivrKeyDetailReport countTotal use time:{} s", stopwatch.elapsed(TimeUnit.SECONDS));
        return ivrDetail;
    }

    private void mergeData(Page<IvrKeyDetailVOPlus> phoneInfoPage, List<IvrKeyDetailVOPlus> ivrList) {
        Map<String, List<IvrKeyDetailVOPlus>> recordIdMap =
            ivrList.stream().filter(item -> StringUtils.isNotBlank(item.getRecordId()))
                .collect(Collectors.groupingBy(IvrKeyDetailVOPlus::getRecordId));
        List<IvrKeyDetailVOPlus> phoneList = phoneInfoPage.getRecords();
        phoneList.forEach(item -> {
            List<IvrKeyDetailVOPlus> list = recordIdMap.get(item.getRecordId());
            if (CollectionUtils.isNotEmpty(list)) {
                BeanUtils.copyProperties(list.get(0), item, CommonUtils.getNullPropertyNames(list.get(0)));
            }
        });
    }
    

    private IvrKeyDetailVO buildIvrDetail(IvrKeyDetailVOPlus item, String hotLine) {

        IvrKeyDetailVO vo = new IvrKeyDetailVO();
        try {

            BeanUtils.copyProperties(item, vo);
            if (JIAFEN_HOTLINE.equals(hotLine)) {
                vo.setJiafenZf1(item.getJiafenZf1Work() + item.getJiafenZf1Free());
                vo.setJiafenZf2(item.getJiafenZf2Work() + item.getJiafenZf2Free());
                vo.setJiafenJy1(item.getJiafenJy1Work() + item.getJiafenJy1Free());
                vo.setJiafenJy2(item.getJiafenJy2Work() + item.getJiafenJy2Free());
                vo.setJiafenJy3(item.getJiafenJy3Work() + item.getJiafenJy3Free());
                vo.setJiafenJy4(item.getJiafenJy4Work() + item.getJiafenJy4Free());
                vo.setJiafenHd1(item.getJiafenHd1Work() + item.getJiafenHd1Free());
                vo.setJiafenHd2(item.getJiafenHd2Work() + item.getJiafenHd2Free());
                vo.setJiafenGn1(item.getJiafenGn1Work() + item.getJiafenGn1Free());
                vo.setJiafenGn2(item.getJiafenGn2Work() + item.getJiafenGn2Free());
                vo.setJiafenGn3(item.getJiafenGn3Work() + item.getJiafenGn3Free());
            }
        } catch (Exception e) {
            log.warn("buildIvrDetail error", e);
        }

        return vo;
    }

    /**
     * 是否叶子节点
     * @param hotline
     * @param isEndNode
     * @return
     */
    private List<IvrKeyDict> getIvrKeyDictByHotline(String hotline,Boolean isEndNode) {
        return ivrKeyDictMapper.selectList(
                Wrappers.lambdaQuery(IvrKeyDict.class)
                        .eq(StringUtils.isNotBlank(hotline), IvrKeyDict::getHotline, hotline)
                        .eq(Objects.nonNull(isEndNode), IvrKeyDict::getIsEndNode, isEndNode)
                        .orderByAsc(IvrKeyDict::getCode));
    }

    @Override
    public IvrStatisticsReportVO ivrShuntStatisticsReport(IvrReportReqDTO ivrReportReqDTO) {
        ivrReportReqDTO.setHotlineList(Arrays.asList(ivrReportReqDTO.getHotline().split(",")));
        IvrStatisticsReportVO reportVO = new IvrStatisticsReportVO();
        // 按键总数map
        Map<String, IvrStatisticsArtificialVO> ivrTotalMap = new HashMap<>();
        // 再次转人工数map
        Map<String, IvrStatisticsArtificialVO> ivrArtMap = new HashMap<>();
        // 分流率
        Map<String, IvrStatisticsArtificialVO> shuntRateMap = new HashMap<>();
        // 父节点总按键数
        Map<String, IvrStatisticsArtificialVO> parentTotalMap = new HashMap<>();
        // 父节点转人工数
        Map<String, IvrStatisticsArtificialVO> parentArtMap = new HashMap<>();
        // 父节点分流率
        Map<String, IvrStatisticsArtificialVO> parentShuntMap = new HashMap<>();

        // 计算叶子节点总按键数
        buildIvrTotalMap(ivrTotalMap, ivrReportReqDTO);
        // 计算叶子节点转人工数
        buildIvrArtMap(ivrReportReqDTO, ivrArtMap);
        // 叶子节点部分字段特殊处理
        buildSpecialField2(ivrTotalMap, ivrArtMap);

        // 计算父节点的总按键数
        buildParentTotalMap(parentTotalMap, ivrReportReqDTO);
        // 计算父节点转人工数，直接把子节点相加就行
        buildParentArtMap(parentArtMap, ivrArtMap);
        // 计算贷后服务的转人工数
//        queryDhfwArtificialCount(ivrTotalMap, ivrReportReqDTO, parentTotalMap, parentArtMap, ivrArtMap);
        // 计算父节点分流率
        buildParentShuntRate(parentTotalMap, parentArtMap, parentShuntMap);
        // 子节点分流率
        buildShuntMap(ivrTotalMap, ivrArtMap, shuntRateMap);
        
        reportVO.setTotalMap(ivrTotalMap);
        reportVO.setArtificialMap(ivrArtMap);
        reportVO.setShuntRateMap(shuntRateMap);
        reportVO.setTotalMapFirst(parentTotalMap);
        reportVO.setArtificialMapFirst(parentArtMap);
        reportVO.setShuntRateMapFirst(parentShuntMap);

        return reportVO;
    }

    private void queryDhfwArtificialCount(Map<String, IvrStatisticsArtificialVO> ivrTotalMap,
                                          IvrReportReqDTO ivrReportReqDTO, Map<String, IvrStatisticsArtificialVO> parentTotalMap,
                                          Map<String, IvrStatisticsArtificialVO> parentArtMap, Map<String, IvrStatisticsArtificialVO> ivrArtMap) {

        // 将IVRTotalMap 中的 dhfw 数据迁移到 parentTotalMap
//        for (Entry<String, IvrStatisticsArtificialVO> parentTotalEntry : parentTotalMap.entrySet()) {
//            String callTime = parentTotalEntry.getKey();
//            IvrStatisticsArtificialVO parentTotalVO = parentTotalEntry.getValue();
//            IvrStatisticsArtificialVO ivrTotalVO = ivrTotalMap.get(callTime);
//            if (Objects.nonNull(parentTotalVO) && Objects.nonNull(ivrTotalVO)){
//                parentTotalVO.setDhfw(ivrTotalVO.getDhfw());
//                parentTotalVO.setTotal(CommonUtil.sumString(parentTotalVO.getTotal(), ivrTotalVO.getDhfw()) + "");
//                ivrTotalVO.setDhfw("/");
//            }
//        }
//
//        for (Entry<String, IvrStatisticsArtificialVO> entry : ivrArtMap.entrySet()) {
//            IvrStatisticsArtificialVO vo = entry.getValue();
//            if (Objects.nonNull(vo)) {
//                vo.setDhfw("/");
//            }
//        }
//
//        if (!ivrReportReqDTO.getHotlineList().contains(GXD_HOTLINE)) {
//            return;
//        }
//        
//
//        List<IvrStatisticsArtificialVO> dhfwCountList = ivrSummaryMapper.queryDhfwArtificialCount(ivrReportReqDTO);
//        if (CollectionUtils.isNotEmpty(dhfwCountList)) {
//            Map<String, String> dhfwCountMap = dhfwCountList.stream()
//                .collect(Collectors.toMap(IvrStatisticsArtificialVO::getCallTime, IvrStatisticsArtificialVO::getDhfw));
//            for (Entry<String, IvrStatisticsArtificialVO> parentArtEntry : parentArtMap.entrySet()) {
//                String callTime = parentArtEntry.getKey();
//                IvrStatisticsArtificialVO vo = parentArtEntry.getValue();
//                String count = dhfwCountMap.get(callTime);
//                if (Objects.nonNull(vo)){
//                    vo.setDhfw(count);
//                    vo.setTotal(CommonUtil.sumString(count, vo.getTotal()) + "");
//                }
//            }
//        }
    }

    private void buildSpecialField2(Map<String, IvrStatisticsArtificialVO> ivrTotalMap,
            Map<String, IvrStatisticsArtificialVO> ivrArtMap) {
        // 设置一些固定数据
        for (Entry<String, IvrStatisticsArtificialVO> entry : ivrTotalMap.entrySet()) {
            String callTime = entry.getKey();
            IvrStatisticsArtificialVO totalVO = entry.getValue();
            // 这几个按键本身就是转人工按键，所以总按键数跟转人工数应该相等
            IvrStatisticsArtificialVO ivrArtVO = ivrArtMap.get(callTime);
            if (Objects.isNull(ivrArtVO)){
                ivrArtVO = new IvrStatisticsArtificialVO();
            }
            ivrArtVO.setRegistered11("/");
            ivrArtVO.setRegistered12("/");
            ivrArtVO.setRegistered13("/");
            ivrArtVO.setRegistered21("/");
            ivrArtVO.setRegistered22("/");
            ivrArtVO.setRegistered24("/");
            ivrArtVO.setRegistered31("/");
            ivrArtVO.setRegistered32("/");
            ivrArtVO.setRegistered41("/");
            ivrArtVO.setRegistered43("/");
            ivrArtVO.setOverdue1("/");
            ivrArtVO.setTransfer1("/");
            ivrArtVO.setTransfer2("/");
            // 下面这些按键再次转人工数跟总按键数是一样的，因为这些按键是直接转人工的
            if (Objects.nonNull(totalVO)) {
                ivrArtVO.setOverdue3(totalVO.getOverdue3());
                ivrArtVO.setOverdue4(totalVO.getOverdue4());
                ivrArtVO.setTransfer3(totalVO.getTransfer3());
                ivrArtVO.setVip1(totalVO.getVip1());
                ivrArtVO.setNotRegistered1(totalVO.getNotRegistered1());
                ivrArtVO.setTotal(CommonUtil.sumString( totalVO.getOverdue3(),
                    totalVO.getOverdue4(), totalVO.getTransfer3(), totalVO.getVip1(), totalVO.getNotRegistered1(), ivrArtVO.getRegistered23(),
                    ivrArtVO.getRegistered34(), ivrArtVO.getRegistered35(), ivrArtVO.getRegistered36(),
                    ivrArtVO.getRegistered33(), ivrArtVO.getRegistered42(), ivrArtVO.getOverdue2()) + "");
            }

            ivrArtMap.put(callTime, ivrArtVO);

        }

    }

    private void buildParentArtMap(Map<String, IvrStatisticsArtificialVO> parentArtMap,
            Map<String, IvrStatisticsArtificialVO> ivrArtMap) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        for (Entry<String, IvrStatisticsArtificialVO> artEntry : ivrArtMap.entrySet()) {
            IvrStatisticsArtificialVO vo = new IvrStatisticsArtificialVO();
            IvrStatisticsArtificialVO ivrVo = artEntry.getValue();
            vo.setRegistered11("/");
            vo.setRegistered21(Optional.ofNullable(ivrVo.getRegistered23()).orElse("0"));
            vo.setRegistered31(String.valueOf(CommonUtil.sumString(ivrVo.getRegistered33(), ivrVo.getRegistered34(),
                    ivrVo.getRegistered35(), ivrVo.getRegistered36())));
            vo.setRegistered41(Optional.ofNullable(ivrVo.getRegistered42()).orElse("0"));
            vo.setTotal(String.valueOf(CommonUtil.sumString(vo.getRegistered21(),vo.getRegistered31(),vo.getRegistered41())));
            setDiagonal(vo);

            parentArtMap.put(artEntry.getKey(), vo);
        }
        log.info("buildParentArtMap use time:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private void buildParentTotalMap(Map<String, IvrStatisticsArtificialVO> parentTotalMap,
            IvrReportReqDTO ivrReportReqDTO) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<IvrKeyDict> parentIvrKey = getIvrKeyDictByHotline("", false);
        // 将父节点的code转换为子节点的code
        parentIvrKey.forEach(key -> key.setCode(key.getCode() + "_1"));
        List<IvrStatisticsArtificialVOPlus> parentTotalCount = ivrSummaryMapper
                .getIvrKeyTotalByDict(parentIvrKey, ivrReportReqDTO);
        if (CollectionUtils.isNotEmpty(parentTotalCount)) {
            for (IvrStatisticsArtificialVOPlus item : parentTotalCount) {
                IvrStatisticsArtificialVO vo = new IvrStatisticsArtificialVO();
                BeanUtils.copyProperties(item, vo);
                setDiagonal(vo);
                parentTotalMap.put(vo.getCallTime(), vo);
            }
        }

        log.info("buildParentTotalMap use time:{}ms", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private void buildIvrTotalMap(Map<String, IvrStatisticsArtificialVO> ivrTotalMap,
            IvrReportReqDTO ivrReportReqDTO) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        // 查询按键字典表，查询出全部叶子节点并且不为转人工的案件，如果是会员的按键，需要计算转人工
        List<IvrKeyDict> ivrkeyDict = getIvrKeyDictByHotline("",true).stream()
                .filter(item -> "0".equals(item.getIsArtifical()) || artificalAsEndType.contains(item.getType())).collect(
                        Collectors.toList());
        // 根据字典表查询出各个按键的数量
        List<IvrStatisticsArtificialVOPlus> ivrKeyTotal = ivrSummaryMapper
                .getIvrKeyTotalByDict(ivrkeyDict, ivrReportReqDTO);

        // 根据日期分组
        if (CollectionUtils.isNotEmpty(ivrKeyTotal)) {
            for (IvrStatisticsArtificialVOPlus item : ivrKeyTotal) {
                IvrStatisticsArtificialVO vo = new IvrStatisticsArtificialVO();
                BeanUtils.copyProperties(item, vo);
                vo.setJiafenZf1(item.getJiafenZf1Work() + item.getJiafenZf1Free() + "");
                vo.setJiafenZf2(item.getJiafenZf2Work() + item.getJiafenZf2Free() + "");
                vo.setJiafenJy1(item.getJiafenJy1Work() + item.getJiafenJy1Free() + "");
                vo.setJiafenJy2(item.getJiafenJy2Work() + item.getJiafenJy2Free() + "");
                vo.setJiafenJy3(item.getJiafenJy3Work() + item.getJiafenJy3Free() + "");
                vo.setJiafenJy4(item.getJiafenJy4Work() + item.getJiafenJy4Free() + "");
                vo.setJiafenHd1(item.getJiafenHd1Work() + item.getJiafenHd1Free() + "");
                vo.setJiafenHd2(item.getJiafenHd2Work() + item.getJiafenHd2Free() + "");
                vo.setJiafenGn1(item.getJiafenGn1Work() + item.getJiafenGn1Free() + "");
                vo.setJiafenGn2(item.getJiafenGn2Work() + item.getJiafenGn2Free() + "");
                vo.setJiafenGn3(item.getJiafenGn3Work() + item.getJiafenGn3Free() + "");
                ivrTotalMap.put(vo.getCallTime(), vo);
            }
        }

        log.info("buildIvrTotalMap use time:{}ms", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private void buildShuntMap(Map<String, IvrStatisticsArtificialVO> ivrTotalMap,
            Map<String, IvrStatisticsArtificialVO> ivrArtMap, Map<String, IvrStatisticsArtificialVO> shuntRateMap) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            for (String callTime : ivrTotalMap.keySet()) {
                IvrStatisticsArtificialVO shuntRateVo = new IvrStatisticsArtificialVO();
                IvrStatisticsArtificialVO totalVO = ivrTotalMap.get(callTime);
                if (ivrArtMap.containsKey(callTime)) {
                    IvrStatisticsArtificialVO artVO = ivrArtMap.get(callTime);
                    Field[] fields = IvrStatisticsArtificialVO.class.getDeclaredFields();
                    for (int i = 2; i < fields.length; i++) {
                        Field field = fields[i];
                        ReflectionUtils.makeAccessible(field);


                        if (Objects.isNull(field.get(artVO))) {
                            ReflectionUtils.setField(field, artVO, "0");
                        }

                        if ("/".equals(field.get(artVO).toString())){
                            ReflectionUtils.setField(field,shuntRateVo, "100.00%");
                            continue;
                        }
                        Integer artCount = Integer.valueOf((String) field.get(artVO));
                        Integer totalCount = Integer.valueOf((String) field.get(totalVO));
                        String rate = TrUtil.calcPercentage(totalCount - artCount, totalCount);
                        ReflectionUtils.setField(field, shuntRateVo, rate);
                    }
                } else {
                    Field[] fields = IvrStatisticsArtificialVO.class.getDeclaredFields();
                    for (int i = 2; i < fields.length; i++) {
                        Field field = fields[i];
                        ReflectionUtils.makeAccessible(field);
                        if ("0".equals(field.get(totalVO))) {
                            ReflectionUtils.setField(field, shuntRateVo, "0.00%");
                        } else {
                            ReflectionUtils.setField(field, shuntRateVo, "100.00%");
                        }


                    }
                }
                shuntRateMap.put(callTime, shuntRateVo);
            }



        } catch (Exception e) {
            log.error("ivrShuntStatisticsReport ivr分流报表计算分流率异常", e);
        }

        log.info("buildShuntMap use time:{}ms",stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));

    }

    private void buildIvrArtMap(IvrReportReqDTO ivrReportReqDTO, Map<String, IvrStatisticsArtificialVO> ivrArtMap) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        for (String type : IVR_TYPE_LIST) {

            // 根据Type，查询各个业务类型的按键字典
            List<IvrKeyDict> ivrKeyDictList = getIvrKeyDictByType(type, ivrReportReqDTO.getHotlineList());
            if (CollectionUtils.isEmpty(ivrKeyDictList)) {
                continue;
            }
            // 获取转人工的按键值作为查询参数
            List<String> artificialKey = ivrKeyDictList.stream().filter(item -> "1".equals(item.getIsArtifical()))
                    .map(IvrKeyDict::getPressKey).collect(Collectors.toList());
            ivrReportReqDTO.setArtificialKey(artificialKey);
            // 设置热线参数
            ivrReportReqDTO.setHotline(ivrKeyDictList.get(0).getHotline());
            // 遍历非人工按键，获取在非人工按键之后转人工的数量
            ivrKeyDictList.stream().filter(item -> "0".equals(item.getIsArtifical()) && Boolean.TRUE.equals(item.getIsEndNode())).forEach(item -> {
                ivrReportReqDTO.setNotArtificialKey(item.getPressKey());
                if ("registered".equals(type)){
                    ivrReportReqDTO.setArtificialKey(ivrKeyDictList.stream()
                        .filter(
                            key -> "1".equals(key.getIsArtifical()) && key.getPressKey().contains(item.getPressKey()))
                        .map(IvrKeyDict::getPressKey).collect(Collectors.toList()));
                    if (CollectionUtils.isEmpty(ivrReportReqDTO.getArtificialKey())){
                        return;
                    }
                }
                List<IvrCountVO> countVOList = ivrSummaryMapper.getIvrArtificialPro(ivrReportReqDTO);
                if (CollectionUtils.isNotEmpty(countVOList)) {
                    for (IvrCountVO ivrCountVO : countVOList) {
                        if (ivrArtMap.containsKey(ivrCountVO.getCallTime())) {
                            IvrStatisticsArtificialVO artificialVO = ivrArtMap
                                    .get(ivrCountVO.getCallTime());
                            setCountByReflex(item.getCode(), ivrCountVO.getIvrCount(), artificialVO);
                        } else {
                            IvrStatisticsArtificialVO artificialVO = new IvrStatisticsArtificialVO();
                            setCountByReflex(item.getCode(), ivrCountVO.getIvrCount(), artificialVO);
                            ivrArtMap.put(ivrCountVO.getCallTime(), artificialVO);
                        }
                    }
                }

            });
        }

        log.info("buildIvrArtMap use time:{}ms", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private void buildParentShuntRate(
            Map<String, IvrStatisticsArtificialVO> parentTotalMap,
            Map<String, IvrStatisticsArtificialVO> parentArtMap,
            Map<String, IvrStatisticsArtificialVO> parentShuntMap) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        for (String callTime : parentTotalMap.keySet()) {
            IvrStatisticsArtificialVO shuntRateVo = new IvrStatisticsArtificialVO();
            IvrStatisticsArtificialVO totalVo = parentTotalMap.get(callTime);
            if (parentArtMap.containsKey(callTime)) {
                Integer register21 = Integer.parseInt(totalVo.getRegistered21());
                Integer register31 = Integer.parseInt(totalVo.getRegistered31());
                Integer register41 = Integer.parseInt(totalVo.getRegistered41());
                Integer total = Integer.parseInt(totalVo.getTotal());
                IvrStatisticsArtificialVO artVo = parentArtMap.get(callTime);
                Integer artRegister21 = Integer.parseInt(artVo.getRegistered21());
                Integer artRegister31 = Integer.parseInt(artVo.getRegistered31());
                Integer artRegister41 = Integer.parseInt(artVo.getRegistered41());
                Integer artTotal = Integer.parseInt(artVo.getTotal());

                shuntRateVo.setRegistered11("100.00%");
                shuntRateVo.setRegistered21(TrUtil.calcPercentage(register21 - artRegister21, register21));
                shuntRateVo.setRegistered31(TrUtil.calcPercentage(register31 - artRegister31, register31));
                shuntRateVo.setRegistered41(TrUtil.calcPercentage(register41 - artRegister41, register41));
                shuntRateVo.setTotal(TrUtil.calcPercentage(total - artTotal, total));
                setDiagonal(shuntRateVo);
            } else {
                buildSpecialField(totalVo, shuntRateVo);

            }
            parentShuntMap.put(callTime, shuntRateVo);

        }
        log.info("buildParentShuntRate use time:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private void buildSpecialField(IvrStatisticsArtificialVO totalVo, IvrStatisticsArtificialVO shuntRateVo) {
        if ("0".equals(totalVo.getRegistered11())){
            shuntRateVo.setRegistered11("0.00%");
        } else {
            shuntRateVo.setRegistered11("100.00%");
        }

        if ("0".equals(totalVo.getRegistered21())){
            shuntRateVo.setRegistered21("0.00%");
        } else {
            shuntRateVo.setRegistered21("100.00%");
        }

        if ("0".equals(totalVo.getRegistered31())){
            shuntRateVo.setRegistered31("0.00%");
        } else {
            shuntRateVo.setRegistered31("100.00%");
        }

        if ("0".equals(totalVo.getRegistered41())){
            shuntRateVo.setRegistered41("0.00%");
        } else {
            shuntRateVo.setRegistered41("100.00%");
        }
    }

    private void setDiagonal(IvrStatisticsArtificialVO vo) {

        vo.setDs1("/");
        vo.setDs2("/");
        vo.setDs3("/");
        vo.setZl1("/");
        vo.setZl2("/");
        vo.setZl3("/");
        vo.setZl4("/");
        vo.setZl5("/");

        vo.setWallet1("/");
        vo.setWallet2("/");
        vo.setWallet3("/");
        vo.setWallet4("/");
        vo.setWallet5("/");
        vo.setWallet6("/");
        vo.setWallet7("/");

        vo.setJiafenZf1("/");
        vo.setJiafenZf2("/");
        vo.setJiafenJy1("/");
        vo.setJiafenJy2("/");
        vo.setJiafenJy3("/");
        vo.setJiafenJy4("/");
        vo.setJiafenHd1("/");
        vo.setJiafenHd2("/");
        vo.setJiafenGn1("/");
        vo.setJiafenGn2("/");
        vo.setJiafenGn3("/");


    }

    /**
     * 根据业务类型获取按键字典列表
     *
     * @param type
     * @return
     */
    private List<IvrKeyDict> getIvrKeyDictByType(String type, List<String> hotline) {
        return ivrKeyDictMapper.selectList(Wrappers.lambdaQuery(IvrKeyDict.class).eq(IvrKeyDict::getType, type)
                .in(IvrKeyDict::getHotline, hotline));
    }


    /**
     * 通过反射给人工计算对象赋值
     *
     * @param fieldName
     * @param value
     * @param artificialVO
     */
    private void setCountByReflex(String fieldName, String value, IvrStatisticsArtificialVO artificialVO) {
        try {
            Field field = null;
            if (!fieldName.startsWith("jiafen")){
                field = artificialVO.getClass().getDeclaredField(fieldName.replace("_",""));
            } else {
                StringBuilder trueFieldName = new StringBuilder();
                // jiafen_hd_2_free 根据 _ 分割，只保留前三部分，并且hd要改成Hd
                String[] split = fieldName.split("_");
                for (int i = 0; i < split.length - 1; i++) {
                    if (i == 1){
                        // 将第二部分首字母大写
                        String str = split[i].substring(0,1).toUpperCase() + split[i].substring(1);
                        trueFieldName.append(str);
                    } else {
                        trueFieldName.append(split[i]);
                    }
                }
                field = artificialVO.getClass().getDeclaredField(trueFieldName.toString());
            }
            ReflectionUtils.makeAccessible(field);
            // 之前的值
            String lastValue = (String) ReflectionUtils.getField(field, artificialVO);
            if (StringUtils.isBlank(lastValue)){
                lastValue = "0";
            }
            // 之前的值跟现在的值相加
            // 之前不需要相加是因为原来每个按键只会调用一次这个方法，而这次嘉纷的按键分为工作日和非工作日，两者按键不一样，所以会出现2次调用，所以需要相加
            ReflectionUtils.setField(field, artificialVO, Integer.valueOf(value) + Integer.valueOf(lastValue) + "");
            if (StringUtils.isNotBlank(artificialVO.getTotal())) {
                artificialVO.setTotal(String.valueOf(Long.parseLong(value) + Long.parseLong(artificialVO.getTotal())));
            } else {
                artificialVO.setTotal(value);
            }
        } catch (Exception e) {
            log.error("setCountByReflex, 根据反射赋值异常", e);
        }
    }


    @Override
    public void exportIvrKeyDetailReport(HttpServletResponse response, IvrReportReqDTO reqDTO, String fileName) {
        reqDTO.setCurrentPage(1);
        reqDTO.setRowsPerPage(1000000);
        reqDTO.setExport(true);
        Page<IvrKeyDetailVO> sourceList = ivrKeyDetailReport(reqDTO);

        if (Objects.isNull(sourceList) || CollectionUtils.isEmpty(sourceList.getRecords())){
            throw new FastRuntimeException("查询数据为空");
        }
        List<IvrKeyDetailVO> records = sourceList.getRecords();
        List<Object> resultList = new ArrayList<>();

        setResponse(response, fileName);

        ExcelWriter writer = ExcelUtil.getWriter(true);

        // 设置表头单元格格式
        CellStyle titleStyle = getTitleCellStyle(writer);

        String[] hotlineArray = reqDTO.getHotline().split(",");
        String hotline = hotlineArray[0];
        List<IvrKeyDict> ivrKeyDictList = getIvrKeyDictByHotline(hotline, true);
        int totalColumns = COMMON_FIELD_COUNT;
        int objectAddIndex = 0;
        if (GXD_HOTLINE.equals(hotline)) {
            Map<String, List<IvrKeyDict>> map = ivrKeyDictList.stream()
                    .collect(Collectors.groupingBy(IvrKeyDict::getType));
            writer.merge(0, 0, 0,
                COMMON_FIELD_COUNT + REGISTERED_COUNT + VIP_FIELD_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + NOT_REGISTER_COUNT - 1, "信用分期",
                titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + REGISTERED_COUNT - 1, "注册用户", titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT + REGISTERED_COUNT,
                COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT - 1, "逾期用户", titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT,
                    COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT - 1, "债转用户", titleStyle);
            writer.merge(1, 3, COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT,
                    COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT - 1, "会员&特权卡用户", titleStyle);
            writer.merge(1, 3, COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT,
                    COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT + NOT_REGISTER_COUNT - 1, "非注册用户", titleStyle);
            
            writer.merge(2, 2, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + 2, "贷款申请", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 3, COMMON_FIELD_COUNT + 6, "放款问题咨询", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 7, COMMON_FIELD_COUNT + 12, "还款相关问题", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 13, COMMON_FIELD_COUNT + REGISTERED_COUNT - 1, "个人信息修改", titleStyle);
            totalColumns = REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT + NOT_REGISTER_COUNT;
            objectAddIndex = COMMON_FIELD_COUNT;
            records.stream().forEach(item -> {
                IvrKeyDetailGxdVO vo = new IvrKeyDetailGxdVO();
                BeanUtils.copyProperties(item, vo);
                resultList.add(vo);

            });
        } else if (DS_HOTLINE.equals(hotline)) {
            writer.merge(0, 0, 0, COMMON_FIELD_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT -1, hotline, titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + DS_FIELD_COUNT - 1, "电商业务", titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT + DS_FIELD_COUNT,
                    COMMON_FIELD_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT - 1, "租赁业务", titleStyle);
            totalColumns = DS_FIELD_COUNT + ZL_FIELD_COUNT;
            objectAddIndex = COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT
                + NOT_REGISTER_COUNT;
            records.stream().forEach(item -> {
                IvrKeyDetailDSVO vo = new IvrKeyDetailDSVO();
                BeanUtils.copyProperties(item, vo);
                resultList.add(vo);

            });
        } else if (WALLET_HOTLINE.equals(hotline)) {
            writer.merge(0, 0, 0, COMMON_FIELD_COUNT + WALLET_FIELD_COUNT - 1, hotline, titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + WALLET_FIELD_COUNT - 1, "钱夹谷谷", titleStyle);
            totalColumns = WALLET_FIELD_COUNT;
            objectAddIndex = COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT
                + NOT_REGISTER_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT;
            records.stream().forEach(item -> {
                IvrKeyDetailWalletVO vo = new IvrKeyDetailWalletVO();
                BeanUtils.copyProperties(item, vo);
                resultList.add(vo);
            });
        } else if (JIAFEN_HOTLINE.equals(hotline)){
            writer.merge(0, 0, 0, COMMON_FIELD_COUNT + JIAFEN_FIELD_COUNT - 1, hotline, titleStyle);
            writer.merge(1, 1, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + JIAFEN_FIELD_COUNT - 1, "嘉纷理财", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT, COMMON_FIELD_COUNT + 2 - 1, "账户问题", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 2, COMMON_FIELD_COUNT + 2 + 4 - 1, "交易及资金问题", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 2 + 4, COMMON_FIELD_COUNT + 2 + 4 + 2 - 1, "活动问题", titleStyle);
            writer.merge(2, 2, COMMON_FIELD_COUNT + 2 + 4 + 2, COMMON_FIELD_COUNT + 2 + 4 + 2 + 3 - 1, "功能介绍", titleStyle);
            totalColumns = JIAFEN_FIELD_COUNT;
            objectAddIndex = COMMON_FIELD_COUNT + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT
                + NOT_REGISTER_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT + WALLET_FIELD_COUNT;
            records.stream().forEach(item -> {
                IvrKeyDetailJiafenVO vo = new IvrKeyDetailJiafenVO();
                BeanUtils.copyProperties(item, vo);
                resultList.add(vo);
            });
        }

        // 获取公共字段字段
        List<String> titleList = getTitleList(IvrKeyDetailVO.class);

        // 前11列是公共字段
        for (int i = 0; i < COMMON_FIELD_COUNT; i++) {
            writer.merge(1, 3, i, i, titleList.get(i), titleStyle);
        }

        // 获取单独字段
        // 11列之后是 单独字段
        for (int i = 0; i < totalColumns; i++) {
            String title = titleList.get(i + objectAddIndex);
            if ("会员&特权卡用户".equals(title) || "非注册用户".equals(title)){
                continue;
            }
            log.info("tile:{}", title);
            String[] titleArray = title.split("-");
            if (titleArray.length == 2) {
                writer.writeCellValue(i + COMMON_FIELD_COUNT, 3, titleArray[1]);
                writer.setStyle(titleStyle, i + COMMON_FIELD_COUNT, 3);
            }else {
                writer.merge(2, 3, i + COMMON_FIELD_COUNT, i + COMMON_FIELD_COUNT, titleArray[0], titleStyle);
            }
        }

        // 设置列宽
        for (int i = 0; i < titleList.size(); i++) {
            writer.setColumnWidth(i, 15);
        }

        // 跳过表头的4行
        writer.passRows(4);
        // 写入数据
        writer.write(resultList, false);
        int currentRow = writer.getCurrentRow();
        writer.merge(currentRow - 1, currentRow - 1, 0, 10, "合计", writer.getCellStyle());

        closeWriter(writer, response);

    }


    private void closeWriter(ExcelWriter writer, HttpServletResponse response) {
        try {
            writer.flush(response.getOutputStream(), true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            writer.close();
        }
    }


    private <T> List<String> getTitleList(Class<T> claz) {
        List<String> list = new ArrayList<>();
        List<Field> fields = Arrays.asList(claz.getDeclaredFields());
        fields.forEach(field -> {
            ExcelTitleMap excelTitleMap = field.getAnnotation(ExcelTitleMap.class);
            if (excelTitleMap != null) {
                list.add(excelTitleMap.title());
            }
        });

        return list;
    }


    private CellStyle getTitleCellStyle(ExcelWriter writer) {
        CellStyle titleStyle = writer.createCellStyle();
        Font titleFont = writer.createFont();
        //加粗
        titleFont.getBold();
        //设置字体大小
        titleFont.setFontHeightInPoints((short) 10);
        //设置字体
        titleStyle.setFont(titleFont);
        //设置单元格内容是否自动换行
        titleStyle.setWrapText(false);
        //边框颜色
        titleStyle.setBorderBottom(BorderStyle.THIN);
        //边框颜色
        titleStyle.setBorderLeft(BorderStyle.THIN);
        //边框颜色
        titleStyle.setBorderRight(BorderStyle.THIN);
        //边框颜色
        titleStyle.setBorderTop(BorderStyle.THIN);
        //水平 居中对齐
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        //垂直 居中对齐
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //背景颜色
        titleStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        //背景颜色显示
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return titleStyle;
    }


    private void setResponse(HttpServletResponse response, String fileName) {
        try {
            // 设置响应类型
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置响应头信息
            response.setHeader("Content-disposition",
                    "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
            response.setHeader("Content-Transfer-Encoding", "binary");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            // 跨域配置
//            response.addHeader("Access-Control-Allow-Headers", "*");
//            response.addHeader("Access-Control-Allow-Origin", "*");
        } catch (UnsupportedEncodingException e) {
            log.error("exportIvrKeyDetailReport, 设置response异常", e);
        }
    }


    @Override
    public void exportIvrStatisticsReport(HttpServletResponse response, IvrReportReqDTO reqDTO, String fileName) {
        // 先取出原本参数中的hotline，因为在下面查询的时候这个字段会变
        String[] hotlineArray = reqDTO.getHotline().split(",");
        List<String> hotlineList = Arrays.stream(hotlineArray).sorted().collect(Collectors.toList());
        IvrStatisticsReportVO sourceReport = ivrShuntStatisticsReport(reqDTO);
        setResponse(response, fileName);
        ExcelWriter writer = ExcelUtil.getWriter();
        Map<String, IvrStatisticsArtificialVO> artificialMap = sourceReport.getArtificialMap();
        Map<String, IvrStatisticsArtificialVO> totalMap = sourceReport.getTotalMap();
        Map<String, IvrStatisticsArtificialVO> shuntRateMap = sourceReport.getShuntRateMap();
        Map<String, IvrStatisticsArtificialVO> artificialMapFirst = sourceReport.getArtificialMapFirst();
        Map<String, IvrStatisticsArtificialVO> totalMapFirst = sourceReport.getTotalMapFirst();
        Map<String, IvrStatisticsArtificialVO> shuntRateMapFirst = sourceReport.getShuntRateMapFirst();
        Set<String> callDateSet = totalMap.keySet();
        CellStyle titleCellStyle = getTitleCellStyle(writer);
        if (CollectionUtils.isNotEmpty(callDateSet)) {
            writer.renameSheet(0, callDateSet.iterator().next());
        }
        for (String callDate : callDateSet) {
            writer.setSheet(callDate);
            // 绘制excel
            try {
                writer.writeCellValue(0, 0, "开始时间:");
                writer.writeCellValue(1, 0, DateUtil.dateToString(reqDTO.getStartTime()));
                writer.writeCellValue(0, 1, "结束时间:");
                writer.writeCellValue(1, 1, DateUtil.dateToString(reqDTO.getEndTime()));
                ivrStatisReportWriteExcel(titleCellStyle, writer, hotlineList, totalMap.get(callDate),
                        artificialMap.get(callDate), shuntRateMap.get(callDate),totalMapFirst.get(callDate),artificialMapFirst.get(callDate), shuntRateMapFirst.get(callDate));
            } catch (Exception e) {
                log.error("ivrStatisReportWriteExcel 绘制ivr统计报表异常", e);
            }
        }

        closeWriter(writer, response);


    }

    private void ivrStatisReportWriteExcel(CellStyle titleCellStyle, ExcelWriter writer, List<String> hotlineList,
            IvrStatisticsArtificialVO totalVO, IvrStatisticsArtificialVO artVO, IvrStatisticsArtificialVO rateVO,
            IvrStatisticsArtificialVO parentTotalVO,
            IvrStatisticsArtificialVO parentArtVO,
            IvrStatisticsArtificialVO parentRateVO)
            throws IllegalAccessException {
        // 从 y=3 也就是第4行开始写入，因为前面两行显示时间,空一行
        for (int i = 0; i < TITLE_LIST.size(); i++) {
            writer.writeCellValue(i, 3, TITLE_LIST.get(i));
            writer.setStyle(titleCellStyle, i, 3);
        }

        Field[] fields = IvrStatisticsArtificialVO.class.getDeclaredFields();
        // 初始行数4
        int row = 4;
        int row2 = 4;
        for (String hotline : hotlineList) {
            if (GXD_HOTLINE.equals(hotline)) {
                writer.merge(row,
                    row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT + NOT_REGISTER_COUNT - 1,
                    0, 0, hotline, writer.getCellStyle());
                writer.merge(row, row + REGISTERED_COUNT - 1, 1, 1, "注册用户", writer.getCellStyle());
                writer.merge(row + REGISTERED_COUNT, row + REGISTERED_COUNT + OVERDUE_COUNT - 1, 1, 5, "逾期用户",
                    writer.getCellStyle());
                writer.merge(row + REGISTERED_COUNT + OVERDUE_COUNT,
                    row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT - 1, 1, 5, "债转用户", writer.getCellStyle());
                writer.merge(row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT,
                        row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT - 1, 1, 5, "会员&特权卡用户", writer.getCellStyle());
                writer.merge(row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT,
                    row + REGISTERED_COUNT + OVERDUE_COUNT + TRANSFER_COUNT + VIP_FIELD_COUNT + NOT_REGISTER_COUNT - 1,
                    1, 5, "非注册用户", writer.getCellStyle());
                for (int i = 0; i < GXD_TITLE_1.size(); i++) {
                    if ("贷款申请".equals(GXD_TITLE_1.get(i))) {
                        writer.merge(row, row + 2, 2, 2, "贷款申请", writer.getCellStyle());
                        writer.merge(row, row + 2, 3, 3, parentTotalVO.getRegistered11(), writer.getCellStyle());
                        writer.merge(row, row + 2, 4, 4, parentArtVO.getRegistered11(), writer.getCellStyle());
                        writer.merge(row, row + 2, 5, 5, parentRateVO.getRegistered11(), writer.getCellStyle());
                        row += 3;
                        continue;
                    }
                    if ("放款问题咨询".equals(GXD_TITLE_1.get(i))) {
                        writer.merge(row, row + 3, 2, 2, "放款问题咨询", writer.getCellStyle());
                        writer.merge(row, row + 3, 3, 3, parentTotalVO.getRegistered21(), writer.getCellStyle());
                        writer.merge(row, row + 3, 4, 4, parentArtVO.getRegistered21(), writer.getCellStyle());
                        writer.merge(row, row + 3, 5, 5, parentRateVO.getRegistered21(), writer.getCellStyle());
                        row += 4;
                        continue;
                    }
                    if ("还款相关问题".equals(GXD_TITLE_1.get(i))){
                        writer.merge(row, row + 5, 2, 2, "还款相关问题", writer.getCellStyle());
                        writer.merge(row, row + 5, 3, 3, parentTotalVO.getRegistered31(), writer.getCellStyle());
                        writer.merge(row, row + 5, 4, 4, parentArtVO.getRegistered31(), writer.getCellStyle());
                        writer.merge(row, row + 5, 5, 5, parentRateVO.getRegistered31(), writer.getCellStyle());
                        row += 6;
                        continue;
                    }
                    if ("个人信息修改".equals(GXD_TITLE_1.get(i))){
                        writer.merge(row, row + 2, 2, 2, "个人信息修改", writer.getCellStyle());
                        writer.merge(row, row + 2, 3, 3, parentTotalVO.getRegistered41(), writer.getCellStyle());
                        writer.merge(row, row + 2, 4, 4, parentArtVO.getRegistered41(), writer.getCellStyle());
                        writer.merge(row, row + 2, 5, 5, parentRateVO.getRegistered41(), writer.getCellStyle());
                        row += 3;
                        continue;
                    }
                }
                
                // 下面还有9行
                row += 9;            

                for (int i = 0; i < GXD_TITLE_2.size(); i++) {
                    writer.writeCellValue(6, row2, GXD_TITLE_2.get(i));
                    Field field = fields[i + 2];
                    ReflectionUtils.makeAccessible(field);
                    writer.writeCellValue(7, row2, field.get(totalVO));
                    if (Objects.isNull(artVO)) {
                        writer.writeCellValue(8, row2,"0");
                    } else {
                        writer.writeCellValue(8, row2, field.get(artVO));
                    }
                    writer.writeCellValue(9, row2, field.get(rateVO));

                    row2 ++;
                }
            }
            if (DS_HOTLINE.equals(hotline)) {
                writer.merge(row, row + DS_FIELD_COUNT + ZL_FIELD_COUNT - 1, 0, 0, hotline, writer.getCellStyle());
                writer.merge(row, row + DS_FIELD_COUNT - 1, 1, 1, "电商业务", writer.getCellStyle());
                writer.merge(row + DS_FIELD_COUNT, row + DS_FIELD_COUNT + ZL_FIELD_COUNT - 1, 1, 1, "租赁业务", writer.getCellStyle());
                for (int i = 0; i < DS_TITLE.size(); i++) {
                    writer.writeCellValue(2, row, DS_TITLE.get(i));
                    writer.writeCellValue(3, row, "/");
                    writer.writeCellValue(4, row, "/");
                    writer.writeCellValue(5, row, "/");
                    writer.writeCellValue(6, row, "/");
                    Field field = fields[i + REGISTERED_COUNT + VIP_FIELD_COUNT + 2];
                    ReflectionUtils.makeAccessible(field);
                    writer.writeCellValue(7, row, field.get(totalVO));
                    if (Objects.isNull(artVO)) {
                        writer.writeCellValue(8, row, "0");
                    } else {
                        writer.writeCellValue(8, row, field.get(artVO));
                    }
                    writer.writeCellValue(9, row, field.get(rateVO));
                    row ++;
                }

            }

            if (WALLET_HOTLINE.equals(hotline)) {
                writer.merge(row, row + WALLET_FIELD_COUNT - 1, 0, 0, hotline, writer.getCellStyle());
                writer.merge(row, row + WALLET_FIELD_COUNT - 1, 1, 1, "钱夹谷谷", writer.getCellStyle());
                for (int i = 0; i < WALLET_TITLE.size(); i++) {
                    writer.writeCellValue(2, row, WALLET_TITLE.get(i));
                    writer.writeCellValue(3, row, "/");
                    writer.writeCellValue(4, row, "/");
                    writer.writeCellValue(5, row, "/");
                    writer.writeCellValue(6, row, "/");
                    Field field = fields[i + REGISTERED_COUNT + VIP_FIELD_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT + 2];
                    ReflectionUtils.makeAccessible(field);
                    writer.writeCellValue(7, row , field.get(totalVO));
                    if (Objects.isNull(artVO)) {
                        writer.writeCellValue(8, row, "0");
                    } else {
                        writer.writeCellValue(8, row, field.get(artVO));
                    }
                    writer.writeCellValue(9, row, field.get(rateVO));
                    row ++;
                }
            }

            if (JIAFEN_HOTLINE.equals(hotline)){
                writer.merge(row, row + JIAFEN_FIELD_COUNT - 1, 0, 0, hotline, writer.getCellStyle());
                writer.merge(row, row + 2 - 1, 1, 1, "账户问题", writer.getCellStyle());
                writer.merge(row + 2, row + 2 + 4 - 1, 1, 1, "交易及资金问题", writer.getCellStyle());
                writer.merge(row + 2 + 4, row + 2 + 4 + 2 - 1, 1, 1, "活动问题", writer.getCellStyle());
                writer.merge(row + 2 + 4 + 2, row + 2 + 4 + 2 + 3 - 1, 1, 1, "功能介绍", writer.getCellStyle());
                for (int i = 0; i < JIAFEN_TITLE.size(); i++) {
                    writer.writeCellValue(2, row, JIAFEN_TITLE.get(i));
                    writer.writeCellValue(3, row, "/");
                    writer.writeCellValue(4, row, "/");
                    writer.writeCellValue(5, row, "/");
                    writer.writeCellValue(6, row, "/");
                    Field field = fields[i + REGISTERED_COUNT + VIP_FIELD_COUNT + DS_FIELD_COUNT + ZL_FIELD_COUNT + WALLET_FIELD_COUNT + 2];
                    ReflectionUtils.makeAccessible(field);
                    writer.writeCellValue(7, row , field.get(totalVO));
                    if (Objects.isNull(artVO)) {
                        writer.writeCellValue(8, row, "0");
                    } else {
                        writer.writeCellValue(8, row, field.get(artVO));
                    }
                    writer.writeCellValue(9, row, field.get(rateVO));
                    row ++;
                }

            }

        }


        // 合计字段
        writer.merge(row, row, 0, 2, "合计", titleCellStyle);
        writer.writeCellValue(3, row,"总来电数");
        writer.writeCellValue(4, row, totalVO.getTotal());
        writer.writeCellValue(5, row,"转人工数");
        if (Objects.isNull(parentArtVO)) {
            writer.writeCellValue(6, row, "0");
        } else {
            writer.writeCellValue(6, row, Objects.isNull(artVO) ? 0 : artVO.getTotal());
        }
        writer.writeCellValue(7, row,"分流率");
        writer.merge(row, row, 8, 9, rateVO.getTotal(), writer.getCellStyle());
        // 设置列宽
        for (int i = 0; i < TITLE_LIST.size(); i++) {
            writer.setColumnWidth(i, 15);
        }
    }
}
