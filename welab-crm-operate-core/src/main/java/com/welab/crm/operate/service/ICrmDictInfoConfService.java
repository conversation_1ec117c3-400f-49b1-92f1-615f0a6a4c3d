/**
 * @Title: ICrmDictInfoService.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.dict.DictInfoConfReqDTO;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;

/**
 * @description 工单组合服务
 * <AUTHOR>
 * @date 2021-10-15 10:07:06
 * @version v1.0
 */
public interface ICrmDictInfoConfService {
    
    /**
     * 查询字典列表
     * @param req
     * @return
     */
    public Page<DictInfoConfResVO> getDictInfosConf(DictInfoConfReqDTO req);
    
    /**
     * 添加字典记录
     * @param reqDTO
     * @return
     */
    public boolean addDictInfoConf(DictInfoConfReqDTO reqDTO);
    
    /**
     * 更新字典记录
     * @param reqDTO
     * @return
     */
    public boolean updateDictInfoConf(DictInfoConfReqDTO reqDTO);
    
    /**
     * 删除字典数据
     * @param reqDTO
     * @return
     */
    public boolean deleteDictInfoConf(DictInfoConfReqDTO reqDTO);
}
