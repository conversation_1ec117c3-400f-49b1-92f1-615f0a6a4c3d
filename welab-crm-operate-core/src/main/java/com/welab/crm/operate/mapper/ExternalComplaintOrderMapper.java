package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ExternalComplaintOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintQueryDTO;
import com.welab.crm.operate.vo.workorder.ExecutionVO;
import com.welab.crm.operate.vo.workorder.ExternalComplaintListVO;
import com.welab.crm.operate.vo.workorder.OrderContactVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外部投诉记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface ExternalComplaintOrderMapper extends BaseMapper<ExternalComplaintOrder> {

	Page<ExternalComplaintListVO> selectOrderHistoryPage(Page<Object> objectPage, @Param("dto") ExternalComplaintQueryDTO dto);

	List<ExternalComplaintListVO> selectOrderHistoryList(@Param("dto") ExternalComplaintQueryDTO dto);

	List<OrderContactVO> selectOrderContactList(@Param("id") Long id, @Param("loginName") String loginName);
	
	ExecutionVO selectExecutionById(@Param("id") Long id, @Param("loginName") String loginName);

}
