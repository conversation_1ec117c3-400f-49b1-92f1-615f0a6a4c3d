package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.keygen.KeyGenerator;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.vo.CompanyVo;
import com.welab.crm.interview.vo.PersonalDetailsVo;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.interview.vo.ProfileVo;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.domain.DataCustomer;
import com.welab.crm.operate.dto.customer.AddCustReqDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.CustHisOperateMapper;
import com.welab.crm.operate.mapper.DataCustomerMapper;
import com.welab.crm.operate.service.CustomerService;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/11/5 16:13
 */
@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private DataCustomerMapper customerMapper;
    @Resource
    private CustHisOperateMapper custHisOperateMapper;
    @Resource
    private KeyGenerator keyGenerator;

    private static final String FEMALE = "female";
    private static final String MALE = "male";

    @Override
    public boolean isUserExist(String mobile, String type) {
        int count = 0;
        if (StringUtils.isNotBlank(mobile) && StringUtils.isNotBlank(type)) {
            count = customerMapper
                    .selectCount(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, mobile)
                            .eq(DataCustomer::getCustType, type));
        } else {
            throw new FastRuntimeException("手机号或者类型不能为空");
        }
        return count > 0;
    }

    @Override
    public Long saveUserInfoCash(PersonalDetailsVoExpand userInfo) {
        Long id = null;
        if (isUserExist(userInfo.getMobile(), Constant.USER_TYPE_CASH)) {
            List<DataCustomer> dataCustomers = customerMapper.selectList(
                    Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, userInfo.getMobile())
                            .eq(DataCustomer::getCustType, Constant.USER_TYPE_CASH));
            for (DataCustomer dataCustomer : dataCustomers) {
                buildCustomer(dataCustomer, userInfo);
                dataCustomer.setGmtModify(new Date());
                customerMapper.updateById(dataCustomer);
                if (dataCustomer.getUserId().equals(Long.valueOf((userInfo.getUserId())))){
                    id = dataCustomer.getId();
                }
            }
            return id;
        } else {
            DataCustomer dataCustomer = new DataCustomer();
            buildCustomer(dataCustomer, userInfo);
            dataCustomer.setId(keyGenerator.generateKey());
            // 先保存操作历史再保存用户信息
            saveUserMobileChangeHistory(userInfo.getUuid(), Constant.USER_TYPE_CASH,
                    userInfo.getUserId(), dataCustomer.getMobile(), userInfo);
            customerMapper.insert(dataCustomer);
            return dataCustomer.getId();
        }
    }

    /**
     * 对于已经存在的用户，如果是新手机来电则添加用户修改手机历史记录
     *
     * @param type 消费类型，现金贷和钱包处理的方法不同
     */
    private void saveUserMobileChangeHistory(String uuid, String type, Integer userId, String newMobile, PersonalDetailsVoExpand userInfo) {
        LambdaQueryWrapper<DataCustomer> wrapper = Wrappers.lambdaQuery(DataCustomer.class)
                .eq(DataCustomer::getUuid, uuid)
                .eq(DataCustomer::getCustType, type)
                .orderByDesc(DataCustomer::getGmtCreate).last("LIMIT 1");

        DataCustomer customer = customerMapper.selectOne(wrapper);
        if (customer != null) {
            // 对于已经存在的用户，如果是新手机来电则添加用户修改手机历史记录
            CustHisOperate operate = new CustHisOperate();
            //operate.setStaffId(CommonUtils.getCurrentlogged());
            //operate.setGroupCode(CommonUtils.getCurrentloggedOrg());
            operate.setStaffId("Admin");
            operate.setGroupCode("Admin");
            operate.setUserId(userId);
            operate.setOperateType(OperateTypeEnum.USER_INFO_MODIFY.getCode());
            operate.setOperateTime(new Date());
            StringBuilder newValue = new StringBuilder("新手机号码为:");
            StringBuilder oldValue = new StringBuilder("客户自助修改手机号码,旧手机号码为:");
            operate.setOldMobile(customer.getMobile());
            newValue.append(newMobile);
            oldValue.append(customer.getMobile());
            operate.setComment(oldValue + ", " + newValue);
            Date date = new Date();
            if (Objects.nonNull(userInfo)) {
                operate.setGmtModify(userInfo.getUpdateAt());
                operate.setGmtCreate(userInfo.getUpdateAt());
            }
            custHisOperateMapper.insert(operate);
        }
    }

    private DataCustomer buildCustomer(DataCustomer dataCustomer, PersonalDetailsVoExpand userInfo) {
        dataCustomer.setUuid(userInfo.getUuid());
        dataCustomer.setUserId(userInfo.getUserId().longValue());
        dataCustomer.setMobile(userInfo.getMobile());
        dataCustomer.setGender(userInfo.getGender());
        dataCustomer.setCreditline(new BigDecimal(userInfo.getCreditLine()));
        dataCustomer.setAvlCreditline(new BigDecimal(userInfo.getAvailableCredit()));
        dataCustomer.setCreditState(userInfo.getCreditState());
        dataCustomer.setCustType(Constant.USER_TYPE_CASH);
        dataCustomer.setCreateBy("system");
        dataCustomer.setModifyBy("system");
        if (Boolean.TRUE.equals(userInfo.getBlock())) {
            dataCustomer.setIsZx(userInfo.getBlock());
        } else {
            dataCustomer.setIsZx(false);
            dataCustomer.setVip("VIP".equals(userInfo.getIsVip()));
        }
        if (Objects.nonNull(userInfo.getPersonalDetailsVo())) {
            PersonalDetailsVo personalDetailsVo = userInfo.getPersonalDetailsVo();
            if (Objects.nonNull(personalDetailsVo)) {
                ProfileVo profile = personalDetailsVo.getProfile();
                if (Objects.nonNull(profile)) {
                    dataCustomer.setCnid(profile.getCnid());
                    dataCustomer.setCustomerName(profile.getName());
                    dataCustomer.setAddress(removeFourChar(profile.getCurrentAddr()));
                    dataCustomer.setFamilyAddress(removeFourChar(profile.getFamilyAddr()));
                    dataCustomer.setAge(profile.getAge());
                }
                CompanyVo company = personalDetailsVo.getCompany();
                if (Objects.nonNull(company)) {
                    dataCustomer.setCompanyName(company.getCompanyName());
                    dataCustomer.setCompanyAddress(removeFourChar(company.getCompanyAddr()));
                }

            }
        }
        return dataCustomer;
    }

    /**
     * 删除异常字符
     * @param content
     * @return
     */
    public static String removeFourChar(String content) {
        if (StringUtils.isNotBlank(content)) {
            byte[] conbyte = content.getBytes();
            for (int i = 0; i < conbyte.length; i++) {
                if ((conbyte[i] & 0xF8) == 0xF0) {
                    for (int j = 0; j < 4; j++) {
                        conbyte[i + j] = 0x30;// 0x30 int=48   字符=0
                    }
                    i += 3;
                }
            }
            content = new String(conbyte);
            return content.replaceAll("0000", "");
        }
        return content;
    }
    @Override
    public Long saveUserInfoWallet(WalletUserDTO walletUserInfo) {
        Long id = null;
        if (isUserExist(walletUserInfo.getMobile(), Constant.USER_TYPE_WALLET)){
            List<DataCustomer> dataCustomers = customerMapper.selectList(
                    Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, walletUserInfo.getMobile())
                            .eq(DataCustomer::getCustType, Constant.USER_TYPE_WALLET));
            for (DataCustomer dataCustomer : dataCustomers) {
                buildWalletUserInfo(dataCustomer, walletUserInfo);
                dataCustomer.setGmtModify(new Date());
                customerMapper.updateById(dataCustomer);
                if (Objects.isNull(walletUserInfo.getUserId()) || (dataCustomer.getUserId()
                        .equals(Long.valueOf((walletUserInfo.getUserId()))))) {
                    id = dataCustomer.getId();
                }
            }

        } else {
            DataCustomer dataCustomer = new DataCustomer();
            buildWalletUserInfo(dataCustomer, walletUserInfo);
            dataCustomer.setId(keyGenerator.generateKey());
            // 先保存操作历史再保存用户信息
            saveUserMobileChangeHistory(walletUserInfo.getUuid(), Constant.USER_TYPE_WALLET,
                    walletUserInfo.getUserId(), dataCustomer.getMobile(), null);
            customerMapper.insert(dataCustomer);
            return dataCustomer.getId();
        }

        return id;
    }

    private void buildWalletUserInfo(DataCustomer dataCustomer, WalletUserDTO walletUserInfo) {
        if (Objects.nonNull(dataCustomer) && Objects.nonNull(walletUserInfo)) {
            BeanUtils.copyProperties(walletUserInfo, dataCustomer,"id");
            dataCustomer.setCustomerName(walletUserInfo.getName());
            dataCustomer.setAddress(walletUserInfo.getCurrentAddr());
            if (Objects.nonNull(walletUserInfo.getUserId())) {
                dataCustomer.setUserId(walletUserInfo.getUserId().longValue());
            }
            dataCustomer.setCreditline(walletUserInfo.getCreditLine());
            dataCustomer.setAvlCreditline(walletUserInfo.getAvlCreditLine());
            dataCustomer.setCreditState(walletUserInfo.getCreditStateCode());
            dataCustomer.setCreateBy("system");
            dataCustomer.setModifyBy("system");
            if (StringUtils.isNotBlank(walletUserInfo.getRegisterAt())) {
                dataCustomer.setRegisterTime(DateUtil.stringToDate(walletUserInfo.getRegisterAt()));
            }
            dataCustomer.setRegisterOrigin(walletUserInfo.getOrigin());
            dataCustomer.setCustType(Constant.USER_TYPE_WALLET);
            if (StringUtils.isNotBlank(walletUserInfo.getBankDate())) {
                dataCustomer.setBankDate(DateUtil.stringToDate(walletUserInfo.getBankDate()));
            }
            if (Boolean.FALSE.equals(walletUserInfo.getBlock())) {
                dataCustomer.setIsZx(walletUserInfo.getBlock());
            } else {
                dataCustomer.setIsZx(Boolean.TRUE);
            }

        }
    }


    @Override
    public Boolean saveUserInfo(AddCustReqDTO addCustReqDTO) {
        DataCustomer dataCustomer = new DataCustomer();
        convertProperties(addCustReqDTO);
        if (Objects.nonNull(addCustReqDTO)) {
            List<DataCustomer> customerList = customerMapper.selectList(
                    Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, addCustReqDTO.getMobile())
                            .isNull(DataCustomer::getUserId)
                            .and(qr -> qr.ne(DataCustomer::getIsZx, true).or().isNull(DataCustomer::getIsZx))
                            .orderByDesc(DataCustomer::getGmtModify));
            if (CollectionUtils.isEmpty(customerList)) {
                BeanUtils.copyProperties(addCustReqDTO, dataCustomer);
                dataCustomer.setCustomerName(addCustReqDTO.getName());
                dataCustomer.setCustType(addCustReqDTO.getType());
                return customerMapper.insert(dataCustomer) > 0;
            } else {
                DataCustomer customer = customerList.get(0);
                log.info("客户已存在,mobile{}:", customer.getMobile());
                customer.setCustomerName(addCustReqDTO.getName());
                customer.setGender(addCustReqDTO.getGender());
                customer.setGmtModify(new Date());
                return customerMapper.updateById(customer) > 0;
            }
        }
        return false;
    }

    private void convertProperties(AddCustReqDTO addCustReqDTO) {
        if (Objects.nonNull(addCustReqDTO) && StringUtils.isNotBlank(addCustReqDTO.getGender())) {
            switch (addCustReqDTO.getGender()) {
                case FEMALE:
                    addCustReqDTO.setGender("女");
                    break;
                case MALE:
                    addCustReqDTO.setGender("男");
                    break;
                default:
                    break;
            }
        }
    }


    @Override
    public CashCustInfoVO queryUserInfoCash(String mobile) {
        CashCustInfoVO cashCustInfoVO = new CashCustInfoVO();
        if (StringUtils.isNotBlank(mobile)) {
            List<DataCustomer> dataCustomerList = customerMapper
                    .selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, mobile)
                            .isNull(DataCustomer::getUserId)
                            .and(qr -> qr.ne(DataCustomer::getIsZx, true).or().isNull(DataCustomer::getIsZx))
                            .orderByDesc(DataCustomer::getModifyBy)
                    );
            if (CollectionUtils.isNotEmpty(dataCustomerList)) {
                DataCustomer dataCustomer = dataCustomerList.get(0);
                BeanUtils.copyProperties(dataCustomer, cashCustInfoVO);
                cashCustInfoVO.setName(dataCustomer.getCustomerName());
            } else {
                throw new CrmOperateException("用户不存在");
            }
        }
        return cashCustInfoVO;
    }

    @Override
    public CashCustInfoVO queryUserInfoByMobileAndType(String mobile, String type) {
        CashCustInfoVO cashCustInfoVO = new CashCustInfoVO();
        List<DataCustomer> dataCustomerList = customerMapper.selectList(
                Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, mobile)
                        .eq(DataCustomer::getCustType, type));
        if (CollectionUtils.isNotEmpty(dataCustomerList)){
            DataCustomer dataCustomer = dataCustomerList.get(0);
            BeanUtils.copyProperties(dataCustomer, cashCustInfoVO);
            cashCustInfoVO.setName(dataCustomer.getCustomerName());
            cashCustInfoVO.setAge(String.valueOf(dataCustomer.getAge()));
        }

        return cashCustInfoVO;
    }

    @Override
    public Long queryUserUuidByMobile(String mobile) {
        List<DataCustomer> customers = customerMapper.selectList(Wrappers.<DataCustomer>lambdaQuery()
                .eq(DataCustomer::getMobile, mobile));
        if (!customers.isEmpty()) {
            for (DataCustomer customer : customers) {
                if (StringUtils.isNotBlank(customer.getUuid())) {
                    return Long.parseLong(customer.getUuid());
                }
            }
        }
        return null;
    }

    @Override
    public List<CashCustInfoVO> queryMobileByUuid(String uuid, String mobile) {
        List<DataCustomer> customers = null;
        if (StringUtils.isNotBlank(uuid)) {
            customers = customerMapper.selectList(Wrappers.<DataCustomer>lambdaQuery()
                    .eq(DataCustomer::getUuid, uuid));
        } else if (StringUtils.isNotBlank(mobile)) {
            customers = customerMapper.selectList(Wrappers.<DataCustomer>lambdaQuery()
                    .eq(DataCustomer::getMobile, mobile));
        }
        if (CollectionUtils.isNotEmpty(customers)) {
            List<CashCustInfoVO> results = new ArrayList<>();
            for (DataCustomer customer : customers) {
                CashCustInfoVO vo = new CashCustInfoVO();
                vo.setMobile(customer.getMobile());
                vo.setId(customer.getId());
                results.add(vo);
            }
            return results;
        } else {
            return Collections.emptyList();
        }
    }

}
