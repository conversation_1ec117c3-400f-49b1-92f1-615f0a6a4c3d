package com.welab.crm.operate.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import com.welab.crm.operate.websocket.handler.KFWebSocketHandler;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/5/6 17:27
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig2 implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
            registry.addHandler(this.webSocketHandler(),"/websocket")
                    .addInterceptors(this.webSocketInterceptor())
                    .setAllowedOrigins("*");
    }

    @Bean
    public KFWebSocketHandler webSocketHandler(){
        return new K<PERSON>WebSocketHandler();
    }

    @Bean
    public WebSocketInterceptor webSocketInterceptor(){
        return new WebSocketInterceptor();
    }
}
