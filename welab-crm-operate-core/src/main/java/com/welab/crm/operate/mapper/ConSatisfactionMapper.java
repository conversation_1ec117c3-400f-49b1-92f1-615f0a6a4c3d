package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.ConSatisfaction;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 满意度调查表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-18
 */
public interface ConSatisfactionMapper extends BaseMapper<ConSatisfaction> {

    List<ReportWorkStatusSummaryVO> selectSatisfactionRate(@Param("filter") ReportWorkStatusDTO dto);

    ReportWorkStatusSummaryVO selectSatisfactionTotalRate(@Param("filter") ReportWorkStatusDTO dto);
}
