package com.welab.crm.operate.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电销UUID表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_uuid")
public class TmkUuid implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一Id UUID******
     */
    private String tmkTaskId;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 合同号
     */
    private String applicationId;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 号码包定义
     */
    private String packageDefine;

    /**
     * 可用额度
     */
    private BigDecimal avlCredit;

    /**
     * 额度状态
     */
    private String creditStatus;

    /**
     * 是否进件
     */
    private String isIncome;

    /**
     * 是否提现
     */
    private String isWithdrawal;

    /**
     * 身份证
     */
    private String cnid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private String gender;

    /**
     * 信用额度
     */
    private BigDecimal creditLine;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 号码包创建时间
     */
    private Date packageCreateTime;

    /**
     * 0：未分配；1：已分配；8：已废弃
     */
    private String flag;

    /**
     * 员工Id
     */
    private Long staffId;



    /**
     * 最新联系小结Id
     */
    private Long summaryId;

    /**
     * 分配时间
     */
    private Date assignDate;

    /**
     * 申请时间
     */
    private Date appliedAt;

    /**
     * 确认时间
     */
    private Date confirmedAt;

    /**
     * 拦截规则Id
     */
    private Long assignRuleId;

    /**
     * 名单类型
     * jj:进件类
     * tx:提现类
     */
    private String type;

    /**
     * 拨打次数
     */
    private Integer callNum;
}
