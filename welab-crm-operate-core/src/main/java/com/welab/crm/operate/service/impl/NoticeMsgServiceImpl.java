package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.welab.common.keygen.KeyGenerator;
import com.welab.crm.base.enums.TmkTypeEnum;
import com.welab.crm.base.workflow.common.constant.NoticeTypeEnum;
import com.welab.crm.interview.dto.wechat.WeChatReqBaseDTO;
import com.welab.crm.interview.enums.WeChatMsgEnum;
import com.welab.crm.interview.service.WeChatDubboService;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.dto.notice.NoticeReplyReqDTO;
import com.welab.crm.operate.dto.notice.WoNoticeDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.notice.NoticeMsgDetailVO;
import com.welab.crm.operate.vo.notice.NoticeMsgVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/28 17:36
 */

@Slf4j
@Service
public class NoticeMsgServiceImpl implements NoticeMsgService {

    @Resource
    private NoticeMsgMapper noticeMsgMapper;

    @Resource
    private NoticeReadLogMapper noticeReadLogMapper;

    @Resource
    private NoticeMsgReplyMapper noticeMsgReplyMapper;

    @Resource
    private KeyGenerator keyGenerator;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private CrmOrgStaffServiceImpl crmOrgStaffService;

    @Resource
    private WeChatDubboService weChatDubboService;

    @Resource
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    private static final String SYSTEM = "system";

    private static final String ORDER_MSG = "order_msg";


    @Override
    public Boolean publishNotice(NoticeMsgReqDTO dto) {
        NoticeMsg noticeMsg = new NoticeMsg();

        // 如果通知类型为空，默认为系统通知
        if (StringUtils.isBlank(dto.getNoticeType())){
            dto.setNoticeType(NoticeTypeEnum.SYSTEM.getCode());
        }
        // 如果发的是组，则转换为staff
        if (Objects.nonNull(dto.getIsGroup()) && dto.getIsGroup()) {
            ColStaffReqDTO staffReqDTO = new ColStaffReqDTO();
            staffReqDTO.setGroupCode(dto.getReceiver());
            List<String> loginNameList = crmOrgStaffService.getColStaffList(staffReqDTO).stream()
                    .map(ColStaffResVO::getLoginName).collect(Collectors.toList());
            dto.setReceiver(Joiner.on(",").join(loginNameList));
        }
        BeanUtils.copyProperties(dto, noticeMsg);
        if (Objects.isNull(noticeMsg.getSender())) {
            noticeMsg.setSender(CommonUtils.getCurrentlogged());
        }
        noticeMsg.setStatus(1);
        String[] receivers = dto.getReceiver().split(",");
        Date now = new Date();
        for (String receiver : receivers) {
            noticeMsg.setReceiver(receiver);
            noticeMsg.setMsgId(Constant.NOTICE_PRE + keyGenerator.generateKey());
            noticeMsg.setGmtCreate(now);
            noticeMsgMapper.insert(noticeMsg);
            noticeMsg.setId(null);
        }

        return true;
    }

    @Override
    public Boolean deleteNotice(String msgId) {
        NoticeMsg noticeMsg = noticeMsgMapper
                .selectOne(Wrappers.lambdaQuery(NoticeMsg.class).eq(NoticeMsg::getMsgId, msgId));
        if (Objects.isNull(noticeMsg)) {
            throw new FastRuntimeException("该消息不存在:" + msgId);
        }
        noticeMsg.setStatus(0);
        noticeMsg.setGmtModify(new Date());
        return noticeMsgMapper.updateById(noticeMsg) > 0;
    }

    @Override
    public Page<NoticeMsgVO> queryReceive(NoticeMsgReqDTO dto) {
        dto.setReceiver(CommonUtils.getCurrentlogged());
        Page<NoticeMsgVO> pages = noticeMsgMapper
                .selectNoticeReceiveByPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
        if (Objects.nonNull(pages) && CollectionUtils.isNotEmpty(pages.getRecords())) {
            List<NoticeMsgVO> list = setCount(pages.getRecords());
            pages.setRecords(list);
        }
        return pages;
    }

    @Override
    public Page<NoticeMsgVO> querySend(NoticeMsgReqDTO dto) {
        dto.setSender(CommonUtils.getCurrentlogged());
        Page<NoticeMsgVO> pages = noticeMsgMapper
                .selectNoticeSendByPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
        if (Objects.nonNull(pages) && CollectionUtils.isNotEmpty(pages.getRecords())) {
            List<NoticeMsgVO> list = setCount(pages.getRecords());
            pages.setRecords(list);
        }
        return pages;
    }

    private List<NoticeMsgVO> setCount(List<NoticeMsgVO> list) {
        return list.stream().map(noticeMsgVO -> {
            Integer readCount = noticeReadLogMapper.selectCount(
                    Wrappers.lambdaQuery(NoticeReadLog.class).eq(NoticeReadLog::getMsgId, noticeMsgVO.getMsgId()));
            Integer replyCount = noticeMsgReplyMapper.selectCount(Wrappers.lambdaQuery(NoticeMsgReply.class)
                    .eq(NoticeMsgReply::getMsgId, noticeMsgVO.getMsgId()));
            noticeMsgVO.setReadCount(readCount);
            noticeMsgVO.setReplyCount(replyCount);
            return noticeMsgVO;
        }).collect(Collectors.toList());
    }

    @Override
    public NoticeMsgDetailVO queryMsgDetail(String msgId) {
        NoticeMsg noticeMsg = noticeMsgMapper
                .selectOne(Wrappers.lambdaQuery(NoticeMsg.class).eq(NoticeMsg::getMsgId, msgId));
        NoticeMsgDetailVO detailVO = new NoticeMsgDetailVO();
        BeanUtils.copyProperties(noticeMsg, detailVO);
        detailVO.setPublishTime(noticeMsg.getGmtCreate());
        return detailVO;
    }

    @Override
    public void replyMsg(NoticeReplyReqDTO dto) {
        NoticeMsg noticeMsg = noticeMsgMapper.selectOne(
                Wrappers.lambdaQuery(NoticeMsg.class).eq(NoticeMsg::getMsgId, dto.getMsgId())
                        .eq(NoticeMsg::getStatus, 1));
        if (Objects.isNull(noticeMsg)) {
            throw new FastRuntimeException("消息不存在或者已被删除");
        }
        NoticeMsgReply noticeMsgReply = new NoticeMsgReply();
        BeanUtils.copyProperties(dto, noticeMsgReply);
        noticeMsgReply.setStaffId(CommonUtils.getCurrentlogged());
        noticeMsgReplyMapper.insert(noticeMsgReply);
    }


    @Override
    public void readMsg(String msgId, String staffId) {
        if (StringUtils.isNotBlank(msgId)){
            String[] msgIdArr = msgId.split(",");
            for (String mid : msgIdArr) {
                NoticeReadLog readLog = new NoticeReadLog();
                NoticeReadLog noticeReadLog = noticeReadLogMapper
                        .selectOne(Wrappers.lambdaQuery(NoticeReadLog.class).eq(NoticeReadLog::getMsgId, mid)
                                .eq(NoticeReadLog::getStaffId, staffId));
                if (Objects.isNull(noticeReadLog)) {
                    readLog.setMsgId(mid);
                    readLog.setStaffId(staffId);
                    noticeReadLogMapper.insert(readLog);
                } else {
                    log.warn("该消息已读，msgId:{},staffId:{}", mid, staffId);
                }
            }
        }

    }


    @Override
    public Page<NoticeMsgVO> queryReadMsg(NoticeMsgReqDTO dto) {
        dto.setReceiver(CommonUtils.getCurrentlogged());
        return noticeMsgMapper.selectReadNoticeByPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
    }


    @Override
    public Page<NoticeMsgVO> queryUnreadMsg(NoticeMsgReqDTO dto) {
        dto.setReceiver(CommonUtils.getCurrentlogged());
        return noticeMsgMapper.selectUnReadNoticeByPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
    }

    @Override
    public Map<String, Integer> queryAllTypeCount() {
        CommonUtils.setLogInfo();
        Map<String, Integer> result = new HashMap<>();
        String staffId = CommonUtils.getCurrentlogged();
        List<String> msgTypeList = getAllType();
        if (CollectionUtils.isNotEmpty(msgTypeList)) {
            for (String msgType : msgTypeList) {
                result.put(msgType, getCountByType(msgType, staffId));
            }
        }
        return result;
    }

    @Override
    public Integer getCountByType(String type, String staffId) {
        return noticeMsgMapper.selectAllUnReadNotice(staffId,type).size();
    }


    @Override
    public void allRead(String type) {
        String staffId = CommonUtils.getCurrentlogged();
        List<NoticeMsg> allUnreadMsg = noticeMsgMapper.selectAllUnReadNotice(staffId, type);
        List<String> msgIdList = allUnreadMsg.stream().map(NoticeMsg::getMsgId)
                .collect(Collectors.toList());
        List<NoticeReadLog> noticeReadLogs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(msgIdList)) {
            for (String msgId : msgIdList) {
                NoticeReadLog readLog = new NoticeReadLog();
                readLog.setStaffId(staffId);
                readLog.setMsgId(msgId);
                noticeReadLogs.add(readLog);
            }
            noticeReadLogMapper.insertBatchSomeColumn(noticeReadLogs);
        }

    }


    @Override
    public List<NoticeMsgVO> queryAllBanner() {
        List<NoticeMsg> noticeMsgList = noticeMsgMapper.selectAllUnReadNotice(CommonUtils.getCurrentlogged(), null);
        return noticeMsgList.stream().filter(NoticeMsg::getIsBanner).map(noticeMsg -> {
            NoticeMsgVO vo = new NoticeMsgVO();
            BeanUtils.copyProperties(noticeMsg, vo);
            return vo;
        }).collect(Collectors.toList());
    }


    private List<String> getAllType() {
        return opDictInfoMapper.selectList(
                Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "messageType")
                        .eq(OpDictInfo::getStatus, true)).stream().map(OpDictInfo::getType)
                .collect(Collectors.toList());
    }


    @Override
    public void workOrderNoticeByType(WoNoticeDTO woNoticeDTO) {
        if (NoticeTypeEnum.ENTERPRISE_WECHAT.getCode().equals(woNoticeDTO.getNoticeType())){
            log.info("workOrderNoticeByType，开始跨部门提交");
            WeChatReqBaseDTO reqBaseDTO = new WeChatReqBaseDTO();
            reqBaseDTO.setContent(woNoticeDTO.getContent());
            reqBaseDTO.setMentionedMobileList(Arrays.asList(woNoticeDTO.getMobile().split(",")));
            reqBaseDTO.setMsgType(WeChatMsgEnum.TEXT.getValue());
            weChatDubboService.pushMsgToWeChatRobot(reqBaseDTO);
        }
    }



    @Override
    public void publishTmkMsg(Set<String> tmkTypeSet,Integer count, String staffId){
        //去重
        List<String> list = new ArrayList<>();
        for (String s : tmkTypeSet) {
            list.add(TmkTypeEnum.getState(s));
        }
        StringBuilder s = new StringBuilder();
        for (String s1 : list) {
            s.append(s1).append("  ");
        }
        NoticeMsgReqDTO dto = new NoticeMsgReqDTO();
        dto.setType("system_msg");
        dto.setNoticeType("system");
        dto.setSender("system");
        dto.setIsBanner(true);
        dto.setTitle("电销任务分配通知");
        dto.setContent("【电销类型】:" + s.toString().trim() + "\t" + "【数量】:" + count );
        InAuthCrmStaff staff = inAuthCrmStaffMapper.selectById(Long.valueOf(staffId));
        if (Objects.nonNull(staff)){
            dto.setReceiver(staff.getLoginName());
        }
        JSONObject json = new JSONObject();
        json.put("tmkType", list);
        json.put("count",count);
        dto.setJsonDetail(json.toJSONString());

        publishNotice(dto);
    }


    @Override
    public Boolean queryTmkUnReadMsg(String tmkTaskId) {
        return noticeMsgMapper.selectUnReadNoticeByTmkTaskId(tmkTaskId) > 0;
    }

    @Override
    public void publishBannerNotice(List<String> approval, NoticeMsgReqDTO dto) {
        dto.setType("system_msg");
        dto.setNoticeType("system");
        dto.setSender("system");
        dto.setIsBanner(true);
        //待审批人逐个通知
        for (String staffId : approval) {
            dto.setReceiver(staffId);
            publishNotice(dto);
        }
    }
}
