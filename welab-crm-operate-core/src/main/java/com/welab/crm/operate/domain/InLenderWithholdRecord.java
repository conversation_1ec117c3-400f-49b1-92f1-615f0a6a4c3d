package com.welab.crm.operate.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代扣记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_lender_withhold_record")
public class InLenderWithholdRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    private String serviceNo;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 代扣金额
     */
    private BigDecimal amount;

    /**
     * 员工Id
     */
    private Long staffId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 还款类型;YD：按期还款；YH：还清所有逾期
     */
    private String repayType;

    /**
     * 代扣结果
     */
    private String withholdResult;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 代扣渠道
     */
    private String payCode;

    /**
     * 详细结果
     */
    private String detail;

    /**
     * 回调结果
     */
    private String callbackResult;

    /**
     * 回调金额
     */
    private BigDecimal callbackAmount;

    /**
     * 咨询状态
     * 1-已逾期；2-待还款
     */
    private Integer consultationStatus;


}
