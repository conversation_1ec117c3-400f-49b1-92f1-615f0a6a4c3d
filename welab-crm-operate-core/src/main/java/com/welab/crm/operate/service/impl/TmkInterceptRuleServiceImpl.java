package com.welab.crm.operate.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.operate.exception.CrmOperateException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkInterceptRule;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkInterceptRuleReqDTO;
import com.welab.crm.operate.mapper.TmkInterceptRuleMapper;
import com.welab.crm.operate.service.TmkInterceptRuleService;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptHistoryVO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptRuleVO;
import com.welab.exception.FastRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 电销拦截规则service服务
 * <AUTHOR>
 * @date 2021-10-26
 */
@Slf4j
@Service
public class TmkInterceptRuleServiceImpl implements TmkInterceptRuleService {

    @Resource
    private TmkInterceptRuleMapper tmkInterceptRuleMapper;

	@Override
	public Page<TmkInterceptRuleVO> queryInterceptRuleList(TmkInterceptRuleReqDTO reqDTO) {
		log.info("queryInterceptRuleList,reqDTO:{}", JSON.toJSONString(reqDTO));
		String tmkType = reqDTO.getTmkType();
		List<String> tmkTypes = null;
		if (StringUtils.isNotBlank(tmkType)) {
			tmkTypes = Arrays.asList(tmkType.split(","));
		}
		return tmkInterceptRuleMapper
				.queryInterceptRuleByPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO, tmkTypes);
	}

	@Override
	public void addInterceptRule(TmkInterceptRuleReqDTO reqDTO) {
		// 检验uuid业务的拦截规则，生效中的只能有一条数据
		checkAddUuidRule(reqDTO);
		TmkInterceptRule ruleInfo = new TmkInterceptRule();
		BeanUtils.copyProperties(reqDTO, ruleInfo);
		tmkInterceptRuleMapper.insert(ruleInfo);
	}

    @Override
    public void updateInterceptRule(TmkInterceptRuleReqDTO reqDTO) {
    	TmkInterceptRule ruleInfo;
    	
    	if(reqDTO.getId() != null) {
    		ruleInfo = tmkInterceptRuleMapper.selectById(reqDTO.getId());
			// 检验uuid业务的拦截规则，生效中的只能有一条数据
			checkUpdateUuidRule(reqDTO, ruleInfo);
    		if (ruleInfo != null) {
    			BeanUtils.copyProperties(reqDTO, ruleInfo);
    			ruleInfo.setGmtModify(new Date());
    			tmkInterceptRuleMapper.updateById(ruleInfo);
    		}else {
    			log.error("电销拦截规则记录ID{}不存在",reqDTO.getId());
        		throw new FastRuntimeException("电销拦截规则记录ID{}不存在",String.valueOf(reqDTO.getId()));
    		}
    	}else {
    		log.error("电销拦截规则记录ID更新不能为空");
    		throw new FastRuntimeException("电销拦截规则记录ID更新不能为空");
    	}
    }
    
    @Override
	public boolean deleteInterceptRule(BatchInfoReqDTO ids) {
		try {
			for (Long id : ids.getIds()) {
				tmkInterceptRuleMapper.deleteById(id);
			}
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new FastRuntimeException("根据id删除内部电销拦截规则产生异常......", e);
		}
	}
    
    @Override
    public Page<TmkInterceptHistoryVO> queryInterceptHistoryList(TmkInterceptRuleReqDTO reqDTO) {
        log.info("queryInterceptHistoryList reqDTO:{}", JSON.toJSONString(reqDTO));
        
        return tmkInterceptRuleMapper
                .queryInterceptHistoryByPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
    }

	private void checkUpdateUuidRule(TmkInterceptRuleReqDTO reqDTO, TmkInterceptRule ruleInfo) {
		if ("uuid".equals(reqDTO.getTmkType())) {
			checkNumberProperty(reqDTO);
			// 不是生效的可以直接更新，生效中的只能有一条
			if (!"1".equals(reqDTO.getStatus())) {
				return;
			}
			TmkInterceptRule interceptRule = getEnableUuidRule();
			if (interceptRule != null && !interceptRule.getId().equals(ruleInfo.getId())) {
				throw new CrmOperateException("uuid拦截规则已存在启用中的数据不能再启动另一条数据");
			}
		}
	}

	private void checkNumberProperty(TmkInterceptRuleReqDTO reqDTO) {
		try {
			Integer.parseInt(reqDTO.getRemark());
		} catch (Exception e) {
			log.warn("checkNumberProperty 已推送次数:{}", reqDTO.getRemark());
			throw new CrmOperateException("uuid拦截规则配置的已推送次数必须是数字类型");
		}
	}

	private void checkAddUuidRule(TmkInterceptRuleReqDTO reqDTO) {
		if ("uuid".equals(reqDTO.getTmkType())) {
			checkNumberProperty(reqDTO);
			// 检验uuid业务的拦截规则，生效中的只能有一条数据
			TmkInterceptRule interceptRule = getEnableUuidRule();
			if (interceptRule != null) {
				throw new CrmOperateException("uuid拦截规则已存在启用中的数据不能重复创建");
			}
		}
	}

	private TmkInterceptRule getEnableUuidRule(){
		LambdaQueryWrapper<TmkInterceptRule> wrapper = Wrappers.<TmkInterceptRule>lambdaQuery()
				.eq(TmkInterceptRule::getStatus, "1")
				.eq(TmkInterceptRule::getTmkType, "uuid");
		return tmkInterceptRuleMapper.selectOne(wrapper);
	}
}
