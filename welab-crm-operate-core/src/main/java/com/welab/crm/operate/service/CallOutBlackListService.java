package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.BlackListCheckDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListApprovalDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutInterceptionRulesReqDTO;
import com.welab.crm.operate.vo.blacklist.callout.CalloutBlackListVO;

import java.util.List;

/**
 * 外呼黑名单服务
 * <AUTHOR> 
 */
public interface CallOutBlackListService {

	/**
	 * 添加外呼黑名单
	 */
	void addCallOutBlackList(CalloutBlackListReqDTO dto);


	/**
	 * 分页查询外呼黑名单
	 */
	Page<CalloutBlackListVO> queryCallOutBlackList(CalloutBlackListReqDTO dto);


	/**
	 * 审批
	 */
	void approvalCalloutBlackList(CalloutBlackListApprovalDTO dto);


	/**
	 * 取消拉黑
	 */
	void cancelCalloutBlackList(List<Long> ids);


	/**
	 * 添加外呼拦截规则
	 * @param dto
	 */
	void addCalloutInterceptionRules(CalloutInterceptionRulesReqDTO dto);


	/**
	 * 更新外呼拦截规则
	 */
	void updateCalloutInterceptionRules(CalloutInterceptionRulesReqDTO dto);


	/**
	 * 查询外呼拦截规则
	 */
	Page<CalloutInterceptionRulesReqDTO> queryCalloutInterceptionRules(BaseReqDTO dto);

	/**
	 * 删除外呼拦截规则
	 */
	void deleteInterceptionRules(List<Long> ids);


	/**
	 * 检查该客户是否运行外呼
	 */
	boolean checkBlackList(BlackListCheckDTO blackListCheckDTO);


}
