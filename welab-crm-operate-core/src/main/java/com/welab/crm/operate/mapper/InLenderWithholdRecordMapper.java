package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.InLenderWithholdRecord;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.withhold.WithholdReportVO;
import com.welab.crm.operate.dto.withhold.WithholdReqDTO;
import com.welab.crm.operate.vo.withhold.WithholdVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代扣记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface InLenderWithholdRecordMapper extends BaseMapper<InLenderWithholdRecord> {

    Page<WithholdVO> queryWithholdRecordByCondition(Page<WithholdVO> page, @Param("dto") WithholdReqDTO dto);

    /**
     * 查询代扣报表
     * @param dto
     * @return
     */
    List<WithholdReportVO> queryWithholdReport(@Param("dto") ReportBaseDTO dto);

    /**
     * 获取有记录的所有组
     * @return
     */
    List<String> queryWithholdRecordByConditionGroups();
}
