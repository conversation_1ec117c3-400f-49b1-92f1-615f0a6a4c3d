package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.repay.RepayImportDTO;
import com.welab.crm.operate.dto.repay.RepayQueryDTO;
import com.welab.crm.operate.vo.repay.RepayQueryVO;

/**
 * <AUTHOR>
 * @date 2022-11-05
 */
public interface RepayQueryService {

    /**
     * 查询客户还款明细下载列表
     */
    Page<RepayQueryVO> getRepayQuery(RepayQueryDTO queryDTO);


    /**
     * 根据id获取文件下载链接
     * @param id
     * @return
     */
    String getFileUrl(Long id);

    /**
     * 导入查询客户列表
     */
    void importQueryUuidList(ExcelList<RepayImportDTO> uuidList, StaffVO staffVO);
}
