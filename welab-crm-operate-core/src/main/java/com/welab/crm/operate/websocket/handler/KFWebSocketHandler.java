package com.welab.crm.operate.websocket.handler;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.domain.OpStaffStatusHis;
import com.welab.crm.operate.dto.staff.ReportStaffStatusDTO;
import com.welab.crm.operate.service.impl.StaffStatusServiceImpl;
import com.welab.crm.operate.websocket.util.WebSocketUtil;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/5/6 17:31
 */
public class KFWebSocketHandler extends TextWebSocketHandler implements InitializingBean {

    @Autowired
    private ApplicationContext applicationContext;

    private Logger logger = LoggerFactory.getLogger(getClass());

    private static StaffStatusServiceImpl staffStatusService;

    private static AuthHandler authHandler;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("[onOpen][session({}) 接入]", session);
        String mobile = (String) session.getAttributes().get("mobile");
        if (StringUtils.isNotBlank(mobile)) {
            // 签入的时候更新一下之前未结束的状态，因为有可能在发布的时候容器直接被销毁了，所以没有在 afterConnectionClosed 里更新到
            try {
                staffStatusService.updateAllStatus(mobile);
                saveStatus("login", mobile);
                authHandler.execute(session, mobile);
            } catch (Exception e){
                logger.warn("afterConnectionEstablished 更新用户工作状态失败", e);
                // 手动关闭连接
                session.close();
            }
        }

    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage txmessage) throws Exception {
        String message = txmessage.getPayload();
        logger.debug("[onMessage][session({}) 接收到一条消息({})]", session, message);
        try {
            if (StringUtils.isBlank(message)||"heartbeat".equals(message)){
                logger.debug("心跳链接，直接返回");
                session.sendMessage(txmessage);
                return;
            }
            JSONObject jsonMessage = JSON.parseObject(message);

            String statusBefore = jsonMessage.getString("statusBefore");
            String statusAfter = jsonMessage.getString("statusAfter");
            String mobile = WebSocketUtil.getMobileBySession(session);
            if (StringUtils.isNotBlank(statusBefore)) {
                updateStatus(statusBefore, mobile);
            }
            if (StringUtils.isNotBlank(statusAfter)) {
                saveStatus(statusAfter, mobile);
            }

        } catch (Exception e) {
            logger.info("[onMessage][session({}) message({}) 发生异常]", session, e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.info("[onError][session({}) 发生异常]", session, exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        logger.info("[onClose][session({}) 连接关闭。关闭原因是({})}]", session, JSON.toJSONString(status));
        String mobile = WebSocketUtil.getMobileBySession(session);
        if (StringUtils.isNotBlank(mobile)) {
            staffStatusService.updateAllStatus(mobile);
        }

        WebSocketUtil.removeSession(session);
    }

    private void saveStatus(String status,String mobile){
        ReportStaffStatusDTO dto = new ReportStaffStatusDTO();
        dto.setMobile(mobile);
        dto.setStatus(status);
        dto.setStartTime(DateUtil.currentDateStr());
        staffStatusService.reportStatusMsg(dto);
    }

    private void updateStatus(String status,String mobile){
        OpStaffStatusHis lastStatusRecord = staffStatusService.getLastStatusRecord(mobile, status);
        if (Objects.nonNull(lastStatusRecord)) {
            Date now = new Date();
            lastStatusRecord.setEndTime(now);
            lastStatusRecord.setGmtModify(now);
            lastStatusRecord.setDuration(
                    (long) Seconds.secondsBetween(new DateTime(lastStatusRecord.getStartTime()), new DateTime(now))
                            .getSeconds());
            staffStatusService.updateStaffStatus(lastStatusRecord);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        staffStatusService = applicationContext.getBean(StaffStatusServiceImpl.class);
        authHandler = applicationContext.getBean(AuthHandler.class);
    }
}
