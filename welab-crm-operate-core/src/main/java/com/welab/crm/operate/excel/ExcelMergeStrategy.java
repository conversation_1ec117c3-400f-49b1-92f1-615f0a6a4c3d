package com.welab.crm.operate.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * excel合并策略类
 */
@Slf4j
public class ExcelMergeStrategy implements SheetWriteHandler {

    /**
     * 合并单元格集合
     * 四个参数：首行，尾行，首列，尾列
     */
    private final List<CellRangeAddress> cellRangeAddressList;

    public ExcelMergeStrategy(List<CellRangeAddress> cellRangeAddressList) {
        this.cellRangeAddressList = cellRangeAddressList;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        cellRangeAddressList.forEach(cellRangeAddress -> writeSheetHolder.getSheet().addMergedRegionUnsafe(cellRangeAddress));
    }
}
