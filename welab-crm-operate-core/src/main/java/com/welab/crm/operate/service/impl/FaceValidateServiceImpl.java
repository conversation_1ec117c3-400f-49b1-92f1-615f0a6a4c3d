package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.enums.ValidFaceTypeEnum;
import com.welab.crm.interview.enums.ValidFaceVendorEnum;
import com.welab.crm.operate.domain.ConWebotHistory;
import com.welab.crm.operate.dto.report.FaceReportDTO;
import com.welab.crm.operate.mapper.ConWebotHistoryMapper;
import com.welab.crm.operate.service.FaceValidateService;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.face.FaceCauseVO;
import com.welab.crm.operate.vo.face.FaceDayVO;
import com.welab.crm.operate.vo.face.FaceDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class FaceValidateServiceImpl implements FaceValidateService {

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private ConWebotHistoryMapper historyMapper;

    @Override
    public Page<FaceDetailVO> listDetailsByPage(FaceReportDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ConWebotHistory> page = getPage(dto);
        Page<ConWebotHistory> queryPage = historyMapper.getDetailsByPage(page, dto);
        Page<FaceDetailVO> resultPage = new Page<>();
        BeanUtils.copyProperties(queryPage, resultPage);
        if (CollectionUtils.isEmpty(queryPage.getRecords())) {
            return resultPage;
        }
        List<FaceDetailVO> voList = new ArrayList<>(queryPage.getRecords().size());
        for (ConWebotHistory record : queryPage.getRecords()) {
            FaceDetailVO vo = new FaceDetailVO();
            BeanUtils.copyProperties(record, vo);
            vo.setFinalResult(getValidateResult(record.getFinalCode()));
            vo.setSendTime(DateUtils.formatDate(record.getSendTime(), DateUtils.DATE_FORMAT));
            vo.setValidateResult(getValidateResult(record.getValidateCode()));
            if (record.getValidateTime() != null) {
                vo.setValidateTime(DateUtils.formatDate(record.getValidateTime(), DateUtils.DATE_FORMAT));
            }
            vo.setValidateType(ValidFaceTypeEnum.getDescByValue(record.getValidateType()));
            vo.setVendor(ValidFaceVendorEnum.getDescByValue(record.getVendor()));
            voList.add(vo);
        }
        resultPage.setRecords(voList);
        return resultPage;
    }

    @Override
    public List<FaceDetailVO> listDetails(FaceReportDTO dto) {
        setPageSize(dto);
        Page<FaceDetailVO> page = listDetailsByPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return Collections.emptyList();
        } else {
            return page.getRecords();
        }
    }

    @Override
    public Page<FaceDayVO> listDayDataByPage(FaceReportDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ConWebotHistory> page = getPage(dto);
        Page<FaceDayVO> originPage = historyMapper.getDayDataByPage(page, dto);
        if (CollectionUtils.isEmpty(originPage.getRecords())) {
            return originPage;
        }
        List<FaceDayVO> records = originPage.getRecords();
        for (FaceDayVO vo : records) {
            vo.setAvgValidateTimes(getNum(new BigDecimal(vo.getAvgValidateCount()), new BigDecimal(vo.getSuccessCount())));
            vo.setSuccessRate(getRate(new BigDecimal(vo.getSuccessCount()),
                    new BigDecimal(vo.getSendCount() - vo.getUnValidateCount())));
        }
        // 查询统计汇总数据(不分页)
        setPageSize(dto);
        Page<ConWebotHistory> page1 = getPage(dto);
        Page<FaceDayVO> totalPage = historyMapper.getDayDataByPage(page1, dto);
        FaceDayVO totalVo = new FaceDayVO();
        List<FaceDayVO> records1 = totalPage.getRecords();
        for (int i = 0; i < records1.size(); i++) {
            FaceDayVO vo = records1.get(i);
            if (i == 0) {
                BeanUtils.copyProperties(vo, totalVo);
            } else {
                totalVo.setAvgValidateCount(totalVo.getAvgValidateCount() + vo.getAvgValidateCount());
                totalVo.setValidateCount(totalVo.getValidateCount() + vo.getValidateCount());
                totalVo.setUnValidateCount(totalVo.getUnValidateCount() + vo.getUnValidateCount());
                totalVo.setSendCount(totalVo.getSendCount() + vo.getSendCount());
                totalVo.setSuccessCount(totalVo.getSuccessCount() + vo.getSuccessCount());
                totalVo.setFirstSuccessCount(totalVo.getFirstSuccessCount() + vo.getFirstSuccessCount());
            }
        }

        totalVo.setDay("总计");
        totalVo.setAvgValidateTimes(getNum(new BigDecimal(totalVo.getAvgValidateCount()), new BigDecimal(totalVo.getSuccessCount())));
        totalVo.setSuccessRate(getRate(new BigDecimal(totalVo.getSuccessCount()),
                new BigDecimal(totalVo.getSendCount() - totalVo.getUnValidateCount())));
        records.add(totalVo);
        return originPage;
    }

    @Override
    public List<FaceDayVO> listDayData(FaceReportDTO dto) {
        setPageSize(dto);
        Page<FaceDayVO> page = listDayDataByPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return Collections.emptyList();
        } else {
            return page.getRecords();
        }
    }

    @Override
    public Page<FaceCauseVO> listCauseByPage(FaceReportDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        Page<ConWebotHistory> page = getPage(dto);
        Page<FaceCauseVO> originPage = historyMapper.getCauseByPage(page, dto);
        if (CollectionUtils.isNotEmpty(originPage.getRecords())) {
            int total = 0;
            for (FaceCauseVO record : originPage.getRecords()) {
                total += record.getFailCount();
            }
            BigDecimal totalDecimal = new BigDecimal(total);
            for (FaceCauseVO record : originPage.getRecords()) {
                BigDecimal value = new BigDecimal(record.getFailCount());
                record.setFailRate(getRate(value, totalDecimal));
            }
            FaceCauseVO causeTotal = historyMapper.getCauseTotal(dto);
            causeTotal.setFailCause("合计");
            causeTotal.setFailRate("100%");
            originPage.getRecords().add(causeTotal);
        }
        return originPage;
    }

    @Override
    public List<FaceCauseVO> listCause(FaceReportDTO dto) {
        setPageSize(dto);
        Page<FaceCauseVO> page = listCauseByPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return Collections.emptyList();
        } else {
            return page.getRecords();
        }
    }

    private void setPageSize(FaceReportDTO dto) {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(100000);
    }

    private String getRate(BigDecimal up, BigDecimal down) {
        if (down.doubleValue() == 0) {
            return "100%";
        }
        BigDecimal realUp = up.multiply(new BigDecimal(100));
        BigDecimal successRate = realUp.divide(down, 2, RoundingMode.HALF_UP);
        return successRate + "%";
    }

    private String getNum(BigDecimal up, BigDecimal down) {
        if (down.doubleValue() == 0) {
            return "0";
        }
        return up.divide(down, 2, RoundingMode.HALF_UP).toString();
    }

    private String getValidateResult(Integer code) {
        return code == null ? "未验证" : code == 0 ? "成功" : "失败";
    }

    private Page<ConWebotHistory> getPage(FaceReportDTO dto) {
        return new Page<>(dto.getCurrentPage(), dto.getRowsPerPage());
    }
}
