package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通知公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("notice_msg")
public class NoticeMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 唯一索引
     */
    private String msgId;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 接受者
     */
    private String receiver;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 1：正常；0：删除
     */
    private Integer status;

    /**
     * 文件名
     */
    private String fileName;


    /**
     * 是否跑马灯
     */
    private Boolean isBanner;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 通知类型
     */
    private String noticeType;

    /**
     * 工单号
     */
    private String workOrderNo;

    /**
     * 该字段用于传给前端需要的关键字，用json字符串储存
     */
    private String jsonDetail;


}
