package com.welab.crm.operate.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.WoType;
import com.welab.crm.operate.dto.workorder.WorkOrderTypeReqDTO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeVO;

/**
 * <p>
 * 工单分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface WoTypeMapper extends BaseMapper<WoType> {
	Page<WorkOrderTypeVO> queryWorkOrderTypeByPage(Page<WorkOrderTypeVO> page, @Param("reqDTO") WorkOrderTypeReqDTO reqDTO);
	
	List<WorkOrderTypeVO> queryWorkOrderTypeList(@Param("reqDTO") WorkOrderTypeReqDTO reqDTO);
}
