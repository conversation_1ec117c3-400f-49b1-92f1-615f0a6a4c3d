package com.welab.crm.operate.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 贷款信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("data_loan_application")
public class DataLoanApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 工单id
     */
    private Long woTaskId;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 申请时间
     */
    private Date applyTime;
    
    /**
     * 审批时间
     */
    private Date approvalTime;
    
    /**
     * 确认时间
     */
    private Date confirmTime;
    
    /**
     * 放款时间
     */
    private Date loanTime;

    /**
     * 渠道号
     */
    private String channelCode;
    
    /**
     * 资金方
     */
    private String partnerCode;


    /**
     * 资金方编码
     */
    private String partnerCodeNew;
    
    /**
     * 申请期限
     */
    private String applyTenor;
	
	/**
     * 审批期限
     */
    private String approvalTenor;
	
	/**
     * 用户等级
     */
    private String userLevel;
	
	/**
     * 订单状态
     */
    private String status;
	
	/**
     * 申请金额
     */
    private BigDecimal applyAmount;
	
	/**
     * 审批金额
     */
    private BigDecimal approvalAmount;

    /**
     * 总利率
     */
    private String totalRate;

    /**
     * 债转公司
     */
    private String transferCompany;
}
