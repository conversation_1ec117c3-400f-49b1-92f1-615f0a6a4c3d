package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpStaffStatusHis;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.model.ReportCallInWorkStatusModel;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 坐席工作状态历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2021-11-20
 */
public interface OpStaffStatusHisMapper extends BaseMapper<OpStaffStatusHis> {

    /**
     * 查询工作状态报表
     */
    Page<ReportWorkStatusSummaryVO> selectWorkStatusSummary(Page<ReportWorkStatusSummaryVO> page, @Param("filter") ReportWorkStatusDTO dto);

    /**
     * 客服呼入工作状态报表
     */
    Page<ReportCallInWorkStatusModel> selectCallInWorkStatus(Page<ReportCallInWorkStatusModel> page, @Param("filter") ReportWorkStatusDTO dto);

    /**
     * 查询员工效能报表
     */
    Page<ReportWorkStatusSummaryVO> selectStaffEfficiency(Page<ReportWorkStatusSummaryVO> page, @Param("filter") ReportWorkStatusDTO dto);

    /**
     * 查询员工每一天的工作总时长(loginTime和logoutTime,ACW状态表示的话后处理时长)
     */
    List<ReportWorkStatusSummaryVO> selectLoginDurationByDay(ReportWorkStatusDTO dto);
}
