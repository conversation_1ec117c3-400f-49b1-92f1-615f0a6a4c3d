package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkSummaryDict;
import com.welab.crm.operate.dto.dict.tmk.TmkSumDictQueryDTO;
import com.welab.crm.operate.dto.dict.tmk.TmkSummaryDictDTO;

/**
 * 电销话务小结服务
 *
 * <AUTHOR>
 * @date 2022/2/18 14:30
 */
public interface TmkSummaryDictService {

    /**
     * 查询营销小结记录列表
     */
    Page<TmkSummaryDict> listTmkSummaryDict(TmkSumDictQueryDTO queryDTO);

    /**
     * 新增营销小结记录
     */
    void addTmkSummaryDict(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO);

    /**
     * 更新营销小结记录
     */
    void updateTmkSummaryDict(String staffCode, TmkSummaryDictDTO tmkSummaryDictDTO);
}
