package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;

@Service
public class CustomUserDetailsService implements UserDetailsService {
    @Autowired
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        InAuthCrmStaff staff = inAuthCrmStaffMapper.selectOne(Wrappers.lambdaQuery(InAuthCrmStaff.class).eq(InAuthCrmStaff::getLoginName, username)
                .eq(InAuthCrmStaff::getIsStatus, 1));
        
        if (staff == null) {
            throw new UsernameNotFoundException("用户名或密码不正确");
        }

        return new org.springframework.security.core.userdetails.User(
                staff.getLoginName(),
                staff.getPassword(),
                getAuthorities());
    }

    private Collection<? extends GrantedAuthority> getAuthorities() {
        return Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"));
    }
}