package com.welab.crm.operate.service;

import com.alibaba.druid.util.StringUtils;
import com.google.code.kaptcha.Producer;
import com.welab.exception.FastRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCommands;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Service
public class CaptchaService {

    private final Producer captchaProducer;
    private final JedisCommands jedisCommands;
    
    // 验证码有效期（秒）
    private static final int CAPTCHA_EXPIRE_SECOND = 5 * 60;
    
    
    
    @Autowired
    public CaptchaService(Producer captchaProducer, JedisCommands jedisCommands) {
        this.captchaProducer = captchaProducer;
        this.jedisCommands = jedisCommands;
    }
    
    /**
     * 生成验证码图片
     */
    public byte[] generateCaptchaImage(String uuid) {
        // 生成验证码文本
        String captchaText = captchaProducer.createText();
        
        // 存储到Redis，有效期5分钟

        jedisCommands.setex(getRedisKey(uuid), CAPTCHA_EXPIRE_SECOND, captchaText);


        // 生成验证码图片
        BufferedImage image = captchaProducer.createImage(captchaText);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", outputStream);
        } catch (IOException e) {
            throw new FastRuntimeException("生成验证码图片失败", e);
        }
        
        return outputStream.toByteArray();
    }
    
    /**
     * 验证验证码
     */
    public boolean validateCaptcha(String uuid, String code) {
        if (StringUtils.isEmpty(uuid) || StringUtils.isEmpty(code)) {
            return false;
        }
        
        String key = getRedisKey(uuid);
        String storedCode = jedisCommands.get(key);
        if (storedCode == null) {
            return false;
        }
        
        // 验证后删除验证码
        jedisCommands.del(key);
        
        // 不区分大小写验证
        return storedCode.equalsIgnoreCase(code);
    }
    
    
    public String getRedisKey(String uuid) {
        return "crm:captcha:" + uuid;
    }
}