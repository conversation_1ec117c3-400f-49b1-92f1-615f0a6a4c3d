package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.constant.ParamConstant;
import com.welab.crm.operate.domain.SettleProofApplyRecord;
import com.welab.crm.operate.enums.LzCallbackEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.SettleProofApplyRecordMapper;
import com.welab.crm.operate.service.ISettlementProofService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.settlement.SettleProofApplyRecordVO;
import com.welab.crm.operate.vo.settlement.SettlementProofReqDTO;
import com.welab.crm.operate.vo.settlement.SettlementProofVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/22
 */
@Service
@Slf4j
public class SettlementProofServiceImpl implements ISettlementProofService {

    @Autowired
    private SettleProofApplyRecordMapper settleProofApplyRecordMapper;

    @Resource
    private IUploadService uploadService;

    @Resource
    private FinanceService financeService;


    @Value("${lender.pre.url}")
    private String lenderUrl;


    private static final Integer HTTP_TIME_OUT = 60000;


    @Override
    public void addProofRecord(SettlementProofVO dto, String applicationId) {
        try {

            SettleProofApplyRecord vo = new SettleProofApplyRecord();
            vo.setApplyTime(new Date());
            vo.setStaffId(CommonUtils.getCurrentloggedStaffId().toString());
            vo.setApplicationId(applicationId);
            vo.setPartnerCode(dto.getPartnerCode());
            vo.setState(dto.getStatus());
            vo.setFilePath(dto.getFileUrl());
            settleProofApplyRecordMapper.insert(vo);
        } catch (Exception e) {
            throw new FastRuntimeException("system.add.error.default", e.getMessage());
        }
    }


    @Override
    public void updateProofRecord(SettlementProofVO dto, String applicationId) {
        try {
            QueryWrapper<SettleProofApplyRecord> wrapper = new QueryWrapper<SettleProofApplyRecord>();
            wrapper.eq("application_id", applicationId);
            SettleProofApplyRecord entity = new SettleProofApplyRecord();
            entity.setFilePath(dto.getFileUrl());
            settleProofApplyRecordMapper.update(entity, wrapper);
        } catch (Exception e) {
            throw new FastRuntimeException("system.update.error.default", e.getMessage());
        }
    }

    @Override
    public Page<SettleProofApplyRecordVO> queryProofRecord(SettlementProofReqDTO dto) {
        try {
            Page<SettleProofApplyRecordVO> result = settleProofApplyRecordMapper.query(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public String queryDownloadUrl(Long id) {
        SettleProofApplyRecord record = settleProofApplyRecordMapper.selectById(id);
        if (Objects.isNull(record)){
            throw new FastRuntimeException("结清证明不存在");
        }
        
        return record.getFilePath();
    }

    @Override
    public SettlementProofVO getOppoSettlement(String applicationId) {
        SettlementProofVO vo = new SettlementProofVO();

        try {
            log.info("getOppoSettlement 获取oppo结清证明文件，合同号:{}", applicationId);
            String oppoSettlementUrl = lenderUrl + Constant.OPPO_SETTLEMENT_GET_URL;
            HashMap<String, Object> map = new HashMap<>();
            map.put("applicationId", applicationId);
            HttpConfig config = HttpConfig.custom().url(oppoSettlementUrl).map(map)
                    .connectTimeout(HTTP_TIME_OUT).socketTimeout(HTTP_TIME_OUT).requestTimeout(HTTP_TIME_OUT);
            String resultStr = HttpClientUtil.post(config);
            log.info("getOppoSettlement 接口返回数据:{}", resultStr);
            Response response = JSON.parseObject(resultStr, Response.class);
            if (Response.isSuccess(response)) {
                log.info("获取oppo结清证明文件成功,applicationId:{}", applicationId);
                    // 返回文件oss下载路径
                    String fileUrl = uploadToOssAndGetFileUrl((String)response.getResult(), applicationId + "结清证明.pdf");
                    vo.setPartnerCode(ParamConstant.OPPO);
                    vo.setFileUrl(fileUrl);
                    vo.setStatus(LzCallbackEnum.SUCCESS.getMsg());
                    vo.setMsg("获取oppo结清文件成功");
                    addProofRecord(vo, applicationId);

            } else {
                log.warn("getOppoSettlement 获取oppo结清证明失败, applicationId:{}, response:{}", applicationId, JSON.toJSON(response));
                vo.setStatus(LzCallbackEnum.FAIL.getMsg());
                vo.setMsg(response.getMessage());
            }
        } catch (Exception e){
            log.error("getOppoSettlement，异常, applicationId:" + applicationId, e);
            vo.setStatus(LzCallbackEnum.FAIL.getMsg());
            vo.setMsg(e.getMessage());
        }

        return vo;
    }

    @Override
    public SettlementProofVO getSettlement(String applicationId, String partnerCode, String standardPartnerCode) {
        SettlementProofVO vo = new SettlementProofVO();
        try {
            log.info("getSettlement applicationId:{},partnerCode:{}", applicationId, partnerCode);
            String fileBase64 = financeService.getSettlementUrl(applicationId, partnerCode);
            log.info("getSettlement fileBase64:{}", fileBase64);
            if (StringUtils.isNotBlank(fileBase64)) {
                vo.setPartnerCode(standardPartnerCode);
                vo.setFileUrl(uploadToOssAndGetFileUrl(fileBase64, applicationId + "结清证明.pdf"));
                vo.setStatus(LzCallbackEnum.SUCCESS.getMsg());
                vo.setMsg("获取结清文件成功");
                addProofRecord(vo, applicationId);
            } else {
                log.warn("getSettlement 获取结清证明失败, applicationId:{}, partnerCode:{}", applicationId, partnerCode);
                vo.setStatus(LzCallbackEnum.FAIL.getMsg());
                vo.setMsg("获取结清证明失败");
            }
        } catch (Exception e){
            log.error("getSettlement，异常, applicationId:" + applicationId, e);
            vo.setStatus(LzCallbackEnum.FAIL.getMsg());
            vo.setMsg(e.getMessage());
        }
        return vo;
        
    }

    /**
     * 上传文件到oss并且返回下载地址
     * @param fileBase64 文件base64编码
     * @param fileName 文件名
     * @return
     * @throws Exception
     */
    private String uploadToOssAndGetFileUrl(String fileBase64, String fileName) throws Exception {
        byte[] bytes = new BASE64Decoder().decodeBuffer(fileBase64);
        // 上传到oss
        Response<String> uploadResponse = uploadService.uploadFile(bytes, fileName);
        if (Response.isSuccess(uploadResponse)) {
            log.info("结清证明上传oss成功, fileName:{}", fileName);
            String tfileName = uploadResponse.getResult();
            Map<String, Object> filePathMap = uploadService.getUploadFile(Arrays.asList(tfileName));
            return (String)filePathMap.get(tfileName);
        }

        log.info("结清证明上传oss失败, fileName:{}", fileName);
        throw new CrmOperateException("结清证明上传oss失败");
    }


    /**
     * 是否重复申请
     * @param applicationId
     */
    @Override
    public boolean queryDupApply(String applicationId) {
        QueryWrapper<SettleProofApplyRecord> wrapper = new QueryWrapper<SettleProofApplyRecord>();
        wrapper.eq("application_id", applicationId);
        wrapper.orderByDesc("gmt_create");
        List<SettleProofApplyRecord> resList = settleProofApplyRecordMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(resList)) {
            return false;
        }
        SettleProofApplyRecord res = resList.get(0);
        if (StringUtils.isBlank(res.getFilePath())) {
            throw new FastRuntimeException("system.add.error.default", "请勿重复申请!");
        }

        // 兰州银行不更新
        if (ParamConstant.LZBANK.equals(res.getPartnerCode())) {
            return true;
        }
        // 更新链接
        String filePath = res.getFilePath();
        String fileName = getFileNameByPath(filePath);
        Map<String, Object> uploadFile = uploadService.getUploadFile(Arrays.asList(fileName));
        res.setFilePath((String)uploadFile.get(fileName));
        res.setGmtModify(new Date());
        settleProofApplyRecordMapper.updateById(res);

        return true;
    }

    private String getFileNameByPath(String url) {
        try {
            int start = url.indexOf("documents/");
            int end = url.indexOf("?Expires");
            String result = url.substring(start + "documents/".length(), end);
            return URLDecoder.decode(result, "UTF-8");
        } catch (Exception e) {
            log.warn("getFileNameByPath error", e);
            throw new FastRuntimeException("更新结清文档链接异常");
        }

    }
}
