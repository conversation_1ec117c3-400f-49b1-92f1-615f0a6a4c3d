package com.welab.crm.operate.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummaryReportQueryDTO;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummarySaveDTO;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryDictVO;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryReportVO;
import com.welab.crm.operate.vo.callbackSummary.CallbackTypeVO;
import com.welab.crm.operate.vo.workorder.WorkOrderLogVO;

import java.util.List;
import java.util.Map;

/**
 * 回电小结服务
 * 
 * <AUTHOR>
 */
public interface CallbackSummaryService {

    /**
     * 查询回电小结字典数据
     * 
     * @return
     */
    CallbackSummaryDictVO queryCallbackSummaryDict();

    /**
     * 查询回电小结字典另一种方式
     * 
     * @return
     */
    Map<String, List<CallbackTypeVO>> queryCallbackSummaryDict2();

    /**
     * 更新回电小结字典
     * 
     * @param dictVO
     */
    void updateCallbackSummaryDict(CallbackSummaryDictVO dictVO);

    /**
     * 更新回电小结字典另一种方式
     * 
     * @param map
     */
    void updateCallbackSummaryDict2(Map<String, List<CallbackTypeVO>> map);

    /**
     * 添加回电小结
     * 
     * @param dto
     */
    void addCallbackSummary(CallbackSummarySaveDTO dto);

    /**
     * 查询回电小结报表
     * 
     * @param dto
     * @return
     */
    Page<CallbackSummaryReportVO> queryCallbackSummaryReport(CallbackSummaryReportQueryDTO dto);


    /**
     * 查询回电小结记录
     * @param orderNo
     */
    List<WorkOrderLogVO> queryCallbackSummaryLogList(String orderNo);

}
