package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电话小结收藏记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_call_summary")
public class InCallSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 手机
     */
    private String staffMobile;

    /**
     * 描述
     */
    private String description;

    /**
     * 子类id
     */
    private Long lastDictId;

    /**
     * 状态 0-无 1-置顶
     */
    private Integer topStatus;

    /**
     * 置顶时间
     */
    private Date topTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
