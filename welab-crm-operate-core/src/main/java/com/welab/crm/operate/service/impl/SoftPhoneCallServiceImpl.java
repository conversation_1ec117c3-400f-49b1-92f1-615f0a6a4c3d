package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.welab.common.keygen.KeyGenerator;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.interview.dto.ConPhoneSummaryDTO;
import com.welab.crm.interview.service.ConPhoneSummaryService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.vo.ai.AiSummaryResVO;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.phone.AgentSkillUpdateDTO;
import com.welab.crm.operate.dto.phone.PhoneLoginInfoReqDTO;
import com.welab.crm.operate.dto.phone.PhoneSummaryReqDTO;
import com.welab.crm.operate.dto.phone.SoftPhoneReqDTO;
import com.welab.crm.operate.enums.CallTypeEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.model.OnlineHistoryModel;
import com.welab.crm.operate.service.SoftPhoneCallService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.agent.AgentVO;
import com.welab.crm.operate.vo.ai.AiTmkCallBackVO;
import com.welab.crm.operate.vo.phone.IsSummaryVO;
import com.welab.crm.operate.vo.phone.PhoneLoginInfoResVO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.phone.SoftPhoneInfoVO;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.loanprocedure.vo.LoanVO;
import com.welab.privacy.util.http.HttpClients;
import com.welab.security.util.MD5Util;
import com.welab.util.security.MD5;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/21 15:19
 */
@Slf4j
@Service
public class SoftPhoneCallServiceImpl implements SoftPhoneCallService {

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Resource
    private ConPhoneSummaryMapper conPhoneSummaryMapper;

    @Resource
    private KeyGenerator keyGenerator;

    @Resource
    private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private InPhoneEnterpriseConfMapper inPhoneEnterpriseConfMapper;

    @Resource
    private TmkSummaryMapper tmkSummaryMapper;

    @Resource
    private DataCustomerMapper customerMapper;

    @Resource
    private AiTmkCallbackMapper aiTmkCallbackMapper;

    @Resource
    private TrTokenService trTokenService;
    
    @Resource
    private CallbackSummaryMapper callbackSummaryMapper;

    @Resource
    private TrUtil trUtil;

    @Resource
    private AsyncTaskExecutor phoneSummaryExecutor;

    @Resource
    private ConPhoneSummaryService conPhoneSummaryService;
    
    @Resource
    private LoanApplicationService loanApplicationService;
    
    @Resource
    private CallInfoAiSummaryMapper callInfoAiSummaryMapper;

    @Value("${online.system.http.url.pre}")
    private String onlineSystemUrlPre;

    @Value("${online.system.user.token}")
    private String onlineHistoryUserToken;

    @Value("${online.system.app.key}")
    private String onlineSystemAppKey;

    @Value("${ai.summary.base.url}")
    private String aiSummaryBaseUrl;

    private static final Integer AI_TIME_OUT = 60000;


    private static final String MIN_CALL_DURATION = "00:00:30";



    @Override
    public void addSoftPhoneRecord(SoftPhoneReqDTO reqDTO) {
//        log.info("addSoftPhoneRecord, params:{}", JSON.toJSONString(reqDTO));
        if (StringUtils.isBlank(reqDTO.getCdrMainUniqueId())) {
            log.warn("addSoftPhoneRecord RequestUniqueId is null, customerNumber:{}", reqDTO.getCdrCustomerNumber());
        }

        QueryWrapper<ConPhoneCallInfo> wrapper = new QueryWrapper<>();
        if ("1".equals(reqDTO.getCdrCallType())) {
            wrapper.eq("cdr_main_unique_id", reqDTO.getCdrMainUniqueId());
            wrapper.eq("cdr_callee_cno", reqDTO.getCno());
        } else {
            wrapper.eq("cdr_main_unique_id", reqDTO.getCdrMainUniqueId());
            wrapper.eq("cdr_cno", reqDTO.getCno());
        }
        // 先查询，防止回调比添加快
        ConPhoneCallInfo info = conPhoneCallInfoMapper.selectOne(wrapper);

        if (Objects.isNull(info)) {
            info = new ConPhoneCallInfo();
        }

        BeanUtils.copyProperties(reqDTO, info, CommonUtils.getNullPropertyNames(reqDTO));

        if ("1".equals(reqDTO.getCdrCallType())) {
            info.setCdrCalleeCno(reqDTO.getCno());
        } else {
            info.setCdrCno(reqDTO.getCno());
        }
        info.setStaffId(CommonUtils.getCurrentloggedStaffId());
        info.setGroupCode(CommonUtils.getCurrentloggedOrg());
        insertOrUpdate(info);


    }

    @Override
    public Page<SoftPhoneInfoVO> querySoftPhoneRecord(SoftPhoneReqDTO reqDTO) {
        log.info("querySoftPhoneRecord,SoftPhoneReqDTO:{}", JSON.toJSONString(reqDTO));

        Page<ConPhoneCallInfo> pageInfo = conPhoneCallInfoMapper
                .selectListByPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
        Page<SoftPhoneInfoVO> pageVo = new Page<>();
        if (Objects.nonNull(pageInfo) && CollectionUtils.isNotEmpty(pageInfo.getRecords())) {
            List<SoftPhoneInfoVO> result = pageInfo.getRecords().stream().map(this::convertToSoftPhoneVo)
                    .collect(Collectors.toList());
            pageVo.setRecords(result);
            pageVo.setTotal(pageInfo.getTotal());
            pageVo.setCurrent(pageInfo.getCurrent());
            pageVo.setSize(pageInfo.getSize());
        }

        return pageVo;
    }

    @Override
    public void savePhoneSummary(PhoneSummaryReqDTO reqDTO) {
//        log.info("savePhoneSummary, params:{}", JSON.toJSONString(reqDTO));
        queryConsultationStatus(reqDTO);
        ConPhoneSummary summary = conPhoneSummaryMapper.selectOne(Wrappers.lambdaQuery(ConPhoneSummary.class)
                .eq(ConPhoneSummary::getCdrMainUniqueId, reqDTO.getCdrMainUniqueId())
                .eq(ConPhoneSummary::getStaffId, CommonUtils.getCurrentloggedStaffId()));
        if (Objects.isNull(summary)) {
            summary = new ConPhoneSummary();
        }
        BeanUtils.copyProperties(reqDTO, summary, CommonUtils.getNullPropertyNames(reqDTO));
        summary.setSaveSummaryTime(new Date());
        saveSummary(summary);

        // 更新电话小结字典 sort 字段
        updateSortCount(reqDTO.getCallSummaryCode());
        // 如果是呼入类型的小结同步到催收系统
        final ConPhoneSummary s = summary;
        phoneSummaryExecutor.execute(()->pushSummaryToCollection(s));
    }

    /**
     * 更新电话小结
     * @param callSummaryCode
     */
    private void updateSortCount(String callSummaryCode) {
        opDictInfoMapper.increaseSort(Long.valueOf(callSummaryCode));
    }

    /**
     * 查询咨询状态
     * @param reqDTO
     */
    private void queryConsultationStatus(PhoneSummaryReqDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getUserId())) {
            List<LoanVO> loanVOList = loanApplicationService.getLoanVOList(reqDTO.getUserId());
            for (int i = 0; i < loanVOList.size(); i++) {
                LoanVO loanVO = loanVOList.get(i);
                if (loanVO.getOverdueDay() > 0) {
                    reqDTO.setConsultationStatus(1);
                    break;
                }

                if (i == (loanVOList.size() - 1)) {
                    reqDTO.setConsultationStatus(2);
                }
            }
        }
    }

    private void saveSummary(ConPhoneSummary summary) {
        if (Objects.nonNull(summary.getId())) {
            summary.setGmtModify(new Date());
            conPhoneSummaryMapper.updateById(summary);
        } else {
            conPhoneSummaryMapper.insert(summary);
        }
    }

    private void pushSummaryToCollection(ConPhoneSummary summary) {
        try {
            LambdaQueryWrapper<ConPhoneCallInfo> wrapper = Wrappers.<ConPhoneCallInfo>lambdaQuery()
                    .eq(ConPhoneCallInfo::getCdrMainUniqueId, summary.getCdrMainUniqueId());
            List<ConPhoneCallInfo> infoList = conPhoneCallInfoMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(infoList)) {
                log.error("pushSummaryToCollection get callInfo null: {}", summary);
                return;
            }
            ConPhoneCallInfo callInfo = infoList.get(0);
            // 呼入的才需要同步到催收系统->2023.6.12 呼入呼出都要同步催收系统
            /*if (!CallTypeEnum.IN_CALL.getValue().equals(callInfo.getCdrCallType())) {
                return;
            }*/
            ConPhoneSummaryDTO dto = new ConPhoneSummaryDTO();
            if (!CallTypeEnum.IN_CALL.getValue().equals(callInfo.getCdrCallType())) {
                dto.setCdrCalleeCno(callInfo.getCdrCno());
            } else {
                dto.setCdrCalleeCno(callInfo.getCdrCalleeCno());
            }
            dto.setCdrStartTime(callInfo.getGmtCreate());
            BeanUtils.copyProperties(summary, dto);
            dto.setMainId(summary.getId());
            dto.setCreateUser(summary.getStaffId());
            dto.setLstUpdUser(summary.getStaffId());
            DataCustomer customer = customerMapper.selectById(dto.getCustomerId());
            if (customer.getUserId() == null) {
                log.info("pushSummaryToCollection return,  cause userId null, param: {}", summary);
                return;
            } else {
                dto.setUserId(customer.getUserId());
            }
            dto.setCdrCallType(callInfo.getCdrCallType());
            conPhoneSummaryService.savePhoneSummary(dto);
        } catch (Exception e) {
            log.error("pushSummaryToCollection exception: {}, param: {}", e.getMessage(), summary, e);
        }
    }

    @Override
    public List<PhoneSummaryVO> queryPhoneSummaryByPage(PhoneSummaryReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getUuid()) && StringUtils.isBlank(reqDTO.getMobile())) {
            log.warn("queryPhoneSummaryByPage 参数均为空,loginName:{},请求参数:{}", CommonUtils.getCurrentlogged(),
                    JSON.toJSONString(reqDTO));
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(reqDTO.getMobile())) {
            if (!reqDTO.getMobile().matches(Constant.MOBILE)) {
                reqDTO.setMobile(AesUtils.decrypt(reqDTO.getMobile()));
            }
            
        }

        List<DataCustomer> dataCustomers = null;
        if (StringUtils.isNotBlank(reqDTO.getUuid())) {
            // 根据uuid查询出全部的customer表记录
            dataCustomers = customerMapper
                    .selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getUuid, reqDTO.getUuid()));
        } else if (StringUtils.isNotBlank(reqDTO.getMobile())) {
            // 根据手机号查询出全部的customer表记录
            dataCustomers = customerMapper
                    .selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, reqDTO.getMobile()));
        }
        if (CollectionUtils.isEmpty(dataCustomers)) {
            return new ArrayList<>();
        }
        DataCustomer customer = dataCustomers.get(0);
        List<Long> customerIdList = dataCustomers.stream().map(DataCustomer::getId).collect(Collectors.toList());
        reqDTO.setCustomerIdList(customerIdList);
        List<PhoneSummaryVO> phoneSummaryVOS = conPhoneSummaryMapper.selectSummary(reqDTO);
        for (PhoneSummaryVO phoneSummaryVO : phoneSummaryVOS) {
            OpDictInfo opDictInfo = new OpDictInfo();
            opDictInfo.setCategory("callType");
            opDictInfo.setType(phoneSummaryVO.getCallType());
            phoneSummaryVO.setCallType(getDictInfo(opDictInfo));
            if (Objects.isNull(phoneSummaryVO.getCallTime())){
                phoneSummaryVO.setCallTime(phoneSummaryVO.getGmtCreate());
            }
            if (StringUtils.isBlank(phoneSummaryVO.getPhoneNumber())){
                phoneSummaryVO.setPhoneNumber(reqDTO.getMobile());
            }
            // 名单类型
            setMapName(phoneSummaryVO);

            // 手机号脱敏
            phoneSummaryVO.setPhoneNumber(SecurityUtil.maskMobile(phoneSummaryVO.getPhoneNumber()));
        }
        List<AiTmkCallBackVO> ais = aiTmkCallbackMapper.queryCallBack(customer.getUserId());
        List<PhoneSummaryVO> callBacks = null;
        if (null != ais && !ais.isEmpty()) {
            callBacks = ais.stream().map(t -> {
                PhoneSummaryVO item = getPhoneSummaryVO(t);
                item.setMapName(t.getType());
                return item;
            }).collect(Collectors.toList());
        }
        if (null != callBacks && !callBacks.isEmpty()) {
            phoneSummaryVOS.addAll(callBacks);
        }
        List<AiTmkCallBackVO> uuids = aiTmkCallbackMapper.queryUuidCallBack(customer.getUserId());
        List<PhoneSummaryVO> uuidCallBacks = null;
        if (CollectionUtils.isNotEmpty(uuids)) {
            uuidCallBacks = uuids.stream().map(t -> {
                PhoneSummaryVO item = getPhoneSummaryVO(t);
                item.setMapName("uuid");
                return item;
            }).collect(Collectors.toList());
        }
        if (null != uuidCallBacks && !uuidCallBacks.isEmpty()) {
            phoneSummaryVOS.addAll(uuidCallBacks);
        }
        

        //查询回电小结
        queryCallbackSummary(phoneSummaryVOS, customer.getUuid());
        // 查询在线系统小结
        queryOnlineSummary(phoneSummaryVOS, customer.getUserId());
        phoneSummaryVOS = phoneSummaryVOS.stream().sorted(Comparator.comparing(PhoneSummaryVO::getSaveSummaryTime).reversed()).collect(Collectors.toList());
        phoneSummaryVOS.forEach(item -> item.setPhoneNumber(SecurityUtil.maskMobilePro(item.getPhoneNumber())));
        return phoneSummaryVOS;
        
    }

    public void queryOnlineSummary(List<PhoneSummaryVO> phoneSummaryVOS, Long userId) {

        try {
            // 查询在线系统，返回联系历史
            Map<String, String> params = new HashMap<>();
            params.put("userId", String.valueOf(userId));
            params.put("userToken", onlineHistoryUserToken);
            String flowCode = UUID.randomUUID().toString();
            params.put("flowCode", flowCode);
            String timestamp = String.valueOf(System.currentTimeMillis());
            params.put("timestamp", timestamp);
            params.put("sign", MD5Util.md5(onlineSystemAppKey + timestamp + flowCode));

            String resStr =
                    HttpClients.create().setUrl(onlineSystemUrlPre + "/api/user/history").addURLParams(params).doGet();
            if (StringUtils.isNotBlank(resStr)) {
                JSONObject jsonObject = JSON.parseObject(resStr);
                if (jsonObject.getIntValue("ret") != 0) {
                    log.warn("查询在线系统联系历史失败,res:{}", resStr);
                    return;
                }
                List<OnlineHistoryModel> modelList = JSON.parseArray(jsonObject.getString("data"), OnlineHistoryModel.class);
                for (OnlineHistoryModel model : modelList) {
                    PhoneSummaryVO vo = new PhoneSummaryVO();
                    vo.setPhoneNumber(model.getMobile());
                    if (StringUtils.isNotBlank(model.getStartTime())) {
                        vo.setCallTime(DateUtil.stringToDate(model.getStartTime()));
                    }
                    if (StringUtils.isNotBlank(model.getSummaryRemark())) {
                        vo.setSaveSummaryTime(DateUtil.stringToDate(model.getSummaryTime()));
                    } else if (StringUtils.isNotBlank(model.getStartTime())){
                        vo.setSaveSummaryTime(DateUtil.stringToDate(model.getStartTime()));
                    }
                    vo.setCallType("在线");
                    vo.setStaffName(model.getLastServiceUser());
                    vo.setCdrCno(model.getWorkNo());
                    vo.setCallSummary(model.getSummary());
                    vo.setCallComment(model.getSummaryRemark());
                    vo.setGroupName("在线组");
                    vo.setAiSummary(model.getAiSummary());
                    phoneSummaryVOS.add(vo);
                }
            }
        } catch (Exception e){
            log.warn("queryOnlineSummary error", e);
        }

    }

    /**
     * 查询回电小结
     * @param phoneSummaryVOS
     * @param uuid
     */
    private void queryCallbackSummary(List<PhoneSummaryVO> phoneSummaryVOS, String uuid) {
        List<PhoneSummaryVO> summaryList = callbackSummaryMapper.querySummaryByUuid(uuid);
        if (CollectionUtils.isEmpty(summaryList)){
            return;
        }
        // 解析小结内容
        for (PhoneSummaryVO summaryVO : summaryList) {
            // taskID临时保存业务类型+联系结果
            StringBuilder callSummary = new StringBuilder(summaryVO.getTaskId());
            // 后续的小结内容从json串中解析出来
            JSONObject jsonObject = JSON.parseObject(summaryVO.getCallSummary());
            Set<String> typeSet = jsonObject.keySet();
            callSummary.append("(").append(Joiner.on(",").join(typeSet)).append(")");
            summaryVO.setTaskId(null);
            summaryVO.setCallSummary(callSummary.toString());
        }
        phoneSummaryVOS.addAll(summaryList);
    }

    private PhoneSummaryVO getPhoneSummaryVO(AiTmkCallBackVO t){
        PhoneSummaryVO item = new PhoneSummaryVO();
        item.setPhoneNumber(SecurityUtil.maskMobile(t.getMobile()));
        item.setCallTime(t.getCallTime());
        item.setSaveSummaryTime(t.getEndTime());
        item.setCallType(t.getPhoneStatus());
        item.setStaffName("自营AI");
        item.setGroupName("机器人");
        return item;
    }

    private void setMapName(PhoneSummaryVO phoneSummaryVO) {
        if (StringUtils.isNotBlank(phoneSummaryVO.getTaskId())){
            phoneSummaryVO.setMapName(CommonUtils.getMapNameByTaskId(phoneSummaryVO.getTaskId()));
        }
    }

    @Override
    public List<PhoneLoginInfoResVO> getPhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO) {
        try {
            CommonUtils.setLogInfo();
            InPhoneLoginInfo info = new InPhoneLoginInfo();
            info.setUserTel(CommonUtils.getCurrentlogged());
            List<InPhoneLoginInfo> list = getLoginInfoBySelective(info);
            List<InPhoneLoginInfo> resultList = new ArrayList<>();
            for (InPhoneLoginInfo loginInfo : list) {
                String[] orgs = loginInfo.getOrg().split(",");
                for (String org : orgs) {
                    InPhoneLoginInfo result = new InPhoneLoginInfo();
                    BeanUtils.copyProperties(loginInfo, result);
                    result.setOrg(org);
                    resultList.add(result);
                }
            }
            if (CollectionUtils.isNotEmpty(resultList)) {
                List<PhoneLoginInfoResVO> datas = resultList.stream().map(item -> convertToInfoResVO(item, true))
                        .collect(Collectors.toList());
                return datas;
            }
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
        return null;
    }

    @Override
    public Page<PhoneLoginInfoResVO> getLoginInfoByPage(PhoneLoginInfoReqDTO reqDTO) {
        try {
            InPhoneLoginInfo info = new InPhoneLoginInfo();
            BeanUtils.copyProperties(reqDTO, info);

            QueryWrapper<InPhoneLoginInfo> queryWrapper = buildConditionBySelective(info);

            Page<InPhoneLoginInfo> result = inPhoneLoginInfoMapper.selectPage(
                    new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), queryWrapper);
            Page<PhoneLoginInfoResVO> resultVo = new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize());
            if (null != result && CollectionUtils.isNotEmpty(result.getRecords())) {
                List<PhoneLoginInfoResVO> datas = result.getRecords().parallelStream()
                        .map(item -> convertToInfoResVO(item, false)).collect(Collectors.toList());

                resultVo.setRecords(datas);
                resultVo.setTotal(result.getTotal());
                return resultVo;
            }
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", "system.query.error.default");
        }
        return null;
    }

    @Override
    public void addPhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO) {
        sortOrg(reqDTO);
        //查询是否已经存在记录
        if(CollectionUtils.isNotEmpty(getLoginInfoBySelective(getFilter(reqDTO)))) {
            throw new FastRuntimeException("operate.login.info.duplicate","operate.login.info.duplicate");
        }
        inPhoneLoginInfoMapper.insert(getInPhoneLoginInfo(reqDTO));
    }

    private void sortOrg(PhoneLoginInfoReqDTO reqDTO) {
        String[] orgs = reqDTO.getOrg().split(",");
        List<String> sortedList = Arrays.stream(orgs).sorted().collect(Collectors.toList());
        reqDTO.setOrg(String.join(",", sortedList));
    }

    @Override
    public void updatePhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO) {
        InPhoneLoginInfo conf = getLoginInfoById(reqDTO.getId());
        if (null == conf) {
            throw new FastRuntimeException("operate.org.tel.conf.notExist");
        }
        try {
            sortOrg(reqDTO);
            BeanUtils.copyProperties(reqDTO, conf);
            conf.setLstUpdUser(CommonUtils.getCurrentlogged());
            inPhoneLoginInfoMapper.updateById(conf);
        } catch (BeansException e) {
            throw new FastRuntimeException("system.update.error.default", e.getMessage());
        }
    }

    @Override
    public void deletePhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO) {
        InPhoneLoginInfo conf = getLoginInfoById(reqDTO.getId());
        if (null == conf) {
            throw new FastRuntimeException("operate.org.tel.conf.notexist");
        }
        inPhoneLoginInfoMapper.updateById(conf.setStatus(false));
    }

    @Override
    public Boolean isSummary(String cdrMainUniqueId) {
        if (StringUtils.isNotBlank(cdrMainUniqueId)) {
            ConPhoneSummary summary = conPhoneSummaryMapper.selectOne(
                    Wrappers.lambdaQuery(ConPhoneSummary.class)
                            .eq(ConPhoneSummary::getCdrMainUniqueId, cdrMainUniqueId)
                            .eq(ConPhoneSummary::getStaffId, CommonUtils.getCurrentloggedStaffId()));
            return Objects.nonNull(summary);
        }
        return Boolean.TRUE;
    }

    @Override
    public IsSummaryVO lastPhoneIsSummary() {
        IsSummaryVO isSummaryVO = new IsSummaryVO();
        List<IsSummaryVO> summaryVOList = conPhoneCallInfoMapper
                .selectSummaryByStaffId(CommonUtils.getCurrentloggedStaffId());
        if (CollectionUtils.isNotEmpty(summaryVOList)) {
            isSummaryVO = summaryVOList.get(0);
            Integer count = conPhoneSummaryMapper.selectCount(Wrappers.lambdaQuery(ConPhoneSummary.class)
                    .eq(ConPhoneSummary::getCdrMainUniqueId, isSummaryVO.getMainUniqueId()));
            Integer tmkCount = tmkSummaryMapper.selectCount(Wrappers.lambdaQuery(TmkSummary.class)
                    .eq(TmkSummary::getCdrMainUniqueId, isSummaryVO.getMainUniqueId()));
            Integer callbackCount = callbackSummaryMapper.selectCount(Wrappers.lambdaQuery(CallbackSummary.class)
                    .eq(CallbackSummary::getCdrMainUniqueId, isSummaryVO.getMainUniqueId()));
            isSummaryVO.setIsSummary(count > 0 || tmkCount > 0 || callbackCount > 0);
        }
        return isSummaryVO;
    }

    private InPhoneLoginInfo getLoginInfoById(Long id) {
        return inPhoneLoginInfoMapper.selectById(id);
    }

    private InPhoneLoginInfo getInPhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO) {
        InPhoneLoginInfo info = new InPhoneLoginInfo();
        BeanUtils.copyProperties(reqDTO, info, "list");
        return info.setCreateUser(CommonUtils.getCurrentlogged());
    }

    private InPhoneLoginInfo getFilter(PhoneLoginInfoReqDTO reqDTO) {
        return new InPhoneLoginInfo().setIdNo(reqDTO.getIdNo()).setOrg(reqDTO.getOrg());
    }

    public List<InPhoneLoginInfo> getLoginInfoBySelective(InPhoneLoginInfo info) {
        return inPhoneLoginInfoMapper.selectList(buildConditionBySelective(info).orderByAsc("gmt_modify"));
    }

    private QueryWrapper<InPhoneLoginInfo> buildConditionBySelective(InPhoneLoginInfo info) {
        return new QueryWrapper<InPhoneLoginInfo>()
                .eq("status", true)
                .eq(StringUtils.isNotBlank(info.getUserTel()), "user_tel", info.getUserTel())
                .eq(StringUtils.isNotBlank(info.getIdNo()), "id_no", info.getIdNo())
                .eq(StringUtils.isNotBlank(info.getOrg()), "org", info.getOrg());
    }

    private PhoneLoginInfoResVO convertToInfoResVO(InPhoneLoginInfo item, boolean isNeedToken) {
        PhoneLoginInfoResVO vo = new PhoneLoginInfoResVO();
        vo.setUserTel(item.getUserTel());
        vo.setBindTel(item.getTel());
        vo.setBindType(item.getTelType());
        vo.setCno(item.getIdNo());
        vo.setEnterpriseId(item.getOrg());
        vo.setId(item.getId());
//        vo.setLoginStatus(item.getInitState());

        OpDictInfo dict = new OpDictInfo();
        dict.setCategory("bindType");
        dict.setType(item.getTelType());
        vo.setBindTypeName(getDictInfo(dict));
        List<String> enterpriseNameList = new ArrayList<>();
        Map<String, String> nameMap = CommonUtils.getDictTypeContentMap("callOutOrgName", null);
        for (String enterId : vo.getEnterpriseId().split(",")) {
            enterpriseNameList.add(nameMap.getOrDefault(enterId, enterId));
        }
        vo.setEnterpriseName(String.join(",", enterpriseNameList));


//        dict.setCategory("loginStatus");
//        dict.setType(item.getInitState());
//        vo.setLoginStatus(getDictInfo(dict));

        //设置token及时间戳
        if (isNeedToken) {
            vo = getToken(vo);
        }
        return vo;
    }

    public String getDictInfo(OpDictInfo dict) {
        if (StringUtils.isEmpty(dict.getType())) {
            return null;
        }
        OpDictInfo opDictInfo = opDictInfoMapper.selectOne(getQueryWrapper(dict));
        if (opDictInfo == null) {
            return null;
        }
        return opDictInfo.getContent();
    }

    private QueryWrapper<OpDictInfo> getQueryWrapper(OpDictInfo dict) {
        QueryWrapper<OpDictInfo> wrapper = new QueryWrapper<OpDictInfo>();
        wrapper.eq("status", true);
        wrapper.eq(null != dict.getId(), "id", dict.getId());
        wrapper.eq(StringUtils.isNotBlank(dict.getCategory()), "category", dict.getCategory());
        wrapper.eq(StringUtils.isNotBlank(dict.getContent()), "content", dict.getContent());
        wrapper.eq(StringUtils.isNotBlank(dict.getType()), "type", dict.getType());
        wrapper.eq(StringUtils.isNotBlank(dict.getDetail()), "detail", dict.getDetail());
        wrapper.eq(null != dict.getStatus(), "status", dict.getStatus());
        return wrapper;
    }


    private PhoneLoginInfoResVO getToken(PhoneLoginInfoResVO vo) {
        String agentSign = vo.getEnterpriseId();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String realToken = getTokenByCode(vo.getEnterpriseId());
        String finalAgentSign = agentSign + timestamp + realToken;
        vo.setTimestamp(timestamp);
        vo.setPwd(MD5.encrypt(finalAgentSign));
        return vo;
    }

    private String getTokenByCode(String enterpriseId) {
        return trUtil.getTokenByEnterpriseIdUseCache(enterpriseId);
    }

    private SoftPhoneInfoVO convertToSoftPhoneVo(ConPhoneCallInfo item) {
        SoftPhoneInfoVO softPhoneInfoVO = new SoftPhoneInfoVO();
        BeanUtils.copyProperties(item, softPhoneInfoVO);
        return softPhoneInfoVO;
    }

    private void insertOrUpdate(ConPhoneCallInfo info) {
        if (Objects.isNull(info.getId())) {
            conPhoneCallInfoMapper.insert(info);
        } else {
            conPhoneCallInfoMapper.updateById(info);
        }
    }


    @Override
    public PhoneLoginInfoResVO getPhoneLoginInfoByLoginName(String loginName) {
        List<InPhoneLoginInfo> inPhoneLoginInfos = inPhoneLoginInfoMapper
                .selectList(Wrappers.lambdaQuery(InPhoneLoginInfo.class).eq(InPhoneLoginInfo::getUserTel, loginName));
        if (CollectionUtils.isEmpty(inPhoneLoginInfos)){
            throw new CrmOperateException("该人员软电话配置不存在");
        }
        return convertToInfoResVOSimple(inPhoneLoginInfos.get(0));
    }

    private PhoneLoginInfoResVO convertToInfoResVOSimple(InPhoneLoginInfo item){
        PhoneLoginInfoResVO vo = new PhoneLoginInfoResVO();
        vo.setUserTel(item.getUserTel());
        vo.setBindTel(item.getTel());
        vo.setBindType(item.getTelType());
        vo.setCno(item.getIdNo());
        vo.setEnterpriseId(item.getOrg());
        vo.setId(item.getId());
        return vo;
    }


    @Override
    public List<AgentVO> queryAgentList() {
        Map<String, String> params = new HashMap<>();
        params.put("limit", "500");
        JSONArray jsonArray = trUtil.queryMonitorAgent(params);
        List<AgentVO> agentList = jsonArray.toJavaList(AgentVO.class);
        if (CollectionUtils.isNotEmpty(agentList)) {
            Map<String, String> cnoGroupMap = inPhoneLoginInfoMapper.queryAllAgentList().stream()
                .collect(Collectors.toMap(AgentVO::getCno, AgentVO::getGroupName, (s, s2) -> s2));
            for (AgentVO agentVO : agentList) {
                agentVO.setGroupName(cnoGroupMap.get(agentVO.getCno()));
            }
        }

        return agentList;

    }


    @Override
    public void updateAgentSkill(AgentSkillUpdateDTO dto) {
        Map<String, String> params = new HashMap<>();
        params.put("cno", dto.getCno());
        params.put("skillIds", dto.getSkillIds());
        List<String> skillLevels = new ArrayList<>();
        for (int i = 0; i < dto.getSkillIds().split(",").length; i++) {
            skillLevels.add("1");
        }
        params.put("skillLevels", String.join(",", skillLevels));
        String resStr = trUtil.doGet(dto.getEnterpriseId(), "/agent/update", params);
        if (StringUtils.isNotBlank(resStr)){
            JSONObject jObject = JSON.parseObject(resStr);
            if (jObject.getInteger("result") == 0){
                log.debug("更新技能组成功");
            } else {
                log.error("更新技能组失败,params:{},res:{}", JSON.toJSONString(dto), resStr);
                throw new FastRuntimeException("更新技能组失败");
            }
        }
        
    }

    @Override
    public String queryAiSummary(String cdrMainUniqueId) {
        // 验证输入参数
        if (StringUtils.isBlank(cdrMainUniqueId)) {
            log.warn("Invalid cdrMainUniqueId: {}", cdrMainUniqueId);
            throw new FastRuntimeException("通话id不存在");
        }
        

        // 查询数据
        List<CallInfoAiSummary> aiSummaryList = callInfoAiSummaryMapper.selectList(
                Wrappers.lambdaQuery(CallInfoAiSummary.class)
                        .eq(CallInfoAiSummary::getCdrMainUniqueId, cdrMainUniqueId)
                        .select(CallInfoAiSummary::getSummary)
        );

        // 检查结果
        if (CollectionUtils.isNotEmpty(aiSummaryList)) {
            CallInfoAiSummary summary = aiSummaryList.get(0);
            if (summary != null && StringUtils.isNotBlank(summary.getSummary())) {
                return summary.getSummary();
            } else {
                log.warn("No valid summary found for cdrMainUniqueId: {}", cdrMainUniqueId);
            }
        } else {
            log.info("No AI summary found for cdrMainUniqueId: {}", cdrMainUniqueId);
        }

        return "";
    }

    @Override
    public String queryDialogueText(String cdrMainUniqueId) {
        // 验证输入参数
        if (StringUtils.isBlank(cdrMainUniqueId)) {
            log.warn("Invalid cdrMainUniqueId: {}", cdrMainUniqueId);
            return "";
        }

        // 查询数据
        List<CallInfoAiSummary> aiSummaryList = callInfoAiSummaryMapper.selectList(
                Wrappers.lambdaQuery(CallInfoAiSummary.class)
                        .eq(CallInfoAiSummary::getCdrMainUniqueId, cdrMainUniqueId)
                        .select(CallInfoAiSummary::getDialogue)
        );

        // 使用 Optional 安全地获取第一个元素
        return aiSummaryList.stream()
                .findFirst()
                .filter(summary -> StringUtils.isNotBlank(summary.getDialogue()))
                .map(CallInfoAiSummary::getDialogue)
                .orElseGet(() -> {
                    if (aiSummaryList.isEmpty()) {
                        log.info("No dialogue found for cdrMainUniqueId: {}", cdrMainUniqueId);
                    } else {
                        log.info("No valid dialogue found for cdrMainUniqueId: {}", cdrMainUniqueId);
                    }
                    return "";
                });
    }

    @Override
    public String queryAiSummaryCheck(String cdrMainUniqueId) {
        if (StringUtils.isBlank(cdrMainUniqueId)) {
            log.warn("Invalid cdrMainUniqueId: {}", cdrMainUniqueId);
            throw new FastRuntimeException("通话id不存在");
        }

        List<ConPhoneCallInfo> callInfoList = conPhoneCallInfoMapper.selectList(
                Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                        .eq(ConPhoneCallInfo::getCdrMainUniqueId, cdrMainUniqueId)
        );

        if (CollectionUtils.isEmpty(callInfoList)) {
            log.warn("No call records found for cdrMainUniqueId: {}, callInfoList size: {}", cdrMainUniqueId, callInfoList.size());
            throw new FastRuntimeException("通话记录还未生成，请稍候再试");
        }

        Optional<ConPhoneCallInfo> firstCallInfo = callInfoList.stream().findFirst();
        if (!firstCallInfo.isPresent()) {
            log.warn("Unexpected empty callInfoList for cdrMainUniqueId: {}", cdrMainUniqueId);
            throw new FastRuntimeException("通话记录为空，请稍候再试");
        }

        ConPhoneCallInfo callInfo = firstCallInfo.get();

        if (StringUtils.isBlank(callInfo.getCdrRecordFile()) || Objects.isNull(callInfo.getCdrBridgeTime())) {
            log.warn("Invalid callInfo for cdrMainUniqueId: {}, callInfo: {}", cdrMainUniqueId, callInfo);
            throw new FastRuntimeException("该通电话无录音，AI无法生成");
        }

        if (Objects.nonNull(callInfo.getCdrEndBridgeTime()) && callInfo.getCdrEndBridgeTime().compareTo(MIN_CALL_DURATION) <= 0) {
            log.warn("Call duration too short for cdrMainUniqueId: {}, callInfo: {}", cdrMainUniqueId, callInfo);
            throw new FastRuntimeException("该通话录音过短，请自行完成小结备注");
        }

        return queryAiSummary(cdrMainUniqueId);
    }

    @Override
    public String queryAiSummaryRetry(String cdrMainUniqueId) {
        // 查询数据
        List<CallInfoAiSummary> aiSummaryList = callInfoAiSummaryMapper.selectList(
                Wrappers.lambdaQuery(CallInfoAiSummary.class)
                        .eq(CallInfoAiSummary::getCdrMainUniqueId, cdrMainUniqueId));

        // 检查结果
        if (CollectionUtils.isNotEmpty(aiSummaryList)) {
            CallInfoAiSummary summary = aiSummaryList.get(0);
            if (summary != null && StringUtils.isNotBlank(summary.getDialogue())) {
                String newSummary = retryQuerySummary(summary.getDialogue());
                summary.setSummary(newSummary);
                callInfoAiSummaryMapper.updateById(summary);
                return newSummary;
            } else {
                log.warn("queryAiSummaryRetry No valid summary found for cdrMainUniqueId: {}", cdrMainUniqueId);
            }
        } else {
            log.info("queryAiSummaryRetry No AI summary found for cdrMainUniqueId: {}", cdrMainUniqueId);
        }

        return "";
    }

    private String retryQuerySummary(String dialogue) {

        try {
            JSONObject params = new JSONObject();
            params.put("dialogue", dialogue);
            Header[] headers = HttpHeader.custom().contentType(HttpHeader.Headers.APPLICATION_JSON).build();
            HttpConfig httpConfig = HttpConfig.custom()
                    .url(aiSummaryBaseUrl + "/llm/v1/call/summarize/retry")
                    .json(JSON.toJSONString(params))
                    .headers(headers)
                    .requestTimeout(AI_TIME_OUT)
                    .connectTimeout(AI_TIME_OUT)
                    .socketTimeout(AI_TIME_OUT);

            log.info("retryQuerySummary params:{}", JSON.toJSONString(params));
            String resStr = HttpClientUtil.post(httpConfig);
            log.info("retryQuerySummary result:{}", resStr);

            AiSummaryResVO resVO = JSON.parseObject(resStr, AiSummaryResVO.class);
            return resVO.getData().getSummary();
        } catch (Exception e) {
            log.error("retryQuerySummary error", e);
        }


        return "";
    }
}
