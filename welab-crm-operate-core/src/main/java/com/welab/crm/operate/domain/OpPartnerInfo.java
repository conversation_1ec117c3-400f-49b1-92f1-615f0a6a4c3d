package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 资金方信息汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_partner_info")
public class OpPartnerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资金方名称
     */
    private String partnerName;

    /**
     * 机构号
     */
    private String institutionNumber;

    /**
     * 放款卡
     */
    private String loanCard;

    /**
     * 系统批扣时间-还款日
     */
    private String systemRepayTimeNormal;

    /**
     * 宽限期
     */
    private String gracePeriod;

    /**
     * h5还款
     */
    private String h5Repay;

    /**
     * 一期一期提前还款/账单展示
     */
    private String earlyRepayEachInstallment;

    /**
     * 提前结清说明
     */
    private String earlySettlementMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 结清证明说明
     */
    private String settlementProof;

    /**
     * 关闭征信授信额度
     */
    private String quotaStatus;

    /**
     * 发送标识 Y/N
     */
    private String sendStatus;

    /**
     * 发票开具
     */
    private String invoiceIssue;

    /**
     * 结清获取
     */
    private String closeGet;

    /**
     * 发票获取
     */
    private String invoiceGet;

    /**
     * 关闭授信
     */
    private String closeQuota;

}
