package com.welab.crm.operate.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/6
 */
@Data
@ApiModel(value = "天润坐席工作量报表接口响应对象")
public class StatisticAgentWorkloadDay implements Serializable {

    private static final long serialVersionUID = -4892309863210030888L;

    @ApiModelProperty(value = "日期，按日统计时有值")
    private String day;

    @ApiModelProperty(value = "座席工号")
    private String cno;

    @ApiModelProperty(value = "总登录时长")
    private String loginTime;

    @ApiModelProperty(value = "总置忙时长")
    private String pauseTime;

    @ApiModelProperty(value = "总休息时长")
    private String restTime;

    @ApiModelProperty(value = "总空闲时长")
    private String idleTime;

    @ApiModelProperty(value = "外呼呼叫次数")
    private String obCallingCount;

    @ApiModelProperty(value = "外呼通话次数")
    private String obBridgeCount;

    @ApiModelProperty(value = "外呼总通话时长")
    private String obBridgeTime;

    @ApiModelProperty(value = "外呼总整理时长")
    private String obWrapupTime;

    @ApiModelProperty(value = "呼入呼叫次数")
    private String ibCallingCount;

    @ApiModelProperty(value = "呼入通话次数")
    private String ibBridgeCount;

    @ApiModelProperty(value = "呼入总通话时长")
    private String ibTotalTime;

    @ApiModelProperty(value = "呼入总整理时长")
    private String ibWrapupTime;

    @ApiModelProperty(value = "预览外呼双方接听数")
    private String previewObAnsweredCount;
}
