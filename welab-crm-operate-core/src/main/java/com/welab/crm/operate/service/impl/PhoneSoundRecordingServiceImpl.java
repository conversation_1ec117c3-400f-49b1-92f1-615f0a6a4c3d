package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.enums.SatisfactionEnum;
import com.welab.crm.operate.domain.ConPhoneCallInfo;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.operate.mapper.ConPhoneSummaryMapper;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.service.IPhoneSoundRecordingService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.phone.CallInPhoneDetailVO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.screen.CountVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/23
 */
@Service
@Slf4j
public class PhoneSoundRecordingServiceImpl implements IPhoneSoundRecordingService {

    @Autowired
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Autowired
    private TrUtil trUtil;

    @Autowired
    private ConPhoneSummaryMapper conPhoneSummaryMapper;

    @Autowired
    private OpDictInfoMapper opDictInfoMapper;

    @Override
    public String queryRecordFile(PhoneSundRecordingDTO reqDTO) {
        try {
            String result = trUtil.queryRecordFile(reqDTO);
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public Page<CallInPhoneDetailVO> queryDetail(PhoneSundRecordingDTO dto) {
        try {
            CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
            Page<CallInPhoneDetailVO> result = conPhoneCallInfoMapper.queryDetail(new Page<CallInPhoneDetailVO>(dto.getCurPage(), dto.getPageSize()), dto);
            if (Objects.nonNull(result) && Objects.nonNull(result.getRecords()) && !result.getRecords().isEmpty()) {
                result.getRecords().stream().forEach(t -> {
                    if (StringUtils.isBlank(t.getCdrAgentNumber()) || StringUtils.isBlank(t.getCdrClid())) {
                        if (StringUtils.isNotBlank(t.getCdrCalleeCno())) {
                            t.setCdrAgentNumber(t.getCdrCalleeCno());
                        }
                        if (StringUtils.isNotBlank(t.getCdrCustomerNumber())) {
                            t.setCdrClid(t.getCdrCustomerNumber());
                        }
                    }
                    if ("呼入".equals(t.getCdrCallType())) {
                        t.setCdrCustomerNumber(t.getCdrCalleeCno());
                        //呼入数据需要关联电话小结内容
                        ConPhoneCallInfo conPhoneCallInfo = conPhoneCallInfoMapper.selectById(t.getId());
                        List<PhoneSummaryVO> phoneSummaryVOList = conPhoneSummaryMapper.selectSummaryByCdrMainUniqueId(conPhoneCallInfo.getCdrMainUniqueId());
                        if (CollectionUtils.isNotEmpty(phoneSummaryVOList)) {
                            List<PhoneSummaryVO> list =
                                    phoneSummaryVOList.stream()
                                        .filter(item -> StringUtils.isNotBlank(item.getStaffName())
                                            && item.getStaffName().equals(t.getStaffName()))
                                        .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(list)) {
                                PhoneSummaryVO phoneSummaryVO = list.get(0);
                                OpDictInfo opDictInfo = new OpDictInfo();
                                opDictInfo.setCategory("callType");
                                opDictInfo.setType(phoneSummaryVO.getCallType());
                                phoneSummaryVO.setCallType(getDictInfo(opDictInfo));
                                if (Objects.isNull(phoneSummaryVO.getCallTime())) {
                                    phoneSummaryVO.setCallTime(phoneSummaryVO.getGmtCreate());
                                }
                                // 名单类型
                                setMapName(phoneSummaryVO);
                                // 手机号脱敏
                                phoneSummaryVO.setPhoneNumber(SecurityUtil.maskMobile(phoneSummaryVO.getPhoneNumber()));
                                t.setPhoneSummaryVO(phoneSummaryVO);
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(t.getCdrCustomerNumber())) {
                        t.setCdrCustomerNumber(SecurityUtil.maskMobile(t.getCdrCustomerNumber()));
                    }
                    if (StringUtils.isNotBlank(t.getCdrClid())) {
                        t.setCdrClid(SecurityUtil.maskMobile(t.getCdrClid()));
                    }
                    fillSatisfaction(t);
                });
            }
            return result;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    public String getDictInfo(OpDictInfo dict) {
        if (StringUtils.isEmpty(dict.getType())) {
            return null;
        }
        OpDictInfo opDictInfo = opDictInfoMapper.selectOne(getQueryWrapper(dict));
        if (opDictInfo == null) {
            return null;
        }
        return opDictInfo.getContent();
    }

    private QueryWrapper<OpDictInfo> getQueryWrapper(OpDictInfo dict) {
        QueryWrapper<OpDictInfo> wrapper = new QueryWrapper<OpDictInfo>();
        wrapper.eq("status", true);
        wrapper.eq(null != dict.getId(), "id", dict.getId());
        wrapper.eq(StringUtils.isNotBlank(dict.getCategory()), "category", dict.getCategory());
        wrapper.eq(StringUtils.isNotBlank(dict.getContent()), "content", dict.getContent());
        wrapper.eq(StringUtils.isNotBlank(dict.getType()), "type", dict.getType());
        wrapper.eq(StringUtils.isNotBlank(dict.getDetail()), "detail", dict.getDetail());
        wrapper.eq(null != dict.getStatus(), "status", dict.getStatus());
        return wrapper;
    }

    private void setMapName(PhoneSummaryVO phoneSummaryVO) {
        if (StringUtils.isNotBlank(phoneSummaryVO.getTaskId())){
            phoneSummaryVO.setMapName(CommonUtils.getMapNameByTaskId(phoneSummaryVO.getTaskId()));
        }
    }

    private void fillSatisfaction(CallInPhoneDetailVO vo) {
        if (StringUtils.isNotBlank(vo.getSatisfactionEvaluation())) {
            String keys = vo.getSatisfactionEvaluation();
            if (SatisfactionEnum.SATISFIED_CODE_LIST.contains(keys)) {
                vo.setSatisfactionEvaluation(SatisfactionEnum.getDescByCode(keys));
            } else {
                if (keys.length() > 1) {
                    vo.setSatisfactionEvaluation(SatisfactionEnum.getDescByCode(keys.substring(0, 1)) + "#"
                        + SatisfactionEnum.DissatisfactionReasonEnum.getReasonByCode(keys.substring(1)));
                } else {
                    vo.setSatisfactionEvaluation(SatisfactionEnum.getDescByCode(SatisfactionEnum.getDescByCode(keys)));
                }
            }
        }

    }


    @Override
    public List<CountVO> queryCount(PhoneSundRecordingDTO dto) {
        CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
        return conPhoneCallInfoMapper.queryCallInfoGroupCount(dto);
    }
}
