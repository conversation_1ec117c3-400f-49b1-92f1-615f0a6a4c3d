package com.welab.crm.operate.util;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.poi.excel.ExcelWriter;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.xdao.context.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/3/3
 */
@Slf4j
public class CommonUtil {

    /**
     * 除法计算
     *
     * @param number
     * @param total
     * @return
     */
    public static Integer divide(Integer number, Integer total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return 0;
        }
        return number / total;
    }

    /**
     * 计算百分比，保留两位小数
     */
    public static String calcPercentage(Integer number, Integer total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }

    public static String calcPercentageByLong(Long number, Long total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }

    public static String calcPercentageByPercent(Long number, Long total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", 100 - rate) + "%";
    }

    public static String calcPercentageDouble(Double number, Double total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }

    /**
     * 对 list 进行分页
     *
     * @param list
     * @param totalRows   必须不小于0
     * @param rowsPerPage 必须不小于0
     * @param currentPage 必须不小于1
     * @param <T>
     * @return 分页后的 list
     */
    public static <T> List<T> subList(List<T> list, int totalRows, int rowsPerPage, int currentPage) {
        if (Objects.isNull(list) || totalRows < 0 || rowsPerPage < 0 || currentPage < 1) {
            throw new RuntimeException("参数错误");
        }
        if (list.isEmpty()) {
            return list;
        }
        int startIndex = (currentPage - 1) * rowsPerPage;
        int endIndex = currentPage * rowsPerPage;
        if (startIndex >= totalRows) {
            return new ArrayList<>();
        } else if (endIndex >= totalRows) {
            return list.subList(startIndex, totalRows);
        } else {
            return list.subList(startIndex, endIndex);
        }
    }

    /**
     * 对 list 进行分页
     *
     * @param list
     * @param rowsPerPage
     * @param currentPage
     * @param <T>
     * @return Page
     */
    public static <T> Page<T> subList(List<T> list, int rowsPerPage, int currentPage) {
        int totalRows = list.size();
        list = subList(list, totalRows, rowsPerPage, currentPage);
        return Page.initPage(list, totalRows, rowsPerPage, currentPage);
    }


    /**
     * 生成随机文件名：前缀+年月日时分秒+指定位随机数，共25位
     *
     * @return
     */
    public static String getRandomFileName(String prefix, int length) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);
        String rannum = getRandomStr(length);
        String serviceNo = prefix + str + rannum;
        // 当前时间
        return serviceNo;
    }

    /**
     * 获取指定位数的随机数
     *
     * @param length
     * @return
     */
    public static String getRandomStr(int length) {
        String str = "0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder res = new StringBuilder();
        for (int i = 0; i < length; i++) {
            res.append(str.charAt(random.nextInt(str.length())));
        }
        return res.toString();
    }


    public static void setResponse(HttpServletResponse response, String fileName) {
        try {
            // 设置响应类型
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置响应头信息
            response.setHeader("Content-disposition",
                    "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            response.setHeader("Content-Transfer-Encoding", "binary");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            // 跨域配置
//            response.addHeader("Access-Control-Allow-Headers", "*");
//            response.addHeader("Access-Control-Allow-Origin", "*");
        } catch (UnsupportedEncodingException e) {
            log.error("exportIvrKeyDetailReport, 设置response异常", e);
        }
    }


    public static void closeWriter(ExcelWriter writer, HttpServletResponse response) {
        try {
            writer.flush(response.getOutputStream(), true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            writer.close();
        }
    }


    public static  <T> List<String> getTitleList(Class<T> claz) {
        List<String> list = new ArrayList<>();
        List<Field> fields = Arrays.asList(claz.getDeclaredFields());
        fields.forEach(field -> {
            ExcelTitleMap excelTitleMap = field.getAnnotation(ExcelTitleMap.class);
            if (excelTitleMap != null) {
                list.add(excelTitleMap.title());
            }
        });

        return list;
    }

    public static Integer stringToInteger(String s){
        if (Objects.isNull(s)){
            return 0;
        } else {
            return Integer.parseInt(s);
        }
    }

    public static Integer sumString(String... s){
        int sum = 0;
        for (String s1 : s) {
            sum += stringToInteger(s1);
        }
        return sum;
    }


    /**
     * 判断工单状态是否是结案
     * @param status
     * @return
     */
    public static Boolean checkIsEnd(String status) {
        // 判断是否结案
        return StringUtils.isNotBlank(status) && status.endsWith("close");
    }


    /**
     * 查询字符串是否符合手机号格式
     * @param mobile
     * @return
     */
    public static Boolean isMobile(String mobile){
        return Pattern.matches(Constant.MOBILE, mobile);
    }

    
    public static  <T> T calcTotalWorkStatusVo(List<T> totalList, Class<T> tClass) {
        // 加总所有数据计算总计值
        T total;
        try {
            total = tClass.newInstance();
        } catch (Exception e) {
            throw new CrmOperateException("实例化总计对象异常");
        }
        for (T vo : totalList) {
            for (Field field : tClass.getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    String typeName = field.getType().getName();
                    Object targetValue = field.get(vo);
                    if (targetValue != null && typeName.equals("java.lang.Integer")) {
                        int origin = field.get(total) == null ? 0 : (int) field.get(total);
                        Method setVal = getSetMethod(tClass, field, Integer.class);
                        setVal.invoke(total, origin + (int) targetValue);
                    } else if (targetValue != null && typeName.equals("java.lang.String")) {
                        String target = ((String) targetValue).trim();
                        if (target.contains(":") && !target.contains(" ")) {// 只有时间需要加总的时间字段才汇总(例00:00:00)
                            Method setVal = getSetMethod(tClass, field, String.class);
                            if (field.get(total) == null) {
                                setVal.invoke(total, target);
                            } else {
                                String origin = (String) field.get(total);
                                int seconds = DateUtils.getSeconds(target) + DateUtils.getSeconds(origin);
                                setVal.invoke(total, DateUtils.secondsToTime(seconds));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("计算员工工作状态或者效能总计数据异常:  {}", e.getMessage(), e);
                }
            }
        }
        return total;
    }


    public static  <T> Method getSetMethod(Class<?> tClass, Field field, Class<T> type)
            throws NoSuchMethodException {
        return tClass.getMethod("set" + field.getName().substring(0, 1).toUpperCase()
                + field.getName().substring(1), type);
    }


    private static final Map<String, String[]> CONTENT_TYPE_MAPPING = new HashMap<>();

    static {
        // 常见类型
        CONTENT_TYPE_MAPPING.put("text/plain", new String[]{"txt", "log", "conf", "ini"});
        CONTENT_TYPE_MAPPING.put("text/html", new String[]{"html", "htm", "shtml"});
        CONTENT_TYPE_MAPPING.put("text/css", new String[]{"css"});
        CONTENT_TYPE_MAPPING.put("text/javascript", new String[]{"js"});
        CONTENT_TYPE_MAPPING.put("application/json", new String[]{"json"});
        CONTENT_TYPE_MAPPING.put("application/xml", new String[]{"xml"});
        CONTENT_TYPE_MAPPING.put("image/jpeg", new String[]{"jpg", "jpeg", "jpe"});
        CONTENT_TYPE_MAPPING.put("image/png", new String[]{"png"});
        CONTENT_TYPE_MAPPING.put("image/gif", new String[]{"gif"});
        CONTENT_TYPE_MAPPING.put("image/webp", new String[]{"webp"});
        CONTENT_TYPE_MAPPING.put("application/pdf", new String[]{"pdf"});
        CONTENT_TYPE_MAPPING.put("application/zip", new String[]{"zip"});
        CONTENT_TYPE_MAPPING.put("application/x-rar-compressed", new String[]{"rar"});
        CONTENT_TYPE_MAPPING.put("application/msword", new String[]{"doc", "dot"});
        CONTENT_TYPE_MAPPING.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", new String[]{"docx"});
        CONTENT_TYPE_MAPPING.put("application/vnd.ms-excel", new String[]{"xls", "xla"});
        CONTENT_TYPE_MAPPING.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new String[]{"xlsx"});
        CONTENT_TYPE_MAPPING.put("application/vnd.ms-powerpoint", new String[]{"ppt", "ppz", "pps", "pot"});
        CONTENT_TYPE_MAPPING.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", new String[]{"pptx"});
        CONTENT_TYPE_MAPPING.put("audio/mpeg", new String[]{"mp3"});
        CONTENT_TYPE_MAPPING.put("video/mpeg", new String[]{"mpeg", "mpg", "mpe"});
        CONTENT_TYPE_MAPPING.put("video/mp4", new String[]{"mp4"});

        // 一些不常见的但需要处理的类型
        CONTENT_TYPE_MAPPING.put("application/x-gzip", new String[]{"gz", "tgz"}); // gzip 压缩
        CONTENT_TYPE_MAPPING.put("application/x-tar", new String[]{"tar"}); // tar 打包
        CONTENT_TYPE_MAPPING.put("application/x-7z-compressed", new String[]{"7z"});
        CONTENT_TYPE_MAPPING.put("application/octet-stream", new String[]{"bin", "exe", "dll"}); // 二进制流，需要谨慎处理

        // 更多类型可以根据需要添加
    }

    /**
     * 根据 Content-Type 获取文件后缀。
     *
     * @param contentType Content-Type 字符串
     * @return 文件后缀，如果找不到则返回空字符串 ""。如果对应多个后缀，则返回第一个。
     */
    public static String getFileExtension(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return "";
        }

        String[] extensions = CONTENT_TYPE_MAPPING.get(contentType.toLowerCase()); // 忽略大小写
        if (extensions != null && extensions.length > 0) {
            return extensions[0];
        }

        // 一些特殊情况处理，例如 content-type 中包含参数
        if (contentType.contains(";")) {
            contentType = contentType.split(";")[0].trim();
            extensions = CONTENT_TYPE_MAPPING.get(contentType.toLowerCase());
            if (extensions != null && extensions.length > 0) {
                return extensions[0];
            }
        }

        return "";
    }

    public static String getFileNameFromUrl(String url) {
        try {
            URL u = new URL(url);
            String path = u.getPath();
            if (path == null || path.isEmpty()) {
                return null;
            }
            String filename = path.substring(path.lastIndexOf('/') + 1);
            URLConnection urlConnection = u.openConnection();
            String fileExtension = getFileExtension(urlConnection.getContentType());
            return filename + "." + fileExtension;

        } catch (Exception e) {
            return null; 
        }
    }

    public static String getOnlineBase64FileName(String url) {
        try {
            String base64Path = url.substring(url.lastIndexOf('/') + 1);
            String truePath = Base64Decoder.decodeStr(base64Path);
	        return truePath.substring(truePath.lastIndexOf('/') + 1);
        } catch (Exception e) {
            return null;
        }
    }

    public static com.baomidou.mybatisplus.extension.plugins.pagination.Page getMpPage(Page<?> pages) {
        return new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>()
                .setCurrent(pages.getCurrentPage())
                .setSize(pages.getRowsPerPage())
                .setTotal(pages.getTotalPage())
                .setRecords((List<Object>) pages.getList());
    }

    public static void validTime(String startTime, String endTime, int day) {
        Date start = DateUtil.stringToDate(startTime);
        Date end = DateUtil.stringToDate(endTime);
        if (!DateUtil.isBefore(start, end)) {
            throw new CrmOperateException("开始时间必须小于结束时间");
        }
        if (DateUtil.containDays(start, end) > day) {
            throw new CrmOperateException("查询时间不能超过" + day + "天");
        }
    }

    public static String unicodeToString(String str) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        return str;
    }
    
}
