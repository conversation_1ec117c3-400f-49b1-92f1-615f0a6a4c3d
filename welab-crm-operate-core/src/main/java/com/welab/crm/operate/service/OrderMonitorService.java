package com.welab.crm.operate.service;

import com.welab.crm.operate.vo.monitor.OrderCallbackMonitorVO;
import com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO;

import java.util.List;

/**
 * 工单监控服务
 * <AUTHOR> 
 */
public interface OrderMonitorService {

	/**
	 * 查询工单催单情况
	 * @return
	 */
	List<OrderUrgeMonitorVO> queryOrderUrgeList();

	/**
	 * 查询工单回访状态
	 * @return
	 */
	List<OrderCallbackMonitorVO> queryOrderCallbackList();
}
