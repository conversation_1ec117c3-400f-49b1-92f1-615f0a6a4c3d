package com.welab.crm.operate.service.impl;

import com.welab.crm.base.dto.WoAssignLogDTO;
import com.welab.crm.base.enums.AssignTypeEnum;
import com.welab.crm.base.service.WoAssignLogService;
import com.welab.crm.operate.dto.OrderAssignLogDTO;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.util.SecurityUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.Objects;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.workflow.busi.service.WFRunBusiService;
import com.welab.crm.base.workflow.common.constant.TaskAllotEnum;
import com.welab.crm.operate.domain.OpOrderConfig;
import com.welab.crm.operate.domain.WoRuleInfo;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.dto.workorder.AdjustWorkOrderInfo;
import com.welab.crm.operate.dto.workorder.OpOrderConfigDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleAdjustReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleReqDTO;
import com.welab.crm.operate.mapper.OpOrderConfigMapper;
import com.welab.crm.operate.mapper.WoRuleInfoMapper;
import com.welab.crm.operate.service.ICrmOrgStaffService;
import com.welab.crm.operate.service.WorkOrderRuleService;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.workorder.OpOrderConfigVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleVO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO;
import com.welab.exception.FastRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 工单service服务
 * <AUTHOR>
 * @date 2021-10-26
 */
@Slf4j
@Service
public class WorkOrderRuleServiceImpl implements WorkOrderRuleService {

    @Resource
    private WoRuleInfoMapper woRuleInfoMapper;

    @Resource
    private OpOrderConfigMapper opOrderConfigMapper;
    
    @Resource
    private WFRunBusiService runBusiService;
    
    @Resource
    private ICrmOrgStaffService crmOrgStaffService;

    @Resource
	private WoAssignLogService woAssignLogService;

    @Resource
	private InAuthCrmStaffMapper inAuthCrmStaffMapper;

	/**
	 * 工单调剂方式：根据数量
	 */
    private static String COUNT = "count";

	/**
	 * 工单调剂方式：根据详单
	 */
	private static String DETAIL = "detail";

    @Override
    public Page<WorkOrderRuleVO> queryWorkOrderRuleList(WorkOrderRuleReqDTO reqDTO) {
        log.info("queryWorkOrderRuleList,reqDTO:{}", JSON.toJSONString(reqDTO));

        return woRuleInfoMapper
                .queryWorkOrderRuleByPage(new Page<WorkOrderRuleVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
    }

    @Override
    public void addWorkOrderRule(WorkOrderRuleReqDTO reqDTO) {
    	WoRuleInfo woRuleInfo = new WoRuleInfo();
    	BeanUtils.copyProperties(reqDTO, woRuleInfo);
		woRuleInfo.setModifyStaffId(reqDTO.getCreateStaffId());
		woRuleInfo.setGmtHandle(new Date());
    	woRuleInfoMapper.insert(woRuleInfo);
    }

    @Override
    public void updateWorkOrderRule(WorkOrderRuleReqDTO reqDTO) {
    	WoRuleInfo woRuleInfo = null;
    	
    	if(reqDTO.getId() != null) {
    		woRuleInfo = woRuleInfoMapper.selectById(reqDTO.getId());
    		if (woRuleInfo != null) {
    			BeanUtils.copyProperties(reqDTO, woRuleInfo);
    			woRuleInfo.setGmtModify(new Date());
    			woRuleInfo.setModifyStaffId(reqDTO.getCreateStaffId());
    			woRuleInfo.setGmtHandle(new Date());
    			woRuleInfoMapper.updateById(woRuleInfo);
    		}else {
    			log.error("分单规则记录ID{}不存在",reqDTO.getId());
        		throw new FastRuntimeException("分单规则记录ID{}不存在",String.valueOf(reqDTO.getId()));
    		}
    	}else {
    		log.error("分单规则记录ID{}不存在！");
    		throw new FastRuntimeException("分单规则记录ID{}不存在");
    	}
    }
    
    @Override
    public void updateWorkOrderRuleAll(WorkOrderRuleReqDTO reqDTO) {
    	woRuleInfoMapper.updateWorkOrderRuleAll(reqDTO.getStatus(), reqDTO.getRuleType());
    }
    
    @Override
	public boolean deleteWorkOrderRule(BatchInfoReqDTO ids) {
		try {
			for (Long id : ids.getIds()) {
				woRuleInfoMapper.deleteById(id);
			}
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new FastRuntimeException("根据id删除内部分单规则产生异常......", e);
		}
	}
    
    @Override
    public Page<WorkOrderRuleAdjustVO> queryWorkOrderRuleAdjustList(WorkOrderRuleAdjustReqDTO reqDTO) {
        log.info("queryWorkOrderRuleAdjustLis reqDTO:{}", JSON.toJSONString(reqDTO));
        
        if(COUNT.equalsIgnoreCase(reqDTO.getMethod())) {
        	return woRuleInfoMapper
                    .queryWorkOrderAdjustCountByPage(new Page<WorkOrderRuleAdjustVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
        }else if(DETAIL.equalsIgnoreCase(reqDTO.getMethod())) {
			Page<WorkOrderRuleAdjustVO> pages = woRuleInfoMapper
					.queryWorkOrderAdjustListByPage(
							new Page<WorkOrderRuleAdjustVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
			if (Objects.nonNull(pages) && CollectionUtils.isNotEmpty(pages.getRecords())){
				pages.getRecords().parallelStream().forEach(item -> item.setMobile(SecurityUtil.maskMobile(item.getMobile())));
			}
			return pages;
		}else {
        	return null;
        }
    }
    
    @Override
	public void adjustWorkOrderRule(WorkOrderRuleAdjustReqDTO reqDTO) {
		List<AdjustWorkOrderInfo> orderList = reqDTO.getAssignList();
		if (CollectionUtils.isEmpty(orderList)) {
			return;
		}
		log.info("adjustWorkOrderRule orderList size:{}", orderList.size());
		if (COUNT.equalsIgnoreCase(reqDTO.getMethod())) {
			adjustByCount(reqDTO, orderList);
		} else if (DETAIL.equalsIgnoreCase(reqDTO.getMethod())) {
			adjustByDetail(reqDTO, orderList);
		}
	}
    
    @Override
    public OpOrderConfigVO queryOrderRuleConfig() {
        return opOrderConfigMapper.queryOrderRuleConfig();
    }

    @Override
    public void updateOrderRuleConfig(OpOrderConfigDTO reqDTO) {
    	OpOrderConfigVO vo = opOrderConfigMapper.queryOrderRuleConfig();
    	OpOrderConfig opOrderConfig = new OpOrderConfig();
    	BeanUtils.copyProperties(reqDTO, opOrderConfig);
    	if(vo != null) {
			opOrderConfig.setGmtModify(new Date());
    		opOrderConfigMapper.updateOrderRuleConfig(reqDTO);
    	}else {
    		opOrderConfigMapper.insert(opOrderConfig);
    	}
    }
    
    @Override
    public List<WorkOrderTypeTotalVO> typeTotalWorkOrder() {
        return woRuleInfoMapper.typeTotalWorkOrder();
    }

	private void adjustByCount(WorkOrderRuleAdjustReqDTO reqDTO, List<AdjustWorkOrderInfo> orderList) {
		//查询需要调剂的工单
		List<WorkOrderRuleAdjustVO> adjustList = woRuleInfoMapper.queryWorkOrderAdjustList(reqDTO);
		if (CollectionUtils.isEmpty(adjustList)) {
			return;
		}
		log.info("adjustWorkOrderRule adjustList size:{}", adjustList.size());
		// 当前是第几条工单
		int curOrderNum = 1;
		String operatorId = reqDTO.getOperatorId();
		// 操作人名称
		String operateName = inAuthCrmStaffMapper.selectById(operatorId).getStaffName();
		for (AdjustWorkOrderInfo orderInfo : orderList) {
			// 是否按组分配
			boolean assignByGroup = StringUtils.isBlank(orderInfo.getAssignStaffId());
			// 查询分配组的所有员工
			List<String> staffIdList = getAssignedStaffIdByGroupCode(assignByGroup,
				orderInfo.getAssignGroupCode());
			//业务代码分配坐席 与任务
			Map<String, String> staffWithTask = new HashMap<String, String>();
			// 当前员工索引
			int curStaffIndex = 0;
			// 该组需要分配的工单
			int orderAssignNum = orderInfo.getAssignNum();
			List<WoAssignLogDTO> logDTOList = new ArrayList<>();
			List<OrderAssignLogDTO> assignLogList = new ArrayList<>();
			while (adjustList.size() > 0) {
				WorkOrderRuleAdjustVO adjustVo = adjustList.get(0);
				if (!assignByGroup) {
					// 按指定人分配
					//调用流程接口完成分单操作
					staffWithTask.put(adjustVo.getTaskId(), orderInfo.getAssignStaffId());
					woRuleInfoMapper.updateWorkOrderInfo(adjustVo.getOrderNo());
					// 记录分单历史
					logDTOList.add(buildLogDTO(adjustVo.getOrderNo(), orderInfo.getAssignGroupCode(),
						orderInfo.getAssignStaffId(), operatorId));
					// 记录工单流水
					assignLogList.add(buildAssignLogDTO(adjustVo.getOrderNo(), orderInfo.getAssignStaffId(), "操作人:" + operateName));
				} else {
					// 按组分配
					if (staffIdList.size() > 0) {
						if (curStaffIndex >= staffIdList.size()) {
							curStaffIndex = 0;
						}
						String staffId = staffIdList.get(curStaffIndex);
						// 记录分单历史
						logDTOList.add(buildLogDTO(adjustVo.getOrderNo(), orderInfo.getAssignGroupCode(), staffId,
							operatorId));
						//调用流程接口完成分单操作
						staffWithTask.put(adjustVo.getTaskId(), staffId);
						woRuleInfoMapper.updateWorkOrderInfo(adjustVo.getOrderNo());
						// 记录工单流水
						assignLogList.add(buildAssignLogDTO(adjustVo.getOrderNo(), staffId, "操作人:" + operateName));
						curStaffIndex++;
					}
				}
				adjustList.remove(0);
				curOrderNum++;
				// 超出要分配的数量则推出工单循环，换下一个组或人分配工单
				if (curOrderNum > orderAssignNum) {
					log.info("adjustWorkOrderRule curOrderNum:{}", curOrderNum);
					curOrderNum = 1;
					break;
				}
			}
			// 记录分单历史
			woAssignLogService.assignLog(logDTOList);
			if (staffWithTask.size() > 0) {
				//调用流程接口完成分单操作
				runBusiService.allotTasks(staffWithTask, TaskAllotEnum.MANUAL_RECEIVE);
			}
			// 保存工单流水
			saveBatchAssignLog(assignLogList);
		}
	}

	private void saveBatchAssignLog(List<OrderAssignLogDTO> assignLogList) {
		if (CollectionUtils.isNotEmpty(assignLogList)) {
			for (OrderAssignLogDTO assignLogDTO : assignLogList) {
				saveLogs(assignLogDTO.getOrderNo(), assignLogDTO.getStaffId(), assignLogDTO.getComment());
			}
		}
	}

	private void adjustByDetail(WorkOrderRuleAdjustReqDTO reqDTO, List<AdjustWorkOrderInfo> orderList) {
		// 指定要分配的工单
		List<String> taskList = reqDTO.getIdList();
		if (CollectionUtils.isEmpty(taskList)) {
			return;
		}
		log.info("adjustWorkOrderRule taskList size:{}", taskList.size());
		// 当前是第几条工单
		int curOrderNum = 1;
		String operatorId = reqDTO.getOperatorId();
		// 操作人姓名
		String operateName = inAuthCrmStaffMapper.selectById(operatorId).getStaffName();
		for (AdjustWorkOrderInfo orderInfo : orderList) {
			// 是否按组分配
			boolean assignByGroup = StringUtils.isBlank(orderInfo.getAssignStaffId());
			// 查询分配组的所有员工
			List<String> staffIdList = getAssignedStaffIdByGroupCode(assignByGroup,
				orderInfo.getAssignGroupCode());
			// 业务代码分配坐席 与任务
			Map<String, String> staffWithTask = new HashMap<String, String>();
			int j = 0;
			List<WoAssignLogDTO> logDTOList = new ArrayList<>();
			List<OrderAssignLogDTO> assignLogList = new ArrayList<>();
			while (taskList.size() > 0) {
				String taskId = taskList.get(0);
				if (!assignByGroup) {
					//调用流程接口完成分单操作
					staffWithTask.put(taskId, orderInfo.getAssignStaffId());
					String orderNo = woRuleInfoMapper.queryWorkOrderNoByTaskId(taskId);
					if (StringUtils.isNotBlank(orderNo)) {
						woRuleInfoMapper.updateWorkOrderInfo(orderNo);
					}
					// 记录分单历史
					logDTOList.add(buildLogDTO(orderNo, orderInfo.getAssignGroupCode(), orderInfo.getAssignStaffId(),
						operatorId));
					assignLogList.add(buildAssignLogDTO(orderNo,orderInfo.getAssignStaffId(),"操作人:" + operateName));
				} else {
					if (staffIdList.size() > 0) {
						if (j >= staffIdList.size()) {
							j = 0;
						}
						//调用流程接口完成分单操作
						staffWithTask.put(taskId, staffIdList.get(j));
						String orderNo = woRuleInfoMapper.queryWorkOrderNoByTaskId(taskId);
						if (StringUtils.isNotBlank(orderNo)) {
							woRuleInfoMapper.updateWorkOrderInfo(orderNo);
						}
						// 记录分单历史
						logDTOList.add(buildLogDTO(orderNo, orderInfo.getAssignGroupCode(), staffIdList.get(j),
							operatorId));
						assignLogList.add(buildAssignLogDTO(orderNo, staffIdList.get(j), "操作人:" + operateName));
						j++;
					}
				}
				taskList.remove(0);
				curOrderNum++;
				if (curOrderNum > orderInfo.getAssignNum()) {
					log.info("adjustWorkOrderRule curOrderNum:{}", curOrderNum);
					curOrderNum = 1;
					break;
				}
			}
			woAssignLogService.assignLog(logDTOList);
			if (staffWithTask.size() > 0) {
				//调用流程接口完成分单操作
				runBusiService.allotTasks(staffWithTask, TaskAllotEnum.MANUAL_RECEIVE);
			}

			// 保存工单流水
			saveBatchAssignLog(assignLogList);
		}
	}

	private OrderAssignLogDTO buildAssignLogDTO(String orderNo, String assignStaffId, String comment) {
		OrderAssignLogDTO assignLogDTO = new OrderAssignLogDTO();
		assignLogDTO.setOrderNo(orderNo);
		assignLogDTO.setStaffId(assignStaffId);
		assignLogDTO.setComment(comment);
		return assignLogDTO;
	}

	private WoAssignLogDTO buildLogDTO(String orderNo, String groupCode, String staffId, String operatorId){
		WoAssignLogDTO logDTO = new WoAssignLogDTO();
		logDTO.setOrderNo(orderNo);
		logDTO.setGroupCode(groupCode);
		logDTO.setStaffId(staffId);
		logDTO.setAssignType(AssignTypeEnum.ADJUST.getValue());
		logDTO.setCreateStaffId(operatorId);
		return logDTO;
	}

	/**
	 * 查询该组可分配的所有员工
	 * @param assignByGroup
	 * @param assignGroupCode
	 * @return
	 */
	private List<String> getAssignedStaffIdByGroupCode(boolean assignByGroup, String assignGroupCode) {
		List<String> staffIdList = new ArrayList<String>();
		// 按组分配时需要获得该组的所有员工
		if (assignByGroup) {
			// 查询该组的所有员工
			ColStaffReqDTO staffDto = new ColStaffReqDTO();
			staffDto.setGroupCode(assignGroupCode);
			List<ColStaffResVO> staffList = crmOrgStaffService.getColStaffList(staffDto);
			if (staffList != null && staffList.size() > 0) {
				for (ColStaffResVO vo : staffList) {
					staffIdList.add(String.valueOf(vo.getId()));
				}
			}
		}
		return staffIdList;
	}


	/**
	 * 保存工单分单记录到工单流水
	 * @param orderNo 工单号
	 * @param assignStaffId 员工ID
	 */
	private void saveLogs(String orderNo, String assignStaffId, String comment) {
		String executionId = woRuleInfoMapper.queryExeIdByOrderNo(orderNo);
		runBusiService.saveLogs("手动调剂",executionId,assignStaffId, comment);
	}
}
