/**
 * @Title: ICrmDictInfoService.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.dict.CallSummaryReqDTO;
import com.welab.crm.operate.dto.dict.CallSummaryRespDTO;
import com.welab.crm.operate.dto.dict.DictInfoReqDTO;
import com.welab.crm.operate.vo.dict.DictInfoResVO;




/**
 * @description 字典服务
 * <AUTHOR>
 * @date 2021-10-15 10:07:06
 * @version v1.0
 */
public interface ICrmDictInfoService {
    
    /**
     * 查询字典列表
     * @param reqDTO
     * @return
     */
    public Page<DictInfoResVO> getDictInfos(DictInfoReqDTO req);
    
    /**
     * 添加字典记录
     * @param reqDTO
     * @return
     */
    public boolean addDictInfo(DictInfoReqDTO reqDTO);
    
    /**
     * 更新字典记录
     * @param reqDTO
     * @return
     */
    public boolean updateDictInfo(DictInfoReqDTO reqDTO);
    
    /**
     * 删除字典数据
     * @param reqDTO
     * @return
     */
    public boolean deleteDictInfo(DictInfoReqDTO reqDTO);

    /**
     * 查询电话小结列表
     * @param reqDTO
     * @return
     */
    public Page<CallSummaryRespDTO> getCallSummarys(CallSummaryReqDTO reqDTO);

    /**
     * 添加电话小结收藏
     * @param reqDTO
     * @return
     */
    public boolean addCallSummary(CallSummaryReqDTO reqDTO);

    /**
     * 删除电话小结收藏
     * @param reqDTO
     * @return
     */
    public boolean deleteCallSummary(CallSummaryReqDTO reqDTO);

    /**
     * 置顶/取消置顶
     * @param reqDTO
     * @return
     */
    public boolean topCallSummary(CallSummaryReqDTO reqDTO);
}
