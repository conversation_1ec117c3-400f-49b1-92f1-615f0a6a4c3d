package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.monitor.StaffMonitorDTO;
import com.welab.crm.operate.mapper.OpHttpLogRecordMapper;
import com.welab.crm.operate.service.StaffMonitorReportService;
import com.welab.crm.operate.vo.monitor.StaffMonitorReportVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class StaffMonitorReportServiceImpl implements StaffMonitorReportService {

    @Resource
    private OpHttpLogRecordMapper opHttpLogRecordMapper;

    @Override
    public Page<StaffMonitorReportVO> queryMonitorReportPage(StaffMonitorDTO dto) {

        Page<StaffMonitorReportVO> page =
            opHttpLogRecordMapper.queryStaffMonitorReportPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);

        page.setRecords(getAvgList(dto, page.getRecords()));
        return page;
    }

    private List<StaffMonitorReportVO> getAvgList(StaffMonitorDTO dto, List<StaffMonitorReportVO> records) {
        // 查询各个组均值
        List<StaffMonitorReportVO> avgList = opHttpLogRecordMapper.queryAvgCount(dto);
        Map<String, List<StaffMonitorReportVO>> groupDateMap =
            avgList.stream().collect(Collectors.groupingBy(item -> item.getQueryDate() + item.getGroupCode()));

        ;
        List<StaffMonitorReportVO> finalList = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            StaffMonitorReportVO vo = records.get(i);
            finalList.add(vo);
            if (i == records.size() - 1) {
                StaffMonitorReportVO vo1 = groupDateMap.get(vo.getQueryDate() + vo.getGroupCode()).get(0);
                vo1.setGroupCode("均值");
                finalList.add(vo1);
                continue;
            }
            StaffMonitorReportVO nextVo = records.get(i + 1);
            if (!vo.getQueryDate().equals(nextVo.getQueryDate()) || !vo.getGroupCode().equals(nextVo.getGroupCode())) {
                StaffMonitorReportVO vo1 = groupDateMap.get(vo.getQueryDate() + vo.getGroupCode()).get(0);
                vo1.setGroupCode("均值");
                finalList.add(vo1);
            }
        }
        return finalList;
    }

    @Override
    public List<StaffMonitorReportVO> queryMonitorReportList(StaffMonitorDTO dto) {
        List<StaffMonitorReportVO> list = opHttpLogRecordMapper.queryStaffMonitorReportList(dto);

        return getAvgList(dto, list);
    }
}
