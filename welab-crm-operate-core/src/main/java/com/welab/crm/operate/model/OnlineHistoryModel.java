package com.welab.crm.operate.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 在线系统返回联系历史对象
 */
@Data
public class OnlineHistoryModel implements Serializable {
    private static final long serialVersionUID = -6041084796947500158L;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 会话开始时间
     */
    private String startTime;
    /**
     * 最后保存小结时间
     */
    private String summaryTime;
    /**
     * online-在线系统
     */
    private String type;
    /**
     * 最后接待客服的姓名
     */
    private String lastServiceUser;
    /**
     * 客服工号
     */
    private String workNo;
    /**
     * online - 在线客服组
     */
    private String group;
    /**
     * 小结1#小结2#小结3#小结4#小结5
     */
    private String summary;
    /**
     * 小结备注
     */
    private String summaryRemark;

    /**
     * ai小结
     */
    private String aiSummary;
}
