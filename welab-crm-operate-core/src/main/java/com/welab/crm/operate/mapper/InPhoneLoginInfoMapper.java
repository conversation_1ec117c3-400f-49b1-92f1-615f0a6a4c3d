package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.domain.InPhoneLoginInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.vo.agent.AgentVO;
import com.welab.crm.operate.vo.tmkReport.StaffEfficiencyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 座机登录信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface InPhoneLoginInfoMapper extends BaseMapper<InPhoneLoginInfo> {

    /**
     * 根据staffId查询坐席号
     * @param staffIdList
     * @return
     */
    List<String> queryCnoListByStaffId(@Param("list") List<String> staffIdList);


    /**
     * 根据组别查询坐席号
     * @param groupList
     * @return
     */
    List<String> queryCnoListByGroupCode(@Param("list") List<String> groupList);

    /**
     * 根据坐席号查询组别和姓名
     * @param cno
     * @return
     */
    List<InAuthCrmStaff> queryGroupCodeByCno(@Param("cno") String cno);


    /**
     * 查询员工效能报表
     * @param startTime
     * @param endTime
     * @param groupCodeList
     * @return
     */
    List<StaffEfficiencyVO> queryStaffEfficiency(@Param("startTime") String startTime,
            @Param("endTime") String endTime, @Param("groupCodeList") List<String> groupCodeList);


    /**
     * 查询全部的坐席信息
     */
    List<AgentVO> queryAllAgentList();

    /**
     * 查询所有的坐席登录信息
     * @return
     */
    List<InPhoneLoginInfo> queryAll();
}
