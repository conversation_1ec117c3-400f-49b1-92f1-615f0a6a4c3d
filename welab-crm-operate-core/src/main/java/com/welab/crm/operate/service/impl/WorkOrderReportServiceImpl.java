package com.welab.crm.operate.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.service.ProductService;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.enums.AssignTypeEnum;
import com.welab.crm.base.workflow.busi.constant.WFWorkorderStateEnum;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.vo.loan.LoanDetailsVO;
import com.welab.crm.operate.domain.DataLoanApplication;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.report.*;
import com.welab.crm.operate.dto.transferReport.TransferReportDetailDTO;
import com.welab.crm.operate.dto.transferReport.TransferReportRangeDTO;
import com.welab.crm.operate.dto.workorder.FundNameMatchUpDTO;
import com.welab.crm.operate.excel.ExcelMergeStrategy;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import com.welab.crm.operate.mapper.WoReportMapper;
import com.welab.crm.operate.mapper.WoTaskMapper;
import com.welab.crm.operate.model.StatisticAgentWorkloadDay;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.service.WorkOrderReportService;
import com.welab.crm.operate.util.*;
import com.welab.crm.operate.vo.transferReport.MonthTransferBaseVO;
import com.welab.crm.operate.vo.transferReport.RangeTransferReportVO;
import com.welab.crm.operate.vo.transferReport.TransferReportDetailVO;
import com.welab.crm.operate.vo.woReport.*;
import com.welab.crm.operate.vo.workorder.EscalationOrderVo;
import com.welab.crm.operate.vo.workorder.WorkOrderLogVO;
import com.welab.exception.FastRuntimeException;
import com.welab.xdao.context.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作效能统计服务类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/12/24
 */
@Slf4j
@Service
public class WorkOrderReportServiceImpl implements WorkOrderReportService {

    @Resource
    private WoReportMapper woReportMapper;

    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private TrUtil trUtil;

    @Resource
    private WoTaskMapper woTaskMapper;

    @Resource
    private ProductService productService;

    @Value("${report.outbound.efficiency.summary.group}")
    private String outboundEfficiencySummaryGroup;


    /**
     * 工单统计报表明细排序
     */
    private final List<String> statisticDetailsSort = Arrays.asList("现金分期", "钱夹谷谷", "淘新机", "电商业务");

    /**
     * 工单类型字典名称
     */
    private static final String FORM_DICT_NAME = "formType";


    /**
     * 模式类型字典
     */
    private static final String LOAN_MODEL_TYPE = "loan_model_type";
    private static final String DEFAULT_MODEL_TYPE = "助贷模式";


    private static final List<String> NORMAL_ORDER_TYPE = Arrays.asList("普通工单", "投诉工单", "舆情工单");
    private static final List<String> SPECIAL_ORDER_TYPE = Arrays.asList("监管工单", "资方工单");


    @Override
    public ReportSummaryVO getSummaryComplaint(WoReportDTO dto) {
        checkTimeAndConvertList(dto);
        ReportSummaryVO summaryVO = woReportMapper.selectTotalNumber(dto);
        // 查询全部工单类型
        List<OpDictInfo> orderTypeList = CommonUtils.getDict(FORM_DICT_NAME, null);
        List<ReportSummaryDetailsVO> detailsVOList = woReportMapper.selectSummaryDetailsComplaint(dto, orderTypeList);
        setCategories(summaryVO, detailsVOList, null);
        return summaryVO;
    }

    @Override
    public ReportSummaryVO getSummaryProduct(WoReportDTO dto) {
        checkTimeAndConvertList(dto);
        ReportSummaryVO summaryVO = woReportMapper.selectTotalNumber(dto);
        List<ReportSummaryDetailsVO> detailsVOList = woReportMapper.selectSummaryDetailsProduct(dto, null);
        setCategories(summaryVO, detailsVOList, null);
        return summaryVO;
    }

    @Override
    public List<ReportSummaryVO> getSummary(WoReportDTO dto) {
        checkTimeAndConvertList(dto);
        // 查询全部工单类型
        List<OpDictInfo> orderTypeList = CommonUtils.getDict(FORM_DICT_NAME, null);
        if (CollectionUtils.isEmpty(orderTypeList)) {
            throw new FastRuntimeException("工单类型字典为空");
        }
        // 合计行
        ReportSummaryVO total = new ReportSummaryVO();
        List<ReportSummaryVO> summaryVOList = woReportMapper.selectNumber4Type(dto, orderTypeList);
        for (int i = 0; i < orderTypeList.size(); i++) {
            ReportSummaryVO summaryVO = summaryVOList.get(i);
            List<ReportSummaryDetailsVO> detailsVOList = woReportMapper.selectSummaryDetailsProduct(dto,
                    orderTypeList.get(i).getContent());
            setCategories(summaryVO, detailsVOList, total);
        }
        summaryVOList.add(total);
        return summaryVOList;
    }

    @Override
    public Page<ReportSummaryTypeVO> getSummaryType(ReportSummaryTypeDTO dto) {
        checkTimeAndConvertList(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        List<ReportSummaryTypeVO> detailsVOList = woReportMapper.selectSummaryThirdType(dto);
        if (CollectionUtils.isEmpty(detailsVOList)) {
            return Page.initPage(null, 0, rowsPerPage, currentPage);
        }
        // 计算总数
        Integer total = 0;
        Integer endTotal = 0;
        Integer notEndTotal = 0;
        for (ReportSummaryTypeVO vo : detailsVOList) {
            total += vo.getNumber();
            endTotal += vo.getEndCount();
            notEndTotal += vo.getNotEndCount();
        }
        // 计算百分比
        for (ReportSummaryTypeVO vo : detailsVOList) {
            vo.setRate(CommonUtil.calcPercentage(vo.getNumber(), total));
        }
        // 按工单大类排序
        detailsVOList.sort(
                Comparator.comparing(ReportSummaryTypeVO::getFirstType, (x, y) -> {
                    if (x == null && y != null) {
                        return 1;
                    } else if (x != null && y == null) {
                        return -1;
                    } else if (x == null && y == null) {
                        return -1;
                    } else {
                        // 按照读取的list顺序排序
                        for (String sort : statisticDetailsSort) {
                            if (sort.equals(x) || sort.equals(y)) {
                                if (x.equals(y)) {
                                    return 0;
                                } else if (sort.equals(x)) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            }
                        }
                        return 0;
                    }
                })
        );
        // 添加“合计”对象
        ReportSummaryTypeVO totalVO = new ReportSummaryTypeVO();
        totalVO.setType("合计");
        totalVO.setNumber(total);
        totalVO.setEndCount(endTotal);
        totalVO.setNotEndCount(notEndTotal);
        detailsVOList.add(totalVO);
        return CommonUtil.subList(detailsVOList, rowsPerPage, currentPage);
    }

    @Override
    public Page<ReportDetailsVO> getDetails(WoReportDTO dto, boolean isExport) {
        checkTimeAndConvertList(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        long startTime1 = System.currentTimeMillis();
        int totalRows = woReportMapper.selectDetailsCount(dto);
        List<DataLoanApplication> woIds = woReportMapper.selectWoIdsPage(dto);
        if (CollectionUtils.isEmpty(woIds)) {
            return Page.initPage(Collections.emptyList(), totalRows, rowsPerPage, currentPage);
        }
        log.info("工单明细报表,查询工单数量耗时:{} ms", System.currentTimeMillis() - startTime1);
        // 查询工单信息
        long startTime2 = System.currentTimeMillis();
        List<ReportDetailsVO> detailsVOList;
        if (!isExport) {
            // 普通查询用id范围查询，导出则使用过滤条件完整查询
            List<Long> taskList = woIds.stream().map(DataLoanApplication::getWoTaskId).collect(Collectors.toList());
            detailsVOList = woReportMapper.selectDetails(taskList, dto);
        } else {
            detailsVOList = woReportMapper.selectDetails(dto.getOrderIdList(), dto);
        }
        log.info("工单明细报表,查询工单详情耗时:{} ms", System.currentTimeMillis() - startTime2);

        long startTime3 = System.currentTimeMillis();
        // 按工单主键分组，获得贷款号列表
        Map<Long, List<DataLoanApplication>> map = woIds.stream().filter(item -> Objects.nonNull(item.getWoTaskId()))
                .collect(Collectors.groupingBy(DataLoanApplication::getWoTaskId));
        // 筛选出全部的工单号
        List<String> orderNoList = detailsVOList.stream().map(ReportDetailsVO::getOrderNo).collect(Collectors.toList());
        // 根据工单号列表查询出全部的流程数据，用于后续使用
        Map<String, List<WorkOrderLogVO>> orderLogMap = woTaskMapper.queryWorkOrderLogListByBusiKeyList(orderNoList)
                .stream().collect(Collectors.groupingBy(WorkOrderLogVO::getOrderNo));
        // 按贷款条数分页
        List<ReportDetailsVO> result = new ArrayList<>();
        for (ReportDetailsVO vo : detailsVOList) {
            if (Objects.nonNull(vo.getCurComment())) {
                JSONObject curComment = JSONObject.parseObject(vo.getCurComment());
                vo.setCurComment(curComment.getString("opinion"));
            }
            if (Objects.nonNull(vo.getPreComment())) {
                JSONObject preComment = JSONObject.parseObject(vo.getPreComment());
                vo.setPreComment(preComment.getString("opinion"));
            }
            if (Objects.isNull(vo.getStatus())) {
                if (CommonUtil.checkIsEnd(vo.getCloseType())) {
                    buildCurAndPreLog(vo, orderLogMap.get(vo.getOrderNo()));
                }
                vo.setStatus(WFWorkorderStateEnum.getDesc(vo.getCloseType()));
            } else {
                if (CommonUtil.checkIsEnd(vo.getStatus())) {
                    buildCurAndPreLog(vo, orderLogMap.get(vo.getOrderNo()));
                }
                vo.setStatus(WFWorkorderStateEnum.getDesc(vo.getStatus()));
            }
            if (StringUtils.isBlank(vo.getStatus())){
                vo.setStatus(WFWorkorderStateEnum.PROCESS.getDesc());
            }
            if ("1".equals(vo.getCompleteFlag())) {
                vo.setCloseType(WFWorkorderStateEnum.getDesc(vo.getStatus()));
            } else {
                vo.setCloseType(WFWorkorderStateEnum.getDesc(vo.getCloseType()));
            }
            vo.setAssignType(AssignTypeEnum.getText(vo.getAssignType()));
            // 手机号脱敏
            vo.setMobile(StringUtil.hideMobile(vo.getMobile()));
            // 备用号码处理，旧工单的备用号码在 mobile_bak，新工单的备用号码在 mobile_baks，将两个字段合起来
            List<String> mobileBakList = new ArrayList<>();
            if (StringUtils.isNotBlank(vo.getMobileBak())) {
                mobileBakList.add(vo.getMobileBak());
            }
            if (StringUtils.isNotBlank(vo.getMobileBaks())) {
                mobileBakList.addAll(Arrays.asList(vo.getMobileBaks().split(",")));
            }
            vo.setMobileBak(String.join(",", mobileBakList));
            
            if (Objects.nonNull(vo.getUuid())){
                vo.setUuidStr(String.valueOf(vo.getUuid()));
            }
            // 根据工单主键获得贷款号
            List<DataLoanApplication> applicationList = map.get(vo.getId());
            // orderIdList 存在说明是投诉升级报表，不需要拼接贷款数据
            if (CollectionUtils.isNotEmpty(applicationList) && CollectionUtils.isEmpty(dto.getOrderIdList())) {
                Map<String, String> loanModelTypeMap = CommonUtils.getDictTypeContentMap(LOAN_MODEL_TYPE, "");
                if (applicationList.size() == 1) {
                    LoanDetailsVO loan = convertToLoanDetailsVO(applicationList.get(0), loanModelTypeMap);
                    vo.setLoan(loan);
                    result.add(vo);
                } else {
                    for (DataLoanApplication application : applicationList) {
                        ReportDetailsVO reportDetailsVO = new ReportDetailsVO();
                        BeanUtils.copyProperties(vo, reportDetailsVO);
                        // 查询贷款信息列表
                        LoanDetailsVO loan = convertToLoanDetailsVO(application, loanModelTypeMap);
                        reportDetailsVO.setLoan(loan);
                        result.add(reportDetailsVO);
                    }
                }
            } else {
                result.add(vo);
            }
        }
        log.info("工单明细报表,循环调用贷款详情接口耗时:{} ms", System.currentTimeMillis() - startTime3);
        return Page.initPage(result, totalRows, rowsPerPage, currentPage);
    }

    /**
     * 结案工单的当前处理数据和上一处理数据要特殊处理
     *
     * @param vo
     * @param woLogs 日志列表
     */
    private void buildCurAndPreLog(ReportDetailsVO vo, List<WorkOrderLogVO> woLogs) {
        // 查询出全部的工单流水，按照时间倒序
        if (CollectionUtils.isNotEmpty(woLogs) && woLogs.size() >= 2) {
            WorkOrderLogVO cur = woLogs.get(0);
            vo.setCurStaffName(cur.getStaffId());
            vo.setCurGroupName(cur.getGroupName());
            vo.setCurProcessTime(cur.getGmtCreate());
            JSONObject curJson = JSON.parseObject(cur.getComment());
            if (Objects.nonNull(curJson)) {
                vo.setCurComment(curJson.getString("opinion"));
            }

            WorkOrderLogVO pre = woLogs.get(1);
            vo.setPreStaffName(pre.getStaffId());
            vo.setPreGroupName(pre.getGroupName());
            vo.setPreProcessTime(pre.getGmtCreate());
            JSONObject preJson = JSON.parseObject(pre.getComment());
            if (Objects.nonNull(preJson)) {
                vo.setPreComment(preJson.getString("opinion"));
            }
        }
    }


    @Override
    public void exportReportDetail(HttpServletResponse response, WoReportDTO dto, String fileName) {
        Page<ReportDetailsVO> details = getDetails(dto, true);
        long startTime = System.currentTimeMillis();
        // 查询出全部的渠道
        List<String> originList = details.getList().stream().map(t -> {
            return t.getLoan().getOrigin();
        }).distinct().collect(Collectors.toList());
        String origins = StringUtils.join(originList, ",");
        // 查询渠道号和名称的字典
        Map<String, String> originNameMap = productService.listChannelNameByCodes(origins);
        List<ReportDetailsExportVO> exportList = new ArrayList<>(details.getList().size());
        for (ReportDetailsVO detailsVO : details.getList()) {
            ReportDetailsExportVO exportVO = new ReportDetailsExportVO();
            BeanUtils.copyProperties(detailsVO, exportVO);
            LoanDetailsVO loan = detailsVO.getLoan();
            loan.setOrderNo(detailsVO.getOrderNo());
            loan.setMobile(detailsVO.getMobile());
            loan.setUserId(detailsVO.getUserId());
            BeanUtils.copyProperties(detailsVO.getLoan(), exportVO);
            exportVO.setOriginName(originNameMap.get(loan.getOrigin()));
            if (Objects.nonNull(detailsVO.getUuid())) {
                exportVO.setUuid(detailsVO.getUuid().toString());
            }
            if (StringUtils.isNotBlank(detailsVO.getMatchUp()) && StringUtils.isNotBlank(detailsVO.getFundName())) {
                List<FundNameMatchUpDTO> fundNames = JSONArray.parseArray(detailsVO.getMatchUp(), FundNameMatchUpDTO.class);
                StringBuilder sb = new StringBuilder();
                for (FundNameMatchUpDTO tmp : fundNames) {
                    if (Objects.nonNull(tmp.getComplaintsChannel())) {
                        sb.append(tmp.getComplaintsChannel()).append("&");
                        if (CollectionUtils.isNotEmpty(tmp.getFundName())) {
                            int count = 0;
                            for (String fundName : tmp.getFundName()) {
                                count++;
                                if (count == tmp.getFundName().size()) {
                                    sb.append(fundName);
                                } else {
                                    sb.append(fundName).append("&");
                                }
                            }
                        }
                        sb.append("/");
                    }
                }
                exportVO.setFundName(sb.toString());
            }
            exportList.add(exportVO);
        }

        try {
            CommonUtil.setResponse(response, fileName);
            List<CellRangeAddress> rangeList = getCellRangeList(exportList);
            long middleTime = System.currentTimeMillis();
            log.info("exportReportDetail 计算rangeList用时:{} ms", middleTime - startTime);
            // 从第二行数据行和第31列开始需要做合并操作
            EasyExcel.write(new BufferedOutputStream(response.getOutputStream()), ReportDetailsExportVO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet("工单明细报表")
                    .registerWriteHandler(new ExcelMergeStrategy(rangeList))
                    .doWrite(exportList);
            log.info("exportReportDetail 导出工单明细报表用时:{} ms", System.currentTimeMillis() - middleTime);
        } catch (Exception e) {
            log.warn("导出工单报表数据异常, 查询参数: {}, 错误消息: {}", dto, e.getMessage(), e);
            throw new CrmOperateException("导出错误");
        }
    }

    private List<CellRangeAddress> getCellRangeList(List<ReportDetailsExportVO> exportVOList) {
        if (exportVOList.size() > 2) {
            // 1.准备需要合并的单元格的数字
            Map<String, Integer> orderNoRangeMap = new HashMap<>(exportVOList.size(), 1);
            for (int i = 1; i < exportVOList.size(); i++) {
                String currOrderNo = exportVOList.get(i).getOrderNo();
                if (Objects.equals(currOrderNo, exportVOList.get(i - 1).getOrderNo())) {
                    Integer count = orderNoRangeMap.get(currOrderNo);
                    if (count != null && count > 0) {
                        orderNoRangeMap.put(currOrderNo, count + 1);
                    } else {
                        orderNoRangeMap.put(currOrderNo, 2);
                    }
                }
            }
            // 2.构建具体需要合并的range列表
            if (!orderNoRangeMap.isEmpty()) {
                List<CellRangeAddress> addressList = new ArrayList<>(orderNoRangeMap.size() * 35);
                for (int row = 0; row < exportVOList.size(); row++) {
                    String orderNo = exportVOList.get(row).getOrderNo();
                    Integer rangeNum = orderNoRangeMap.get(orderNo);
                    if (rangeNum != null) {
                        orderNoRangeMap.remove(orderNo);
                        //合并资金方与投诉渠道字段
                        /*StringBuilder complaintsChannel = new StringBuilder();
                        StringBuilder fundName = new StringBuilder();
                        for (int i = row; i < row + rangeNum; i++) {
                            complaintsChannel.append(exportVOList.get(i).getComplaintsChannel()).append("#");
                            fundName.append(exportVOList.get(i).getFundName()).append("#");
                        }
                        exportVOList.get(row).setComplaintsChannel(complaintsChannel.toString());
                        exportVOList.get(row).setFundName(fundName.toString());*/
                        // 添加31列合并项->2023.06.07 添加32列合并->2023.07.21 添加34列合并 -> 2025.04.29 35列合并
                        for (int column = 0; column < 35; column++) {
                            addressList.add(new CellRangeAddress(row + 1, row + rangeNum, column, column));
                        }
                    }
                }
                return addressList;
            } else {
                return Collections.emptyList();
            }
        } else {
            return Collections.emptyList();
        }
    }

    private LoanDetailsVO convertToLoanDetailsVO(DataLoanApplication loanApplication, Map<String, String> loanModelTypeMap) {
        LoanDetailsVO vo = new LoanDetailsVO();
        BeanUtils.copyProperties(loanApplication, vo);
        vo.setState(loanApplication.getStatus());
        vo.setOrigin(loanApplication.getChannelCode());
        vo.setTenor(loanApplication.getApprovalTenor());
        vo.setConfirmedAt(loanApplication.getConfirmTime());
        vo.setPartnerName(loanApplication.getPartnerCode());
        vo.setDisbursedTime(loanApplication.getLoanTime());
        vo.setTotalRate(loanApplication.getTotalRate());
        vo.setLoanModelType(loanModelTypeMap.getOrDefault(loanApplication.getPartnerCodeNew(), DEFAULT_MODEL_TYPE));
        return vo;
    }

    @Override
    public Page<ReportAssignmentSummaryVO> getAssignmentSummary(ReportAssignmentSummaryDTO dto) {
        checkTimeAndConvertList(dto);
        List<ReportAssignmentSummaryVO> list = woReportMapper.selectAssignmentSummary(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        if (CollectionUtils.isEmpty(list)) {
            return Page.initPage(new ArrayList<>(), 0, rowsPerPage, currentPage);
        }
        // 处理数据
        String assignType = dto.getAssignType();
        if (StringUtils.isNotBlank(assignType)) {
            list = list.stream().filter(v -> assignType.equals(v.getAssignType())).collect(Collectors.toList());
        }
        int total = 0;
        for (ReportAssignmentSummaryVO vo : list) {
            total += vo.getCount();
            vo.setAssignType(AssignTypeEnum.getText(vo.getAssignType()));
        }
        ReportAssignmentSummaryVO summaryVO = new ReportAssignmentSummaryVO();
        summaryVO.setAssignType("合计");
        summaryVO.setCount(total);
        list.add(summaryVO);
        return CommonUtil.subList(list, rowsPerPage, currentPage);
    }

    @Override
    public List<ReportAssignmentSummaryVO> getAssignmentSummaryList(ReportAssignmentSummaryDTO dto) {
        setDtoPageParam(dto);
        return getAssignmentSummary(dto).getList();
    }

    @Override
    public Page<ReportAssignmentDetailsVO> getAssignmentDetails(WoReportDTO dto) {
        checkTimeAndConvertList(dto);
        List<ReportAssignmentDetailsVO> detailsVOList = woReportMapper.selectAssignmentDetails(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        if (CollectionUtils.isEmpty(detailsVOList)) {
            return Page.initPage(new ArrayList<>(), 0, rowsPerPage, currentPage);
        }
        int totalRows = detailsVOList.size();
        // 获得当前页列表
        detailsVOList = CommonUtil.subList(detailsVOList, totalRows, rowsPerPage, currentPage);
        detailsVOList.forEach(vo -> {
            vo.setAssignType(AssignTypeEnum.getText(vo.getAssignType()));
        });
        return Page.initPage(detailsVOList, totalRows, rowsPerPage, currentPage);
    }

    @Override
    public List<ReportAssignmentDetailsVO> getAssignmentDetailsList(WoReportDTO dto) {
        setDtoPageParam(dto);
        return getAssignmentDetails(dto).getList();
    }

    @Override
    public Page<ReportEfficiencySummaryVO> getEfficiencySummary(ReportEfficiencySummaryDTO dto) {
        checkTimeAndConvertList(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        List<ReportEfficiencySummaryVO> result = new ArrayList<>();
        List<ReportEfficiencySummaryVO> summaryVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getType())) {
            // 按指定工单类型查询报表
            OpDictInfo opDictInfo = opDictInfoMapper.selectOne(Wrappers.lambdaQuery(OpDictInfo.class)
                    .eq(OpDictInfo::getId, dto.getType()));

            List<ReportEfficiencySummaryVO> list = woReportMapper.selectEfficiencySummary(dto,
                    opDictInfo.getContent(), getWorkOrderReportGroupList(dto));
            // 排除创建工单数量为0的数据，提单量和分单量都为0，则这条数据无意义可以排除掉
            list = list.stream().filter(summary -> summary.getCreateNum() > 0 || summary.getAssignNum() > 0).collect(Collectors.toList());
            list = buildEfficiencySummaryVO(list, opDictInfo.getContent(), summaryVOList);
            result.addAll(list);
            result.addAll(summaryVOList);
        } else {
            // 查询所有工单类型报表
            List<OpDictInfo> orderTypeList = CommonUtils.getDict(FORM_DICT_NAME, null);

            for (int i = 0; i < orderTypeList.size(); i++) {
                String orderType = orderTypeList.get(i).getContent();
                List<ReportEfficiencySummaryVO> orderList = woReportMapper.selectEfficiencySummary(dto,
                        orderType, getWorkOrderReportGroupList(dto));
                // 排除创建工单数量为0的数据，提单量和分单量都为0，则这条数据无意义可以排除掉
                orderList = orderList.stream().filter(summary -> summary.getCreateNum() > 0 || summary.getAssignNum() > 0).collect(Collectors.toList());
                orderList = buildEfficiencySummaryVO(orderList, orderType, summaryVOList);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    result.addAll(orderList);
                }
                result.add(summaryVOList.get(i));
            }
        }
        // 添加总合计
        buildEfficiencySummaryVO(summaryVOList, "总", result);
        return CommonUtil.subList(result, rowsPerPage, currentPage);
    }

    @Override
    public List<ReportEfficiencySummaryVO> getEfficiencySummaryList(ReportEfficiencySummaryDTO dto) {
        setDtoPageParam(dto);
        return getEfficiencySummary(dto).getList();
    }

    @Override
    public Page<OutboundEfficiencySummaryVO> getOutboundEfficiencySummary(OutboundEfficiencySummaryDTO dto) {
        checkTimeAndConvertList(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        List<String> groups = Arrays.asList(outboundEfficiencySummaryGroup.split(","));
        List<OutboundEfficiencySummaryVO> voList = woReportMapper.selectOutboundEfficiencySummary(dto, groups);
        int totalRows = voList.size();
        // 获得当前页列表
        voList = CommonUtil.subList(voList, totalRows, rowsPerPage, currentPage);
        if (CollectionUtils.isEmpty(voList)) {
            return Page.initPage(new ArrayList<>(), totalRows, rowsPerPage, currentPage);
        }
        buildOutboundEfficiencySummaryVOList(dto, voList);
        //去掉外呼为0的数据
        voList.removeIf(t -> t.getCallCount() == 0);
        return Page.initPage(voList, totalRows, rowsPerPage, currentPage);
    }

    @Override
    public List<OutboundEfficiencySummaryVO> getOutboundEfficiencySummaryList(OutboundEfficiencySummaryDTO dto) {
        setDtoPageParam(dto);
        return getOutboundEfficiencySummary(dto).getList();
    }

    @Override
    public Page<CentralMonitoringVO> getCentralMonitoring(CentralMonitoringDTO dto) {
        checkTimeAndConvertList(dto);
        if (StringUtils.isBlank(dto.getGroupCode())) {
            throw new CrmOperateException("组别参数不能为空");
        }
        if (StringUtils.isNotBlank(dto.getGroupCode())) {
            dto.setGroupCodesList(Arrays.asList(dto.getGroupCode().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getStaffId())) {
            dto.setStaffIds(Arrays.asList(dto.getStaffId().split(",")));
        }
        List<CentralMonitoringVO> voList = woReportMapper.selectCentralMonitoring(dto);
        int rowsPerPage = dto.getRowsPerPage();
        int currentPage = dto.getCurrentPage();
        int totalRows = voList.size();
        // 获得当前页列表
        voList = CommonUtil.subList(voList, totalRows, rowsPerPage, currentPage);
        if (CollectionUtils.isEmpty(voList)) {
            return Page.initPage(null, totalRows, rowsPerPage, currentPage);
        }
        // 获取天润报表
        Map<String, StatisticAgentWorkloadDay> map = getStatisticAgentGroupByCno(dto);
        for (CentralMonitoringVO vo : voList) {
            StatisticAgentWorkloadDay agent = map.get(vo.getIdNo());
            if (Objects.isNull(agent)) {
                vo.setCallCount(0);
                vo.setConnectedNumber(0);
                vo.setConnectedRate("0%");
                vo.setTotalCallTime("0");
                vo.setAvgCallTime("0");
                continue;
            }
            vo.setCallCount(Integer.valueOf(agent.getObCallingCount()));
            vo.setConnectedNumber(Integer.valueOf(agent.getObBridgeCount()));

            if (vo.getCallCount() != null && vo.getCallCount() != 0
                    && vo.getConnectedNumber() != null && vo.getConnectedNumber() != 0) {
                vo.setConnectedRate(new BigDecimal(vo.getConnectedNumber()).multiply(new BigDecimal(100)).divide(new BigDecimal(vo.getCallCount()), 2, BigDecimal.ROUND_HALF_UP).toString() + "%");
            } else {
                vo.setConnectedRate("0%");
            }

            String totalTime = "0";

            if (StringUtils.isNotBlank(agent.getObBridgeTime())) {

                String[] my = agent.getObBridgeTime().split(":");

                int hour = Integer.parseInt(my[0]);

                int min = Integer.parseInt(my[1]);

                int sec = Integer.parseInt(my[2]);

                totalTime = String.valueOf(hour * 3600 + min * 60 + sec);
            }

            if (!"0".equals(totalTime)
                    && vo.getConnectedNumber() != null && vo.getConnectedNumber() != 0) {

                vo.setAvgCallTime(new BigDecimal(totalTime).divide(new BigDecimal(vo.getConnectedNumber()), 0, BigDecimal.ROUND_HALF_UP).toString());
            } else {
                vo.setAvgCallTime("0");
            }
            vo.setTotalCallTime(agent.getObBridgeTime());

        }
        return Page.initPage(voList, totalRows, rowsPerPage, currentPage);
    }

    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<RangeTransferReportVO> queryTransferRangeReport(TransferReportRangeDTO dto) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<RangeTransferReportVO> basePage = woReportMapper
                .selectRangeTransferReportPage(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(dto.getCurPage(), dto.getPageSize()), dto);

        // 查询合计数据
        Map<String, String> totalMap = woReportMapper.queryTotalGroupByTransferCompany(dto.getStartTime(), dto.getEndTime())
                .stream().collect(Collectors.toMap(RangeTransferReportVO::getTransferCompany, RangeTransferReportVO::getTotal));
        // 查询环比合计数据
        Map<String, String> roundTotalMap = Collections.emptyMap();
        if (StringUtils.isNotBlank(dto.getRoundStartTime()) && StringUtils.isNotBlank(dto.getRoundEndTime())) {
            roundTotalMap = woReportMapper.queryTotalGroupByTransferCompany(dto.getRoundStartTime(), dto.getRoundEndTime())
                    .stream().collect(Collectors.toMap(RangeTransferReportVO::getTransferCompany, RangeTransferReportVO::getTotal));
        }

        for (RangeTransferReportVO record : basePage.getRecords()) {
            record.setTotal(totalMap.getOrDefault(record.getTransferCompany(), "0"));
            if (StringUtils.isNotBlank(dto.getRoundStartTime()) && StringUtils.isNotBlank(dto.getRoundEndTime())) {
                record.setRoundTimeTotal(roundTotalMap.getOrDefault(record.getTransferCompany(), "0"));
                record.setRoundCompare(CommonUtil.calcPercentage(Integer.parseInt(record.getTotal()) - Integer.parseInt(record.getRoundTimeTotal()), Integer.parseInt(record.getTotal())));
            }
        }
        return basePage;
    }

    @Override
    public List<RangeTransferReportVO> queryTransferRangeReportList(TransferReportRangeDTO dto) {
        List<RangeTransferReportVO> list = woReportMapper
                .selectRangeTransferReportList(dto);

        // 查询合计数据
        Map<String, String> totalMap = woReportMapper.queryTotalGroupByTransferCompany(dto.getStartTime(), dto.getEndTime())
                .stream().collect(Collectors.toMap(RangeTransferReportVO::getTransferCompany, RangeTransferReportVO::getTotal));
        // 查询环比合计数据
        Map<String, String> roundTotalMap = Collections.emptyMap();
        if (StringUtils.isNotBlank(dto.getRoundStartTime()) && StringUtils.isNotBlank(dto.getRoundEndTime())) {
            roundTotalMap = woReportMapper.queryTotalGroupByTransferCompany(dto.getRoundStartTime(), dto.getRoundEndTime())
                    .stream().collect(Collectors.toMap(RangeTransferReportVO::getTransferCompany, RangeTransferReportVO::getTotal));
        }

        for (RangeTransferReportVO record : list) {
            record.setTotal(totalMap.getOrDefault(record.getTransferCompany(), "0"));
            if (StringUtils.isNotBlank(dto.getRoundStartTime()) && StringUtils.isNotBlank(dto.getRoundEndTime())) {
                record.setRoundTimeTotal(roundTotalMap.getOrDefault(record.getTransferCompany(), "0"));
                record.setRoundCompare(CommonUtil.calcPercentage(Integer.parseInt(record.getTotal()) - Integer.parseInt(record.getRoundTimeTotal()), Integer.parseInt(record.getTotal())));
            }
        }
        return list;
    }

    @Override
    public List<List<String>> queryTransferMonthReport(TransferReportRangeDTO dto) {
        List<List<String>> rowList = new ArrayList<>();
        List<MonthTransferBaseVO> baseList = woReportMapper.selectMonthTransferList(dto);
        List<String> companyList = baseList.stream().map(MonthTransferBaseVO::getTransferCompany).distinct().collect(Collectors.toList());
        Map<String, String> dataMap = baseList.stream().collect(
                Collectors.toMap(item -> item.getTransferCompany() + "_" + item.getDateStr(), MonthTransferBaseVO::getCount));
        for (String company : companyList) {
            String startMonth = dto.getStartTime().substring(0, 7);
            String endMonth = dto.getEndTime().substring(0, 7);
            Integer total = 0;
            List<String> row = new ArrayList<>();
            row.add(company);
            while (startMonth.compareTo(endMonth) <= 0) {
                String countNumber = dataMap.getOrDefault(company + "_" + startMonth, "0");
                row.add(countNumber);
                startMonth = DateUtil.dateToString(DateUtil.plusMonths(DateUtil.stringToDate(startMonth), 1)).substring(0, 7);
                total += Integer.parseInt(countNumber);
            }
            row.add(String.valueOf(total));
            rowList.add(row);
        }

        return rowList;
    }

    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<TransferReportDetailVO> queryTransferDetailReport(TransferReportDetailDTO dto) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<TransferReportDetailVO> page = woReportMapper.selectTransferDetailReportPage(
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(dto.getCurPage(), dto.getPageSize()), dto);
        page.getRecords().forEach(item -> item.setMobile(SecurityUtil.maskMobilePro(item.getMobile())));

        return page;
    }

    @Override
    public List<TransferReportDetailVO> queryTransferDetailReportList(TransferReportDetailDTO dto) {
        List<TransferReportDetailVO> list = woReportMapper.selectTransferDetailReportList(dto);
        list.forEach(item -> item.setMobile(SecurityUtil.maskMobilePro(item.getMobile())));
        return list;
    }

    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReportDetailsVO> queryComplaintEscalationReport(WoReportDTO dto) {
        CommonUtil.validTime(dto.getStartTime(), dto.getEndTime(), 31);
        List<OpDictInfo> orderTypeDictList = CommonUtils.getDict(FORM_DICT_NAME, "");
        if (CollectionUtils.isEmpty(orderTypeDictList)) {
            return null;
        }
        List<Long> normalOrderIds = orderTypeDictList
                .stream().filter(item -> NORMAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());

        List<Long> specialOrderIds = orderTypeDictList
                .stream().filter(item -> SPECIAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());
        
        List<EscalationOrderVo> escalationOrderVoList = woTaskMapper.selectEscalationOrder(dto, normalOrderIds, specialOrderIds);

        if (CollectionUtils.isEmpty(escalationOrderVoList)) {
            return null;
        }
        List<Long> ids = new ArrayList<>();
        for (EscalationOrderVo vo : escalationOrderVoList) {
            ids.add(vo.getOrdinaryId());
            ids.add(vo.getEscalatedId());
        }
        dto.setOrderIdList(ids);
        Page<ReportDetailsVO> details = getDetails(dto, true);
        return CommonUtil.getMpPage(details);

    }

    @Override
    public List<ComplaintEscalationStatisticsReportVO> queryComplaintEscalationStatisticsReport(ReportBaseDTO dto) {
        CommonUtil.validTime(dto.getStartTime(), dto.getEndTime(), 31);
        List<OpDictInfo> orderTypeDictList = CommonUtils.getDict(FORM_DICT_NAME, "");
        Long fundOrderTypeId = orderTypeDictList.stream().filter(item -> item.getContent().equals("资方工单")).findFirst().get().getId();
        Long monitorTypeId = orderTypeDictList.stream().filter(item -> item.getContent().equals("监管工单")).findFirst().get().getId();
        List<ComplaintEscalationStatisticsReportVO> reportList = woReportMapper.selectComplaintOrderData(dto, fundOrderTypeId, monitorTypeId);

        List<Long> normalOrderIds = orderTypeDictList
                .stream().filter(item -> NORMAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());

        List<Long> specialOrderIds = orderTypeDictList
                .stream().filter(item -> SPECIAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());

        WoReportDTO woReportDTO = new WoReportDTO();
        woReportDTO.setStartTime(dto.getStartTime());
        woReportDTO.setEndTime(dto.getEndTime());
        List<EscalationOrderVo> escalationOrderVoList = woTaskMapper.selectEscalationOrder(woReportDTO, normalOrderIds, specialOrderIds);
        for (ComplaintEscalationStatisticsReportVO reportVO : reportList) {
            reportVO.setTotalComplaintCount(reportVO.getFundComplaintCount() + reportVO.getMonitorComplaintCount());
            if (reportVO.getTime().equals("周期")) {
                reportVO.setEscalationUserCount(escalationOrderVoList.size());
            } else {
                List<EscalationOrderVo> filterList = escalationOrderVoList.stream()
                        .filter(item -> DateUtil.dateToString(item.getEscalatedDate(), DateUtil.TimeFormatter.YYYY_MM_DD).equals(reportVO.getTime()))
                        .collect(Collectors.toList());
                reportVO.setEscalationUserCount(filterList.size());
            }
            
            reportVO.setNoContactUserCount(reportVO.getComplaintUserCount() - reportVO.getEscalationUserCount());
            reportVO.setEscalationRate(CommonUtil.calcPercentage(reportVO.getEscalationUserCount(), reportVO.getComplaintUserCount()));

        }
        // 计算合计行
        ComplaintEscalationStatisticsReportVO summary = reportList.stream().reduce(new ComplaintEscalationStatisticsReportVO(0, 0, 0, 0, 0, 0)
                , ComplaintEscalationStatisticsReportVO::add
                , ComplaintEscalationStatisticsReportVO::add);
        summary.setTime("合计");
        summary.setEscalationRate(CommonUtil.calcPercentage(summary.getEscalationUserCount(), summary.getComplaintUserCount()));
        
        reportList.add(summary);

        return reportList;
    }

    @Override
    public List<ComplaintEscalationReasonReportVO> queryComplaintEscalationReasonReport(WoReportDTO dto) {
        CommonUtil.validTime(dto.getStartTime(), dto.getEndTime(), 31);
        List<OpDictInfo> orderTypeDictList = CommonUtils.getDict(FORM_DICT_NAME, "");
        List<Long> normalOrderIds = orderTypeDictList
                .stream().filter(item -> NORMAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());

        List<Long> specialOrderIds = orderTypeDictList
                .stream().filter(item -> SPECIAL_ORDER_TYPE.contains(item.getContent())).map(OpDictInfo::getId).collect(Collectors.toList());

        List<EscalationOrderVo> escalationOrderVoList = woTaskMapper.selectEscalationOrder(dto, normalOrderIds, specialOrderIds);
        if (CollectionUtils.isEmpty(escalationOrderVoList)) {
            return Collections.emptyList();
        }

        List<ComplaintEscalationReasonReportVO> reportList = new ArrayList<>();
        Map<String, List<EscalationOrderVo>> threeTypeMap = escalationOrderVoList.stream().collect(Collectors.groupingBy(EscalationOrderVo::getOrdinaryOrderThreeType));
        
        threeTypeMap.forEach((threeType, escalationOrderVos) -> {
            ComplaintEscalationReasonReportVO reportVO = new ComplaintEscalationReasonReportVO(threeType, 0, 0, 0, 0, 0, 0);
            for (EscalationOrderVo escalationOrderVo : escalationOrderVos) {
                long daysBetween = DateUtil.getDaysBetween(escalationOrderVo.getOrdinaryDate(), escalationOrderVo.getEscalatedDate());
                if (daysBetween <= 0) {
                    reportVO.addZeroDayCount();
                } else if (daysBetween <= 3) {
                    reportVO.addThreeDayCount();
                } else if (daysBetween <= 7) {
                    reportVO.addSevenDayCount();
                } else if (daysBetween <= 10) {
                    reportVO.addTenDayCount();
                } else if (daysBetween <= 20) {
                    reportVO.addTwentyDayCount();
                } else {
                    reportVO.addThirtyDayCount();
                }
            }
            reportList.add(reportVO);
        });
        
        // 计算合计行
        ComplaintEscalationReasonReportVO summary = reportList.stream().reduce(new ComplaintEscalationReasonReportVO("合计", 0, 0, 0, 0, 0, 0)
                , ComplaintEscalationReasonReportVO::add
                , ComplaintEscalationReasonReportVO::add);
        reportList.add(summary);
        return reportList;
    }

    private void checkTimeAndConvertList(WoReportDTO dto) {
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());
        if (StringUtils.isNotBlank(dto.getOrderTypes())) {
            dto.setOrderTypeList(Arrays.asList(dto.getOrderTypes().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getOrderStates())) {
            dto.setOrderStateList(Arrays.asList(dto.getOrderStates().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getGroupCodes())) {
            dto.setGroupCodeList(Arrays.asList(dto.getGroupCodes().split(",")));
        }
    }

    private void setDtoPageParam(WoReportDTO reportDTO) {
        reportDTO.setCurrentPage(1);
        reportDTO.setRowsPerPage(1000000);
    }

    /**
     * 查询工单统计报表可以查询的对应字典配置组别列表
     */
    private List<String> getWorkOrderReportGroupList(ReportEfficiencySummaryDTO summaryDTO) {
        if (StringUtils.isBlank(summaryDTO.getGroupCode())) {
            List<OpDictInfo> groups = opDictInfoMapper.selectList(Wrappers.lambdaQuery(OpDictInfo.class)
                    .eq(OpDictInfo::getCategory, "workOrderReportGroup"));
            return groups.stream().map(OpDictInfo::getType).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    private void buildOutboundEfficiencySummaryVOList(OutboundEfficiencySummaryDTO dto,
                                                      List<OutboundEfficiencySummaryVO> voList) {
        // 获取天润报表
        Map<String, StatisticAgentWorkloadDay> map = getStatisticAgentGroupByCno(dto);
        // 组装报表字段
        for (OutboundEfficiencySummaryVO vo : voList) {
            StatisticAgentWorkloadDay agent = map.get(vo.getIdNo());
            if (Objects.isNull(agent)) {
                continue;
            }
            vo.setCallCount(Integer.valueOf(agent.getObCallingCount()));
            vo.setConnectedNumber(Integer.valueOf(agent.getPreviewObAnsweredCount()));
            vo.setConnectedRate(CommonUtil.calcPercentage(vo.getConnectedNumber(), vo.getCallCount()));
            vo.setLoginTime(agent.getLoginTime());
            vo.setWrapUpDuration(agent.getObWrapupTime());
            vo.setPauseTime(agent.getPauseTime());
            vo.setRestTime(agent.getRestTime());
            vo.setIdleTime(agent.getIdleTime());
            // 呼入总时长
            int incomingCall = DateUtils.getSeconds(agent.getObBridgeTime());
            // 外呼总时长
            int outbound = DateUtils.getSeconds(agent.getIbTotalTime());
            // 总通话时长
            int totalDuration = incomingCall + outbound;
            int avgCallDuration = 0;
            if (vo.getConnectedNumber() != 0) {
                avgCallDuration = CommonUtil.divide(totalDuration, vo.getConnectedNumber());
            }
            vo.setAvgCallDuration(DateUtils.secondsToTime(avgCallDuration));
            int wrapUpDuration = DateUtils.getSeconds(vo.getWrapUpDuration());
            int loginTime = DateUtils.getSeconds(vo.getLoginTime());
            vo.setWorkingHourUtilization(CommonUtil.calcPercentage((wrapUpDuration + totalDuration), loginTime));
            vo.setCallUtilization(CommonUtil.calcPercentage(totalDuration, loginTime));
        }
    }

    private List<StatisticAgentWorkloadDay> sendTr(WoReportDTO dto, Integer statisticMethod, Integer timeRangeType) {
        String startTime = dto.getStartTime().substring(0, 10);
        String endTime = dto.getEndTime().substring(0, 10);
        return trUtil.reportWorkLoad(startTime, endTime, statisticMethod, timeRangeType);
    }

    /**
     * 获取天润报表
     *
     * @return
     */
    private Map<String, StatisticAgentWorkloadDay> getStatisticAgentGroupByCno(WoReportDTO dto) {
        List<StatisticAgentWorkloadDay> list = sendTr(dto, 2, 4);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(StatisticAgentWorkloadDay::getCno, t -> t));
        }
        return new HashMap<>();
    }

    private void setCategories(ReportSummaryVO summaryVO, List<ReportSummaryDetailsVO> detailsVOList, ReportSummaryVO sumVO) {
        Integer total = summaryVO.getTotalNumber();

        // 计算百分比
        for (ReportSummaryDetailsVO vo : detailsVOList) {
            vo.setRate(CommonUtil.calcPercentage(vo.getNumber(), total));
        }
        summaryVO.setFirst(detailsVOList.get(0));
        summaryVO.setSecond(detailsVOList.get(1));
        summaryVO.setThird(detailsVOList.get(2));
        summaryVO.setFourth(detailsVOList.get(3));
        if (detailsVOList.size() >= 5) {
            summaryVO.setFifth(detailsVOList.get(4));
        }

        // 计算合计行
        if (Objects.nonNull(sumVO)) {
            sumVO.setType("合计");
            if (Objects.nonNull(sumVO.getTotalNumber())) {
                sumVO.setTotalNumber(sumVO.getTotalNumber() + total);
            } else {
                sumVO.setTotalNumber(total);
            }
            if (Objects.nonNull(sumVO.getClosedNumber())) {
                sumVO.setClosedNumber(sumVO.getClosedNumber() + summaryVO.getClosedNumber());
            } else {
                sumVO.setClosedNumber(summaryVO.getClosedNumber());
            }

            sumVO.setClosedRate(CommonUtil.calcPercentage(sumVO.getClosedNumber(), sumVO.getTotalNumber()));

            if (Objects.nonNull(sumVO.getFirst())) {
                ReportSummaryDetailsVO item = detailsVOList.get(0);
                ReportSummaryDetailsVO sum = sumVO.getFirst();
                sum.setNumber(sum.getNumber() + item.getNumber());
                sum.setClosedNumber(sum.getClosedNumber() + item.getClosedNumber());
                sum.setRate(CommonUtil.calcPercentage(sum.getNumber(), sumVO.getTotalNumber()));
                sum.setClosedRate(CommonUtil.calcPercentage(sum.getClosedNumber(), sum.getNumber()));
            } else {
                ReportSummaryDetailsVO vo = new ReportSummaryDetailsVO();
                BeanUtils.copyProperties(detailsVOList.get(0), vo);
                sumVO.setFirst(vo);
            }


            if (Objects.nonNull(sumVO.getSecond())) {
                ReportSummaryDetailsVO item = detailsVOList.get(1);
                ReportSummaryDetailsVO sum = sumVO.getSecond();
                sum.setNumber(sum.getNumber() + item.getNumber());
                sum.setClosedNumber(sum.getClosedNumber() + item.getClosedNumber());
                sum.setRate(CommonUtil.calcPercentage(sum.getNumber(), sumVO.getTotalNumber()));
                sum.setClosedRate(CommonUtil.calcPercentage(sum.getClosedNumber(), sum.getNumber()));

            } else {
                ReportSummaryDetailsVO vo = new ReportSummaryDetailsVO();
                BeanUtils.copyProperties(detailsVOList.get(1), vo);
                sumVO.setSecond(vo);
            }

            if (Objects.nonNull(sumVO.getThird())) {
                ReportSummaryDetailsVO item = detailsVOList.get(2);
                ReportSummaryDetailsVO sum = sumVO.getThird();
                sum.setNumber(sum.getNumber() + item.getNumber());
                sum.setClosedNumber(sum.getClosedNumber() + item.getClosedNumber());
                sum.setRate(CommonUtil.calcPercentage(sum.getNumber(), sumVO.getTotalNumber()));
                sum.setClosedRate(CommonUtil.calcPercentage(sum.getClosedNumber(), sum.getNumber()));

            } else {
                ReportSummaryDetailsVO vo = new ReportSummaryDetailsVO();
                BeanUtils.copyProperties(detailsVOList.get(2), vo);
                sumVO.setThird(vo);
            }

            if (Objects.nonNull(sumVO.getFourth())) {
                ReportSummaryDetailsVO item = detailsVOList.get(3);
                ReportSummaryDetailsVO sum = sumVO.getFourth();
                sum.setNumber(sum.getNumber() + item.getNumber());
                sum.setClosedNumber(sum.getClosedNumber() + item.getClosedNumber());
                sum.setRate(CommonUtil.calcPercentage(sum.getNumber(), sumVO.getTotalNumber()));
                sum.setClosedRate(CommonUtil.calcPercentage(sum.getClosedNumber(), sum.getNumber()));

            } else {
                ReportSummaryDetailsVO vo = new ReportSummaryDetailsVO();
                BeanUtils.copyProperties(detailsVOList.get(3), vo);
                sumVO.setFourth(vo);
            }
        }
    }


    private List<ReportEfficiencySummaryVO> buildEfficiencySummaryVO(List<ReportEfficiencySummaryVO> list,
                                                                     String type, List<ReportEfficiencySummaryVO> summaryVOList) {
        ReportEfficiencySummaryVO summaryVO = new ReportEfficiencySummaryVO();
        summaryVO.setType(type + "合计");
        initZero(summaryVO);

        if (CollectionUtils.isNotEmpty(list)) {
            int totalCreateNum = 0;
            int totalReturnNum = 0;
            int totalAssignNum = 0;
            int totalCloseNum = 0;
            int totalResolvedNum = 0;
            int totalUnresolvedNum = 0;
            int totalNotConnectedNum = 0;
            int totalReminderNum = 0;
            for (ReportEfficiencySummaryVO vo : list) {
                vo.setErrorRate(CommonUtil.calcPercentage(vo.getReturnNum(), vo.getCreateNum()));
                vo.setCloseRate(CommonUtil.calcPercentage(vo.getCloseNum(), vo.getAssignNum()));
                vo.setResolvedRate(CommonUtil.calcPercentage(vo.getResolvedNum(), vo.getAssignNum()));
                vo.setUnresolvedRate(CommonUtil.calcPercentage(vo.getUnresolvedNum(), vo.getAssignNum()));
                vo.setNotConnectedRate(CommonUtil.calcPercentage(vo.getNotConnectedNum(), vo.getAssignNum()));
                vo.setReminderRate(CommonUtil.calcPercentage(vo.getReminderNum(), vo.getAssignNum()));
                totalCreateNum += vo.getCreateNum();
                totalReturnNum += vo.getReturnNum();
                totalAssignNum += vo.getAssignNum();
                totalCloseNum += vo.getCloseNum();
                totalResolvedNum += vo.getResolvedNum();
                totalUnresolvedNum += vo.getUnresolvedNum();
                totalNotConnectedNum += vo.getNotConnectedNum();
                totalReminderNum += vo.getReminderNum();
            }
            // 添加合计
            summaryVO.setType(type + "合计");
            summaryVO.setCreateNum(totalCreateNum);
            summaryVO.setReturnNum(totalReturnNum);
            summaryVO.setAssignNum(totalAssignNum);
            summaryVO.setCloseNum(totalCloseNum);
            summaryVO.setResolvedNum(totalResolvedNum);
            summaryVO.setUnresolvedNum(totalUnresolvedNum);
            summaryVO.setNotConnectedNum(totalNotConnectedNum);
            summaryVO.setReminderNum(totalReminderNum);
            if (Objects.isNull(summaryVO.getErrorRate())) {
                summaryVO.setErrorRate(CommonUtil.calcPercentage(summaryVO.getReturnNum(), summaryVO.getCreateNum()));
            }
            if (Objects.isNull(summaryVO.getCloseRate())) {
                summaryVO.setCloseRate(CommonUtil.calcPercentage(summaryVO.getCloseNum(), summaryVO.getAssignNum()));
            }
            if (Objects.isNull(summaryVO.getResolvedRate())) {
                summaryVO
                        .setResolvedRate(CommonUtil.calcPercentage(summaryVO.getResolvedNum(), summaryVO.getAssignNum()));
            }
            if (Objects.isNull(summaryVO.getUnresolvedRate())) {
                summaryVO.setUnresolvedRate(CommonUtil.calcPercentage(summaryVO.getUnresolvedNum(),
                        summaryVO.getAssignNum()));
            }
            if (Objects.isNull(summaryVO.getNotConnectedRate())) {
                summaryVO.setNotConnectedRate(CommonUtil.calcPercentage(summaryVO.getNotConnectedNum(),
                        summaryVO.getAssignNum()));
            }
            if (Objects.isNull(summaryVO.getReminderRate())) {
                summaryVO.setReminderRate(CommonUtil
                        .calcPercentage(summaryVO.getReminderNum(), summaryVO.getAssignNum()));
            }
        }
        summaryVOList.add(summaryVO);
        return list;
    }

    /**
     * 初始化为0
     *
     * @param summaryVO
     */
    private void initZero(ReportEfficiencySummaryVO summaryVO) {
        summaryVO.setCreateNum(0);
        summaryVO.setReturnNum(0);
        summaryVO.setAssignNum(0);
        summaryVO.setCloseNum(0);
        summaryVO.setResolvedNum(0);
        summaryVO.setUnresolvedNum(0);
        summaryVO.setNotConnectedNum(0);
        summaryVO.setReminderNum(0);
    }
}
