package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.authority.service.OrgService;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.domain.ContractSendRecord;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.dto.AuditBatchInfoReqDTO;
import com.welab.crm.operate.dto.ContractSendDTO;
import com.welab.crm.operate.dto.ContractSendReqDTO;
import com.welab.crm.operate.dto.message.SmsSendDTO;
import com.welab.crm.operate.enums.ContractSendApproveStateEnum;
import com.welab.crm.operate.mapper.ContractSendRecordMapper;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.service.ContractSendService;
import com.welab.crm.operate.service.ICrmOrgService;
import com.welab.crm.operate.service.MessageValidateService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.ContractSendVO;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ContractSendServiceImpl implements ContractSendService {
	@Resource
	private ContractSendRecordMapper contractSendRecordMapper;
	
	@Resource
	private MessageValidateService messageValidateService;
	
	@Resource
	private InAuthCrmStaffMapper inAuthCrmStaffMapper;
	
	@Resource
	private StaffService staffService;
	
	@Resource
	private ICrmOrgService crmOrgService;
	
	
	private static final String SPECIAL_GROUP = "tsz";
	
	private static final List<String> successStatusList = new ArrayList<>(Arrays.asList("1","3","4"));
	
	@Override
	public Page<ContractSendVO> queryContractSendPage(ContractSendReqDTO reqDTO) {
		Page<ContractSendVO> page = new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize());
		Page<ContractSendVO> voPage = contractSendRecordMapper.queryContractSendPage(page, reqDTO);
		voPage.getRecords().forEach(item -> translateFields(item , reqDTO.getQueryFlag()));
		return voPage;
	}

	private void translateFields(ContractSendVO item, Integer queryFlag) {
		if (queryFlag == 2) {
			if (successStatusList.contains(item.getStatus())) {
				item.setStatus(ContractSendApproveStateEnum.AGREE.getText());
			} else {
				item.setStatus(ContractSendApproveStateEnum.getState(Integer.valueOf(item.getStatus())));
			}
		} else {
			item.setStatus(ContractSendApproveStateEnum.getState(Integer.valueOf(item.getStatus())));
		}
		if ("1".equals(item.getSendType())){
			item.setSendType("查看且下载");
		} else {
			item.setSendType("仅查看");
		}
		
		item.setMobile(SecurityUtil.maskMobile2(item.getMobile()));
		
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void sendContract(ContractSendDTO reqDTO, StaffVO staffVO) {
		if (reqDTO.getSendType() == 2){
			Long contractSendId = insertRecord(reqDTO, ContractSendApproveStateEnum.AGREE.getValue());
			sendSms(reqDTO, contractSendId, staffVO);
		} else if (reqDTO.getSendType() == 1){
			insertRecord(reqDTO, ContractSendApproveStateEnum.WAITING.getValue());
		}

	}

	private void sendSms(ContractSendDTO reqDTO, Long contractSendId, StaffVO staffVO) {
		SmsSendDTO sendDTO = new SmsSendDTO();
		BeanUtils.copyProperties(reqDTO, sendDTO);
		JSONObject params = new JSONObject();
		params.put("contractSendId", String.valueOf(contractSendId));
		sendDTO.setOtherParams(JSON.toJSONString(params));
		messageValidateService.sendMsg(sendDTO,staffVO);
		
	}

	private Long insertRecord(ContractSendDTO reqDTO, Integer status) {
		ContractSendRecord sendRecord = new ContractSendRecord();
		sendRecord.setSendType(reqDTO.getSendType());
		sendRecord.setAppNo(reqDTO.getApplicationId());
		sendRecord.setUuid(reqDTO.getUuid());
		sendRecord.setCustomerName(reqDTO.getName());
		sendRecord.setStatus(status);
		sendRecord.setGmtCreate(new Date());
		sendRecord.setGmtModify(new Date());
		sendRecord.setGroupCode(CommonUtils.getCurrentloggedOrg());
		sendRecord.setGroupName(CommonUtils.getCurrentloggedOrgName());
		sendRecord.setStaffId(CommonUtils.getCurrentloggedStaffId());
		sendRecord.setStaffName(CommonUtils.getCurrentloggedName());
		sendRecord.setPartnerCode(reqDTO.getPartnerCode());
		sendRecord.setPartnerName(reqDTO.getPartnerName());
		sendRecord.setMobile(reqDTO.getMobile());
		sendRecord.setCustomerId(reqDTO.getCustomerId());
		sendRecord.setMessageId(reqDTO.getMessageId());
		
		contractSendRecordMapper.insert(sendRecord);
		
		return sendRecord.getId();
		
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void auditContract(AuditBatchInfoReqDTO reqDTO) {
		List<ContractSendRecord> totalList = contractSendRecordMapper.selectBatchIds(reqDTO.getIds());
		if (totalList.isEmpty()){
			throw new FastRuntimeException("审批记录不存在");
		}
		if (totalList.stream().filter(item -> item.getStatus() == ContractSendApproveStateEnum.WAITING.getValue()).count() != reqDTO.getIds().size()){
			throw new FastRuntimeException("存在已审批的订单");
		}
		reqDTO.getIds().forEach(id -> {
			ContractSendRecord sendRecord = contractSendRecordMapper.selectById(id);
			sendRecord.setStatus(reqDTO.getStatus());
			sendRecord.setGmtModify(new Date());
			contractSendRecordMapper.updateById(sendRecord);
			if (ContractSendApproveStateEnum.AGREE.getValue() == reqDTO.getStatus()){
				ContractSendDTO dto = buildContractSendDTO(sendRecord);
				StaffVO staffVO = buildStaffVO(sendRecord.getStaffId());
				sendSms(dto, id, staffVO);
			} 
		});
		
		
	}

	private StaffVO buildStaffVO(Long staffId) {
		InAuthCrmStaff staffInfo = inAuthCrmStaffMapper.selectById(staffId);
		return staffService.getStaffByMobile(staffInfo.getStaffMobile());
	}

	private ContractSendDTO buildContractSendDTO(ContractSendRecord sendRecord) {
		ContractSendDTO dto = new ContractSendDTO();
		dto.setApplicationId(sendRecord.getAppNo());
		dto.setCustomerId(sendRecord.getCustomerId());
		dto.setMessageId(sendRecord.getMessageId());
		dto.setMobile(sendRecord.getMobile());
		dto.setName(sendRecord.getCustomerName());
		dto.setSendTime(DateUtil.dateToString(new Date()));
		return dto;
	}

	@Override
	public List<ContractSendVO> queryContractSendList(ContractSendReqDTO reqDTO) {
		List<ContractSendVO> list = contractSendRecordMapper.queryContractSendList(reqDTO);
		list.forEach(item -> translateFields(item, reqDTO.getQueryFlag()));
		return list;
	}

	@Override
	public JSONObject querySendType() {
		String currentloggedOrg = CommonUtils.getCurrentloggedOrg();

		List<String> managerOrgs = crmOrgService.getManagerOrgs(SPECIAL_GROUP);

		JSONObject sendType = new JSONObject();
		sendType.put("2", "仅查看");
		if (managerOrgs.contains(currentloggedOrg)){
			sendType.put("1", "查看且下载");
		}
		return sendType;
	}
}
