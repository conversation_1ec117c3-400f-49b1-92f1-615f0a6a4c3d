package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.UserBlackInfoVO;
import com.welab.crm.operate.domain.BlackProductionAttachment;
import com.welab.crm.operate.domain.BlackProductionUserInfo;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.SingleApprovalDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionAddReqDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionQueryReqDTO;
import com.welab.crm.operate.enums.BlackProductionSourceEnum;
import com.welab.crm.operate.enums.BlackProductionUserTypeEnum;
import com.welab.crm.operate.mapper.BlackProductionAttachmentMapper;
import com.welab.crm.operate.mapper.BlackProductionUserInfoMapper;
import com.welab.crm.operate.service.BlackProductionService;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportFailVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportTempVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BlackProductionServiceImpl implements BlackProductionService {

    @Resource
    private BlackProductionUserInfoMapper blackProductionUserInfoMapper;

    @Resource
    private BlackProductionAttachmentMapper blackProductionAttachmentMapper;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private NoticeMsgService noticeMsgService;

    @Override
    public void addBlackProduction(BlackProductionAddReqDTO reqDTO) {
        BlackProductionUserInfo blackProductionUserInfo = new BlackProductionUserInfo();
        BeanUtils.copyProperties(reqDTO, blackProductionUserInfo);
        blackProductionUserInfo.setUserMobile(AesUtils.decrypt(reqDTO.getUserMobile()));
        blackProductionUserInfo.setBlackProductionMobile(AesUtils.decrypt(reqDTO.getBlackProductionMobile()));
        blackProductionUserInfo.setApplyStaffId(CommonUtils.getCurrentloggedStaffId());
        blackProductionUserInfo.setApplyTime(new Date());
        blackProductionUserInfo.setSource(BlackProductionSourceEnum.CS.getCode());
        if (StringUtils.isNotBlank(reqDTO.getUuid())) {
            UserBlackInfoVO userInfoVO = userInfoService.getUserIdByUuid(reqDTO.getUuid());
            if (Objects.nonNull(userInfoVO)) {
                blackProductionUserInfo.setIdNo(userInfoVO.getCnid());
            }
        }
        blackProductionUserInfo.setApprovalStatus(CallOutBlackListServiceImpl.ApprovalStatusEnum.APPLY.getCode());
        blackProductionUserInfoMapper.insert(blackProductionUserInfo);

        if (CollectionUtils.isNotEmpty(reqDTO.getFileNameList())) {
            for (String fileName : reqDTO.getFileNameList()) {
                BlackProductionAttachment attachment = new BlackProductionAttachment();
                attachment.setUserInfoId(blackProductionUserInfo.getId());
                attachment.setFileName(fileName);
                blackProductionAttachmentMapper.insert(attachment);
            }
        }
        //发送待审批通知
        /*List<OpDictInfo> dictList = CommonUtils.getDict("black-production", null);
        if (CollectionUtils.isEmpty(dictList)) {
            return;
        }
        Map<String, List<String>> approverMap = new HashMap<>();
        for (OpDictInfo info : dictList) {
            approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> firstApprover = approverMap.get("1");
        NoticeMsgReqDTO req = new NoticeMsgReqDTO();
        req.setTitle("黑产信息审批通知");
        req.setContent("【黑产信息审批】:黑产信息审批" + "\t" + "【数量】:" + 1);
        noticeMsgService.publishBannerNotice(firstApprover, req);*/
    }

    @Override
    public Page<BlackProductionVO> queryBlackProductionUserInfoPage(BlackProductionQueryReqDTO reqDTO) {
        Page<BlackProductionVO> blackVoPage = blackProductionUserInfoMapper
            .queryBlackProductionPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
        blackVoPage.getRecords().forEach(this::buildBlackProductionVO);
        return blackVoPage;
    }

    @Override
    public List<BlackProductionImportFailVO> importBlackProductionVo(List<BlackProductionImportTempVO> importList, Long staffId) {
        List<BlackProductionUserInfo> insertList = new ArrayList<>();
        List<BlackProductionImportFailVO> failList = new ArrayList<>();
        for (BlackProductionImportTempVO importVo : importList) {
            BlackProductionUserInfo blackProductionUserInfo = new BlackProductionUserInfo();
            blackProductionUserInfo.setBlackProductionMobile(importVo.getBlackProductionMobile());
            blackProductionUserInfo.setBlackProductionEmail(importVo.getBlackProductionEmail());
            blackProductionUserInfo.setUserType(BlackProductionUserTypeEnum.getCodeByDesc(importVo.getUserType()));
            blackProductionUserInfo.setApplyStaffId(staffId);
            blackProductionUserInfo.setApplyTime(new Date());
            blackProductionUserInfo.setApprovalStaffId(blackProductionUserInfo.getApplyStaffId());
            blackProductionUserInfo.setApprovalTime(new Date());
            blackProductionUserInfo
                .setApprovalStatus(CallOutBlackListServiceImpl.ApprovalStatusEnum.APPROVED.getCode());
            if (StringUtils.isNotBlank(importVo.getUuid())) {
                try {
                    UserBlackInfoVO userInfoVO = userInfoService.getUserIdByUuid(importVo.getUuid());
                    blackProductionUserInfo.setIdNo(userInfoVO.getCnid());
                    blackProductionUserInfo.setName(userInfoVO.getName());
                    blackProductionUserInfo.setUuid(userInfoVO.getUuid());
                    blackProductionUserInfo.setUserMobile(userInfoVO.getMobile());
                    blackProductionUserInfo.setSource(BlackProductionSourceEnum.CS.getCode());
                } catch (Exception e) {
                    log.info("importBlackProductionVo exception:{}", e.getMessage());
                    BlackProductionImportFailVO fail = new BlackProductionImportFailVO();
                    fail.setUuid(importVo.getUuid());
                    failList.add(fail);
                    continue;
                }
            } else {
                blackProductionUserInfo.setSource(BlackProductionSourceEnum.AIF.getCode());
            }

            insertList.add(blackProductionUserInfo);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            blackProductionUserInfoMapper.insertBatchSomeColumn(insertList);
        }
        //没有导入的uuid返回list
        return failList;
    }

    @Override
    public void approvalBlackProductionRecord(SingleApprovalDTO dto) {
        List<OpDictInfo> dictList = CommonUtils.getDict("black-production", null);
        if (CollectionUtils.isEmpty(dictList)) {
            return;
        }
        Map<String, List<String>> approverMap = new HashMap<>();
        for (OpDictInfo info : dictList) {
            approverMap.put(info.getType(), Arrays.asList(info.getContent().split(",")));
        }
        List<String> firstApprover = approverMap.get("1");
        if (!firstApprover.contains(CommonUtils.getCurrentlogged())) {
            throw new FastRuntimeException("无审批权限");
        }
        BlackProductionUserInfo bpui = blackProductionUserInfoMapper.selectById(dto.getId());
        bpui.setApprovalStatus(dto.getApprovalStatus());
        bpui.setApprovalStaffId(CommonUtils.getCurrentloggedStaffId());
        bpui.setApprovalTime(new Date());
        blackProductionUserInfoMapper.updateById(bpui);
    }

    private void buildBlackProductionVO(BlackProductionVO blackProductionVO) {
        if (StringUtils.isNotBlank(blackProductionVO.getAttachmentNames())) {
            blackProductionVO.setAttachmentList(Arrays.asList(blackProductionVO.getAttachmentNames().split(",")));
        }
        blackProductionVO.setBlackProductionMobile(SecurityUtil.maskMobile2(blackProductionVO.getBlackProductionMobile()));
        blackProductionVO.setUserMobile(SecurityUtil.maskMobile2(blackProductionVO.getUserMobile()));
        blackProductionVO.setIdNo(SecurityUtil.maskCnid2(blackProductionVO.getIdNo()));
    }
    
    
}
