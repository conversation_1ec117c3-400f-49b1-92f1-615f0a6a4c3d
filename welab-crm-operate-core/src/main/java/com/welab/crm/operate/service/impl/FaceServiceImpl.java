package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.authority.service.UserService;
import com.welab.authority.vo.UserVO;
import com.welab.common.response.Response;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.domain.CsBasePhoto;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.mapper.CsBasePhotoMapper;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.model.AiRetModel;
import com.welab.crm.operate.service.FaceService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.exception.FastRuntimeException;
import com.welab.privacy.util.http.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class FaceServiceImpl implements FaceService {
	@Resource
	private InAuthCrmStaffMapper inAuthCrmStaffMapper;

	@Resource
	private UserService authorityUserService;

	@Resource
	private IUploadService iUploadService;

	@Resource
	private CsBasePhotoMapper csBasePhotoMapper;


	@Value("${snap.check.url}")
	private String snapCheckUrl;

	@Value("${featureidentify.url}")
	private String featureidentifyUrl;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void uploadBasePhoto(byte[] bs, Long staffId, String xUserToken) {


		InAuthCrmStaff staff = inAuthCrmStaffMapper.selectById(staffId);
		if (Objects.isNull(staff)) {
			throw new FastRuntimeException("员工不存在");
		}

		// 查询 x-user-id
		UserVO authorityUser = authorityUserService.getUserByPhone(staff.getStaffMobile());
		if (Objects.isNull(authorityUser)) {
			throw new FastRuntimeException("用户在金乌系统不存在");
		}

		// 上传底照到 OSS
		String fileName = saveBasePhotoToOss(bs, staffId);

		// 校验底照
		checkBasePhoto(fileName);

		log.info("uploadBasePhoto, staffId:{}, xUserId:{}", staffId, authorityUser.getId());
		// 推送到AI作为人脸登陆凭证
		pushBasePhotoToAi(String.valueOf(authorityUser.getId()), xUserToken, bs);


		// 成功则保存到客服系统
		saveBasePhotoToCs(staff, fileName, CommonUtils.getCurrentloggedStaffId());
	}

	private String saveBasePhotoToOss(byte[] bs, Long staffId) {
		String fileName = staffId + "_" + System.currentTimeMillis() + "_base_photo.jpg";
		Response<String> res = iUploadService.uploadFile(bs, fileName);
		if (Response.isSuccess(res)) {
			return res.getResult();
		} else {
			throw new FastRuntimeException("上传底照到OSS失败");
		}
	}

	private void saveBasePhotoToCs(InAuthCrmStaff staff, String filaName, Long uploadStaffId) {

		// 查询是否有历史底照
		CsBasePhoto historyBasePhoto = csBasePhotoMapper.selectOne(Wrappers.lambdaQuery(CsBasePhoto.class)
				.eq(CsBasePhoto::getStaffId, staff.getId()).eq(CsBasePhoto::getIsDefault, 1));
		// 有历史底照则更新 IsDefault 为 0
		if (Objects.nonNull(historyBasePhoto)) {
			CsBasePhoto csBasePhoto = historyBasePhoto.setIsDefault(false);
			csBasePhotoMapper.updateById(csBasePhoto);
		}


		// 插入底照记录
		CsBasePhoto basePhoto = new CsBasePhoto(staff.getId(), filaName, true, uploadStaffId);
		csBasePhotoMapper.insert(basePhoto);
		
		// 更新员工表
		staff.setIsUploadBasePhoto(true);
		inAuthCrmStaffMapper.updateById(staff);


	}


	private void pushBasePhotoToAi(String xUserId, String xUserToken, byte[] bs) {
		try {
			String photoBase64Str = new String(Base64.getEncoder().encode(bs));
			JSONObject requestBody = new JSONObject();
			requestBody.put("file", photoBase64Str);
			requestBody.put("allowChange", 1);


			String resStr = HttpClientUtil
					.post(HttpConfig.custom().url(featureidentifyUrl + "/featureidentify-web-server/face/faceRegister")
							.headers(HttpHeader.custom().contentType("application/json").other("x-user-id", xUserId)
									.other("x-user-token", xUserToken).build())
							.json(requestBody.toJSONString()));
			log.info("pushBasePhotoToAi http res:{},x-user-id:{}", resStr, xUserId);
			if (StringUtils.isNotBlank(resStr)) {
				Response response = JSON.parseObject(resStr, Response.class);
				if (!Response.isSuccess(response)) {
					throw new FastRuntimeException(response.getMessage());
				}
			} else {
				throw new FastRuntimeException("上传登陆底照失败");
			}

		} catch (Exception e) {
			log.error("pushBasePhotoToAi, 上传人脸验证照片失败，x-user-id:{}", xUserId, e);
			throw new FastRuntimeException(e.getMessage());
		}
	}


	private void checkBasePhoto(String basePhotoName) {
		String staffId = CommonUtils.getCurrentlogged();
		// 获取底照oss链接
		Map<String, Object> map = iUploadService.getUploadFile(Arrays.asList(basePhotoName));
		String basePhotoOssUrl = map.get(basePhotoName).toString();
		JSONObject req = new JSONObject();
		req.put("photo_url", basePhotoOssUrl);
		String resStr = HttpClients.create().setUrl(snapCheckUrl + "/api/photo/register_check")
				.setRequestBody(req.toJSONString()).doPost();
		log.info("staffId:{},checkBasePhoto res:{}", staffId, resStr);
		AiRetModel resModel = null;
		try {
			resModel = JSON.parseObject(resStr, AiRetModel.class);
		} catch (Exception e) {
			log.warn("checkBasePhoto 解析返回结果失败,staffId:{}res:{}", staffId, resStr, e);
		}
		if (Objects.isNull(resModel)) {
			log.error("checkBasePhoto 解析返回结果失败,staffId:{}res:{}", staffId, resStr);
			throw new FastRuntimeException("底照校验不成功");
		}
		Integer ret = resModel.getRet();
		if (ret != 0) {
			log.error("checkBasePhoto 人脸校验不成功：{}", resStr);
			throw new FastRuntimeException("底照校验不成功:" + resModel.getMsg());
		}

	}


	@Override
	public String queryBasePhoto(Long staffId) {
		CsBasePhoto basePhoto = csBasePhotoMapper.selectOne(Wrappers.lambdaQuery(CsBasePhoto.class)
				.eq(CsBasePhoto::getStaffId, staffId).eq(CsBasePhoto::getIsDefault, 1));
		if (Objects.isNull(basePhoto)) {
			throw new FastRuntimeException("查询不到底照");
		}
		Map<String, Object> uploadFile = iUploadService.getUploadFile(Arrays.asList(basePhoto.getFileName()));
		if (Objects.nonNull(uploadFile) && !uploadFile.isEmpty()) {
			return uploadFile.get(basePhoto.getFileName()).toString();
		}
		return "";
	}
}
