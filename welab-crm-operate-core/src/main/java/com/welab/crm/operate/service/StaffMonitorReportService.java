package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.monitor.StaffMonitorDTO;
import com.welab.crm.operate.vo.monitor.StaffMonitorReportVO;

import java.util.List;

/**
 * 用户监控报表
 * <AUTHOR>
 */
public interface StaffMonitorReportService {


	/**
	 * 监控平台用户报表分页查询
	 * @return
	 */
	Page<StaffMonitorReportVO> queryMonitorReportPage(StaffMonitorDTO dto);


	/**
	 * 查询监控报表不分页
	 * @param dto
	 * @return
	 */
	List<StaffMonitorReportVO> queryMonitorReportList(StaffMonitorDTO dto);
}
