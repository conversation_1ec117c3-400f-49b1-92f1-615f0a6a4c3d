package com.welab.crm.operate.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.transferReport.TransferReportDetailDTO;
import com.welab.crm.operate.dto.transferReport.TransferReportRangeDTO;
import com.welab.crm.operate.vo.transferReport.MonthTransferBaseVO;
import com.welab.crm.operate.vo.transferReport.RangeTransferReportVO;
import com.welab.crm.operate.vo.transferReport.TransferReportDetailVO;
import com.welab.crm.operate.vo.woReport.*;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.DataLoanApplication;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.report.CentralMonitoringDTO;
import com.welab.crm.operate.dto.report.OutboundEfficiencySummaryDTO;
import com.welab.crm.operate.dto.report.ReportAssignmentSummaryDTO;
import com.welab.crm.operate.dto.report.ReportEfficiencySummaryDTO;
import com.welab.crm.operate.dto.report.ReportSummaryTypeDTO;
import com.welab.crm.operate.dto.report.WoReportDTO;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 工单报表 Mapper 接口
 * @date 2022/1/4
 */
public interface WoReportMapper extends BaseMapper {

    /**
     * 查询各“工单三类”数量
     *
     * @param dto
     * @return
     */
    List<ReportSummaryTypeVO> selectSummaryThirdType(ReportSummaryTypeDTO dto);

    /**
     * 查询工单统计报表投诉类子分类
     *
     * @param dto
     * @param orderTypeList
     * @return
     */
    List<ReportSummaryDetailsVO> selectSummaryDetailsComplaint(@Param("dto") WoReportDTO dto,@Param("list") List<OpDictInfo> orderTypeList);

    /**
     * 查询工单总数，结案数和结案率
     *
     * @param dto
     * @return
     */
    ReportSummaryVO selectTotalNumber(WoReportDTO dto);

    /**
     * 查询4种工单类型工单数量，结案数和结案率
     *
     * @param dto
     * @param orderTypeList
     * @return
     */
    List<ReportSummaryVO> selectNumber4Type(@Param("dto") WoReportDTO dto,@Param("list") List<OpDictInfo> orderTypeList);

    /**
     * 查询工单统计报表产品类子分类
     *
     * @param dto
     * @param type 工单类型
     * @return
     */
    List<ReportSummaryDetailsVO> selectSummaryDetailsProduct(@Param("cond") WoReportDTO dto,
        @Param("type") String type);

    /**
     * 查询工单明细报表总数
     * @param dto
     * @return
     */
    int selectDetailsCount(@Param("cond") WoReportDTO dto);

    /**
     * 分页查询工单表id
     * @param dto
     * @return
     */
    List<DataLoanApplication> selectWoIdsPage(@Param("cond") WoReportDTO dto);

    /**
     * 根据工单表id查询工单明细，不包括贷款信息
     */
    List<ReportDetailsVO> selectDetails(@Param("idList") List<Long> idList,  @Param("cond") WoReportDTO dto);

    /**
     * 查询分单统计
     *
     * @param dto
     * @return
     */
    List<ReportAssignmentSummaryVO> selectAssignmentSummary(ReportAssignmentSummaryDTO dto);

    /**
     * 查询分单明细
     *
     * @param dto
     * @return
     */
    List<ReportAssignmentDetailsVO> selectAssignmentDetails(WoReportDTO dto);

    /**
     * 查询外呼效能报表
     *
     * @param dto
     * @param groups 默认查询组别
     * @return
     */
    List<OutboundEfficiencySummaryVO> selectOutboundEfficiencySummary(@Param("cond") OutboundEfficiencySummaryDTO dto,
        @Param("list") List<String> groups);

    /**
     * 查询工单效能报表
     *
     * @param dto
     * @param type 工单类型
     * @return
     */
    List<ReportEfficiencySummaryVO> selectEfficiencySummary(@Param("cond") ReportEfficiencySummaryDTO dto,
        @Param("type") String type, @Param("list") List<String> groups);

    /**
     * 查询中央监控
     */
    List<CentralMonitoringVO> selectCentralMonitoring(@Param("cond") CentralMonitoringDTO dto);

    /**
     * 查询债转投诉统计报表-周期
     * @param objectPage 分页参数
     * @param dto 查询参数
     * @return
     */
    Page<RangeTransferReportVO> selectRangeTransferReportPage(Page<Object> objectPage, @Param("dto") TransferReportRangeDTO dto);

    /**
     * 查询根据债转公司统计数据
     * @param starTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<RangeTransferReportVO> queryTotalGroupByTransferCompany(@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询债转投诉统计报表-周期 返回list
     * @param dto 查询参数
     * @return
     */
    List<RangeTransferReportVO> selectRangeTransferReportList(@Param("dto") TransferReportRangeDTO dto);


    /**
     * 查询债转月份数据
     * @param dto
     * @return
     */
    List<MonthTransferBaseVO> selectMonthTransferList(@Param("dto") TransferReportRangeDTO dto);

    /**
     * 分页查询债转工单明细报表
     * @param page
     * @param dto
     * @return
     */
	Page<TransferReportDetailVO> selectTransferDetailReportPage(Page<Object> page, @Param("dto") TransferReportDetailDTO dto);


    /**
     * 查询债转工单明细报表
     * @param page
     * @param dto
     * @return
     */
    List<TransferReportDetailVO> selectTransferDetailReportList(@Param("dto") TransferReportDetailDTO dto);

    /**
     * 查询投诉工单数据
     *
     * @param dto
     * @param fundOrderTypeId
     * @param monitorTypeId
     * @return
     */
    List<ComplaintEscalationStatisticsReportVO> selectComplaintOrderData(@Param("dto") ReportBaseDTO dto, @Param("fid") Long fundOrderTypeId, @Param("mid") Long monitorTypeId);
    
}
