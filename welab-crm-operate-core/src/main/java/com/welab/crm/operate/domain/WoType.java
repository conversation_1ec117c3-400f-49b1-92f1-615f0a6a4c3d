package com.welab.crm.operate.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wo_type")
public class WoType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer id;

    /**
     * 工单编码
     */
    private String code;

    /**
     * 工单名称
     */
    private String name;

    /**
     * 工单父类编码
     */
    private String pcode;

    /**
     * 可用标识：0,不可用;1,可用
     */
    private String userflag;

    /**
     * 工单类型；1工单大类,2工单二类,3工单三类
     */
    private Integer type;

    /**
     * 优先级
     */
    private Integer level;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
