package com.welab.crm.operate.util;

import com.google.common.base.Preconditions;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class IRRCalculatorUtil {

    // 最大迭代次数（防止无限循环）
    private static final int MAX_ITERATIONS = 100;
    // 精度阈值（误差小于此值时停止迭代）
    private static final BigDecimal EPSILON = new BigDecimal("0.0000000001");
    // 初始猜测利率（例如 10%）
    private static final BigDecimal INITIAL_RATE_GUESS = new BigDecimal("0.1");

    /**
     * 根据总还款金额和期数反推年利率
     * @param totalAmount 总还款金额（本金 + 总利息）
     * @param tenor 贷款期数（月数）
     * @param principal 本金
     * @return 年利率（小数形式，例如 0.12 表示 12%）
     */
    public static BigDecimal calculateAnnualInterestRate(
            BigDecimal totalAmount, 
            int tenor, 
            BigDecimal principal) {
        
        // 参数校验
        Preconditions.checkArgument(tenor > 0, "期数必须大于0");
        Preconditions.checkArgument(totalAmount.compareTo(principal) > 0, "总金额必须大于本金");
        Preconditions.checkArgument(principal.compareTo(BigDecimal.ZERO) > 0, "本金必须大于0");

        // 计算月利率
        BigDecimal monthlyRate = newtonRaphson(totalAmount, tenor, principal);
        
        // 月利率转年利率
        return monthlyRate.multiply(BigDecimal.valueOf(12))
                         .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 牛顿迭代法求解月利率
     */
    private static BigDecimal newtonRaphson(
            BigDecimal totalAmount, 
            int tenor, 
            BigDecimal principal) {
        
        BigDecimal rate = INITIAL_RATE_GUESS; // 初始猜测利率
        BigDecimal prevRate;
        int iteration = 0;

        do {
            prevRate = rate;
            // 计算当前利率下的函数值 f(r) 和导数 f'(r)
            BigDecimal[] fAndDf = calculateFunctionAndDerivative(rate, totalAmount, tenor, principal);
            BigDecimal f = fAndDf[0];
            BigDecimal df = fAndDf[1];
            
            // 牛顿迭代公式: r_new = r_old - f(r)/f'(r)
            rate = rate.subtract(f.divide(df, 10, RoundingMode.HALF_UP));
            
            // 防止负利率（利率不可能为负）
            if (rate.compareTo(BigDecimal.ZERO) < 0) {
                rate = BigDecimal.ZERO;
                break;
            }
            
            iteration++;
        } while (iteration < MAX_ITERATIONS 
                && rate.subtract(prevRate).abs().compareTo(EPSILON) > 0);

        return rate;
    }

    /**
     * 计算方程 f(r) = 总还款金额 - 预测总金额，及其导数 df/dr
     */
    private static BigDecimal[] calculateFunctionAndDerivative(
            BigDecimal monthlyRate, 
            BigDecimal totalAmount, 
            int tenor, 
            BigDecimal principal) {
        
        // 计算每期还款额 PMT
        BigDecimal pmt = CalculateUntil.pmt(monthlyRate, tenor, principal.negate(), BigDecimal.ZERO, 0);
        // 预测总还款金额 = PMT * 期数
        BigDecimal predictedTotal = pmt.multiply(BigDecimal.valueOf(tenor));
        // f(r) = 实际总金额 - 预测总金额
        BigDecimal f = totalAmount.subtract(predictedTotal);

        // 计算导数 df/dr（通过数值微分简化）
        BigDecimal delta = new BigDecimal("0.0001");
        BigDecimal pmtDelta = CalculateUntil.pmt(monthlyRate.add(delta), tenor, principal.negate(), BigDecimal.ZERO, 0);
        BigDecimal predictedTotalDelta = pmtDelta.multiply(BigDecimal.valueOf(tenor));
        BigDecimal fDelta = totalAmount.subtract(predictedTotalDelta);
        BigDecimal df = fDelta.subtract(f).divide(delta, 10, RoundingMode.HALF_UP);

        return new BigDecimal[] { f, df };
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = calculateAnnualInterestRate(new BigDecimal("13599.56")
                        .subtract(new BigDecimal("1300")), 
                12, new BigDecimal("10000"));
        System.out.println("bigDecimal = " + bigDecimal);
    }

    
    
    
}