package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电销预约回电表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_appoint")
public class TmkAppoint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预约时间
     */
    private Date appointTime;

    /**
     * 电销任务Id
     */
    private String tmkTaskId;

    /**
     * 备注
     */
    private String comment;

    /**
     * 预约人
     */
    private Long staffId;

    /**
     * 状态；0：未推送预约通知；1：已推送
     */
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 推送通知原因
     */
    private Date pushTime;

    /**
     * 用户Id
     */
    private Integer userId;


}
