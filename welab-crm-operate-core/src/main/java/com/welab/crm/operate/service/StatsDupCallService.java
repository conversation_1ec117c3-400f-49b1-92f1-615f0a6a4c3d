package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.vo.woReport.ReportDupCallDetailVO;
import com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO;

import java.util.List;

/**
 * 客户重复来电报表服务类
 *
 * <AUTHOR>
 * @date 2022/3/11 14:30
 */
public interface StatsDupCallService {

    /**
     * 按时间段、时间间隔和热线号码筛选客户重复来电总量数据列表分页
     */
    Page<ReportDupCallTotalVO> listTotalByPage(ReportDupCallDTO callDTO);

    /**
     * 按时间段、时间间隔和热线号码筛选客户重复来电总量数据列表
     */
    List<ReportDupCallTotalVO> listTotal(ReportDupCallDTO callDTO);


    /**
     * 按时间段、时间间隔和热线号码筛选客户重复来电明细数据列表分页
     */
    Page<ReportDupCallDetailVO> listDetailByPage(ReportDupCallDTO callDTO);

    /**
     * 按时间段、时间间隔和热线号码筛选客户重复来电明细数据列表
     */
    List<ReportDupCallDetailVO> listDetail(ReportDupCallDTO callDTO);
}
