package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.ai.AIPushConfigDTO;
import com.welab.crm.operate.dto.ai.AIPushStateDTO;
import com.welab.crm.operate.dto.ai.AiPushHistoryDTO;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.ai.AiTmkPushVO;

import java.util.List;

public interface AiPushService {

    /**
     * 查询全部ai推送配置项
     */
    List<AiTmkConfigVO> getAIPushConfig();

    /**
     * 新增ai推送配置项
     *
     * @param staffCode 操作人编号
     * @param addDTO    将要新增的配置值
     */
    void addAIPushConfig(String staffCode, AIPushConfigDTO addDTO);

    /**
     * 更新ai推送配置项
     *
     * @param staffCode 操作人编号
     * @param updateDTO 将要更新的配置值
     */
    void updateAIPushConfig(String staffCode, AIPushConfigDTO updateDTO);

    /**
     * 更新ai推送状态配置项
     *
     * @param staffCode 操作人编号
     * @param stateDTO  将要更新的状态数据
     */
    void updatePushState(String staffCode, AIPushStateDTO stateDTO);

    /**
     * 删除ai推送配置项(做的是逻辑删除操作)
     */
    void deleteAIPushConfig(String staffCode, Long id);

    /**
     * ai外呼推送数据量历史查询
     *
     * @param historyDTO 包含外呼配置主键id和分页数据参数
     * @return 分页历史推送量数据
     */
    Page<AiTmkPushVO> getAiConfigPushHistory(AiPushHistoryDTO historyDTO);
}
