package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 债转结清主表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_loan_transfer")
public class LoanTransfer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 贷款号数量
     */
    private Integer quantities;

    /**
     * 是否上传附件: 0-未上传,1-已上传
     */
    private Boolean withAttachment;

    /**
     * 审批状态: 0-待审批,1-审批成功,2-审批拒绝
     */
    private Integer approveState;

    /**
     * 审批时间
     */
    private Date approveTime;

    /**
     * 审批人
     */
    private String auditor;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 审批说明
     */
    private String remark;
}
