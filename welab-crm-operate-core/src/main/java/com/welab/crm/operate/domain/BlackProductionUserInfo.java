package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 黑产用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("black_production_user_info")
public class BlackProductionUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 申请人
     */
    private Long applyStaffId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 黑产手机号
     */
    private String blackProductionMobile;

    /**
     * 黑产邮箱
     */
    private String blackProductionEmail;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 审批人
     */
    private Long approvalStaffId;

    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 用户类型 1-确认黑产；2-疑似黑产
     */
    private Integer userType;

    /**
     * 数据来源；1-AIF 联盟；2-客服系统
     */
    private Integer source;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 审批状态；0-待审批；1-通过；2-拒绝
     */
    private Integer approvalStatus;


}
