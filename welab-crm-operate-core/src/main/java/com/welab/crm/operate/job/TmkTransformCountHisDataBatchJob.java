package com.welab.crm.operate.job;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.domain.CallInStatisticsReport;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.mapper.CallInStatisticsReportMapper;
import com.welab.crm.operate.service.IvrReportService;
import com.welab.crm.operate.service.TmkTaskService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.welab.crm.operate.constant.TmkTaskTypeConstant.TMK_TYPE_LIST;

/**
 * 电销转化量历史数据跑批任务
 */
@Slf4j
public class TmkTransformCountHisDataBatchJob implements SimpleJob {

	@Resource
	private CallInStatisticsReportMapper callInStatisticsReportMapper;


	@Resource
	private TmkTaskService tmkTaskService;

	@Override
	public void execute(ShardingContext shardingContext) {
		// 跑批的历史数据范围 从 6月1号 到 8月21号
		Date endDay = DateUtil.stringToDate("2023-08-21 00:00:00");
		Date startDay = DateUtil.stringToDate("2023-06-01 00:00:00");

		for (Date d = startDay; !d.after(endDay); d = DateUtil.plusDays(d, 1)) {
			log.info("开始同步{}" + "数据", DateUtil.dateToString(d));

			CallInStatisticsReport callInStatisticsReport = callInStatisticsReportMapper.selectOne(Wrappers.lambdaQuery(CallInStatisticsReport.class)
					.eq(CallInStatisticsReport::getCountDay, d)
					.eq(CallInStatisticsReport::getHotline, "10100518"));


			if (Objects.isNull(callInStatisticsReport)) {
				continue;
			}

			Date startTime = DateUtils.getStartOfDate(d);
			Date endTime = DateUtils.getEndOfDate(d);

			// 查询电销转化量
			Integer tmkTransformCount = queryTmkTransformCount(startTime, endTime);
			callInStatisticsReport.setTmkTransformNum(tmkTransformCount);
			callInStatisticsReportMapper.updateById(callInStatisticsReport);
		}


	}

	private Integer queryTmkTransformCount(Date startTime, Date endTime) {
		Integer tmkTransformCount = 0;
		TmkTransformReportReqDTO dto = new TmkTransformReportReqDTO();
		dto.setStartTime(DateUtil.dateToString(startTime));
		dto.setEndTime(DateUtil.dateToString(endTime));
		for (String tmkType : TMK_TYPE_LIST) {
			dto.setTmkType(tmkType);
			List<TmkTransformReportVO> transformList = tmkTaskService.queryTmkTransformData(dto);
			if (CollectionUtils.isNotEmpty(transformList)) {
				for (TmkTransformReportVO transformReportVO : transformList) {
					tmkTransformCount += Optional.ofNullable(transformReportVO.getTransformCount()).orElse(0);
					tmkTransformCount += Optional.ofNullable(transformReportVO.getHisTransformCount()).orElse(0);
				}
			}
		}
		return tmkTransformCount;
	}


}