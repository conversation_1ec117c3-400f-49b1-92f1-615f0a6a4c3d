package com.welab.crm.operate.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkInterceptRule;
import com.welab.crm.operate.dto.tmkRule.TmkInterceptRuleReqDTO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptHistoryVO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptRuleVO;

import java.util.List;

/**
 * <p>
 * 电销拦截规则配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
public interface TmkInterceptRuleMapper extends BaseMapper<TmkInterceptRule> {
	Page<TmkInterceptRuleVO> queryInterceptRuleByPage(Page<TmkInterceptRuleVO> page,
													  @Param("reqDTO") TmkInterceptRuleReqDTO reqDTO, @Param("tmkTypes") List<String> tmkTypes);
	
	Page<TmkInterceptHistoryVO> queryInterceptHistoryByPage(Page<TmkInterceptHistoryVO> page, @Param("reqDTO") TmkInterceptRuleReqDTO reqDTO);
}
