package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 满意度调查表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_satisfaction")
public class ConSatisfaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 企业ID
     */
    private String cdrEnterpriseId;

    /**
     * 中继号码
     */
    private String cdrNumberTrunk;

    /**
     * 热线号码
     */
    private String cdrHotline;

    /**
     * 通话标识
     */
    private String cdrMainUniqueId;

    /**
     * 客户号码
     */
    private String cdrCustomerNumber;

    /**
     * 呼叫类型
     */
    private String cdrCallType;

    /**
     * 开始时间
     */
    private Date svStartTime;

    /**
     * 结束时间
     */
    private Date svEndTime;

    /**
     * 座席工号
     */
    private String bridgedCno;

    /**
     * 转移信息，转移后的满意度调查时有值
     */
    private String cdrTransfer;

    /**
     * 按键值
     */
    private String svKeys;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
