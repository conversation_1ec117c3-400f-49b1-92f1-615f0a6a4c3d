package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.enums.ValidFaceCodeEnum;
import com.welab.crm.interview.enums.ValidFaceTypeEnum;
import com.welab.crm.interview.enums.ValidFaceVendorEnum;
import com.welab.crm.interview.service.MessageService;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseDetail;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.crm.operate.domain.ConWebotToken;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.message.SmsSendDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.mapper.ConWebotTokenMapper;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.MessageValidateService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class MessageValidateServiceImpl implements MessageValidateService {

    @Resource
    private ConWebotTokenMapper conWebotTokenMapper;

    @Resource
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Resource
    private JedisCommands jedisCommands;
    
    @Resource
    private MessageService messageService;


    @Resource
    private CustOperateService custOperateService;

    /**
     * redis key前缀
     */
    private static final String REDIS_KEY_PRE = "crm_sms_sendTimes_";

    @Override
    public void addGroupToResponse(ResponseVO<List<ResponseData>> response) {
        if (CollectionUtils.isNotEmpty(response.getData())) {
            for (ResponseData data : response.getData()) {
                if (ValidFaceTypeEnum.SMS.getValue().equals(data.getSource())) {
                    // 短信验证方式查询发送人所属发送组
                    LambdaQueryWrapper<ConWebotToken> wrapper = Wrappers.<ConWebotToken>lambdaQuery()
                            .eq(ConWebotToken::getToken, data.getToken());
                    List<ConWebotToken> tokenList = conWebotTokenMapper.selectList(wrapper);
                    if (CollectionUtils.isNotEmpty(tokenList)) {
                        String staffId = tokenList.get(0).getCreateUser();
                        data.setServiceUser(staffId);
                        data.setGroup(getSendGroup(staffId));
                    }
                } else if (StringUtils.isNotBlank(data.getServiceUser())) {
                    // 在线验证方式查询发送人所属发送组
                    data.setGroup(getSendGroup(data.getServiceUser()));
                }

                data.setSource(ValidFaceTypeEnum.getDescByValue(data.getSource()));
                data.setCodeDesc(ValidFaceCodeEnum.getDescByValue(data.getCode()));
                data.setToken(null);
                for (ResponseDetail detail : data.getDetail()) {
                    detail.setVendor(ValidFaceVendorEnum.getDescByValue(detail.getVendor()));
                    detail.setCodeDesc(ValidFaceCodeEnum.getDescByValue(detail.getCode()));
                }
            }
        }
    }

	@Override
	public String sendMsg(SmsSendDTO dto, StaffVO staffVO) {
        limitSendFrequency(staffVO.getStaffMobile());
        if (StringUtils.isBlank(dto.getName())){
            dto.setName("未知");
        }
        MessageSendDTO sendDTO = new MessageSendDTO();
        BeanUtils.copyProperties(dto, sendDTO);
        sendDTO.setStaffId(staffVO.getId().toString());
        sendDTO.setLoginName(staffVO.getLoginName());
        if (StringUtils.isNotBlank(dto.getOtherParams())) {
            sendDTO.setOtherParams(JSON.parseObject(dto.getOtherParams(), Map.class));
        }
        String errMsg = messageService.sendMessage(sendDTO);
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.MESSAGE_SEND.getCode());
       return errMsg;
	}

    /**
     * 限制短信发送频率，每个客服每分钟发送限制
     */
    private void limitSendFrequency(String mobile) {
        String redisKey = REDIS_KEY_PRE + mobile;
        String count = jedisCommands.get(redisKey);
        List<OpDictInfo> dict = CommonUtils.getDict("interface_limit_times", "sms");
        int maxCount = Integer.parseInt(dict.get(0).getContent());
        if (StringUtils.isBlank(count)) {
            jedisCommands.set(redisKey, "1");
            jedisCommands.expire(redisKey, 60);
        } else {
            int intCount = Integer.parseInt(count);
            Long ttl = jedisCommands.ttl(redisKey);
            if (-1 == ttl) {
                jedisCommands.del(redisKey);
            } else if (intCount < maxCount) {
                jedisCommands.incr(redisKey);
            } else {
                throw new FastRuntimeException("发送短信频率过高，请稍后再试");
            }
        }

    }

	private String getSendGroup(String sendUser) {
        LambdaQueryWrapper<InAuthCrmStaff> sWrapper = Wrappers.<InAuthCrmStaff>lambdaQuery()
                .eq(InAuthCrmStaff::getLoginName, sendUser)
                .eq(InAuthCrmStaff::getIsStatus, 1);
        InAuthCrmStaff staff = inAuthCrmStaffMapper.selectOne(sWrapper);
        if (staff != null) {
            return staff.getGroupName();
        } else {
            return "";
        }
    }
}
