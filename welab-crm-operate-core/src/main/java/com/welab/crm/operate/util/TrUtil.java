package com.welab.crm.operate.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.dto.report.ReportPhoneSummaryDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.model.AgentStatistic;
import com.welab.crm.operate.model.StatisticAgentWorkloadDay;
import com.welab.exception.FastRuntimeException;
import com.welab.privacy.util.http.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 天润接口请求工具类
 * @date 2022/3/3
 */
@Slf4j
@Component
public class TrUtil {

    @Value("${tr.call.base.url}")
    private String baseUrl;
    @Value("${tr.call.enterpriseId}")
    private String enterpriseId;

    @Resource
    private TrTokenService trTokenService;

    @Resource
    private JedisCommands jedisCommands;

    /**
     * 天润token前缀
     */
    private static final String TR_TOKEN_REDIS_KEY = "crm_tr_token_";

    public List<StatisticAgentWorkloadDay> reportWorkLoad(String startTime, String endTime, Integer statisticMethod,
        Integer timeRangeType){
        return reportWorkLoad(startTime, endTime, statisticMethod, timeRangeType, 999, null);
    }

    /**
     * 查询坐席工作量报表
     *
     * @param startTime       查询开始时间 格式 yyyy-MM-dd
     * @param endTime         查询结束时间 格式 yyyy-MM-dd, 必须大于等于开始时间
     * @param statisticMethod 统计方法， 0：分时 1：分日 2：汇总
     * @param timeRangeType   统计报表类型，1：日报表 2：周报表 3：月报表 4：自定义时间
     * @param limit           查询条数,最大不能超过1000，接口默认10
     * @param cnos            根据座席工号查询指定座席的工作量统计,多个座席工号之间使用英文逗号","分隔,为空查询所有坐席
     * @return
     */
    public List<StatisticAgentWorkloadDay> reportWorkLoad(String startTime, String endTime, Integer statisticMethod,
        Integer timeRangeType, Integer limit, String cnos) {
        Map<String, String> params = initParams();
        params.put("statisticMethod", statisticMethod.toString());
        params.put("timeRangeType", timeRangeType.toString());
        params.put("limit", limit.toString());
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("cnos", cnos);
        String reportWorkLoadUrl = baseUrl + "/agentReport/agentWorkload";
        try {
            String response = HttpClients.create().setUrl(reportWorkLoadUrl).addURLParams(params).doGet();
            JSONObject json = JSONObject.parseObject(response);
            if ("0".equals(json.get("result"))) {
                List<StatisticAgentWorkloadDay> list = JSONObject
                    .parseObject(json.getJSONObject("data").getString("list"),
                        new TypeReference<List<StatisticAgentWorkloadDay>>() {
                        });
                return list;
            } else {
                throw new CrmOperateException("天润报表查询失败: " + response);
            }
        } catch (Exception e) {
            log.error("reportWorkLoad error. url={}, params={}", reportWorkLoadUrl, params.toString(), e);
            throw new CrmOperateException("天润报表查询异常");
        }
    }

    /**
     * 坐席实时统计，只有当天数据
     *
     * @return
     */
    public List<AgentStatistic> monitorStatistics() {
        Map<String, String> params = initParams();
        String monitorStatisticsUrl = baseUrl + "/monitor/statistics/agent";
        try {
            String response = HttpClients.create().setUrl(monitorStatisticsUrl).addURLParams(params).doGet();
            JSONObject json = JSONObject.parseObject(response);
            if (0 == (Integer) json.get("result")) {
                List<AgentStatistic> list = JSONObject
                    .parseObject(json.getJSONObject("data").getString("agentStatistics"),
                        new TypeReference<List<AgentStatistic>>() {
                        });
                return list;
            } else {
                throw new CrmOperateException("天润报表查询失败: " + response);
            }
        } catch (Exception e) {
            log.error("monitorStatistics error. url={}, params={}", monitorStatisticsUrl, params.toString(), e);
            throw new CrmOperateException("天润报表查询异常");
        }
    }

    private Map<String, String> initParams() {
        return getTrBaseParams(enterpriseId, getTokenByEnterpriseId(enterpriseId));
    }

    public String getTokenByEnterpriseIdUseCache(String enterpriseId){
        String token = jedisCommands.get(TR_TOKEN_REDIS_KEY + enterpriseId);
        if (StringUtils.isBlank(token)){
            token = trTokenService.getTokenByEnterpriseId(enterpriseId);
            jedisCommands.set(TR_TOKEN_REDIS_KEY + enterpriseId , token);
            jedisCommands.expire(TR_TOKEN_REDIS_KEY + enterpriseId , 60 * 60 * 6);
        }
        return token;
    }

    public String getTokenByEnterpriseId(String enterpriseId){
        String token = trTokenService.getTokenByEnterpriseId(enterpriseId);
        jedisCommands.set(TR_TOKEN_REDIS_KEY + enterpriseId , token);
        jedisCommands.expire(TR_TOKEN_REDIS_KEY + enterpriseId , 60 * 60 * 6);
        return token;
    }


    public static Map<String, String> getTrBaseParams(String enterpriseId, String sign) {
        Map<String, String> params = new HashMap<>(16);
        params.put("validateType", "2");
        params.put("enterpriseId", enterpriseId);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        params.put("sign", Md5Util.md5(enterpriseId + timestamp + sign));
        params.put("timestamp", timestamp);
        return params;
    }

    /**
     * 计算百分比
     *
     * @param number
     * @param total
     * @return
     */
    public static String calcPercentage(Integer number, Integer total) {
        if (Objects.isNull(number) || Objects.isNull(total) || total == 0) {
            return "0.00%";
        }
        double rate = number / total.doubleValue() * 100;
        // 保留两位小数
        return String.format("%.2f", rate) + "%";
    }

    /**
     * 获取录音文件
     * @param reqDTO
     * @return
     */
    public String queryRecordFile(PhoneSundRecordingDTO reqDTO) {
        // 获取enterpriseId
        String recordFile = reqDTO.getRecordFile();
        String enterpriseId = recordFile.substring(0, 7);
        Map<String, String> params = TrUtil.getTrBaseParams(enterpriseId, getTokenByEnterpriseId(enterpriseId));
        String recordgetUrl = baseUrl + "/record/getUrl";
        params.put("recordType", "record");
        params.put("recordFile", recordFile);
        try {
            String response = HttpClients.create().setUrl(recordgetUrl).addURLParams(params).doGet();
            JSONObject json = JSONObject.parseObject(response);
            if ("0".equals(json.get("result"))) {
                String data = json.getString("data");
                return data;
            } else {
                throw new CrmOperateException("天润获取录音地址失败: " + response);
            }
        } catch (Exception e) {
            log.error("queryRecordFile error={}", e);
            throw new CrmOperateException("天润获取录音地址异常");
        }
    }

    /**
     * 中继报表来电分析
     * @param reqDTO
     * @return
     */
    public String queryTrunkReportIb(ReportPhoneSummaryDTO reqDTO) {
        Map<String, String> params = initParams();
        String trunkReportUrl = baseUrl + "/trunkReport/ib";
        params.put("timeRangeType", reqDTO.getTimeRangeType() + "");
        params.put("statisticMethod", reqDTO.getStatisticMethod() + "");
        params.put("startTime", reqDTO.getStartTime());
        params.put("limit", 1000 + "");
        if (4 == reqDTO.getTimeRangeType() && StringUtils.isNotBlank(reqDTO.getEndTime())) {
            params.put("endTime", reqDTO.getEndTime());
        }
        if (StringUtils.isNotBlank(reqDTO.getHotline())) {
            params.put("hotlines", reqDTO.getHotline());
        }
        //分时统计
        if (0 == reqDTO.getStatisticMethod()) {
            params.put("startHour", "00");
            params.put("endHour", "23");
        }
        /*if (Objects.nonNull(reqDTO.getStartHour())) {
            params.put("startHour", reqDTO.getStartHour() + "");
        }
        if (Objects.nonNull(reqDTO.getEndHour())) {
            params.put("endHour", reqDTO.getEndHour() + "");
        }*/
        try {
            String response = HttpClients.create().setUrl(trunkReportUrl).addURLParams(params).doGet();
            JSONObject json = JSONObject.parseObject(response);
            if ("0".equals(json.get("result"))) {
                String data = json.getString("data");
                return data;
            } else {
                throw new CrmOperateException("天润获取中继报表来电分析失败: " + response);
            }
        } catch (Exception e) {
            log.error("queryTrunkReportIb error={}", e);
            throw new CrmOperateException("天润获取中继报表来电分析异常");
        }
    }

    /**
     * 解析天润返回结果
     * 
     * @param str 返回字符串
     * @return
     */
    public static JSONObject parseTrResult(String str) {
        if (StringUtils.isBlank(str)) {
            return new JSONObject();
        }
        try {
            JSONObject jsonObject = JSON.parseObject(str);
            if (!"0".equals(jsonObject.getString("result"))) {
                log.info("查询天润数据失败");
                return new JSONObject();
            }
            return jsonObject.getJSONObject("data");

        } catch (Exception e) {
            log.error("解析字符串为json对象失败:{}", str);
            return new JSONObject();
        }
    }

    public String doGet(String enterpriseId, String path, Map<String, String> params) {
        Map<String, String> baseParams = getTrBaseParams(enterpriseId, getTokenByEnterpriseIdUseCache(enterpriseId));
        baseParams.putAll(params);
        return HttpClients.create().setUrl(baseUrl + path).addURLParams(baseParams).doGet();
    }
    
    
    public static Integer getIntegerFromJsonObject(JSONObject jsonObject, String fieldName) {
        Integer integer = jsonObject.getInteger(fieldName);
        return Objects.isNull(integer) ? 0 : integer;
    }


    /**
     * 坐席实时监控
     * @param params
     * @return
     */
    public JSONArray queryMonitorAgent(Map<String,String> params){
        String resStr = doGet(enterpriseId, "/monitor/agent", params);
        JSONObject jsonObject = TrUtil.parseTrResult(resStr);
        if (Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
            log.error("queryAgentWorkOverview 请求天润数据失败");
            return new JSONArray();
        }

        return jsonObject.getJSONArray("agentStatuses");
    }


    /**
     * 队列实时统计
     * @param params
     * @return
     */
    public JSONArray queryMonitorQueueStatistic(Map<String,String> params){
        String res2 = doGet(enterpriseId, "/monitor/statistics/queue", params);
        JSONObject manData = TrUtil.parseTrResult(res2);
        if (Objects.isNull(manData) || manData.isEmpty()) {
            log.error("queryMonitorQueueStatistic 请求天润数据失败");
            return new JSONArray();
        }
        return manData.getJSONArray("queueStatistics");
    }

    /**
     * 队列实时统计(分时)
     * @param params
     * @return
     */
    public JSONArray queryMonitorQueueDetailStatistic(Map<String,String> params){
        String res2 = doGet(enterpriseId, "/monitor/statistics/queueDetail", params);
        JSONObject manData = TrUtil.parseTrResult(res2);
        if (Objects.isNull(manData) || manData.isEmpty()) {
            log.error("queryMonitorQueueDetailStatistic 请求天润数据失败");
            return new JSONArray();
        }
        return manData.getJSONArray("queueStatistics");
    }

    /**
     * 中控实时统计
     * @param params
     * @return
     */
    public JSONArray queryTRTrunkStatistic(Map<String, String> params) {
        String res1 = doGet(enterpriseId, "/monitor/statistics/trunk", params);
        JSONObject callData = TrUtil.parseTrResult(res1);
        if (Objects.isNull(callData) || callData.isEmpty()) {
            log.error("queryTRTrunkStatistic 请求天润数据失败");
            return new JSONArray();
        }
        return callData.getJSONArray("trunkStatistics");
    }

    /**
     * 队列实时监控
     * @param params
     * @return
     */
    public JSONArray queryMonitorQueue(Map<String, String> params) {
        String res = doGet(enterpriseId, "/monitor/queue", params);
        JSONObject json = TrUtil.parseTrResult(res);
        if (Objects.isNull(json) || json.isEmpty()) {
            log.warn("queryMonitorQueue 请求天润数据失败");
            return new JSONArray();
        }
        return json.getJSONArray("queueStatuses");
    }

    /**
     * 满意度实时统计
     * @param params
     * @return
     */
    public JSONArray queryMonitorStatisticsInvestigation(Map<String, String> params){
        String res = doGet(enterpriseId, "/monitor/statistics/investigation", params);
        JSONObject jsonObject = TrUtil.parseTrResult(res);
        if (Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
            log.error("queryMonitorStatisticsInvestigation 请求天润数据失败");
            return new JSONArray();
        }

        return jsonObject.getJSONArray("investigationStatistics");
    }


    /**
     * 坐席
     * @param params
     * @return
     */
    public JSONArray queryMonitorStatisticsAgent(Map<String, String> params){
        String res = doGet(enterpriseId, "/monitor/statistics/agent", params);
        JSONObject jsonObject = TrUtil.parseTrResult(res);
        if (Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
            log.error("queryMonitorStatisticsAgent 请求天润数据失败");
            return new JSONArray();
        }

        return jsonObject.getJSONArray("agentStatistics");
    }



}
