package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.vo.callbackSummary.CallbackTypeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface OpDictInfoMapper extends BaseMapper<OpDictInfo> {

    /**
     * 批量插入回电小结字典
     * @param voList
     * @param category
     */
    void insertBatchSummaryDict(@Param("list") List<CallbackTypeVO> voList,@Param("category") String category);

	/**
	 * 根据主键更新字典 sort 字段+1
	 * @param id 主键
	 */
	void increaseSort(@Param("id") Long id);
}
