package com.welab.crm.operate.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.WoType;
import com.welab.crm.operate.dto.workorder.WorkOrderTypeReqDTO;
import com.welab.crm.operate.mapper.WoTypeMapper;
import com.welab.crm.operate.service.WorkOrderTypeService;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeVO;
import com.welab.exception.FastRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 工单service服务
 * <AUTHOR>
 * @date 2021-10-26
 */
@Slf4j
@Service
public class WorkOrderTypeServiceImpl implements WorkOrderTypeService {

    @Resource
    private WoTypeMapper woTypeMapper;


    @Override
    public Page<WorkOrderTypeVO> queryWorkOrderTypeList(WorkOrderTypeReqDTO reqDTO) {
        log.info("queryWorkOrderTypeList,reqDTO:{}", JSON.toJSONString(reqDTO));

        return woTypeMapper
                .queryWorkOrderTypeByPage(new Page<WorkOrderTypeVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
    }

    @Override
    public void addWorkOrderType(WorkOrderTypeReqDTO reqDTO) {
    	WorkOrderTypeReqDTO dto = new WorkOrderTypeReqDTO();
    	dto.setCode(reqDTO.getCode());
    	List<WorkOrderTypeVO> typeList = woTypeMapper.queryWorkOrderTypeList(reqDTO);
    	if(typeList != null && typeList.size() > 0) {
    		log.error("工单编码{}已存在",reqDTO.getCode());
    		throw new FastRuntimeException("工单编码{}已存在",reqDTO.getCode());
    	}
    	WoType woType = new WoType();
    	BeanUtils.copyProperties(reqDTO, woType);
    	woTypeMapper.insert(woType);
    }

    @Override
    public void updateWorkOrderType(WorkOrderTypeReqDTO reqDTO) {
    	WoType woType = null;
    	
    	if(reqDTO.getId() != null) {
    		woType = woTypeMapper.selectById(reqDTO.getId());
    		if (woType != null) {
    			BeanUtils.copyProperties(reqDTO, woType);
    			woType.setGmtModify(new Date());
            	woTypeMapper.updateById(woType);
    		}else {
    			log.error("工单分类记录ID{}不存在",reqDTO.getId());
        		throw new FastRuntimeException("工单分类记录ID{}不存在",String.valueOf(reqDTO.getId()));
    		}
    	}else {
    		log.error("工单分类记录ID{}不存在！");
    		throw new FastRuntimeException("工单分类记录ID{}不存在");
    	}
    }
}
