package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.service.ConRepeatCallService;
import com.welab.crm.interview.service.SatisfactionService;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.crm.operate.domain.InPhoneLoginInfo;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.service.ScreenService;
import com.welab.crm.operate.service.WorkStatusReportService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.TrUtil;
import com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsReportVO;
import com.welab.crm.operate.vo.personalPanel.HourUtilisation;
import com.welab.crm.operate.vo.personalPanel.IndividualEffectiveness;
import com.welab.crm.operate.vo.personalPanel.OverAllData;
import com.welab.crm.operate.vo.personalPanel.PersonalPanelVO;
import com.welab.crm.operate.vo.phone.CallInfoTotalData;
import com.welab.crm.operate.vo.screen.AgentEfficientVO;
import com.welab.crm.operate.vo.screen.AgentOverview;
import com.welab.crm.operate.vo.screen.CountVO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大屏监控服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScreenServiceImpl implements ScreenService {

    /**
     * 电销部门 enterpriseId
     */
    @Value("${tr.tmk.enterpriseId}")
    private String tmkEnterpriseId;

    /**
     * 客服部门 enterpriseId
     */
    @Value("${tr.call.enterpriseId}")
    private String kfEnterpriseId;

    @Resource
    private TrUtil trUtil;
    
    @Resource
    private OpStaffExtraStatusLogMapper opStaffExtraStatusLogMapper;
    
    @Resource
    private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;
    
    @Resource
    private ConRepeatCallService conRepeatCallService;

    @Resource
    private SatisfactionService satisfactionService;
    private static final String BHRUZ = "bhhrz";

    private static final List<String> groupCodes = new ArrayList<>(Arrays.asList("wdhrz", "bhhrz"));

    /**
     * 坐席实时统计查询字段
     */
    private static final List<String> AGENT_STATISTIC_QUERY_FIELDS = new ArrayList<>(Arrays.asList("cno", "name",
        "ib_bridge_time", "ib_bridge_count", "login_time", "ib_wrapup_time", "ib_answered_count"));

    /**
     * 通话中状态
     */
    private static final List<String> CALLING_STATE = new ArrayList<>(Arrays.asList("呼叫中", "响铃", "通话"));


    @Resource
    InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Resource
    private ConPhoneSummaryMapper conPhoneSummaryMapper;

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Resource
    private IvrReportServiceImpl ivrReportService;

    @Resource
    private WorkStatusReportService reportService;

    @Override
    public AgentOverview queryAgentWorkOverview() {
        AgentOverview agentOverview = new AgentOverview();
        // 只需要查维度呼入组的员工->2023.11.02添加北海呼入组的员工统计
        //List<String> cnos = inAuthCrmStaffMapper.queryCnosByGroup(WDHRZ);
        List<String> cnos = inAuthCrmStaffMapper.queryCnosByGroups(groupCodes);
        if (CollectionUtils.isEmpty(cnos)) {
            log.warn("queryAgentWorkOverview 组内无在职员工:{}", BHRUZ);
            return agentOverview;
        }
        // 过滤掉为空的记录
        cnos = cnos.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        String cnosStr = Joiner.on(",").join(cnos);
        Map<String, String> params = new HashMap<>();
        params.put("cnos", cnosStr);
        params.put("limit", "500");
        // 查询天润坐席监控数据
        JSONArray agentStatuses = trUtil.queryMonitorAgent(params);
        if (CollectionUtils.isEmpty(agentStatuses)) {
            return agentOverview;
        }
        // 在线数
        Integer onlineNumber = 0;
        // 空闲数
        Integer freeNumber = 0;
        // 通话中数
        Integer callingNumber = 0;
        for (int i = 0; i < agentStatuses.size(); i++) {
            JSONObject agentStatus = agentStatuses.getJSONObject(i);
            Integer loginStatus = agentStatus.getInteger("loginStatus");
            // 登录状态，0离线，1在线，2置忙，3整理
            if (Objects.nonNull(loginStatus) && loginStatus != 0) {
                onlineNumber++;
            }

            // 座席状态：离线，失效，空闲，置忙，整理，呼叫中，响铃，通话
            String state = agentStatus.getString("state");
            if ("空闲".equals(state)) {
                freeNumber++;
            }
            if (CALLING_STATE.contains(state)) {
                callingNumber++;
            }
        }
        agentOverview.setOnlineNumber(onlineNumber);
        agentOverview.setFreeNumber(freeNumber);
        agentOverview.setCallingNumber(callingNumber);
        return agentOverview;
    }

    @Override
    public List<CountVO> queryTop10Question() {
        // 获取电话小结中排名前十的小结三类
        return conPhoneSummaryMapper.queryTop10Question();
    }

    @Override
    public JSONObject queryZkOverview() {
        JSONObject result = new JSONObject();
        Map<String, String> params = new HashMap<>();
        // 获取当前实时排队数
        setQueueEntryCount(result, params);

        params.clear();

        // 获取总来电数
        setTotalCallInfo(result, params);

        // 转人工数
        // setManData(result, params);

        return result;
    }

    @Override
    public List<CountVO> queryCallMap() {
        return conPhoneCallInfoMapper.queryProvinceCount().stream()
            .filter(item -> StringUtils.isNotBlank(item.getName())).collect(Collectors.toList());
    }

    private void setManData(JSONObject result, Map<String, String> params) {
        // 转人工数量
        params.clear();
        JSONArray queueStatistics = trUtil.queryMonitorQueueStatistic(params);
        if (CollectionUtils.isEmpty(queueStatistics)) {
            return;
        }
        int toManCount = 0;
        int manCount = 0;
        for (int i = 0; i < queueStatistics.size(); i++) {
            Integer count1 = queueStatistics.getJSONObject(i).getInteger("enter_count");
            toManCount += Objects.isNull(count1) ? 0 : count1;
            Integer count2 = queueStatistics.getJSONObject(i).getInteger("complete_count");
            manCount += Objects.isNull(count2) ? 0 : count2;
        }

        result.put("转人工服务数", toManCount);
        result.put("人工接起数", manCount);
    }

    private void setTotalCallInfo(JSONObject result, Map<String, String> params) {
        CallInfoTotalData callInfoTotalData = queryCalInfoTotalData(params);
        result.put("来电总数", callInfoTotalData.getTotalCount());
        result.put("转人工服务数", callInfoTotalData.getToQueueCount());
        result.put("人工接起数", callInfoTotalData.getAnswerCount());
        result.put("接通率", callInfoTotalData.getAnswerRate());
    }


    @Override
    public CallInfoTotalData queryCalInfoTotalData(Map<String, String> params) {
        // 中继实时统计接口
        JSONArray trunkStatistics = trUtil.queryTRTrunkStatistic(params);
        if (CollectionUtils.isEmpty(trunkStatistics)) {
            return new CallInfoTotalData(0, 0, 0, "0.00%");
        }
        int totalCount = 0;
        int toQueueCount = 0;
        int answerCount = 0;

        for (int i = 0; i < trunkStatistics.size(); i++) {
            JSONObject jsonObject = trunkStatistics.getJSONObject(i);
            totalCount += TrUtil.getIntegerFromJsonObject(jsonObject, "ib_total_count");
            toQueueCount += TrUtil.getIntegerFromJsonObject(jsonObject, "ib_queue_count");
            answerCount += TrUtil.getIntegerFromJsonObject(jsonObject, "ib_answered_count");
        }
        return new CallInfoTotalData(totalCount, toQueueCount, answerCount,
            CommonUtil.calcPercentage(answerCount, toQueueCount));
    }

    private void setQueueEntryCount(JSONObject result, Map<String, String> params) {
        result.put("排队数", queryRealTimeQueues(params));
    }


    @Override
    public Integer queryRealTimeQueues(Map<String, String> params) {

        params.put("queueMonitorFields","queueParams");
        // 天润队列监控接口
        JSONArray queueStatuses = trUtil.queryMonitorQueue(params);
        if (CollectionUtils.isEmpty(queueStatuses)) {
            return 0;
        }

        int queueEntryCount = 0;
        for (int i = 0; i < queueStatuses.size(); i++) {
            JSONObject jsonObject = queueStatuses.getJSONObject(i);
            JSONObject queueParams = jsonObject.getJSONObject("queueParams");
            queueEntryCount += TrUtil.getIntegerFromJsonObject(queueParams, "queueEntryCount");
        }
        return queueEntryCount;
    }

    @Override
    public JSONObject queryHoursCallInfo() {
        JSONObject result = new JSONObject();
        Map<String, String> params = new HashMap<>();
        // 队列实时统计接口(分时)
        JSONArray queueStatistics = trUtil.queryMonitorQueueDetailStatistic(params);
        if (CollectionUtils.isEmpty(queueStatistics)) {
            return new JSONObject();
        }
        for (int i = 0; i < queueStatistics.size(); i++) {
            JSONObject hoursJson = queueStatistics.getJSONObject(i);
            for (String hour : hoursJson.keySet()) {
                Integer enterCount = TrUtil.getIntegerFromJsonObject(hoursJson.getJSONObject(hour), "enter_count");
                Integer completeCount =
                    TrUtil.getIntegerFromJsonObject(hoursJson.getJSONObject(hour), "complete_count");
                JSONObject callInfo = result.getJSONObject(hour);
                if (Objects.isNull(callInfo)) {
                    callInfo = new JSONObject();
                    callInfo.put("进线量", enterCount);
                    callInfo.put("接起量", completeCount);
                    callInfo.put("接通率", CommonUtil.calcPercentage(completeCount, enterCount));
                } else {
                    int enterCount2 = enterCount + callInfo.getInteger("进线量");
                    int completeCount2 = completeCount + callInfo.getInteger("接起量");
                    callInfo.put("进线量", enterCount2);
                    callInfo.put("接起量", completeCount2);
                    callInfo.put("接通率", CommonUtil.calcPercentage(completeCount2, enterCount2));
                }
                result.put(hour, callInfo);
            }
        }
        JSONObject total = new JSONObject();
        total.put("hoursCallInfo", result);
        total.put("repeat-data", queryRepeatData());
        return total;
    }

    @Override
    public JSONObject queryServiceLevel() {
        JSONObject result = new JSONObject();
        // 查询满意度
        querySatisfaction(result);

        // 查询首问解决率
        queryFirstQuestionSolveRate(result);

        // 查询各热线来电数量
        queryHotlineCallInfoCount(result);

        // 查询工时利用率
        queryWorkUtilRate(result);

        // 查询IVR分流率
        queryIvrShuntRate(result);

        return result;
    }

    private void queryIvrShuntRate(JSONObject result) {
        IvrReportReqDTO reqDTO = new IvrReportReqDTO();
        reqDTO.setHotline("10100518,4006000799,4006040888,4000666730");
        reqDTO.setPeriod("range");
        reqDTO.setStartTime(DateUtils.getStartOfToday());
        reqDTO.setEndTime(DateUtils.getEndOfToday());
        IvrStatisticsReportVO ivrStatisticsReportVO = ivrReportService.ivrShuntStatisticsReport(reqDTO);
        if (Objects.isNull(ivrStatisticsReportVO)){
            setShuntRateZero(result);
            return;
        }
        Map<String, IvrStatisticsArtificialVO> shuntRateMap = ivrStatisticsReportVO.getShuntRateMap();
        if (MapUtils.isEmpty(shuntRateMap)){
            setShuntRateZero(result);
            return;
        }
        IvrStatisticsArtificialVO vo = shuntRateMap.get("统计");
        if (Objects.isNull(vo)){
            setShuntRateZero(result);
            return;
        }
        result.put("IVR分流率", vo.getTotal());
    }

    private void setShuntRateZero(JSONObject result) {
        result.put("IVR分流率","0.00%");
    }

    private void queryWorkUtilRate(JSONObject result) {
        JSONArray jsonArray = queryWdhrzCallData();
        if (CollectionUtils.isEmpty(jsonArray)){
            result.put("工时利用率","0.00%");
            return;
        }
        
        // 查询维度呼入组的提单时间总长
        List<ReportWorkStatusSummaryVO> submitOrderDurationList = queryTodaySubmitOrderDuration(BHRUZ);
        Long totalSubmitOrderDuration = 0L;
        if (CollectionUtils.isNotEmpty(submitOrderDurationList)){
            totalSubmitOrderDuration = Long.valueOf(submitOrderDurationList.get(0).getSubmitOrderDuration());
        }

        // 总呼入通话时长
        Long totalBridgeTime = 0L;
        // 总整理时长
        Long totalWrapupTime = 0L;
        // 总登陆时长
        Long totalLoginTime = 0L;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject agentStatistic = jsonArray.getJSONObject(i);
            // 呼入整理时长
            Long ibWrapupTime = agentStatistic.getLong("ib_wrapup_time");
            // 登陆时长
            Long loginTime = agentStatistic.getLong("login_time");
            // 呼入坐席通话时长
            Long ibBridgeTime = agentStatistic.getLong("ib_bridge_time");

            totalBridgeTime += ibBridgeTime;
            totalWrapupTime += ibWrapupTime;
            totalLoginTime += loginTime;
        }

        // 计算总的工时利用率
        result.put("工时利用率",CommonUtil.calcPercentageByLong(totalWrapupTime + totalBridgeTime + totalSubmitOrderDuration, totalLoginTime));
        /*ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setCurrentPage(1);
        dto.setRowsPerPage(5000);
        dto.setStartTime(DateUtil.dateToString(new Date(DateUtil.getToday(0, 0, 0).getMillis())));
        dto.setEndTime(DateUtil.dateToString(new Date(DateUtil.getToday(23, 59, 59).getMillis())));
        dto.setPeriod("day");
        dto.setGroupCode(BHRUZ);
        Page<ReportWorkStatusSummaryVO> summaryPage = reportService.getWorkStatusSummaryPage(dto);
        if(CollectionUtils.isEmpty(summaryPage.getRecords())){
            result.put("工时利用率","0.00%");
            return;
        }
        int wrapUpDuration = 0;
        int pauseDuration = 0;
        //int restDuration = 0;
        int eatingDuration = 0;
        int meetingDuration = 0;
        int trainingDuration = 0;
        int callTotalDuration = 0;
        long loginDuration = 0;
        for (ReportWorkStatusSummaryVO vo : summaryPage.getRecords()) {
            // 话后处理总时长
            if (StringUtils.isNotBlank(vo.getWrapUpDuration())) {
                int wrapUpDurationItem = DateUtils.getSeconds(vo.getWrapUpDuration());
                wrapUpDuration += wrapUpDurationItem;
            }
            //忙碌时长
            if(StringUtils.isNotBlank(vo.getPauseDuration())){
                int pauseDurationItem = DateUtils.getSeconds(vo.getPauseDuration());
                pauseDuration += pauseDurationItem;
            }
            //小休时长
            *//*if(StringUtils.isNotBlank(vo.getRestDuration())){
                int restDurationItem = DateUtils.getSeconds(vo.getRestDuration());
                restDuration += restDurationItem;
            }*//*
            //午餐时长
            if(StringUtils.isNotBlank(vo.getEatingDuration())){
                int eatingDurationItem = DateUtils.getSeconds(vo.getEatingDuration());
                eatingDuration += eatingDurationItem;
            }
            //会议时长
            if(StringUtils.isNotBlank(vo.getMeetingDuration())){
                int meetingDurationItem = DateUtils.getSeconds(vo.getMeetingDuration());
                meetingDuration += meetingDurationItem;
            }
            //培训时长
            if(StringUtils.isNotBlank(vo.getTrainingDuration())){
                int trainingDurationItem = DateUtils.getSeconds(vo.getTrainingDuration());
                trainingDuration += trainingDurationItem;
            }
            if (StringUtils.isNotBlank(vo.getCallTotalDuration())) {
                int callTotalDurationItem = DateUtils.getSeconds(vo.getCallTotalDuration());
                callTotalDuration += callTotalDurationItem;
            }
            if (vo.getLogoutTime() != null && vo.getLoginTime() != null) {
                // 工作总时长
                long loginDurationItem = getWorkDuration(vo);
                loginDuration += loginDurationItem;
            }
        }
        result.put("工时利用率", CommonUtil.calcPercentageByPercent((long) (wrapUpDuration + pauseDuration + callTotalDuration + eatingDuration + meetingDuration + trainingDuration), loginDuration));*/
    }

    private int getWorkDuration(ReportWorkStatusSummaryVO vo) {
        if (vo.getLogoutTime() != null && vo.getLoginTime() != null) {
            // 工作总时长
            LocalDateTime logOutTime = LocalDateTime.parse(vo.getLogoutTime(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT));
            LocalDateTime loginTime = LocalDateTime.parse(vo.getLoginTime(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT));
            return (int) (Duration.between(loginTime, logOutTime).toMillis() / 1000);
        } else {
            return 0;
        }
    }

    @Override
    public List<AgentEfficientVO> queryAgentEfficient() {

        List<AgentEfficientVO> list = new ArrayList<>();

        // 查询维度呼入组通话数据->2023.11.02添加北海呼入组的员工统计
        //JSONArray jsonArray = queryWdhrzCallData();
        JSONArray jsonArray = queryCallData();
        
        if (CollectionUtils.isEmpty(jsonArray)) {
            return list;
        }

        // 查询维度呼入组的提单时间总长->2023.11.02添加北海呼入组的员工统计
        //List<ReportWorkStatusSummaryVO> submitOrderDurationList = queryTodaySubmitOrderDurationGroupByStaff(WDHRZ);
        List<ReportWorkStatusSummaryVO> submitOrderDurationList = queryTodaySubmitOrderDurationGroupByStaffs();
        Map<String, Integer> cnoOrderDurationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(submitOrderDurationList)) {
            cnoOrderDurationMap = submitOrderDurationList.stream().collect(Collectors.toMap(ReportWorkStatusSummaryVO::getCno, ReportWorkStatusSummaryVO::getSubmitOrderDuration));
        }
        
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject agentStatistic = jsonArray.getJSONObject(i);
            AgentEfficientVO vo = new AgentEfficientVO();
            vo.setName(agentStatistic.getString("name"));
            // 接待量为座席来电接听数
            vo.setIbAnsweredCount(agentStatistic.getInteger("ib_answered_count"));
            // 呼入坐席通话时长
            Long ibBridgeTime = agentStatistic.getLong("ib_bridge_time");
            // 通话次数
            Integer ibBridgeCount = agentStatistic.getInteger("ib_bridge_count");
            if (Objects.nonNull(ibBridgeCount) && ibBridgeCount != 0) {
                vo.setAvgCallTime(ibBridgeTime / ibBridgeCount);
            }

            // 呼入整理时长
            Long ibWrapupTime = agentStatistic.getLong("ib_wrapup_time");
            // 登陆时长
            Long loginTime = agentStatistic.getLong("login_time");
            // 提交工单时长
            String cno = agentStatistic.getString("cno");
            Integer orderDuration = cnoOrderDurationMap.get(cno);
            if (Objects.isNull(orderDuration)){
                orderDuration = 0;
            }
            // 工时利用率
            vo.setWorkUtilizationRate(CommonUtil.calcPercentageByLong(ibWrapupTime + ibBridgeTime + orderDuration, loginTime));
            list.add(vo);
        }
        // 剔除平均呼入时常为0的统计数据
        list.removeIf(t -> null == t.getAvgCallTime() || 0 == t.getAvgCallTime());
        return list;
    }

    private List<ReportWorkStatusSummaryVO> queryTodaySubmitOrderDuration(String groupCode) {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setGroupCode(groupCode);
        dto.setStartTime(DateUtil.dateToString(DateUtils.getStartOfToday()));
        dto.setEndTime(DateUtil.dateToString(DateUtils.getEndOfToday()));
        return opStaffExtraStatusLogMapper.selectSubmitOrderTime(dto);
    }

    private List<ReportWorkStatusSummaryVO> queryTodaySubmitOrderDurationGroupByStaff(String groupCode) {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setGroupCode(groupCode);
        dto.setStartTime(DateUtil.dateToString(DateUtils.getStartOfToday()));
        dto.setEndTime(DateUtil.dateToString(DateUtils.getEndOfToday()));
        return opStaffExtraStatusLogMapper.selectSubmitOrderTimeGroupByStaff(dto);
    }

    private List<ReportWorkStatusSummaryVO> queryTodaySubmitOrderDurationGroupByStaffs() {
        ReportWorkStatusDTO dto = new ReportWorkStatusDTO();
        dto.setGroupListCodes(groupCodes);
        dto.setStartTime(DateUtil.dateToString(DateUtils.getStartOfToday()));
        dto.setEndTime(DateUtil.dateToString(DateUtils.getEndOfToday()));
        return opStaffExtraStatusLogMapper.selectSubmitOrderTimeGroupByStaffs(dto);
    }

    /**
     * 查询维度呼入组通话数据
     * @return
     */
    private JSONArray queryWdhrzCallData() {
        String cnosStr = queryWdhrzCons();
        if (StringUtils.isBlank(cnosStr)){
            return new JSONArray();
        }
        Map<String, String> params = new HashMap<>();
        params.put("cnos", cnosStr);
        params.put("fields", Joiner.on(",").join(AGENT_STATISTIC_QUERY_FIELDS));
        params.put("limit", "500");
        return trUtil.queryMonitorStatisticsAgent(params);
    }

    /**
     * 查询维度呼入组与北海呼入组通话数据
     * @return
     */
    private JSONArray queryCallData() {
        String cnosStr = queryCons();
        if (StringUtils.isBlank(cnosStr)){
            return new JSONArray();
        }
        Map<String, String> params = new HashMap<>();
        params.put("cnos", cnosStr);
        params.put("fields", Joiner.on(",").join(AGENT_STATISTIC_QUERY_FIELDS));
        params.put("limit", "500");
        return trUtil.queryMonitorStatisticsAgent(params);
    }

    private JSONArray queryCallDataByCnos(List<String> cnos) {
        if (CollectionUtils.isEmpty(cnos)){
            return new JSONArray();
        }
        Map<String, String> params = new HashMap<>();
        params.put("cnos", Joiner.on(",").join(cnos));
//        params.put("fields", Joiner.on(",").join(AGENT_STATISTIC_QUERY_FIELDS));
        params.put("limit", "500");
        return trUtil.queryMonitorStatisticsAgent(params);
    }


    private String queryCons() {
        // 只需要查维度呼入组的员工->2023.11.02添加北海呼入组的员工统计
        List<String> cnos = inAuthCrmStaffMapper.queryCnosByGroups(groupCodes);
        if (CollectionUtils.isEmpty(cnos)) {
            log.warn("queryAgentEfficient 组内无在职员工:{}", BHRUZ);
            return "";
        }
        // 过滤掉为空的记录
        cnos = cnos.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return Joiner.on(",").join(cnos);
    }

    private String queryWdhrzCons() {
        // 只需要查维度呼入组的员工
        List<String> cnos = inAuthCrmStaffMapper.queryCnosByGroup(BHRUZ);
        if (CollectionUtils.isEmpty(cnos)) {
            log.warn("queryAgentEfficient 组内无在职员工:{}", BHRUZ);
            return "";
        }
        // 过滤掉为空的记录
        cnos = cnos.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return Joiner.on(",").join(cnos);
    }

    @Override
    public JSONObject queryRepeatData() {
        JSONObject result = new JSONObject();

        // 分时查询总转人工服务数
        setTotalHourManCount(result);

        // 分时查询重复来电数
        setHourRepeatCount(result);

        return result;
    }

    private void setHourRepeatCount(JSONObject result) {
        List<CountVO> countVOList = conPhoneCallInfoMapper.queryRepeatCountHour();
        for (CountVO countVO : countVOList) {
            String hour = countVO.getName();
            int intHour = Integer.parseInt(hour);
            /*if (intHour % 2 == 0) {
                intHour = intHour - 1;
            }*/
            JSONObject callInfo = result.getJSONObject(String.valueOf(intHour));
            callInfo.put("1小时内重复进线量", countVO.getCnt());
            callInfo.put("重复进线率",CommonUtil.calcPercentage(countVO.getCnt(), callInfo.getInteger("总转人工服务数")));

            result.put(String.valueOf(intHour), callInfo);
        }
    }

    private void setTotalHourManCount(JSONObject result) {
        Map<String, String> params = new HashMap<>();
        JSONArray queueStatistics = trUtil.queryMonitorQueueDetailStatistic(params);
        if (CollectionUtils.isEmpty(queueStatistics)) {
            return;
        }

        for (int i = 0; i < queueStatistics.size(); i++) {
            JSONObject hoursJson = queueStatistics.getJSONObject(i);
            for (String hour : hoursJson.keySet()) {
                Integer enterCount = TrUtil.getIntegerFromJsonObject(hoursJson.getJSONObject(hour), "enter_count");
                int intHour = Integer.parseInt(hour);
                /*if (intHour % 2 == 0) {
                    intHour = intHour - 1;
                }
                if (intHour < 0){
                    continue;
                }*/
                JSONObject callInfo = result.getJSONObject(String.valueOf(intHour));
                if (Objects.isNull(callInfo)) {
                    callInfo = new JSONObject();
                    callInfo.put("总转人工服务数", enterCount);
                } else {
                    int enterCount2 = enterCount + callInfo.getInteger("总转人工服务数");
                    callInfo.put("总转人工服务数", enterCount2);
                }
                result.put(String.valueOf(intHour), callInfo);
            }
        }
    }

    private void queryHotlineCallInfoCount(JSONObject result) {
        JSONArray trunkStatistics = trUtil.queryTRTrunkStatistic(new HashMap<>());
        if (CollectionUtils.isEmpty(trunkStatistics)) {
            return;
        }
        for (int i = 0; i < trunkStatistics.size(); i++) {
            JSONObject jsonObject = trunkStatistics.getJSONObject(i);
            Integer totalCount = TrUtil.getIntegerFromJsonObject(jsonObject, "ib_total_count");
            switch (jsonObject.getString("hotline")) {
                case "4006000799":
                    result.put("电商业务", totalCount);
                    break;
                case "10100518":
                    result.put("现金贷", totalCount);
                    break;
                case "4006040888":
                    result.put("钱夹谷谷", totalCount);
                    break;
                case "4000666730":
                    result.put("嘉纷理财", totalCount);
                    break;
                default:
                    break;
            }
        }
    }

    private void queryFirstQuestionSolveRate(JSONObject result) {
        List<CountVO> countVOS = conPhoneCallInfoMapper.queryRepeatCount();
        int repeatCount = 0;
        int totalCount = 0;
        for (CountVO countVO : countVOS) {
            if ("totalCount".equals(countVO.getName())) {
                totalCount = countVO.getCnt();
                continue;
            }
            if ("repeatCount".equals(countVO.getName())) {
                repeatCount = countVO.getCnt();
            }
        }

        // 首问解决率 = (来电接起总量-2小时重复来电数)/来电接起总量
        result.put("首问解决率", CommonUtil.calcPercentage(totalCount - repeatCount, totalCount));
    }

    private void querySatisfaction(JSONObject result) {
        Map<String, String> params = new HashMap<>();
        JSONArray investigationStatistics = trUtil.queryMonitorStatisticsInvestigation(params);
        if (CollectionUtils.isEmpty(investigationStatistics)) {
            return;
        }
        // 非常满意数量
        int vsNum = 0;
        // 满意数量
        int sNum = 0;
        // 总评价数
        int totalNum = 0;
        for (int i = 0; i < investigationStatistics.size(); i++) {
            // 按键为1表示非常满意
            Integer key1 = TrUtil.getIntegerFromJsonObject(investigationStatistics.getJSONObject(i), "key_1");
            // 按键为2标识满意
            Integer key2 = TrUtil.getIntegerFromJsonObject(investigationStatistics.getJSONObject(i), "key_2");
            // 总按键数
            Integer keyPress = TrUtil.getIntegerFromJsonObject(investigationStatistics.getJSONObject(i), "key_press");
            vsNum += key1;
            sNum += key2;
            totalNum += keyPress;
        }
        result.put("满意度", CommonUtil.calcPercentage(vsNum + sNum, totalNum));

    }


    @Override
    public PersonalPanelVO queryPersonalPanelVO() {
        PersonalPanelVO panelVO = new PersonalPanelVO();

        // 1、个人面板汇总数据
        OverAllData overAllData = new OverAllData();
        Map<String, String> params = new HashMap<>();
        overAllData.setQueuedUsersNumber(queryRealTimeQueues(params));
        overAllData.setFreeAgentNumber(Optional.ofNullable(queryAgentWorkOverview().getFreeNumber()).orElse(0));
        params.clear();
        CallInfoTotalData callInfoTotalData = queryCalInfoTotalData(params);
        overAllData.setAnswerRate(callInfoTotalData.getAnswerRate());

        // 2、个人工时利用率数据
        HourUtilisation hourUtilisation = new HourUtilisation();

        // 3、个人效能数据
        IndividualEffectiveness effectiveness = new IndividualEffectiveness();
        // 查询工号
        String loginName = CommonUtils.getCurrentlogged();
        List<InPhoneLoginInfo> phoneLoginInfos = inPhoneLoginInfoMapper
            .selectList(Wrappers.lambdaQuery(InPhoneLoginInfo.class).eq(InPhoneLoginInfo::getUserTel, loginName));

        String todayStart = DateUtil.dateToString(DateUtils.getStartOfToday());
        String todayEnd = DateUtil.dateToString(DateUtils.getEndOfToday());
        if (CollectionUtils.isNotEmpty(phoneLoginInfos)) {
            // 工号
            String cno = phoneLoginInfos.get(0).getIdNo();
            ReportWorkStatusDTO workStatusDTO = new ReportWorkStatusDTO();
            workStatusDTO.setCurrentPage(1);
            workStatusDTO.setRowsPerPage(5000);
            workStatusDTO.setStartTime(todayStart);
            workStatusDTO.setEndTime(todayEnd);
            workStatusDTO.setPeriod("day");
//            workStatusDTO.setGroupCode(BHRUZ);
            workStatusDTO.setStaffId(CommonUtils.getCurrentloggedStaffId());
            List<ReportWorkStatusSummaryVO> workStatusSummaryVOList =
                reportService.listWorkStatusSummary(workStatusDTO);
            if (CollectionUtils.isNotEmpty(workStatusSummaryVOList)) {
                ReportWorkStatusSummaryVO workStatusSummaryVO = workStatusSummaryVOList.get(0);
                hourUtilisation.setLoginDuration(workStatusSummaryVO.getLoginDuration());
                hourUtilisation.setBusyDuration(workStatusSummaryVO.getPauseDuration());
                hourUtilisation.setCallDuration(workStatusSummaryVO.getCallTotalDuration());
                hourUtilisation.setHourUtilisation(workStatusSummaryVO.getWorkingHourUtilization());
                effectiveness.setAnswerNumber(workStatusSummaryVO.getCallInConnectedNumber());
                effectiveness.setSatisfiedRate(workStatusSummaryVO.getSatisfiedRate());
            }

            // 当天北海呼入组出席人数
            Integer todayAttendNumber = conPhoneCallInfoMapper.queryTodayAttendNumber(Collections.singletonList(BHRUZ));
            if (Objects.nonNull(todayAttendNumber) && !todayAttendNumber.equals(0)) {
                effectiveness.setCallInNumber(callInfoTotalData.getToQueueCount() / todayAttendNumber);
                effectiveness.setAnswerRate(
                    CommonUtil.calcPercentage(effectiveness.getAnswerNumber(), effectiveness.getCallInNumber()));

                // 首问解决率
                RepeatCallDTO repeatCallDTO = new RepeatCallDTO();
                repeatCallDTO.setHotline("10100518");
                repeatCallDTO.setStartTime(todayStart);
                repeatCallDTO.setEndTime(todayEnd);
                repeatCallDTO.setCnos(Collections.singletonList(cno));
                List<ReportResolvedRateVO> resolvedRateVOList = conRepeatCallService.queryRecordList(repeatCallDTO);
                if (CollectionUtils.isNotEmpty(resolvedRateVOList)) {
                    effectiveness.setResolvedRate(resolvedRateVOList.get(0).getResolvedRate());
                }
            }
        }

        panelVO.setOverAllData(overAllData);
        panelVO.setHourUtilisation(hourUtilisation);
        panelVO.setIndividualEffectiveness(effectiveness);

        return panelVO;
    }
    
}
