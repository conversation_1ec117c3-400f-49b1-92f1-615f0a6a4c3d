package com.welab.crm.operate.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 钱包电销表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_wallet")
public class TmkWallet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电销任务唯一Id(wallet*******)
     */
    private String tmkTaskId;

    /**
     * 用户UUID
     */
    private String uuid;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private String gender;

    /**
     * 身份证号
     */
    private String cnid;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 状态标识
     */
    private String flag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 贷款状态
     */
    private String state;

    /**
     * 注册渠道
     */
    private String regOrigin;

    /**
     * 申请渠道
     */
    private String applyOrigin;

    /**
     * 申请时间
     */
    private Date appliedAt;

    /**
     * 审批时间
     */
    private Date approvedAt;

    /**
     * 学历
     */
    private String education;

    /**
     * 信用额度
     */
    private BigDecimal creditLine;

    /**
     * 可用额度
     */
    private BigDecimal avlCredit;

    /**
     * 第一次消费时间
     * 第一次消费时间
     */
    private Date firstConsuDate;

    /**
     * 消费金额
     */
    private BigDecimal consuAmount;

    /**
     * 是否消费
     */
    private String isConsu;

    /**
     * 第一次消费成功时间
     */
    private Date firstConsuSuccDate;

    /**
     * 员工Id
     */
    private Long staffId;

    /**
     * 注册时间
     */
    private Date registerAt;


    /**
     * 最新联系小结Id
     */
    private Long summaryId;

    /**
     * 分配时间
     */
    private Date assignDate;

    /**
     * 额度状态
     */
    private String creditState;


    /**
     * 产品名称
     */
    private String productCode;

    /**
     * 拨打次数
     */
    private Integer callNum;
}
