/**
 * @Title: ICrmColOrgService.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service;

import java.util.List;
import java.util.Map;

import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColOrgReqDTO;
import com.welab.crm.operate.vo.staff.ColOrgInfoResVO;

/**
 * @description 阻止service
 * <AUTHOR>
 * @date 2021-11-03 15:58:26
 * @version v1.0
 */
public interface ICrmOrgService {
    
    /**  
     * 添加组
     * @param dto
     * @return  
     */
    public void addColOrgInfo(ColOrgReqDTO dto);

    /**  
     * 删除组 
     * @param reqDTO  
     */
    public void deleteColOrgInfo(BatchDictInfoReqDTO reqDTO);

    /**  
     * 查询组织树
     * @param reqDTO
     * @return  
     */
    public  List<ColOrgInfoResVO> updateColOrgInfoList(ColOrgReqDTO reqDTO);

    /**  
     * 更新组 
     * @param reqDTO  
     */
    public void updateColOrg(ColOrgReqDTO reqDTO);

    /**  
     * 获取关系范围组
     * @param code
     * @return  
     */
    String getManagerOrgList(String code);

    /**  
     * 根据code获取组name
     * @param code
     * @return  
     */
    public String getOrgNameByCode(String code);

    /**  
     * 获取组下拉框值
     * @param code
     * @return  
     */
    public List<ColOrgInfoResVO> getOrgSelectList(String code);

    public List<String> getManagerOrgs(String code);

    public List<String> getManagerOaOrgs(String code);

    /**
     * 是否管理组
     * @param code
     * @return
     */
    public boolean isManaGroup(String code);


    /**
     * 获取全部组别map，key 为 groupCode，value 为 groupName
     */
    Map<String,String> getGroupCodeNameMap();

    /**
     * 同步全部组织到考试系统
     */
    void syncAllOrgToExamSystem();

    
    ColOrgInfoResVO getOrgInfoByCode(String code);
}
