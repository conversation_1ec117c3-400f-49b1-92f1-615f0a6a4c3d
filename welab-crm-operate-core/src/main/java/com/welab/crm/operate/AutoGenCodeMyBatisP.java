package com.welab.crm.operate;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

import com.welab.common.config.DefaultConfigService;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.FileOutConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

/**
 * mybatis代码生成器，默认生成mapper.xml，mapper.java，domain对象。
 * 
 * Created by Bryce Yao on 2021-08-13.
 */
public class AutoGenCodeMyBatisP {

    public static void main(String[] args) {
        // 加载配置
        DefaultConfigService config = new DefaultConfigService("welab-crm-operate", "server.properties");
        config.init();

        /******** 要自动生成的表名 *******/
        String[] scannerT = "external_complaint_order".split(",");

        /******** 重新生成时文件是否覆盖 *******/
        boolean overrideFlag = true;

        String packagePath = "com.welab.crm.operate";

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir") + "/welab-crm-operate-core";
        gc.setOutputDir(projectPath + "\\src\\main\\java");
        gc.setAuthor("工具生成,请勿手动修改");
        gc.setOpen(true);
        gc.setFileOverride(overrideFlag);
        gc.setDateType(DateType.ONLY_DATE); // 定义生成的实体类中日期类型
        gc.setBaseResultMap(true);
        gc.setBaseColumnList(true);
        // gc.setSwagger2(true); // 实体属性 Swagger2 注解
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(config.getProperty("jdbc.url"));
        // dsc.setSchemaName("public");
        dsc.setDriverName(config.getProperty("jdbc.driver"));
        dsc.setUsername(config.getProperty("jdbc.username"));
        dsc.setPassword(config.getProperty("jdbc.password"));
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        // 此处要注意：parent + moduleName 为包的名字，在这个包下，创建对应的controller
        pc.setParent(packagePath);
        pc.setModuleName("");// 模块名
        pc.setController("controller");//
        pc.setService("service");
        pc.setServiceImpl("service.impl");
        pc.setEntity("domain");
        pc.setMapper("mapper");
        mpg.setPackageInfo(pc);

        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        // 如果模板引擎是 freemarker
        String templatePath = "/templates/mapper.xml.ftl";
        // 如果模板引擎是 velocity
        // String templatePath = "/templates/mapper.xml.vm";

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 自定义配置会被优先输出
        focList.add(new FileOutConfig(templatePath) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/src/main/resources/mapper/" + pc.getModuleName() + "/"
                        + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        /*
         * cfg.setFileCreate(new IFileCreate() {
         * 
         * @Override public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String
         * filePath) { // 判断自定义文件夹是否需要创建 checkDir("调用默认方法创建的目录，自定义目录用"); if (fileType ==
         * FileType.MAPPER) { // 已经生成 mapper 文件判断存在，不想重新生成返回 false return !new
         * File(filePath).exists(); } // 允许生成模板文件 return true; } });
         */
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();

        // 配置自定义输出模板
        // 指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
        // templateConfig.setEntity("templates/entity2.java");
        // templateConfig.setService();
        // templateConfig.setController();

        templateConfig.setXml(null);// 不生成默认路径下的xml
        templateConfig.setController(null);// 不生成默认路径下的Controller
        templateConfig.setServiceImpl(null);// 不生成默认路径下的ServiceImpl
        templateConfig.setService(null);// 不生成默认路径下的Service
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        // strategy.setSuperEntityClass("你自己的父类实体,没有就不用设置!");
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        // 公共父类
        // strategy.setSuperControllerClass("你自己的父类控制器,没有就不用设置!");
        // 写于父类中的公共字段
//        strategy.setSuperEntityColumns("id");
        strategy.setInclude(scannerT);
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setTablePrefix(pc.getModuleName() + "_");

        strategy.setTableFillList(Arrays.asList(
                new TableFill("id", FieldFill.INSERT),
                new TableFill("gmt_create", FieldFill.INSERT),
                new TableFill("gmt_modify", FieldFill.INSERT_UPDATE)
        ));
        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        mpg.execute();
    }
}
