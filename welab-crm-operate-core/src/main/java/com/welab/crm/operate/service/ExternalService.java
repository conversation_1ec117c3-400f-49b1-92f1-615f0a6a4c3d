package com.welab.crm.operate.service;

import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.finance.loanprocedure.dubbo.LoanDubboService;
import com.welab.finance.loanprocedure.enums.LoanStateEnum;
import com.welab.finance.loanprocedure.vo.LoanVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Date：2025/4/27 11:36
 */
@Slf4j
@Service
public class ExternalService {

    @Autowired
    private LoanDubboService loanDubboService;
    @Autowired
    private ICrmOrgStaffService crmOrgStaffService;

    public static final List<String> CLOSED = Arrays.asList("closed", "early_settled");
    public boolean isClosedIn3Year(String applicationId) {
        Response<LoanVO> loanByAppId = loanDubboService.findLoanByAppId(applicationId);
        if (Response.isSuccess(loanByAppId) && null != loanByAppId.getResult()) {
            LoanVO loanVO = loanByAppId.getResult();
            if (CLOSED.contains(loanVO.getStatus())
                    && DateUtil.plusYears(loanVO.getClosedAt(), 3).before(new Date())) {
                log.info("结清时间{}在3年前", DateUtil.dateToString(loanVO.getClosedAt()));
                return false;
            }
        }
        return true;
    }

    public boolean isSpecificGroup(String mobile, List<String> groupList){
        ColStaffReqDTO reqDTO = new ColStaffReqDTO();
        reqDTO.setStaffMobile(mobile);
        List<ColStaffResVO> colStaffList = crmOrgStaffService.getColStaffList(reqDTO);
        if(CollectionUtils.isEmpty(colStaffList)){
            log.info("{}不在任何组",mobile);
            return false;
        }
        ColStaffResVO colStaffResVO = colStaffList.get(0);
        log.info("{}在{}", mobile, colStaffResVO.getGroupName());
        Optional<String> first = groupList.stream().filter(group -> colStaffResVO.getGroupName().contains(group)).findFirst();
        return first.isPresent();
    }

    public boolean isClosedIn1Year(Date transTime) {
        if (null != transTime) {
            if (DateUtil.plusYears(transTime, 1).before(new Date())) {
                log.info("完成时间{}在1年前", DateUtil.dateToString(transTime));
                return false;
            }
        }
        return true;
    }
}
