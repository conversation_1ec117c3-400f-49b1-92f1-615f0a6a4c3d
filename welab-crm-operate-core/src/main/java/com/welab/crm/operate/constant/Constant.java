package com.welab.crm.operate.constant;

/**
 * <AUTHOR>
 * @date 2021/10/29 18:03
 */
public class Constant {


    /**
     * 消息id前缀
     */
    public static final String NOTICE_PRE = "msg";

    /**
     * 消金贷用户类型
     */
    public static final String USER_TYPE_CASH = "cash";


    /**
     * 钱包用户类型
     */
    public static final String USER_TYPE_WALLET = "wallet";


    /**
     * 时间间隔
     */
    public static final String YEAR = "year";
    public static final String MONTH = "month";
    public static final String WEEK = "week";
    public static final String DAY = "day";


    /**
     * RSA公钥
     */
    public static final String RSA_PUB_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/Cp5Np5A2CZUa1ul650/Nq29StCqCDBa3fvqHnAfW79MLH9iIa70Uh722nZM+oI4qyvRWtQkkZRVXGcfcVluzYaLm87JiZ38dB2T0fM49vNPqqh/0bmkRBFaPTBu4fUo91ubdppRJzuUfqKt8graB/0Ur4tAJvGzPbgksIr9abQIDAQAB";

    /**
     * RSA私钥
     */
    public static final String RSA_PRI_KEY = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAL8Knk2nkDYJlRrW6XrnT82rb1K0KoIMFrd++oecB9bv0wsf2IhrvRSHvbadkz6gjirK9Fa1CSRlFVcZx9xWW7NhoubzsmJnfx0HZPR8zj280+qqH/RuaREEVo9MG7h9Sj3W5t2mlEnO5R+oq3yCtoH/RSvi0Am8bM9uCSwiv1ptAgMBAAECgYA0An4Kgh3euEkhn3S9AxBU2+FY0Lfp48SjfCxUkwQHXg9ErgzTRxGY70OU3d1iZW+O8uYGXQ76G5LtvZUNxWz9NfqyKR84PI3bGL5NMCjMqL3Tozxvbie3vK/w86vs6BO7q7SYdXvdaLmUSyIx2ZFGS04YtKynqbab6fYdz0Ji+QJBAPWKPKAnOZtBlYC4D3fv07phPPvXB2JkospLKEVeUreW9kce0MHx4PL6G//m0vUHHc5PaiC3KLMQujyIZ0moSj8CQQDHLgrG/3I0zvepqjK42jZy5uSl0YLN/PE/lmmkq//QbG6Rac8qiLas0Csp870oDiYOLKcd70O3zQIaJMzrrrhTAkADIdRMJrPxaxDBJ5fq68BDrGHexItvkeERu4uz9tQPS3mwxo/bKX65Gr+osAUY4xJaQ2ETMcLvJdN6WjMXGXJ5AkEAwd+YD/DvCmX2fYg0DOgiHZ+PKbQP/U7ayEkxFV9yBj61NQdn5f/ix71sdk5QbV+wPRXLqQCOyqZbCHWPnUyZDwJBAL7p2trUTUNEc90GCei27mOY052DUPBChH07Lw/afhAH4Gub8ze2jcxDGAYaSBPov7twFp6uDzNFSIw1IJQLS0I=";


    /**
     * oppo结清文件请求URL
     */
    public static final String OPPO_SETTLEMENT_GET_URL = "/oppo-investment/api/v1/get/usersettlement";

    /**
     * 手机号码正则
     */
    public static final String MOBILE = "^1[3456789]\\d{9}$";
    
    
    public static final String ID_NO_18 = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    public static final String ID_NO_15 = "^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$";
    
    public static final long MAX_FILE_SIZE = 1024L * 1024 * 20;



    public static final String MOBILE_GROUP2 = "(1[3|4|5|7|8]\\d{9})(((\\D{1}|$)[\\s\\S]*)+)";
    public static final String ID_NO_REX = "[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]";
    
    
    public static final String NOT_COMPLETE = "未完成";
    public static final String COMPLETE = "已完成";
}
