package com.welab.crm.operate.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 电销类型常量类
 * @date 2022/2/22 15:16
 */
public class TmkTaskTypeConstant {

    /**
     * 超级会员
     */
    public static final String CJHY = "cjhy";

    /**
     * 钱夹谷谷
     */
    public static final String WALLET = "wallet";

    /**
     * 进件模式
     */
    public static final String LOAN = "loan";

    /**
     * 额度模式
     */
    public static final String CREDIT = "credit";
    
    /**
     * UUID模式
     */
    public static final String UUID = "uuid";


    public static List<String> TMK_TYPE_LIST = Collections.unmodifiableList(Arrays.asList(CJHY, WALLET, LOAN, CREDIT, UUID));

}
