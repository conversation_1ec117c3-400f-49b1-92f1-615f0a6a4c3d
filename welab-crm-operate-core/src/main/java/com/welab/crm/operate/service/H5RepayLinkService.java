package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.RepaymentCalculateDTO;
import com.welab.common.response.Response;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkQueryDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkSendDTO;
import com.welab.crm.operate.vo.repaylink.H5RepayLinkVO;
import com.welab.crm.operate.vo.repaylink.H5RepaySummaryReportVO;
import com.welab.thirdparty.partner.wld.ProxyInteractionRequest;

import java.util.List;
import java.util.Map;

/**
 * h5还款链接服务
 * 
 * <AUTHOR>
 */
public interface H5RepayLinkService {

    /**
     * 是否支持自定义还款
     * 
     * @param appNo 合同号
     * @return
     */
    Response<Map<String, Boolean>> isAllowOfflineRepayment(String appNo);

    /**
     * 根据还款模式计算还款金额
     * 
     * @param dto
     * @return
     */
    String calRepayAmount(RepaymentCalculateDTO dto);

    /**
     * 获取h5还款链接
     * 
     * @param reqDTO
     * @return
     */
    Response<String> getRepayLink(ProxyInteractionRequest reqDTO);


    /**
     * 查询h5还款链接发送列表
     * @param dto 查询参数
     * @return 发送列表
     */
    Page<H5RepayLinkVO> queryRecord(H5RepayLinkQueryDTO dto);


    /**
     * 发送h5还款链接
     * @param dto
     * @return
     */
    String sendH5RepayLink(H5RepayLinkSendDTO dto, StaffVO staffVO);


    Integer queryRepayLinkRecordSendCount(String appNo);


    /**
     * 查询h5还款链接还款统计报表
     */
    List<H5RepaySummaryReportVO> queryReportSummaryReport(ReportBaseDTO dto);
}
