package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客服人员记录表（同步认证平台）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_auth_crm_staff")
public class InAuthCrmStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 姓名
     */
    private String staffName;

    /**
     * 手机
     */
    private String staffMobile;

    /**
     * 所属组code
     */
    private String groupCode;

    /**
     * 所属组名
     */
    private String groupName;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 员工状态，休息，在线
     */
    private String staffStatus;

    /**
     * 是否管理员
     */
    private Integer isManager;

    /**
     * 记录状态，1：有效，0：无效
     */
    private Integer isStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 休息时间区间 yyyy-MM-dd HH:mm:ss~yyyy-MM-dd HH:mm:ss
     */
    private String restTime;


    /**
     * 是否上传了底照
     */
    private Boolean isUploadBasePhoto;
    
    /**
     * 登录类型
     * code- 验证码
     * face- 人脸
     */
    private String loginType;

    /**
     * 是否展示上传底照菜单
     */
    private Boolean isDisplayUploadFaceMenu;

    /**
     * 密码
     */
    private String password;

}
