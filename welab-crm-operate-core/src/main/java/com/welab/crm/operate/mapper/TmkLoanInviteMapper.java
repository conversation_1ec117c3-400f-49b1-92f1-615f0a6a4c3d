package com.welab.crm.operate.mapper;

import com.welab.crm.operate.vo.phone.CallInPhoneDetailVO;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkLoanInvite;
import com.welab.crm.operate.dto.telemarketing.TmkAssignDetailReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReportBaseReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.vo.telemarketing.AiPushReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkAssignDetailReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkLoanReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;

/**
 * <p>
 * 电销放款邀约表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
public interface TmkLoanInviteMapper extends BaseMapper<TmkLoanInvite> {

    /**
     * 根据条件查询电销任务列表
     * @param page
     * @param tmkReqDTO
     * @return
     */
    Page<TmkTaskDetailVO> queryLoanInviteTaskPage(Page<TmkTaskDetailVO> page, @Param("dto") TmkReqDTO tmkReqDTO);

    /**
     * 查询放款邀约转化数据
     * @param dto
     * @return
     */
    List<TmkTransformReportVO> queryLoanInviteTransformData(@Param("dto") TmkTransformReportReqDTO dto);

    /**
     * 查询ai外呼营销数据
     * @param dto
     * @return
     */
    Page<AiPushReportVO> queryAiPushDetail(Page<AiPushReportVO> page, @Param("dto") TmkReportBaseReqDTO dto);

    /**
     * 查询放款邀约外拨营销小结数据
     * @param dto
     * @return
     */
    Page<TmkLoanReportVO> queryTmkLoanData(Page<TmkLoanReportVO> page, @Param("dto") TmkTransformReportReqDTO dto);

    /**
     * 查询电销名单分配明细数据
     * @param dto
     * @return
     */
    Page<TmkAssignDetailReportVO> queryTmkAssignDetailData(Page<TmkAssignDetailReportVO> page,
            @Param("dto") TmkAssignDetailReportReqDTO dto);
}
