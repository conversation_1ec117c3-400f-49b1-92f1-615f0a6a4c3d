package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.DataCustomer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface DataCustomerMapper extends BaseMapper<DataCustomer> {

	List<DataCustomer> queryByUserIdList(@Param("userIdList") List<Integer> userIdList);
}
