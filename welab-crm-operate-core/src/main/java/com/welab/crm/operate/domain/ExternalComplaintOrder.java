package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 外部投诉记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("external_complaint_order")
public class ExternalComplaintOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 登录名称
     */
    private String loginName;

    /**
     * 渠道单号，多个用逗号隔开
     */
    private String channelOrderNo;

    /**
     * 借据单号，多个用逗号隔开
     */
    private String debtOrderNo;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 备用手机号，多个用逗号隔开
     */
    private String mobileBak;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 关联工单号
     */
    private String orderNo;

    /**
     * 加急标识
     */
    private String urgentFlag;

    /**
     * 回访标识
     */
    private String callbackFlag;

    /**
     * 快捷工单id；op_dict_info_conf 表id
     */
    private Long quickOrderId;

    /**
     * 快捷工单内容
     */
    private String quickOrder;

    /**
     * 投诉渠道
     */
    private String complaintsChannel;

    /**
     * 资方简称
     */
    private String fundName;

    /**
     * 工单类型
     */
    private String type;

    /**
     * 工单大类
     */
    private Long orderOneClass;

    /**
     * 工单二类
     */
    private Long orderTwoClass;

    /**
     * 工单三类
     */
    private Long orderThreeClass;

    /**
     * 工单子类
     */
    private Long orderCase;

    /**
     * 反馈内容
     */
    private String description;

    /**
     * 用户类型;source-渠道，partner-资金方
     */
    private String userType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
