package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpReduceApplyRecord;
import com.welab.crm.operate.dto.reduce.RecordQueryDTO;
import com.welab.crm.operate.dto.reduce.ReduceReportDTO;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundVO;
import com.welab.crm.operate.vo.reduce.ReduceDetailReportVO;
import com.welab.crm.operate.vo.reduce.ReduceStatisticsReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 减免申请记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface OpReduceApplyRecordMapper extends BaseMapper<OpReduceApplyRecord> {

	/**
	 * 查询申请记录
	 */
	Page<ReduceAndRefundVO> selectApplyRecordPage(Page<Object> page, @Param("dto") RecordQueryDTO dto);

	/**
	 * 查询申请记录
	 */
	List<ReduceAndRefundVO> selectApplyRecordPage(@Param("dto") RecordQueryDTO dto);

	/**
	 * 分页查询减免退款明细
	 */
	Page<ReduceDetailReportVO> selectReduceRefundDetailPage(Page<Object> objectPage, @Param("dto") ReduceReportDTO dto);

	/**
	 * 查询减免退款统计数据
	 */
	Page<ReduceStatisticsReportVO> queryStatisticData(Page<Object> objectPage, @Param("dto") ReduceReportDTO dto);

	/**
	 * 查询所有申请人
	 */
	List<String> selectApplyRecordApplyName();
}
