package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 重复来电表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_repeat_call")
public class ConRepeatCall implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 热线
     */
    private String hotline;

    /**
     * 坐席工号
     */
    private String cno;

    /**
     * 员工id
     */
    private String staffId;

    /**
     * 客户手机号
     */
    private String mobile;

    /**
     * 2小时重复来电数
     */
    private Integer repeatNumber;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
