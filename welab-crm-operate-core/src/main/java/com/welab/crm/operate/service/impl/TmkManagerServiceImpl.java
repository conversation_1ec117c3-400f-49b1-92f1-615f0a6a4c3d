package com.welab.crm.operate.service.impl;

import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkAssignHistory;
import com.welab.crm.operate.dto.tmkManager.TmkAssignReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerAdjustReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkManager.TotalTmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustInfo;
import com.welab.crm.operate.dto.workorder.AdjustWorkOrderInfo;
import com.welab.crm.operate.enums.TmkDistributionTypeEnum;
import com.welab.crm.operate.mapper.TmkAssignHistoryMapper;
import com.welab.crm.operate.mapper.TmkRuleInfoMapper;
import com.welab.crm.operate.service.TmkManagerService;
import com.welab.crm.operate.service.TmkTaskService;
import com.welab.crm.operate.vo.tmkManager.TmkAssignVO;
import com.welab.crm.operate.vo.tmkManager.TmkManagerVO;
import com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 电销数据分配规则service服务
 * <AUTHOR>
 * @date 2022-02-24
 */
@Slf4j
@Service
public class TmkManagerServiceImpl implements TmkManagerService {

    @Resource
    private TmkRuleInfoMapper tmkRuleInfoMapper;

    @Resource
	private TmkAssignHistoryMapper tmkAssignHistoryMapper;
    
    @Resource
	private TmkTaskService tmkTaskService;

    @Resource
	private NoticeMsgServiceImpl noticeMsgService;

    @Override
    public Page<TmkManagerVO> queryTmkManagerList(TmkManagerReqDTO reqDTO) {
    	
        log.info("queryTmkInfoList,reqDTO:{}", JSON.toJSONString(reqDTO));
		CommonUtils.checkTimeThan31(reqDTO.getDistributeStartDate(),reqDTO.getDistributeEndDate());

		Page<TmkManagerVO> pages = tmkRuleInfoMapper
				.queryTmkManagerByPage(new Page<TmkManagerVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
		// 手机号脱敏
		if (Objects.nonNull(pages)){
			List<TmkManagerVO> records = pages.getRecords();
			if (CollectionUtils.isNotEmpty(records)){
				records.forEach(item -> item.setMobile(SecurityUtil.maskMobile(item.getMobile())));
			}
		}

		return pages;
	}
    
    @Override
    public Page<TotalTmkManagerVO> totalTmkManager(TmkManagerReqDTO reqDTO) {
    	CommonUtils.checkTimeThan31(reqDTO.getDistributeStartDate(),reqDTO.getDistributeEndDate());
        return tmkRuleInfoMapper.totalTmkManager(new Page<TmkManagerVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
    }
    
    @Override
    public void adjustTmkManager(TmkManagerAdjustReqDTO reqDTO) {
		//指定要分配的工单
		List<TmkRuleAdjustInfo> taskList = reqDTO.getTaskList();
		//如果是根据数量再分配
		if (CollectionUtils.isEmpty(taskList)) {
			List<TotalTmkManagerVO> adjustList = reqDTO.getAssignList();
			if(CollectionUtils.isNotEmpty(adjustList)) {
				TmkManagerReqDTO tmkReqDto = new TmkManagerReqDTO();
				List<String> groupList = new ArrayList<String>();
				List<String> staffIdList = new ArrayList<String>();
				for(TotalTmkManagerVO info : adjustList) {
					groupList.add(info.getGroupCode());
					staffIdList.add(info.getStaffId());
				}
				tmkReqDto.setGroupCode(groupList);
				tmkReqDto.setStaffId(staffIdList);
				BeanUtils.copyProperties(reqDTO,tmkReqDto,"groupCode","staffId");
				
				Page<TmkManagerVO> pageList = tmkRuleInfoMapper
                .queryTmkManagerByPage(new Page<TmkManagerVO>(1, 100000), tmkReqDto);
				
				if(pageList != null && pageList.getRecords() != null 
						&& pageList.getRecords().size() > 0) {
					taskList = new ArrayList<TmkRuleAdjustInfo>();
					for(TmkManagerVO tmkVo : pageList.getRecords()) {
						TmkRuleAdjustInfo tmkRuleInfo = new TmkRuleAdjustInfo();
						tmkRuleInfo.setTmkTaskId(tmkVo.getTmkTaskId());
						tmkRuleInfo.setTmkType(tmkVo.getTmkType());
						tmkRuleInfo.setCustomerName(tmkVo.getCustomerName());
						tmkRuleInfo.setMobile(tmkVo.getMobile());
						tmkRuleInfo.setApplicationId(tmkVo.getApplicationId());
						tmkRuleInfo.setGroupCode(tmkVo.getGroupCode());
						tmkRuleInfo.setStaffId(tmkVo.getStaffId());
						taskList.add(tmkRuleInfo);
					} 
				}
			}
		}
		log.info("adjustTmkRule taskList size:{}", taskList.size());
		//当前是第几条工单
		int curOrderNum = 1;
		String operatorId = reqDTO.getOperatorId();
		List<AdjustWorkOrderInfo> adjustList = reqDTO.getAdjustList();
		// 保存分配的业务类型的list
		Set<String> tmkTypeSet = new HashSet<>();
		for (AdjustWorkOrderInfo adjustInfo : adjustList) {
			// 业务代码分配坐席 与任务
			while (taskList.size() > 0) {
				TmkRuleAdjustInfo taskInfo = taskList.get(0);
				
				//调用流程接口完成分单操作
				tmkTaskService.updateLatestResultCode(taskInfo.getTmkType(), taskInfo.getTmkTaskId(), 
						 null, Long.parseLong(adjustInfo.getAssignStaffId()), "2");
				// 记录分单历史
				Date now = new Date();
				TmkAssignHistory tmkAssignHistory = new TmkAssignHistory();
				tmkAssignHistory.setTmkTaskId(taskInfo.getTmkTaskId());
				tmkAssignHistory.setGroupCode(adjustInfo.getAssignGroupCode());
				tmkAssignHistory.setStaffId(adjustInfo.getAssignStaffId());
				tmkAssignHistory.setCreateStaffId(operatorId);
				tmkAssignHistory.setDistributionTime(now);
				tmkAssignHistory.setGmtCreate(now);
				tmkAssignHistory.setGmtModify(now);
				tmkAssignHistory.setDistributionType(TmkDistributionTypeEnum.REDISTRIBUTION.getValue());
				tmkAssignHistory.setMobile(taskInfo.getMobile());
				tmkAssignHistory.setUsername(taskInfo.getCustomerName());
				tmkAssignHistory.setApplicationId(taskInfo.getApplicationId());
				tmkAssignHistory.setCreateGroupCode(reqDTO.getOperatorGroupCode());
				tmkAssignHistory.setPreGroupCode(taskInfo.getGroupCode());
				tmkAssignHistory.setPreStaffId(taskInfo.getStaffId());
				tmkAssignHistory.setUuid(taskInfo.getUuid());
				tmkAssignHistory.setUserId(taskInfo.getUserId());
				tmkAssignHistory.setProductName(taskInfo.getProductName());
				tmkAssignHistory.setApplyOrigin(taskInfo.getApplyOrigin());
				tmkAssignHistoryMapper.insert(tmkAssignHistory);
				taskList.remove(0);
				curOrderNum++;
				tmkTypeSet.add(taskInfo.getTmkType());
				if (curOrderNum > adjustInfo.getAssignNum()) {
					log.info("adjustTmkRule curOrderNum:{}", curOrderNum);
					noticeMsgService.publishTmkMsg(tmkTypeSet,curOrderNum - 1, adjustInfo.getAssignStaffId());
					curOrderNum = 1;
					tmkTypeSet.clear();
					break;
				}
			}
		}
	}
    
    @Override
    public Page<TmkAssignVO> queryTmkAssignList(TmkAssignReqDTO reqDTO) {
    
        log.info("queryTmkAssignList,reqDTO:{}", JSON.toJSONString(reqDTO));
		CommonUtils.checkTimeThan31(reqDTO.getDistributeStartDate(),reqDTO.getDistributeEndDate());

		Page<TmkAssignVO> pages = tmkRuleInfoMapper
				.queryTmkAssignByPage(new Page<TmkAssignVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
		if (Objects.nonNull(pages)){
			List<TmkAssignVO> records = pages.getRecords();
			if (CollectionUtils.isNotEmpty(records)){
				records.forEach(item -> item.setMobile(SecurityUtil.maskMobile(item.getMobile())));
			}
		}
		return pages;
	}
}
