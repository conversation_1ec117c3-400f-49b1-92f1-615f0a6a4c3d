package com.welab.crm.operate.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池对象生成配置
 *
 * <AUTHOR>
 * @date 2023/3/10 10:39
 */
@Configuration
public class AsyncExecutorConfig {

    @Bean(name = "phoneSummaryExecutor")
    public AsyncTaskExecutor phoneSyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("phone-summary-executor-");
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(2);
        // 设置拒绝策略rejection-policy：当pool已经达到max size的时候，直接抛出异常，丢弃任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 等允许核心线程超时，防止没必要的空耗线程
        executor.setAllowCoreThreadTimeOut(true);
        // 设置非核心线程超时回收时间,单位：秒
        executor.setKeepAliveSeconds(600);
        executor.setQueueCapacity(1000);
        executor.initialize();
        return executor;
    }
}
