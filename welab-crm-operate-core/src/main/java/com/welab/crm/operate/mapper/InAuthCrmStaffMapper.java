package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 客服人员记录表（同步认证平台） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface InAuthCrmStaffMapper extends BaseMapper<InAuthCrmStaff> {

    /**
     * 查询指定组别内,在职员工的坐席工号列表
     * @param groupCode
     * @return
     */
    List<String> queryCnosByGroup(@Param("groupCode") String groupCode);

    /**
     * 查询指定组别内,在职员工的坐席工号列表
     * @param groupCodes
     * @return
     */
    List<String> queryCnosByGroups(@Param("list") List<String> groupCodes);
}
