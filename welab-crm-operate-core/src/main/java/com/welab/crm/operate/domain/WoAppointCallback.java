package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工单预约回电表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wo_appoint_callback")
public class WoAppointCallback implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预约时间
     */
    private Date appointTime;

    /**
     * 预约标题
     */
    private String appointTitle;

    /**
     * 推送通知时间
     */
    private Date pushTime;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单状态,0否,1是
     */
    private String status;

    /**
     * 预约人ID
     */
    private String staffId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;


}
