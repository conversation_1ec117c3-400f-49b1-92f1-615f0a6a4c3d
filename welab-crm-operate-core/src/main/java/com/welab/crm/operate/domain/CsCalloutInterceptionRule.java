package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 外呼拦截规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cs_callout_interception_rule")
public class CsCalloutInterceptionRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工单状态，多选用,隔开
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String orderStatus;

    /**
     * 标签名称，多选用,隔开
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String labelName;

    /**
     * 生效组别，多选用,隔开
     */
    private String groupCodes;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 创建人
     */
    private Long createStaffId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
