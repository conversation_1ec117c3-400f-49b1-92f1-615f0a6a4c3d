package com.welab.crm.operate.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.welab.exception.FastRuntimeException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EasyExcelUtils {

	/**
	 * 文件下载并且失败的时候返回json（默认失败了会返回一个有部分数据的Excel）
	 */
	public static void export(HttpServletResponse response, List data,Class head,String fileName,String sheetName) throws IOException {
		// 这里注意 使用swagger 会导致各种问题，请直接用浏览器或者用postman
		try {
			setResponse(response, fileName);
			// 这里需要设置不关闭流
			EasyExcel.write(response.getOutputStream(), head).autoCloseStream(Boolean.FALSE).sheet(sheetName)
			.doWrite(data);
		} catch (Exception e) {
			// 重置response
			response.reset();
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
			Map<String, String> map = new HashMap<String, String>();
			map.put("status", "failure");
			map.put("message", "下载文件失败" + e.getMessage());
			response.getWriter().println(JSON.toJSONString(map));
		}
	}

	public static void setResponse(HttpServletResponse response, String fileName) {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		try {
			// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
			fileName = URLEncoder.encode(fileName, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new FastRuntimeException("设置请求头异常");
		}
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
		response.setHeader("Content-Transfer-Encoding", "binary");
		response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
	}

	/**
	 * 多sheet导出
	 * @param response
	 * @param list
	 * @param map
	 * @param fileName
	 * @throws IOException  
	 */
	public static void exportSheets(HttpServletResponse response, Map<String,List> map,String fileName) throws IOException {
		try {
			setResponse(response, fileName);
			ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
			Iterator<Entry<String, List>> entries = map.entrySet().iterator();
			int i = 0;
			while(entries.hasNext()){
			    Entry<String, List> entry = entries.next();
			    String key = entry.getKey();
			    List value = entry.getValue();
			    if(CollectionUtils.isEmpty(value)) {
			    	continue;
			    }
			    excelWriter.write(value, EasyExcel.writerSheet(i, key).head(value.get(0).getClass()).build());
			    i++;
			}
			if(0==i) {
				throw new FastRuntimeException("无数据导出");
			}
			excelWriter.finish();
		} catch (Exception var10) {
			response.reset();
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
			Map<String, String> rlt = new HashMap<>();
			rlt.put("status", "failure");
			rlt.put("message", "下载文件失败" + var10.getMessage());
			response.getWriter().println(JSON.toJSONString(rlt));
		} 
	}

	/**
     * 同步无模型读（指定sheet和表头占的行数）
     * @param inputStream
     * @param sheetNo sheet页号，从0开始
     * @param headRowNum 表头占的行数，从0开始（如果要连表头一起读出来则传0）
     * @return List<Map<colNum, cellValue>>
     */
    public static List<List<String>> syncRead(InputStream inputStream, Integer sheetNo, Integer headRowNum){
    	List<Map<Integer, String>> list=  EasyExcelFactory.read(inputStream).sheet(sheetNo).headRowNumber(headRowNum).doReadSync();
    	List<List<String>> resList = new ArrayList<List<String>>();
    	for (Map<Integer, String> temp : list) {
    		List<List<String>> tempList = new ArrayList<List<String>>();
    		tempList.add(temp.values().stream().collect(Collectors.toList()));
    		resList.addAll(tempList);
		}
    	return resList;
    }


	public static<T> List<T> syncReadObject(InputStream inputStream, Class<T> tClass){
		return EasyExcelFactory.read(inputStream).head(tClass).sheet().doReadSync();
	}

	public static<T> List<T> syncReadObject(InputStream inputStream, Class<T> tClass, Integer headRowNum){
		return EasyExcelFactory.read(inputStream).head(tClass).sheet().headRowNumber(headRowNum).doReadSync();
	}


}
