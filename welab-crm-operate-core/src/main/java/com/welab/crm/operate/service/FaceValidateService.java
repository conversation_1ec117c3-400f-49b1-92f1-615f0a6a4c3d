package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.FaceReportDTO;
import com.welab.crm.operate.vo.face.FaceCauseVO;
import com.welab.crm.operate.vo.face.FaceDayVO;
import com.welab.crm.operate.vo.face.FaceDetailVO;

import java.util.List;

/**
 * 人脸验证报表服务
 */
public interface FaceValidateService {

    /**
     * 查询人脸验证指定时间段内的明细数据
     */
    Page<FaceDetailVO> listDetailsByPage(FaceReportDTO dto);

    /**
     * 导出人脸验证指定时间段内的明细数据
     */
    List<FaceDetailVO> listDetails(FaceReportDTO dto);

    /**
     * 统计每天的人脸验证成功失败数据
     */
    Page<FaceDayVO> listDayDataByPage(FaceReportDTO dto);

    /**
     * 导出每天的人脸验证成功失败数据
     */
    List<FaceDayVO> listDayData(FaceReportDTO dto);

    /**
     * 统计失败原因数据
     */
    Page<FaceCauseVO> listCauseByPage(FaceReportDTO dto);

    /**
     * 导出失败原因统计数据
     */
    List<FaceCauseVO> listCause(FaceReportDTO dto);
}
