package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 来电统计报表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("call_in_statistics_report")
public class CallInStatisticsReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 热线
     */
    private String hotline;

    /**
     * 来电数
     */
    private Integer callInNum;

    /**
     * 转人工数
     */
    private Integer toManualNum;

    /**
     * 人工接听数
     */
    private Integer answeredNum;

    /**
     * 通话总时长
     */
    private String totalBridgeTime;

    /**
     * 平均通话时长
     */
    private String avgBridgeTime;

    /**
     * 排队放弃数
     */
    private Integer queueGiveUpNum;

    /**
     * 日期
     */
    private Date countDay;

    /**
     * IVR分流率 = (IVR总按键数 - IVR转人工数) / IVR总按键数
     */
    private String ivrShuntRate;


    /**
     * IVR总按键数
     */
    private Integer ivrTotalNum;

    /**
     * IVR转人工数
     */
    private Integer ivrArtNum;


    /**
     * 电销转化量
     */
    private Integer tmkTransformNum;


}
