package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpHttpLogRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.monitor.StaffMonitorDTO;
import com.welab.crm.operate.vo.monitor.StaffMonitorReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 接口请求记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface OpHttpLogRecordMapper extends BaseMapper<OpHttpLogRecord> {

    /**
     * 分页查询平台用户监控报表
     * 
     * @param objectPage
     * @param dto
     * @return
     */
    Page<StaffMonitorReportVO> queryStaffMonitorReportPage(Page<Object> objectPage, @Param("dto") StaffMonitorDTO dto);

    List<StaffMonitorReportVO> queryStaffMonitorReportList(@Param("dto") StaffMonitorDTO dto);

    /**
     * 查询各组平均值
     * @param dto
     * @return
     */
    List<StaffMonitorReportVO> queryAvgCount(@Param("dto") StaffMonitorDTO dto);
}
