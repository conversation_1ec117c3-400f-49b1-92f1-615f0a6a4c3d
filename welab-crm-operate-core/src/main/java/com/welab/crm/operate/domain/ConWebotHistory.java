package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 青鸾同步创研人脸验证记录
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2023-02-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("con_webot_history")
public class ConWebotHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 逻辑主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 验证类型
     */
    private String validateType;

    /**
     * 创研验证token
     */
    private String token;

    /**
     * 所属组code
     */
    private String sendGroup;

    /**
     * 创建人(发送员工loginName或者邮箱)
     */
    private String sendUser;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 验证次数
     */
    private Integer validateCount;

    /**
     * 最终结果码
     */
    private Integer finalCode;

    /**
     * 最终结果中文
     */
    private String finalMsg;

    /**
     * 验证时间
     */
    private Date validateTime;

    /**
     * 供应商编码
     */
    private String vendor;

    /**
     * 验证结果码
     */
    private Integer validateCode;

    /**
     * 验证结果中文
     */
    private String validateMsg;

    /**
     * 创建人(发送员工staffId)
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
