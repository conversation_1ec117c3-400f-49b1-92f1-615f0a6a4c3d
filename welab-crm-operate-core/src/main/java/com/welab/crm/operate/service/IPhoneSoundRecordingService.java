package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.vo.phone.CallInPhoneDetailVO;
import com.welab.crm.operate.vo.screen.CountVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/23
 */
public interface IPhoneSoundRecordingService {

    /**
     * 获取录音文件地址
     * @param reqDTO
     * @return
     */
    public String queryRecordFile(PhoneSundRecordingDTO reqDTO);

    public Page<CallInPhoneDetailVO> queryDetail(PhoneSundRecordingDTO reqDTO);
    
    
    List<CountVO> queryCount(PhoneSundRecordingDTO dto); 
    
    
}
