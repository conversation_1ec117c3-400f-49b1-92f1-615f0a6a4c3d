package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.StaffQueryDTO;
import com.welab.crm.operate.dto.login.LoginTypeDTO;
import com.welab.crm.operate.vo.loginReport.LoginReportDetailVO;

import java.util.List;

public interface LoginService {

	/**
	 * 前端上报登录记录
	 * @param dto 登录参数
	 */
	void submitLoginRecord(LoginTypeDTO dto);


	/**
	 * 分页查询登录明细
	 */
	Page<LoginReportDetailVO> queryLoginReportDetailPage(StaffQueryDTO dto);


	/**
	 * 分页查询登录明细
	 */
	List<LoginReportDetailVO> queryLoginReportDetailList(StaffQueryDTO dto);
}
