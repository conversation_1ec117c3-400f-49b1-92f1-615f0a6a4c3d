package com.welab.crm.operate.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkVip;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.vo.telemarketing.TmkCjhyReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;

/**
 * <p>
 * 会员电销表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
public interface TmkVipMapper extends BaseMapper<TmkVip> {

    /**
     * 查询超级会员电销数据
     * @param page
     * @param tmkReqDTO
     * @return
     */
    Page<TmkTaskDetailVO> queryTmkVipPage(Page<TmkTaskDetailVO> page,@Param("dto") TmkReqDTO tmkReqDTO);


    /**
     * 查询超级会员转化数据
     * @param dto
     * @return
     */
    List<TmkTransformReportVO> queryVipTransformData(@Param("dto") TmkTransformReportReqDTO dto);
    
    /**
     * 查询超级会员外拨营销小结数据
     * @param dto
     * @return
     */
    Page<TmkCjhyReportVO> queryTmkCjhyData(Page<TmkCjhyReportVO> page, @Param("dto") TmkTransformReportReqDTO dto);

}
