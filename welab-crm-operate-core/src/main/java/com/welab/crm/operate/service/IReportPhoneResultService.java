package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/1
 */
public interface IReportPhoneResultService {

    /**
     * 电话小结明细报表
     * @param dto
     * @return
     */
    public Page<ReportPhoneResultVO> queryDetail(ReportPhoneResultDTO dto);

    /**
     * 导出电话小结明细报表
     * @param dto
     * @return
     */
    public List<ReportPhoneResultVO> queryDetailExcel(ReportPhoneResultDTO dto);

    /**
     * 电话小结统计报表
     * @param dto
     * @return
     */
    public Page<ReportPhoneResultSummaryVO> query(ReportPhoneResultDTO dto);

    /**
     * 导出电话小结统计报表
     * @param dto
     * @return
     */
    public List<ReportPhoneResultSummaryVO> queryExcel(ReportPhoneResultDTO dto);
}
