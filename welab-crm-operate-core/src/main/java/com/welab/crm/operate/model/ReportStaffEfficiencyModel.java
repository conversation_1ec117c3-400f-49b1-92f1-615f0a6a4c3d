package com.welab.crm.operate.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/3/9
 */
@Data
@ApiModel(value = "员工效能报表实体类")
public class ReportStaffEfficiencyModel implements Serializable {

    private static final long serialVersionUID = 5124732552532189432L;

    @ApiModelProperty(value = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    private String cno;

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "团队code")
    private String groupCode;

    @ApiModelProperty(value = "团队")
    private String groupName;

    @ApiModelProperty(value = "首问解决率")
    private String resolvedRate = "0.00%";

    @ApiModelProperty(value = "客户满意度")
    private String satisfiedRate = "0.00%";
}
