package com.welab.crm.operate.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkWallet;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkWalletReportVO;

/**
 * <p>
 * 钱包电销表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-22
 */
public interface TmkWalletMapper extends BaseMapper<TmkWallet> {

    /**
     * 查询钱包电销任务
     * @param page
     * @param tmkReqDTO
     * @return
     */
    Page<TmkTaskDetailVO> queryWalletTaskListPage(Page<TmkTaskDetailVO> page, @Param("dto") TmkReqDTO tmkReqDTO);


    /**
     * 查询钱包转化数据
     * @param dto
     * @return
     */
    List<TmkTransformReportVO> queryWalletTransformData(@Param("dto") TmkTransformReportReqDTO dto);

    /**
     * 查询钱包外拨营销小结数据
     * @param dto
     * @return
     */
    Page<TmkWalletReportVO> queryTmkWalletData(Page<TmkWalletReportVO> page,
            @Param("dto") TmkTransformReportReqDTO dto);
}
