package com.welab.crm.operate.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退款申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_refund_apply_record")
public class OpRefundApplyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 退款时效
     */
    private Integer refundDays;

    /**
     * 退款开始时间
     */
    private Date refundStartTime;

    /**
     * 审核状态；0-申请；1-初审通过；2-复审通过；3-拒绝
     */
    private String approvalStatus;

    /**
     * 推送财务状态
     */
    private String pushFlag;

    /**
     * 申请人
     */
    private Long staffId;

    /**
     * 流水号
     */
    private String requestNo;

    /**
     * 退款结果
     */
    private String result;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


    /**
     * 任务状态；0-进行中；1-完成
     */
    private String taskStatus;

    /**
     * 投诉渠道
     */
    private String complaintChannel;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 资金方名称
     */
    private String partnerCode;



    /**
     * 资金方编码
     */
    private String partnerCodeNew;


    /**
     * 费率
     */
    private BigDecimal totalRate;



    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 减免级别
     */
    private String refundType;
}
