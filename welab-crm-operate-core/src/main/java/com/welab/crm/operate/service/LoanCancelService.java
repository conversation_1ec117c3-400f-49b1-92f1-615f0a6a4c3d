package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.operate.dto.loan.LoanCancelApproveDTO;
import com.welab.crm.operate.dto.loan.LoanCancelDTO;
import com.welab.crm.operate.dto.loan.LoanCancelExcelDTO;
import com.welab.crm.operate.vo.loan.LoanCancelVO;

/**
 * 贷款取消和审核服务类
 *
 * <AUTHOR>
 * @date 2022/2/16 14:30
 */
public interface LoanCancelService {

    /**
     * 根据过滤条件筛选贷款取消主页列表
     */
    Page<LoanCancelVO> listCancel(LoanCancelDTO cancelDTO);

    /**
     * 保存导入的贷款取消数据列表
     *
     * @param staffCode      创建者编码
     * @param contractNoList 贷款号列表
     */
    void saveCancelList(String staffCode, ExcelList<LoanCancelExcelDTO> contractNoList);

    /**
     * 审批将要取消的贷款号列表
     *
     * @param staffCode  审批者编码(当前登陆者)
     * @param approveDTO 审批结果和待审批贷款号列表
     */
    void updateApproveState(String staffCode, LoanCancelApproveDTO approveDTO);
}
