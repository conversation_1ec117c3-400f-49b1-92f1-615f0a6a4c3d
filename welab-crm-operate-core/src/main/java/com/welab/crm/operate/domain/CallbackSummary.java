package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 回电小结表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("callback_summary")
public class CallbackSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    private String uuid;

    /**
     * 天润通话唯一ID
     */
    private String cdrMainUniqueId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 回电结果
     */
    private String contactResult;

    /**
     * 回电结果原因:备注
     */
    private String reasonType;

    /**
     * 通话备注
     */
    private String callbackComment;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 工单号
     */
    private String orderNo;

    /**
     * 处理方案
     */
    private String resolveContent;


}
