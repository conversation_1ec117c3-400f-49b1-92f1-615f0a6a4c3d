package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座机登录信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("in_phone_login_info")
public class InPhoneLoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 呼叫中心编号
     */
    private String org;

    /**
     * 坐席号
     */
    private String idNo;

    /**
     * 登录密码
     */
    private String pwd;

    /**
     * 绑定电话
     */
    private String tel;

    /**
     * 电话类型
     */
    private String telType;

    /**
     * 初始状态
     */
    private String initState;

    /**
     * 状态 0-无效 1-有效
     */
    private Boolean status;

    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 坐席号
     */
    private String seatingNo;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 客服编码
     */
    private String userTel;


}
