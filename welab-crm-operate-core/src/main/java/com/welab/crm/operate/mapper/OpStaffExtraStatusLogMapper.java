package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.OpStaffExtraStatusLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 坐席额外工作状态统计(除去op_staff_status_his 表中状态) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
public interface OpStaffExtraStatusLogMapper extends BaseMapper<OpStaffExtraStatusLog> {

	/**
	 * 查询工单提交时间(根据日期和staff分组)
	 * @param dto
	 * @return
	 */
	List<ReportWorkStatusSummaryVO> selectSubmitOrderTimeGroupByDayAndStaff(@Param("dto") ReportWorkStatusDTO dto);


	/**
	 * 查询工单提交时间(只根据staff分组)
	 * @param dto
	 * @return
	 */
	List<ReportWorkStatusSummaryVO> selectSubmitOrderTimeGroupByStaff(@Param("dto") ReportWorkStatusDTO dto);

	/**
	 * 查询工单提交时间(只根据staff分组)
	 * @param dto
	 * @return
	 */
	List<ReportWorkStatusSummaryVO> selectSubmitOrderTimeGroupByStaffs(@Param("dto") ReportWorkStatusDTO dto);

	/**
	 * 查询工单提交时间(不分组)
	 * @param dto
	 * @return
	 */
	List<ReportWorkStatusSummaryVO> selectSubmitOrderTime(@Param("dto") ReportWorkStatusDTO dto);
}
