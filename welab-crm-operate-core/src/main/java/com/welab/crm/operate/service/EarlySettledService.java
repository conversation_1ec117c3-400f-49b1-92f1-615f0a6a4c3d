package com.welab.crm.operate.service;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.earlySettle.EarlySettledReqDTO;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO;

import java.util.List;

/**
 * 提前结清接口
 */
public interface EarlySettledService {


    /**
     * 报错提前结清记录
     * @param dto 请求参数
     * @param staffVO 客服信息
     */
    void saveEarlySettledRecord(HistoryOperationDTO dto, StaffVO staffVO);


    /**
     * 查询结清信息统计用户维度
     * @param dto 请求参数
     * @return {@link EarlySettledRecordUserSummaryVO}
     */
    List<EarlySettledRecordUserSummaryVO> querySettleUserSummary(EarlySettledReqDTO dto);


    /**
     * 查询结清原因期数统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return json对象
     */
    JSONObject queryReasonTenorSummary(String startTime, String endTime);


    /**
     * 查询渠道来源和资金方统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return json对象
     */
    JSONObject queryOriginPartnerCodeSummary(String startTime, String endTime);
}
