package com.welab.crm.operate.util;

import com.welab.crm.operate.constant.Constant;
import com.welab.exception.FastRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * AES工具类
 */
public class AesUtils {
    /**
     * 密钥
     */
    private static final String KEY = "ece6da16ee3f8aee";

    /**
     * 偏移量
     */
    private static final String IV = "09028f9b80488563";

    /**
     * 生成随机密钥
     */
    public static String generateSecret(int keySize) throws NoSuchAlgorithmException {
        KeyGenerator generator = KeyGenerator.getInstance("AES");
        generator.init(keySize, new SecureRandom());
        SecretKey key = generator.generateKey();
        return byteToHexString(key.getEncoded()).substring(0, 16);
    }

    public static String generateSecretAnother(int keySize) {
        SecureRandom secureRandom = new SecureRandom();
        byte[] bytes = new byte[keySize];
        secureRandom.nextBytes(bytes);
        return byteToHexString(bytes);
    }

    public static String encrypt(String strToEncrypt, String secret, String ivStr) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        SecretKeySpec secretKey = getKey(secret);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
        cipher.init(Cipher.ENCRYPT_MODE, secretKey,iv);
        return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")));
    }

    public static String decrypt(String strToDecrypt, String secret, String ivStr) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        SecretKeySpec secretKey = getKey(secret);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
        cipher.init(Cipher.DECRYPT_MODE, secretKey,iv);
        return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
    }

    private static SecretKeySpec getKey(String myKey) {
        return new SecretKeySpec(myKey.getBytes(), "AES");
    }

    public static String encrypt(String strToEncrypt) {
        try {
            return encrypt(strToEncrypt, KEY, IV);
        } catch (Exception e) {
            throw new FastRuntimeException("aes加密失败");
        }
    }


    public static String decrypt(String strToDecrypt) {
        try {
            return decrypt(strToDecrypt, KEY, IV);
        } catch (Exception e) {
            throw new FastRuntimeException("aes解密失败");
        }
    }

    /**
     * byte数组转化为16进制字符串
     *
     * @param bytes
     * @return
     */
    private static String byteToHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String strHex = Integer.toHexString(bytes[i]);
            if (strHex.length() > 3) {
                sb.append(strHex.substring(6));
            } else {
                if (strHex.length() < 2) {
                    sb.append("0" + strHex);
                } else {
                    sb.append(strHex);
                }
            }
        }
        return sb.toString();
    }
    
    public static String getRealMobile(String mobile){
        if (StringUtils.isBlank(mobile)){
            return mobile;
        }
        if (Pattern.matches(Constant.MOBILE, mobile)){
            return mobile;
        } else {
            return decrypt(mobile);
        }
    }

    public static String getRealIdNo(String idNo){

        if (StringUtils.isBlank(idNo)) {
            return idNo;
        }
        if (Pattern.matches(Constant.ID_NO_18, idNo) || Pattern.matches(Constant.ID_NO_15, idNo)){
            return idNo;
        } else {
            return decrypt(idNo);
        }
    }

    public static void main(String[] args) {
        String realMobile = getRealMobile("5ZZ1dRuR3VpC9Np+u/9N8w==");
        System.out.println("realMobile = " + realMobile);
    }
}