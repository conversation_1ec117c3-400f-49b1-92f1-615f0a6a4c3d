package com.welab.crm.operate.websocket.util;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.welab.crm.operate.ws.SendMsgDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.websocket.RemoteEndpoint;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/1/5 11:09
 */
public class WebSocketUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketUtil.class);

    // ========== 会话相关 ==========

    /**
     * Session 与用户的映射
     */
    private static final Map<WebSocketSession, String> SESSION_USER_MAP = new ConcurrentHashMap<>();
    /**
     * 用户与 Session 的映射
     */
    private static final Map<String, WebSocketSession> USER_SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * 添加 Session 。在这个方法中，会添加用户和 Session 之间的映射
     *
     * @param session Session
     * @param user 用户
     */
    public static void addSession(WebSocketSession session, String user) {
        // 更新 USER_SESSION_MAP
        USER_SESSION_MAP.put(user, session);
        // 更新 SESSION_USER_MAP
        SESSION_USER_MAP.put(session, user);
        // LOGGER.info("当前在线用户:{}",USER_SESSION_MAP.keySet());
    }

    /**
     * 移除 Session 。
     *
     * @param session Session
     */
    public static void removeSession(WebSocketSession session) {
        // 从 SESSION_USER_MAP 中移除
        String user = SESSION_USER_MAP.remove(session);
        // 从 USER_SESSION_MAP 中移除
        if (user != null && user.length() > 0) {
            USER_SESSION_MAP.remove(user);
        }
        // LOGGER.info("当前在线用户:{}",USER_SESSION_MAP.keySet());
    }

    public static String getMobileBySession(WebSocketSession session) {
        return SESSION_USER_MAP.get(session);
    }

    public static WebSocketSession getSessionByMobile(String mobile) {
        return USER_SESSION_MAP.get(mobile);
    }

    /**
     * 发送消息给单个用户的 Session
     *
     * @param session Session
     * @param jsonObject 消息json对象
     */
    public static void send(WebSocketSession session, String message) {
        // 遍历给单个 Session ，进行逐个发送
        sendTextMessage(session, message);
    }

    public static void sendByMobile(SendMsgDTO dto) {
        WebSocketSession session = WebSocketUtil.getSessionByMobile(dto.getMobile());
        WebSocketUtil.send(session, dto.getMessageJson().toJSONString());
    }

    /**
     * 真正发送消息
     *
     * @param session Session
     * @param messageText 消息
     */
    private static void sendTextMessage(WebSocketSession session, String messageText) {
        if (session == null) {
            LOGGER.error("[sendTextMessage][session 为 null]");
            return;
        }

        try {
            session.sendMessage(new TextMessage(messageText));
        } catch (IOException e) {
            LOGGER.error("sendTextMessage error", e);
        }

    }

}
