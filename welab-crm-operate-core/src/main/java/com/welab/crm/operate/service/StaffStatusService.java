package com.welab.crm.operate.service;

import com.welab.crm.operate.domain.OpStaffStatusHis;
import com.welab.crm.operate.dto.staff.ReportStaffStatusDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0

 * @date 2021/11/20 11:25
 */
public interface StaffStatusService {

    /**
     * 上报用户状态
     * @param dto
     */
    void reportStatusMsg(ReportStaffStatusDTO dto);

    /**
     * 上报员工状态开始时间
     * @param status
     * @param mobile
     */
    void submitStatusStartTime(String status, String mobile);

    /**
     * 查询该用户最近的一条没有结束时间的该状态记录
     * @param mobile
     * @param status
     * @return
     */
    OpStaffStatusHis getLastStatusRecord(String mobile, String status);


    /**
     * 查询改用户全部未结束的状态记录
     * @param mobile
     * @param status
     * @return
     */
    List<OpStaffStatusHis> getNotEndRecord(String mobile, String status);

    /**
     * 更新结束时间
     * @param opStaffStatusHis
     */
    void updateStaffStatus(OpStaffStatusHis opStaffStatusHis);


    /**
     * 更新所有未完成的状态
     * @param mobile
     */
    void updateAllStatus(String mobile);

}
