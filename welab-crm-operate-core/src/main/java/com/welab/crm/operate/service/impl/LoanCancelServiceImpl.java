package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.operate.domain.LoanCancel;
import com.welab.crm.operate.dto.loan.LoanCancelApproveDTO;
import com.welab.crm.operate.dto.loan.LoanCancelDTO;
import com.welab.crm.operate.dto.loan.LoanCancelExcelDTO;
import com.welab.crm.operate.enums.ApproveStateEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.LoanCancelMapper;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import com.welab.crm.operate.service.LoanCancelService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.loan.LoanCancelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoanCancelServiceImpl implements LoanCancelService {

    @Resource
    private InAuthCrmStaffService inAuthCrmStaffService;

    @Resource
    private LoansApplicationService loansApplicationService;

    @Resource
    private LoanCancelMapper cancelMapper;

    @Override
    public Page<LoanCancelVO> listCancel(LoanCancelDTO cancel) {
        Page<LoanCancel> page = new Page<LoanCancel>().setCurrent(cancel.getCurPage())
                .setSize(cancel.getPageSize());
        LambdaQueryWrapper<LoanCancel> wrapper = Wrappers.<LoanCancel>lambdaQuery()
                .eq(StringUtils.isNotBlank(cancel.getContractNo()), LoanCancel::getContractNo, cancel.getContractNo())
                // 查询指定审批状态的数据, 否则查询所有
                .eq(cancel.getApproveState() != null, LoanCancel::getApproveState, cancel.getApproveState())
                .ge(cancel.getStartCreateTime() != null, LoanCancel::getGmtCreate, cancel.getStartCreateTime())
                .le(cancel.getEndCreateTime() != null, LoanCancel::getGmtCreate, cancel.getEndCreateTime())
                .orderByDesc(LoanCancel::getId);
        Page<LoanCancel> resultPage = cancelMapper.selectPage(page, wrapper);
        return convertToCancelVOPage(resultPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCancelList(String staffCode, ExcelList<LoanCancelExcelDTO> contractNoList) {
        // 检查数据列表中是否有重复值和当前这些贷款是否有处于待审批的
        checkContracts(contractNoList);
        // 保存贷款取消申请数据
        doSaveCancelList(staffCode, contractNoList);
    }

    @Override
    public void updateApproveState(String staffCode, LoanCancelApproveDTO approveDTO) {
        if (approveDTO.getApproveState() == ApproveStateEnum.AGREE.getValue()) {
            log.info("loan cancel contractNo list: {}", approveDTO.getContractNoList());
            // 先推送数据到消金服务，成功后再更新数据库审批状态
            boolean result = loansApplicationService.loanCancel(approveDTO.getContractNoList());
            if (!result) {
                throw new CrmOperateException("推送贷款取消数据到消金服务发生异常");
            } else {
                doUpdateApproveState(staffCode, approveDTO);
            }
        } else if (approveDTO.getApproveState() == ApproveStateEnum.REJECT.getValue()) {
            doUpdateApproveState(staffCode, approveDTO);
        } else {
            throw new CrmOperateException("审批状态参数错误: 1-审批通过,2-审批拒绝");
        }
    }

    /**
     * 更新数据库中关于合同号对应的审批状态数据
     */
    private void doUpdateApproveState(String staffCode, LoanCancelApproveDTO approveDTO) {
        LambdaUpdateWrapper<LoanCancel> wrapper = Wrappers.<LoanCancel>lambdaUpdate()
                .in(LoanCancel::getContractNo, approveDTO.getContractNoList());
        LoanCancel cancel = new LoanCancel();
        cancel.setApproveState(approveDTO.getApproveState()).setApproveComment(approveDTO.getRemark())
                .setAuditor(staffCode).setLstUpdUser(staffCode);
        cancelMapper.update(cancel, wrapper);
    }

    /**
     * 将贷款取消数据实体类转换为vo对象以用于页面展示
     */
    private Page<LoanCancelVO> convertToCancelVOPage(Page<LoanCancel> resultPage) {
        Page<LoanCancelVO> page = new Page<>();
        BeanUtils.copyProperties(resultPage, page);
        List<LoanCancel> records = resultPage.getRecords();
        List<LoanCancelVO> voList = new ArrayList<>(records.size());
        if (records.isEmpty()) {
            return page.setRecords(voList);
        }
        Set<String> users = records.stream().map(LoanCancel::getCreateUser).collect(Collectors.toSet());
        Map<String, String> nameMap = inAuthCrmStaffService.getStaffNameMap(users);
        for (LoanCancel c : records) {
            LoanCancelVO vo = new LoanCancelVO();
            BeanUtils.copyProperties(c, vo);
            vo.setRemark(c.getApproveComment());
            vo.setCreateUser(nameMap.get(c.getCreateUser()));
            vo.setApproveState(ApproveStateEnum.getState(c.getApproveState()));
            vo.setGmtCreate(DateUtils.formatDate(c.getGmtCreate(), DateUtils.DATE_FORMAT));
            voList.add(vo);
        }
        page.setRecords(voList);
        return page;
    }

    private void doSaveCancelList(String staffCode, ExcelList<LoanCancelExcelDTO> contractNoList) {
        List<LoanCancel> list = new ArrayList<>();
        List<String> pushList = new ArrayList<>();
        for (LoanCancelExcelDTO excelDTO : contractNoList) {
            LoanCancel cancel = new LoanCancel();
            cancel.setContractNo(excelDTO.getContractNo()).setApproveState(ApproveStateEnum.AGREE.getValue())
                    .setCreateUser(staffCode).setAuditor(staffCode).setReason(excelDTO.getReason());
            list.add(cancel);
            pushList.add(excelDTO.getContractNo());
        }
        cancelMapper.insertBatchSomeColumn(list);

        log.info("loan cancel contractNo list: {}", pushList);
        boolean result = loansApplicationService.loanCancel(pushList);
        if (!result) {
            throw new CrmOperateException("推送贷款取消数据到消金服务发生异常");
        }

    }

    /**
     * 检测导入的文件中贷款号是否存在重复值
     */
    private void checkContracts(ExcelList<LoanCancelExcelDTO> cancelList) {
        // 1.检测重复值
        StringBuilder builder = new StringBuilder();
        Set<String> contractSet = new HashSet<>(cancelList.size());
        for (int i = 0; i < cancelList.size(); i++) {
            String contractNo = cancelList.get(i).getContractNo();
            if (contractNo.contains(".") || contractNo.contains("+")) {
                throw new CrmOperateException("第" + (i + 2) + "行数据贷款号不合法");
            } else if (!contractSet.add(contractNo)) {
                builder.append(i + 2).append(",");
            }
        }
        String errorMsg = builder.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            String realErrMsg = errorMsg.substring(0, errorMsg.length() - 1);
            throw new CrmOperateException("第" + realErrMsg + "行贷款号数据有重复");
        }

        // 1.检测是否有待审批中的数据,成功或者失败的数据可以重复导入
        /*List<String> nos = cancelList.stream().map(LoanCancelExcelDTO::getContractNo).collect(Collectors.toList());
        LambdaQueryWrapper<LoanCancel> wrapper = Wrappers.<LoanCancel>lambdaQuery().in(LoanCancel::getContractNo, nos)
                .eq(LoanCancel::getApproveState, ApproveStateEnum.WAITING.getValue());
        List<LoanCancel> existCancels = cancelMapper.selectList(wrapper);
        if (!existCancels.isEmpty()) {
            StringBuilder eBuilder = new StringBuilder();
            eBuilder.append("贷款号");
            for (LoanCancel cancel : existCancels) {
                eBuilder.append(cancel.getContractNo()).append(",");
            }
            eBuilder.append("存在待审批的数据,请勿重复导入");
            throw new CrmOperateException(eBuilder.toString());
        }*/
    }
}
