package com.welab.crm.operate.mapper;

import com.welab.crm.operate.domain.OpEarlySettledRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.earlySettle.EarlySettledReqDTO;
import com.welab.crm.operate.model.EarlySettledOriginPartnerModel;
import com.welab.crm.operate.model.EarlySettledReasonTenorModel;
import com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 贷款提前结清记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface OpEarlySettledRecordMapper extends BaseMapper<OpEarlySettledRecord> {

    /**
     * 查询结清客户统计数据
     * @param dto 查询参数
     * @return {@link EarlySettledRecordUserSummaryVO}
     */
    List<EarlySettledRecordUserSummaryVO> queryUserSummary( @Param("dto") EarlySettledReqDTO dto);


    /**
     * 查询指定时间内的合计数据
     * @param dto {@link EarlySettledReqDTO}
     * @return {@link EarlySettledRecordUserSummaryVO}
     */
    EarlySettledRecordUserSummaryVO queryTotalSummary(@Param("dto") EarlySettledReqDTO dto);

    /**
     * 查询指定时间区间内的结清原因和期数的数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link EarlySettledReasonTenorModel}
     */
    List<EarlySettledReasonTenorModel> queryReasonTenorSummary(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询指定时间内的数据，根据期数分组
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link  EarlySettledReasonTenorModel}
     */
    List<EarlySettledReasonTenorModel> queryTotalCountGroupByTenor(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询指定时间区间内的来源渠道和资金方的数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link EarlySettledOriginPartnerModel}
     */
    List<EarlySettledOriginPartnerModel> queryOriginPartnerSummary(@Param("startTime") String startTime, @Param("endTime") String endTime);


    /**
     * 查询指定时间内的结清数据，根据资金方分组
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link EarlySettledReasonTenorModel}
     */
    List<EarlySettledOriginPartnerModel> queryTotalCountGroupByPartnerCode(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
