package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.dto.notice.NoticeReplyReqDTO;
import com.welab.crm.operate.dto.notice.WoNoticeDTO;
import com.welab.crm.operate.vo.notice.NoticeMsgDetailVO;
import com.welab.crm.operate.vo.notice.NoticeMsgVO;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * 消息通知服务类
 * <AUTHOR>
 * @date 2021/10/28 15:34
 */
public interface NoticeMsgService {


    /**
     * 发布消息
     * @param dto
     */
    Boolean publishNotice(NoticeMsgReqDTO dto);

    /**
     * 删除消息
     * @param msgId
     */
    Boolean deleteNotice(String msgId);


    /**
     * 查询接受到的消息
     * @param dto
     * @return
     */
    Page<NoticeMsgVO> queryReceive(NoticeMsgReqDTO dto);

    /**
     * 查询发送的消息
     * @param dto
     * @return
     */
    Page<NoticeMsgVO> querySend(NoticeMsgReqDTO dto);

    /**
     * 查询消息详情
     * @param msgId
     * @return
     */
    NoticeMsgDetailVO queryMsgDetail(String msgId);


    /**
     * 回复消息
     * @param dto
     */
    void replyMsg(NoticeReplyReqDTO dto);


    /**
     * 已读消息
     * @param msgId 消息Id
     * @param staffId 员工Id
     */
    void readMsg(String msgId, String staffId);


    /**
     * 查询已读消息
     * @param dto
     * @return
     */
    Page<NoticeMsgVO> queryReadMsg(NoticeMsgReqDTO dto);


    /**
     * 查询未读消息
     * @param dto
     * @return
     */
    Page<NoticeMsgVO> queryUnreadMsg(NoticeMsgReqDTO dto);


    /**
     * 查询各类型消息数量
     * @return
     */
    Map<String,Integer> queryAllTypeCount();


    /**
     * 获取该消息类型的数量
     * @param type
     * @param staffId
     * @return
     */
    Integer getCountByType(String type, String staffId);


    /**
     * 消息全部已读
     * @param type 消息类型
     * @return
     */
    void allRead(String type);


    /**
     * 查询所有未读的跑马灯消息
     * @return
     */
    List<NoticeMsgVO> queryAllBanner();


    /**
     * 工单流转时，根据不同的通知类型来进行通知
     * @param woNoticeDTO
     */
    void workOrderNoticeByType(WoNoticeDTO woNoticeDTO);

    void publishTmkMsg(Set<String> tmkTypeSet,Integer count, String staffId);

    /**
     * 查询该电销任务是否有未读的预约消息,true 有，false 没有
     * @param tmkTaskId
     * @return
     */
    Boolean queryTmkUnReadMsg(String tmkTaskId);

    /**
     * 推送待审批消息
     * @param dto
     * @return
     */
    void publishBannerNotice(List<String> approval, NoticeMsgReqDTO dto);
}
