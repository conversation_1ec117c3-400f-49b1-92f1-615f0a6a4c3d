package com.welab.crm.operate.service.impl;

import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.service.UploadAndNoticeService;
import com.welab.crm.operate.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 上传文件并通知实现
 * <AUTHOR>
 */
@Service
public class UploadAndNoticeServiceImpl implements UploadAndNoticeService {
	
	@Resource
	private IUploadService uploadService;
	
	@Resource
	private NoticeMsgService noticeMsgService;
	
	@Resource
	private JedisCommands jedisCommands;
	@Override
	public String uploadFileAndReturnUrl(byte[] byteArray, String fileName) {
		Response<String> stringResponse = uploadService.uploadFile(byteArray, fileName);
		Map<String, Object> uploadFile = uploadService.getUploadFile(Arrays.asList(stringResponse.getResult()));
		return uploadFile.get(stringResponse.getResult()).toString();
	}

	@Override
	public void noticeFileDownloadUrl(String fileName, String fileUrl, String receiver) {
		NoticeMsgReqDTO dto = new NoticeMsgReqDTO();
		dto.setType("system_msg");
		dto.setNoticeType("system");
		dto.setSender("system");
		dto.setIsBanner(true);
		dto.setReceiver(receiver);
		dto.setTitle(fileName);
		dto.setContent(fileUrl);
		noticeMsgService.publishNotice(dto);
	}


	@Override
    public void uploadFileAndNotice(byte[] byteArray, String fileName, String receiver, String ext) {
        String fileUrl = uploadFileAndReturnUrl(byteArray, fileName + ext);
        noticeFileDownloadUrl(fileName + DateUtil.dateToString(new Date(), DateUtil.TimeFormatter.YYYYMMDD), fileUrl,
            receiver);
    }


    @Override
    public String getFileUrlFromCache(String uniqueFileName) {
        String url = jedisCommands.get(uniqueFileName);
        if (StringUtils.isBlank(url)) {
            url = uploadService.getUploadFile(Collections.singletonList(uniqueFileName)).get(uniqueFileName).toString();
            jedisCommands.setex(uniqueFileName, 60 * 60 * 11, url);
        }

        return url;
    }
}
