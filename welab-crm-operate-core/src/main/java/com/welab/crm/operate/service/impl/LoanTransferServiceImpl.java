package com.welab.crm.operate.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.dto.message.MessageTemplateQueryDTO;
import com.welab.crm.interview.service.*;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.loan.*;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.loan.*;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.repayment.dto.WriteoffDTO;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.operate.enums.ApproveStateEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.InAuthCrmStaffService;
import com.welab.crm.operate.service.LoanTransferService;
import com.welab.crm.operate.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class LoanTransferServiceImpl implements LoanTransferService {

    @Resource
    private LoansApplicationService loansApplicationService;
    
    @Resource
    private LoanApplicationService loanApplicationService;
    
    @Resource
    private DataCustomerMapper dataCustomerMapper;
    
    @Resource
    private MessageService messageService;
    

    @Resource
    private InAuthCrmStaffService inAuthCrmStaffService;

    @Resource
    private IUploadService uploadService;

    @Resource
    private LoanTransferMapper transferMapper;

    @Resource
    private LoanTransferContractMapper contractMapper;

    @Resource
    private LoanTransferAttachmentMapper attachmentMapper;
    
    @Resource
    private OpImportInfoMapper importInfoMapper;
    
    @Resource
    private OpOutcaseInfoMapper outcaseInfoMapper;
    
    @Resource
    private OpDeptInfoMapper deptInfoMapper;

    @Resource
    private FinanceService financeService;


    /**
     * 每次批量插入的数据量，防止过大造成性能问题
     */
    private static final int BATCH_SIZE = 400;

    /**
     * 债转公司对应号码字典
     */
    private static final String LOAN_TRANSFER_DEPT_DICT = "loan_transfer_dept_mobile";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveContractNoList(String operator, ExcelList<LoanTransferExcelDTO> contractNoList) {
        // 检查数据列表中是否有重复值
        checkContracts(contractNoList);
        // 保存债转结清主数据
        Long transferId = saveTransferMain(operator, contractNoList.getFileName(), contractNoList.size());
        // 保存债转结清相关的合同列表数据(量大分批次保存)
        saveTranContracts(operator, transferId, contractNoList);
        return transferId;
    }

    @Override
    public Page<LoanTransferVO> listTransfer(LoanTransferDTO tranDTO) {
        Page<LoanTransfer> page = getPage(tranDTO.getCurPage(), tranDTO.getPageSize());
        Page<LoanTransfer> resultPage = transferMapper.selectPageByCondition(page, tranDTO);
        return convertToTransferVOPage(resultPage);
    }

    @Override
    public Page<LoanContactVO> listDetails(LoanTransferIdDTO contractDTO) {
        Page<LoanTransferContract> page = getPage(contractDTO.getCurPage(), contractDTO.getPageSize());
        return contractMapper.selectPageByTransferId(page,contractDTO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAttachments(String operator, List<LoanTransferAttachment> attachments) {
        attachmentMapper.insertBatchSomeColumn(attachments);
        // 更新债转结清主表的附件上传状态为已上传
        LoanTransfer transfer = new LoanTransfer();
        transfer.setId(attachments.get(0).getTransferId());
        transfer.setLstUpdUser(operator);
        transfer.setWithAttachment(Boolean.TRUE);
        transferMapper.updateById(transfer);
    }

    @Override
    public List<LoanTranAttachmentVO> listAttachments(Long transferId) {
        LambdaQueryWrapper<LoanTransferAttachment> wrapper = Wrappers.<LoanTransferAttachment>lambdaQuery()
                .eq(LoanTransferAttachment::getTransferId, transferId);
        List<LoanTransferAttachment> attachments = attachmentMapper.selectList(wrapper);
        if (attachments.isEmpty()) {
            return Collections.emptyList();
        }
        // 从阿里云文件服务获取文件的下载url(只能实时查询，因为下载链接是有时效性的)
        List<String> names = attachments.stream().map(LoanTransferAttachment::getFilePath).collect(Collectors.toList());
        Map<String, Object> pathMap = uploadService.getUploadFile(names);
        List<LoanTranAttachmentVO> results = new ArrayList<>();
        for (LoanTransferAttachment a : attachments) {
            LoanTranAttachmentVO vo = new LoanTranAttachmentVO();
            vo.setId(a.getId());
            vo.setAttachmentName(a.getAttachmentName());
            vo.setDownloadUrl((String) pathMap.get(a.getFilePath()));
            results.add(vo);
        }
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApproveState(String staffCode, LoanTransferIdDTO stateDTO) {
        if (stateDTO.getApproveState() == ApproveStateEnum.AGREE.getValue() ||
                stateDTO.getApproveState() == ApproveStateEnum.REJECT.getValue()) {
            LoanTransfer transfer = new LoanTransfer();
            BeanUtils.copyProperties(stateDTO, transfer);
            transfer.setAuditor(staffCode);
            transfer.setLstUpdUser(staffCode);
            transfer.setApproveTime(new Date());
            transferMapper.updateById(transfer);
        } else {
            throw new CrmOperateException("审批状态参数错误: 1-审批通过,2-审批拒绝");
        }
    }

    @Override
    public void pushLoanData(LoanTransferIdDTO stateDTO) {
        LoanTransfer transfer = transferMapper.selectById(stateDTO.getId());
        // 必须是审批通过的数据才能做推送操作
        if (transfer.getApproveState() == ApproveStateEnum.AGREE.getValue()) {
            LambdaQueryWrapper<LoanTransferContract> wrapper = Wrappers.<LoanTransferContract>lambdaQuery()
                    .eq(LoanTransferContract::getTransferId, stateDTO.getId());
            List<LoanTransferContract> contracts = contractMapper.selectList(wrapper);
            if (contracts == null || contracts.isEmpty()) {
                throw new CrmOperateException("待推送贷款数据为空!");
            }
            List<com.welab.crm.interview.dto.loanTransfer.LoanTransferDTO> list = contracts.stream().map(item -> {
                com.welab.crm.interview.dto.loanTransfer.LoanTransferDTO dto =
                    new com.welab.crm.interview.dto.loanTransfer.LoanTransferDTO();
                dto.setId(item.getId());
                dto.setApplicationId(item.getContractNo());
                return dto;
            }).collect(Collectors.toList());

            try {
                // 推送到外部消金服务
                loansApplicationService.loanSettled(list);
            } catch (Exception e){
                throw new CrmOperateException("pushLoanData 推送债转贷款号异常", e);
            }

        } else {
            throw new CrmOperateException("债转申请还未通过审批,不能推送");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePushState(String operator, Long id) {
        LambdaUpdateWrapper<LoanTransferContract> wrapper = Wrappers.<LoanTransferContract>lambdaUpdate()
                .eq(LoanTransferContract::getTransferId, id);
        LoanTransferContract contract = new LoanTransferContract();
        contract.setPushState(true);
        contract.setLstUpdUser(operator);
        contractMapper.update(contract, wrapper);
    }

    private <T> Page<T> getPage(Integer curPage, Integer pageSize) {
        return new Page<T>().setCurrent(curPage).setSize(pageSize);
    }


    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void pushLoanDataAsync(LoanTransferIdDTO stateDTO) {
        pushLoanData(stateDTO);
        updatePushState("system",stateDTO.getId());
        log.info("异步推送债转结清合同结束");
    }

    /**
     * 检测导入的文件中合同是否存在重复值和合同号是否合法
     */
    private void checkContracts(ExcelList<LoanTransferExcelDTO> transferList) {
        Set<String> contractSet = new HashSet<>(transferList.size());
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < transferList.size(); i++) {
            String contractNo = transferList.get(i).getContractNo();
            if (!contractSet.add(contractNo)) {
                builder.append(i + 2).append(",");
            } else if (contractNo.contains(".") || contractNo.contains("+")) {
                throw new CrmOperateException("第" + (i + 2) + "行数据贷款号不合法");
            }
        }
        String errorMsg = builder.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            String realErrMsg = errorMsg.substring(0, errorMsg.length() - 1);
            throw new CrmOperateException("第" + realErrMsg + "行数据有重复");
        }
    }

    /**
     * 将债转结清主数据实体类转换为vo对象以用于页面展示
     */
    private Page<LoanTransferVO> convertToTransferVOPage(Page<LoanTransfer> resultPage) {
        Page<LoanTransferVO> page = new Page<>();
        BeanUtils.copyProperties(resultPage, page);
        List<LoanTransfer> records = resultPage.getRecords();
        List<LoanTransferVO> voList = new ArrayList<>(records.size());
        if (records.isEmpty()) {
            return page.setRecords(voList);
        }
        Set<String> users = records.stream().map(LoanTransfer::getCreateUser).collect(Collectors.toSet());
        Map<String, String> nameMap = inAuthCrmStaffService.getStaffNameMap(users);
        for (LoanTransfer r : records) {
            LoanTransferVO vo = new LoanTransferVO();
            BeanUtils.copyProperties(r, vo);
            vo.setCreateUser(nameMap.get(r.getCreateUser()));
            vo.setWithAttachment(r.getWithAttachment() ? "是" : "否");
            vo.setGmtCreate(DateUtils.formatDate(r.getGmtCreate(), DateUtils.DATE_FORMAT));
            vo.setApproveState(ApproveStateEnum.getState(r.getApproveState()));
            voList.add(vo);
        }
        page.setRecords(voList);
        return page;
    }

    /**
     * 将债转结清关联合同实体类转换为vo对象以用于页面展示
     */
    private Page<LoanContactVO> convertToContractVOPage(Page<LoanTransferContract> resultPage) {
        Page<LoanContactVO> page = new Page<>();
        BeanUtils.copyProperties(resultPage, page, "records");
        List<LoanTransferContract> records = resultPage.getRecords();
        List<LoanContactVO> voList = new ArrayList<>();
        for (LoanTransferContract c : records) {
            LoanContactVO vo = new LoanContactVO();
            BeanUtils.copyProperties(c, vo);
            vo.setPushState(c.getPushState() ? "已推送" : "未推送");
            vo.setGmtCreate(DateUtils.formatDate(c.getGmtCreate(), DateUtils.DATE_FORMAT));
            voList.add(vo);
        }
        page.setRecords(voList);
        return page;
    }

    /**
     * 保存 债转结清主数据
     *
     * @param operator   发起人
     * @param fileName   导入时的文件名称
     * @param quantities 导入的合同数量
     * @return 债转结清主数据记录主键id
     */
    private Long saveTransferMain(String operator, String fileName, int quantities) {
        LoanTransfer transfer = new LoanTransfer();
        transfer.setFileName(fileName).setWithAttachment(Boolean.FALSE)
                // 默认设置待审批状态
                .setQuantities(quantities).setCreateUser(operator).setApproveState(ApproveStateEnum.WAITING.getValue());
        transferMapper.insert(transfer);
        return transfer.getId();
    }

    /**
     * 保存债转结清主数据关联的合同号列表
     *
     * @param transferId     债转结清主键id
     * @param contractNoList 合同号列表
     */
    private void saveTranContracts(String operator, Long transferId, ExcelList<LoanTransferExcelDTO> contractNoList) {
        for (int idxStart = 0; idxStart < contractNoList.size(); idxStart += BATCH_SIZE) {
            int idxEnd = Math.min(idxStart + BATCH_SIZE, contractNoList.size());
            List<LoanTransferExcelDTO> dtoList = contractNoList.subList(idxStart, idxEnd);
            List<LoanTransferContract> contracts = new ArrayList<>();
            for (LoanTransferExcelDTO excelDTO : dtoList) {
                LoanTransferContract contract = new LoanTransferContract();
                contract.setTransferId(transferId).setContractNo(excelDTO.getContractNo())
                        .setPushState(Boolean.FALSE).setCreateUser(operator);
                contracts.add(contract);
            }
            contractMapper.insertBatchSomeColumn(contracts);
        }
    }

    @Override
    public Page<LoanImportVO> listImport(LoanImportDTO dto) {
    	return importInfoMapper.selectImportByPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOutcaseList(String operator, ExcelList<LoanOutcaseExcelDTO> outcaseList) {
        // 保存委外导入文件信息数据
    	saveImportInfo(operator, "0", outcaseList.getFileName(), outcaseList.size());
    	
    	//删除委外信息历史数据
    	outcaseInfoMapper.delete(null);
    	
        // 保存委外信息列表数据(量大分批次保存)
    	saveOutcaseInfo(outcaseList);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDeptList(String operator, ExcelList<LoanDeptExcelDTO> deptList) {
        // 保存债转导入文件信息数据
    	saveImportInfo(operator, "1", deptList.getFileName(), deptList.size());
    	
    	//删除债转信息历史数据
    	deptInfoMapper.delete(null);
    	
        // 保存债转信息列表数据(量大分批次保存)
    	saveDeptInfo(deptList);
    }
    
    /**
     * 保存导入文件信息数据
     *
     * @param operator   发起人
     * @param fileName   导入时的文件名称
     * @param count 导入的合同数量
     * @return 债转结清主数据记录主键id
     */
    private void saveImportInfo(String operator, String type, String fileName, int count) {
        OpImportInfo importInfo = new OpImportInfo();
        importInfo.setFileName(fileName).setType(type).setCreateUser(operator)
        .setCount(count).setGmtCreate(new Date());
        importInfoMapper.insert(importInfo);
    }
    
    /**
     * 保存委外信息列表
     *
     * @param outcaseList 委外信息列表
     */
    private void saveOutcaseInfo(ExcelList<LoanOutcaseExcelDTO> outcaseList) {
        for (int idxStart = 0; idxStart < outcaseList.size(); idxStart += BATCH_SIZE) {
            int idxEnd = Math.min(idxStart + 1000, outcaseList.size());
            List<LoanOutcaseExcelDTO> dtoList = outcaseList.subList(idxStart, idxEnd);
            List<OpOutcaseInfo> outcaseInfoList = new ArrayList<>();
            for (LoanOutcaseExcelDTO excelDTO : dtoList) {
            	OpOutcaseInfo outcaseInfo = new OpOutcaseInfo();
            	outcaseInfo.setApplicationId(excelDTO.getApplicationId())
            	.setCompanyTel(excelDTO.getCompanyTel());
            	outcaseInfoList.add(outcaseInfo);
            }
            outcaseInfoMapper.insertBatchSomeColumn(outcaseInfoList);
        }
    }
    
    /**
     * 保存债转信息列表
     *
     * @param deptList 债转信息列表
     */
    private void saveDeptInfo(ExcelList<LoanDeptExcelDTO> deptList) {
        for (int idxStart = 0; idxStart < deptList.size(); idxStart += BATCH_SIZE) {
            int idxEnd = Math.min(idxStart + 1000, deptList.size());
            List<LoanDeptExcelDTO> dtoList = deptList.subList(idxStart, idxEnd);
            List<OpDeptInfo> deptInfoList = new ArrayList<>();
            for (LoanDeptExcelDTO excelDTO : dtoList) {
            	OpDeptInfo deptInfo = new OpDeptInfo();
            	deptInfo.setApplicationId(excelDTO.getApplicationId()).setCompanyTel(excelDTO.getCompanyTel())
            	.setCompanyName(excelDTO.getCompanyName()).setDeptDate(excelDTO.getDeptDate());
            	deptInfoList.add(deptInfo);
            }
            deptInfoMapper.insertBatchSomeColumn(deptInfoList);
        }
    }
    
    @Override
    public LoanImportLabelVO getImportLabelInfo(String applicationId) {

        // 先判断债转
        WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(applicationId);
        if (Objects.nonNull(writeoffDTO)) {
            LoanImportLabelVO vo = new LoanImportLabelVO();
            vo.setType("1");
            vo.setCompanyName(writeoffDTO.getCompanyName());
            vo.setDeptDate(writeoffDTO.getTransactionDate());

            // 查询字典获取公司号码
            if (StringUtils.isNotBlank(writeoffDTO.getCompanyName())) {
                List<OpDictInfo> dicts = CommonUtils.getDict(LOAN_TRANSFER_DEPT_DICT, writeoffDTO.getCompanyName());
                if (CollectionUtils.isNotEmpty(dicts) && Objects.nonNull(dicts.get(0))) {
                    vo.setCompanyTel(dicts.get(0).getContent());
                }
            }
            return vo;

        }

        // 最后判断委外
        return importInfoMapper.getImportLabelInfo(applicationId);
    }


    @Override
    public Page<OrderLoanTransferVO> listOrderLoanTransfer(OrderLoanTransferDTO dto) {
        Page<OrderLoanTransferVO> page = transferMapper.selectOrderLoanTransferList(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);
        for (OrderLoanTransferVO record : page.getRecords()) {
            WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(record.getApplicationId());
            if (Objects.nonNull(writeoffDTO)) {
                record.setCompany(writeoffDTO.getCompanyName());
            }
            record.setId(CommonUtil.getRandomStr(11));
        }
        
        return page;
    }


    @Override
    public void sendTransferSms(Long id) {
        // 查询全部贷款列表
        List<LoanTransferContract> contractList = 
                contractMapper.selectList(Wrappers.lambdaQuery(LoanTransferContract.class)
                        .eq(LoanTransferContract::getTransferId, id)).stream()
                        .filter(item -> Objects.isNull(item.getSmsState()) || Boolean.FALSE.equals(item.getSmsState()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contractList)){
            log.warn("sendTransferSms {} 短信发送已完成，无需重复发送", id);
            throw new FastRuntimeException("短信发送已完成,无需重复发送");
        }
        List<String> idList = contractList.stream().map(LoanTransferContract::getContractNo).collect(Collectors.toList());

        // 查询全部贷款信息，并且根据userId分组
        Map<Integer, List<LoanApplicationDTO>> userIdMap = loanApplicationService.getApplicationListByApplications(idList).stream().collect(Collectors.groupingBy(LoanApplicationDTO::getBorrowerId));

        Long messageId = queryMessageId();
        userIdMap.forEach((userId, list) -> {
            String errorMsg = sendSms(userId, messageId);
            if (StringUtils.isNotBlank(errorMsg)){
                updateSmsState(id, list, false);
            } else {
                updateSmsState(id, list, true);
            }
        });


    }

    /**
     * 更新短信发送状态
     *
     * @param id transferId
     * @param list 合同号列表
     * @param state 短信发送状态
     */
    private void updateSmsState(Long id, List<LoanApplicationDTO> list, boolean state) {
        List<String> applicationIdList = list.stream().map(LoanApplicationDTO::getApplicationId).collect(Collectors.toList());
        contractMapper.updateSmsState(id, applicationIdList, state);
    }

    private String sendSms(Integer userId, Long messageId) {
        // 查询客服库，获取手机号，customerId
        List<DataCustomer> list = dataCustomerMapper.selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getUserId, userId).orderByDesc(DataCustomer::getGmtModify));
        if (CollectionUtils.isEmpty(list)){
            log.warn("userId:{},在客服系统查不到", userId);
            return "客服系统查询失败";
        }

        DataCustomer dataCustomer = list.get(0);


        MessageSendDTO sendDTO = new MessageSendDTO();
        sendDTO.setMessageId(messageId);
        sendDTO.setName(dataCustomer.getCustomerName());
        sendDTO.setMobile(dataCustomer.getMobile());
        sendDTO.setSendTime(DateUtil.dateToString(new Date()));
        sendDTO.setStaffId(String.valueOf(CommonUtils.getCurrentloggedStaffId()));
        sendDTO.setCustomerId(dataCustomer.getId());
        
        // 发送短信
        return messageService.sendMessage(sendDTO);

    }

    /**
     * 查询短信模板码值为 kf-ywclwb 的 messageId
     * @return
     */
    private Long queryMessageId() {
        MessageTemplateQueryDTO dto = new MessageTemplateQueryDTO();
        dto.setSmsCode("kf-ywclwb");
        dto.setCurrentPage(1);
        dto.setRowsPerPage(10);
        com.welab.xdao.context.page.Page<MessageTemplateVO> templateList = messageService.getMessageTemplateList(dto);
        if (Objects.isNull(templateList) || CollectionUtils.isEmpty(templateList.getList())){
            throw new FastRuntimeException("短信模板不存在: kf-ywclwb");
        }
        return templateList.getList().get(0).getId();
    }
}
