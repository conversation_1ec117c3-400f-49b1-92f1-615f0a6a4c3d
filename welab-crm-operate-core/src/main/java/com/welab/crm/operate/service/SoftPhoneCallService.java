package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.phone.AgentSkillUpdateDTO;
import com.welab.crm.operate.dto.phone.PhoneLoginInfoReqDTO;
import com.welab.crm.operate.dto.phone.PhoneSummaryReqDTO;
import com.welab.crm.operate.dto.phone.SoftPhoneReqDTO;
import com.welab.crm.operate.vo.agent.AgentVO;
import com.welab.crm.operate.vo.phone.IsSummaryVO;
import com.welab.crm.operate.vo.phone.PhoneLoginInfoResVO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.phone.SoftPhoneInfoVO;
import java.util.List;

/**
 * 软电话通话service
 *
 * <AUTHOR>
 * @date 2021/10/21 15:18
 */
public interface SoftPhoneCallService {


    /**
     * 添加软电话记录
     *
     * @param softPhoneReqDTO
     */
    void addSoftPhoneRecord(SoftPhoneReqDTO softPhoneReqDTO);


    /**
     * 分页查询软电话记录
     *
     * @param softPhoneReqDTO
     * @return
     */
    Page<SoftPhoneInfoVO> querySoftPhoneRecord(SoftPhoneReqDTO softPhoneReqDTO);

    /**
     * 保存电话小结，返回主键Id。
     *
     * @param reqDTO
     * @return 主键Id
     */
    void savePhoneSummary(PhoneSummaryReqDTO reqDTO);

    /**
     * 分页查询电话联系记录
     * @param reqDTO
     * @return
     */
    List<PhoneSummaryVO> queryPhoneSummaryByPage(PhoneSummaryReqDTO reqDTO);

    /**
     * 查询座机登录信息
     * @param reqDTO
     * @return
     */
    List<PhoneLoginInfoResVO> getPhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO);


    /**
     * 分页查询座机登陆信息
     * @param reqDTO
     * @return
     */
    Page<PhoneLoginInfoResVO> getLoginInfoByPage(PhoneLoginInfoReqDTO reqDTO);


    /**
     * 添加座机登录信息
     * @param reqDTO
     * @return
     */
    void addPhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO);


    /**
     * 更新座机登录信息
     * @param reqDTO
     * @return
     */
    void updatePhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO);

    /**
     * 删除座机登录信息
     * @param reqDTO
     * @return
     */
    public void deletePhoneLoginInfo(PhoneLoginInfoReqDTO reqDTO);


    /**
     * 根据天润通话主键查询是否有做电话小结
     * @param cdrMainUniqueId
     * @return
     */
    Boolean isSummary(String cdrMainUniqueId);


    /**
     * 判断当前用户最后一通电话是否做了小结
     * @return
     */
    IsSummaryVO lastPhoneIsSummary();

    /**
     * 根据登陆Id获取软电话人员信息
     * @param loginName
     * @return
     */
    PhoneLoginInfoResVO getPhoneLoginInfoByLoginName(String loginName);

    /**
     * 查询坐席列表
     * @return
     */
	List<AgentVO> queryAgentList();

    /**
     * 更新坐席技能值
     * @param dto
     * @return
     */
	void updateAgentSkill(AgentSkillUpdateDTO dto);

    /**
     * 获取AI小结
     * @param cdrMainUniqueId
     * @return
     */
    String queryAiSummary(String cdrMainUniqueId);

    /**
     * 获取对话文本
     * @param cdrMainUniqueId
     * @return
     */
    String queryDialogueText(String cdrMainUniqueId);

    /**
     * 查询ai小结并检查录音是否存在
     * @param cdrMainUniqueId
     * @return
     */
    String queryAiSummaryCheck(String cdrMainUniqueId);

    /**
     * 重试获取ai小结
     * @param cdrMainUniqueId
     * @return
     */
    String queryAiSummaryRetry(String cdrMainUniqueId);
}
