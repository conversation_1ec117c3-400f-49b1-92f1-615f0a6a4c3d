package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.reduce.*;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundSubmitVO;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundVO;
import com.welab.crm.operate.vo.reduce.ReduceDetailReportVO;
import com.welab.crm.operate.vo.reduce.ReduceStatisticsReportVO;
import com.welab.frs.account.vo.AllowanceCalculateVO;
import com.welab.frs.account.vo.AllowanceRecordVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 减免、退款服务
 */
public interface ReduceAndRefundService {

	/**
	 * 查询提交界面的信息
	 * @param applicationList 贷款号列表
	 */
	List<ReduceAndRefundSubmitVO> querySubmitData(List<String> applicationList);


	/**
	 * 计算可减免金额
	 * @param principal 本金合计
	 * @param tenor 期数
	 * @param otherFee 除本金外的其他金额合计
	 */
	BigDecimal queryReducibleAmount(BigDecimal principal, Integer tenor, BigDecimal otherFee);


	/**
	 * 申请减免
	 *
	 * @param requestNo   唯一流水号
	 * @param currentTime
	 */
	void reduceApply(ReduceApplyDTO dto, String requestNo, Date currentTime);


	/**
	 * 退款申请
	 *
	 * @param requestNo   唯一流水号
	 * @param currentTime
	 */
	void refundApply(RefundApplyDTO dto, String requestNo, Date currentTime);


	/**
	 * 减免、退款申请提交
	 */
	void submitReduceAndRefundApply(ReduceAndRefundApplySubmitDTO dto);


	/**
	 * 审批减免、退款记录
	 */
	void approvalRecord(ReduceApprovalDTO dto);


	/**
	 * 查询申请记录
	 */
	Page<ReduceAndRefundVO> queryApplyRecord(RecordQueryDTO dto);


	/**
	 * 推送减免数据到资金
	 *
	 * @param requestNo     流水号
	 * @param approvalStaff  审批人
	 */
	void pushReduceRecordToLender(String requestNo, String approvalStaff);


	/**
	 * 查询审批界面的信息
	 * @param requestNo 流水号
	 */
	ReduceAndRefundApplySubmitDTO queryApprovalRecord(String requestNo);


	/**
	 * 查询减免退款明细报表
	 */
	Page<ReduceDetailReportVO> reduceAndRefundDetailReport(ReduceReportDTO dto);


	/**
	 * 减免退款统计报表查询
	 */
	Page<ReduceStatisticsReportVO> reduceStatisticReport(ReduceReportDTO dto);


	/**
	 * 根据合同号查询他的减免成功记录
	 * @param appNo
	 * @return
	 */
	List<AllowanceRecordVO> queryReducedRecord(String appNo);

	/**
	 * 保存附件
	 * @param dto
	 * @return 主键id
	 */
	Long saveAttachment(ReduceAttachmentDTO dto);


	/**
	 * 获取附件下载地址
	 * @param id
	 * @return
	 */
	String getAttachmentUrl(Long id);

	/**
	 * 删除附件
	 * @param id
	 */
	void deleteAttachment(Long id);

	/**
	 * 校验贷款号是否可以减免
	 * @param appNoList
	 * @return
	 */
	List<AllowanceCalculateVO> checkReduce(List<String> appNoList);
}
