package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 过河兵 工单关联贷款信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("elite_workorder_loans")
public class EliteWorkorderLoans implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 是否标记
     */
    private String flag;

    /**
     * 关联工单表
     */
    private String objectiveGuid;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 产品名
     */
    private String productName;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 订单状态
     */
    private String orderStatus;


}
