package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ConWebotHistory;
import com.welab.crm.operate.dto.report.FaceReportDTO;
import com.welab.crm.operate.vo.face.FaceCauseVO;
import com.welab.crm.operate.vo.face.FaceDayVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 青鸾同步创研人脸验证记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2023-02-16
 */
public interface ConWebotHistoryMapper extends BaseMapper<ConWebotHistory> {

    Page<ConWebotHistory> getDetailsByPage(Page<ConWebotHistory> page, @Param("filter") FaceReportDTO dto);

    Page<FaceDayVO> getDayDataByPage(Page<ConWebotHistory> page, @Param("filter") FaceReportDTO dto);

    Page<FaceCauseVO> getCauseByPage(Page<ConWebotHistory> page, @Param("filter") FaceReportDTO dto);

    FaceCauseVO getCauseTotal(@Param("filter") FaceReportDTO dto);
}
