package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CsRepayLinkRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkQueryDTO;
import com.welab.crm.operate.vo.repaylink.H5RepayLinkVO;
import com.welab.crm.operate.vo.repaylink.H5RepaySummaryReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * h5还款链接发送记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
public interface CsRepayLinkRecordMapper extends BaseMapper<CsRepayLinkRecord> {

    /**
     * 查询h5还款链接发送记录
     * 
     * @param objectPage 分页参数
     * @param dto 请求参数
     * @return 还款列表
     */
    Page<H5RepayLinkVO> queryRecord(Page<Object> objectPage, @Param("dto") H5RepayLinkQueryDTO dto);

	/**
	 * 获取有记录的所有发送组
	 * @return
	 */
	List<String> queryRecordByGroups();

	/**
	 * 查询h5还款统计
	 *
	 * @param dto
	 * @return
	 */
	List<H5RepaySummaryReportVO> queryRepaySummary(ReportBaseDTO dto);
	
}
