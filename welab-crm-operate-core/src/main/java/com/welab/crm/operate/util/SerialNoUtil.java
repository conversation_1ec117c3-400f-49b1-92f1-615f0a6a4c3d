package com.welab.crm.operate.util;

import com.welab.common.utils.DateUtil;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description 序列号生成工具类
 * @date 2021/12/14
 */
public class SerialNoUtil {

    public static final int APPLICATION_ID_LENGTH = 8;
    public static final String FORMAT_BASE = "0";
    public static final String APPLICATION_ID_RANDOM_FORMAT = StringUtils.repeat(FORMAT_BASE, APPLICATION_ID_LENGTH);

    public static String serialNo(String prefix) {
        StringBuilder builder = new StringBuilder();
        builder.append(prefix);
        builder.append(DateUtil.dateToString((DateUtil.getCurrentDateTime()), "yyyyMMddHHmmssSSS"));
        builder.append(randomNumberString(APPLICATION_ID_RANDOM_FORMAT, APPLICATION_ID_LENGTH));
        return builder.toString();
    }

    public static String serialNo() {
        StringBuilder builder = new StringBuilder();
        builder.append(DateUtil.dateToString((DateUtil.getCurrentDateTime()), "yyyyMMddHHmmssSSS"));
        builder.append(randomNumberString(APPLICATION_ID_RANDOM_FORMAT, APPLICATION_ID_LENGTH));
        return builder.toString();
    }

    /**
     * 生成一串随机数组成的字符串
     *
     * @param format
     * @param randomSize
     * @return
     */
    private static String randomNumberString(String format, int randomSize) {
        DecimalFormat numberFormat = new DecimalFormat(format);
        return numberFormat.format((long) (new SecureRandom().nextDouble() * Math.pow(10, randomSize)));
    }
}
