package com.welab.crm.operate.service.impl;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpTemplateInfo;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.workorder.TemplateInfoReqDTO;
import com.welab.crm.operate.mapper.OpTemplateInfoMapper;
import com.welab.crm.operate.service.TemplateInfoService;
import com.welab.crm.operate.vo.workorder.TemplateInfoVO;
import com.welab.exception.FastRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 工单审批模板service服务
 * <AUTHOR>
 * @date 2021-10-26
 */
@Slf4j
@Service
public class TemplateInfoServiceImpl implements TemplateInfoService {

    @Resource
    private OpTemplateInfoMapper templateInfoMapper;

    @Override
    public Page<TemplateInfoVO> queryTemplateInfoList(TemplateInfoReqDTO reqDTO) {
        log.info("queryTemplateInfoList,reqDTO:{}", JSON.toJSONString(reqDTO));

        return templateInfoMapper
                .queryTemplateInfoByPage(new Page<TemplateInfoVO>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
    }

    @Override
    public void addTemplateInfo(TemplateInfoReqDTO reqDTO) {
    	OpTemplateInfo opTemplateInfo = new OpTemplateInfo();
    	BeanUtils.copyProperties(reqDTO, opTemplateInfo);
    	templateInfoMapper.insert(opTemplateInfo);
    }

    @Override
    public void updateTemplateInfo(TemplateInfoReqDTO reqDTO) {
    	OpTemplateInfo opTemplateInfo = null;
    	
    	if(reqDTO.getId() != null) {
    		opTemplateInfo = templateInfoMapper.selectById(reqDTO.getId());
    		if (opTemplateInfo != null) {
    			BeanUtils.copyProperties(reqDTO, opTemplateInfo);
    			templateInfoMapper.updateById(opTemplateInfo);
    		}else {
    			log.error("工单审批模板记录ID{}不存在",reqDTO.getId());
        		throw new FastRuntimeException("工单审批模板记录ID{}不存在",String.valueOf(reqDTO.getId()));
    		}
    	}else {
    		log.error("工单审批模板记录ID{}不存在！");
    		throw new FastRuntimeException("工单审批模板记录ID{}不存在");
    	}
    }
    
    @Override
	public boolean deleteTemplateInfo(BatchInfoReqDTO ids) {
		try {
			for (Long id : ids.getIds()) {
				templateInfoMapper.deleteById(id);
			}
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new FastRuntimeException("根据id删除工单审批模板产生异常......", e);
		}
	}
}
