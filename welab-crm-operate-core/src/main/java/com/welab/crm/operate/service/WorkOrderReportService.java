package com.welab.crm.operate.service;

import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.report.*;
import com.welab.crm.operate.dto.transferReport.TransferReportDetailDTO;
import com.welab.crm.operate.dto.transferReport.TransferReportRangeDTO;
import com.welab.crm.operate.vo.transferReport.RangeTransferReportVO;
import com.welab.crm.operate.vo.transferReport.TransferReportDetailVO;
import com.welab.crm.operate.vo.woReport.*;
import com.welab.xdao.context.page.Page;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version v1.0
 * @title 工单报表服务
 * @description 工单报表服务
 * @date 2021/12/24
 */
public interface WorkOrderReportService {

    /**
     * 工单统计报表投诉类报表
     */
    ReportSummaryVO getSummaryComplaint(WoReportDTO dto);

    /**
     * 工单统计报表产品类报表
     */
    ReportSummaryVO getSummaryProduct(WoReportDTO dto);

    /**
     * 工单统计报表，投诉及产品一览
     */
    List<ReportSummaryVO> getSummary(WoReportDTO dto);

    /**
     * 工单统计报表工单类型
     */
    Page<ReportSummaryTypeVO> getSummaryType(ReportSummaryTypeDTO dto);

    /**
     * 工单明细报表
     */
    Page<ReportDetailsVO> getDetails(WoReportDTO dto, boolean isExport);

    void exportReportDetail(HttpServletResponse response,WoReportDTO dto,String fileName);

    /**
     * 分单统计报表
     */
    Page<ReportAssignmentSummaryVO> getAssignmentSummary(ReportAssignmentSummaryDTO dto);

    /**
     * 查询分单统计列表
     */
    List<ReportAssignmentSummaryVO> getAssignmentSummaryList(ReportAssignmentSummaryDTO dto);

    /**
     * 分单明细报表
     */
    Page<ReportAssignmentDetailsVO> getAssignmentDetails(WoReportDTO dto);

    /**
     * 查询分单明细列表
     */
    List<ReportAssignmentDetailsVO> getAssignmentDetailsList(WoReportDTO dto);

    /**
     * 工单效能统计报表
     */
    Page<ReportEfficiencySummaryVO> getEfficiencySummary(ReportEfficiencySummaryDTO dto);

    /**
     * 工单效能统计报表
     */
    List<ReportEfficiencySummaryVO> getEfficiencySummaryList(ReportEfficiencySummaryDTO dto);

    /**
     * 外呼效能统计报表
     */
    Page<OutboundEfficiencySummaryVO> getOutboundEfficiencySummary(OutboundEfficiencySummaryDTO dto);

    /**
     * 查询外呼效能统计列表
     */
    List<OutboundEfficiencySummaryVO> getOutboundEfficiencySummaryList(OutboundEfficiencySummaryDTO dto);

    /**
     * 查询工单中央监控
     */
    Page<CentralMonitoringVO> getCentralMonitoring(CentralMonitoringDTO dto);


    /**
     * 查询债转投诉统计报表-周期
     * @param dto
     * @return
     */
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<RangeTransferReportVO> queryTransferRangeReport(TransferReportRangeDTO dto);

    /**
     * 查询债转投诉统计报表-周期，返回list
     * @param dto
     * @return
     */
    List<RangeTransferReportVO> queryTransferRangeReportList(TransferReportRangeDTO dto);

    /**
     * 查询债转统计报表-月度
     * @param dto
     * @return
     */
    List<List<String>> queryTransferMonthReport(TransferReportRangeDTO dto);

    /**
     * 查询债转明细报表
     * @param dto
     * @return
     */
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<TransferReportDetailVO> queryTransferDetailReport(TransferReportDetailDTO dto);


    /**
     * 查询债转明细报表数据返回list
     * @param dto
     * @return
     */
    List<TransferReportDetailVO> queryTransferDetailReportList(TransferReportDetailDTO dto);

    com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReportDetailsVO> queryComplaintEscalationReport(WoReportDTO dto);

    List<ComplaintEscalationStatisticsReportVO> queryComplaintEscalationStatisticsReport(ReportBaseDTO dto);

    List<ComplaintEscalationReasonReportVO> queryComplaintEscalationReasonReport(WoReportDTO dto);
}
