package com.welab.crm.operate.mapper;

import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.TmkUuid;
import com.welab.crm.operate.dto.telemarketing.TmkReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO;
import com.welab.crm.operate.vo.telemarketing.TmkUuidReportVO;

/**
 * <p>
 * 电销UUID表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface TmkUuidMapper extends BaseMapper<TmkUuid> {


    /**
     * 查询UUID电销任务
     * @param page
     * @param tmkReqDTO
     * @return
     */
    Page<TmkTaskDetailVO> queryUuidTaskListPage(Page<TmkTaskDetailVO> page, @Param("dto") TmkReqDTO tmkReqDTO);


    /**
     * 查询任务总数sql
     * @param tmkReqDTO
     * @return
     */
    Long queryTaskCount(@Param("dto") TmkReqDTO tmkReqDTO);
    
    /**
     * 查询UUID外拨营销小结数据
     * @param dto
     * @return
     */
    Page<TmkUuidReportVO> queryTmkUuidData(Page<TmkUuidReportVO> page, @Param("dto") TmkTransformReportReqDTO dto);

    /**
     * 查询UUID转化数据
     * @param dto
     * @return
     */
    List<TmkTransformReportVO> queryUuidTransformData(@Param("dto") TmkTransformReportReqDTO dto);

    /**
     * 查询号码包定义列表
     */
    List<String> selectPackageDefine(@Param("date") Date gmtCreate);
}
