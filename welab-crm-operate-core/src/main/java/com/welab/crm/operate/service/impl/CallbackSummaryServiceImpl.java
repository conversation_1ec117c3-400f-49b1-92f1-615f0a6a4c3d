package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.welab.crm.interview.dto.ConPhoneSummaryDTO;
import com.welab.crm.interview.service.ConPhoneSummaryService;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummaryReportQueryDTO;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummarySaveDTO;
import com.welab.crm.operate.enums.CallTypeEnum;
import com.welab.crm.operate.enums.CallbackResultTypeEnum;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.service.CallbackSummaryService;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryDictVO;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryReportVO;
import com.welab.crm.operate.vo.callbackSummary.CallbackTypeVO;
import com.welab.crm.operate.vo.callbackSummary.ReasonTypeVO;
import com.welab.crm.operate.vo.workorder.WorkOrderLogVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 回电小结服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class CallbackSummaryServiceImpl implements CallbackSummaryService {

    private static final String CALL_BACK_BUSINESS_TYPE = "callback_business_type";
    private static final String CALL_BACK_RESULT = "callback_result";

    @Resource
    private OpDictInfoMapper opDictInfoMapper;

    @Resource
    private CallbackSummaryMapper callbackSummaryMapper;

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private DataCustomerMapper customerMapper;

    @Resource
    private AsyncTaskExecutor phoneSummaryExecutor;

    @Resource
    private ConPhoneSummaryService conPhoneSummaryService;

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;
    
    @Resource
    private WoTaskMapper woTaskMapper;

    @Override
    public CallbackSummaryDictVO queryCallbackSummaryDict() {
        CallbackSummaryDictVO callbackSummaryDictVO = new CallbackSummaryDictVO();
        callbackSummaryDictVO.setBusinessTypeList(getContentListByCategory(CALL_BACK_BUSINESS_TYPE));
        callbackSummaryDictVO.setCallbackResultList(getContentListByCategory(CALL_BACK_RESULT));
        callbackSummaryDictVO.setNotConnectedTypeList(getContentListByCategory(CallbackResultTypeEnum.NOT_CONNECTED.getName()));
        callbackSummaryDictVO.setUnderNegotiationTypeList(getContentListByCategory(CallbackResultTypeEnum.UNDER_NEGOTIATION.getName()));
        callbackSummaryDictVO.setConsensusTypeList(getContentListByCategory(CallbackResultTypeEnum.CONSENSUS.getName()));
        callbackSummaryDictVO.setNegotiationFailedTypeList(getContentListByCategory(CallbackResultTypeEnum.NEGOTIATION_FAILED.getName()));
        return callbackSummaryDictVO;
    }

    @Override
    public Map<String, List<CallbackTypeVO>> queryCallbackSummaryDict2() {
        Map<String, List<CallbackTypeVO>> map = new HashMap<>();
        List<CallbackTypeVO> businessTypeList = getContentListByCategory(CALL_BACK_BUSINESS_TYPE);
        map.put(CALL_BACK_BUSINESS_TYPE, businessTypeList);
        List<CallbackTypeVO> callbackResultList = getContentListByCategory(CALL_BACK_RESULT);
        map.put(CALL_BACK_RESULT, callbackResultList);
        if (CollectionUtils.isEmpty(callbackResultList)) {
            return map;
        }
        List<OpDictInfo> dicts = CommonUtils.getDict(CALL_BACK_RESULT, null);

        for (OpDictInfo dict : dicts) {
            map.put(dict.getType(), getContentListByCategory(dict.getType()));
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCallbackSummaryDict(CallbackSummaryDictVO dictVO) {
        updateCallbackSummaryDict(dictVO.getBusinessTypeList(), CALL_BACK_BUSINESS_TYPE);
        updateCallbackSummaryDict(dictVO.getNotConnectedTypeList(), CallbackResultTypeEnum.NOT_CONNECTED.getName());
        updateCallbackSummaryDict(dictVO.getUnderNegotiationTypeList(), CallbackResultTypeEnum.UNDER_NEGOTIATION.getName());
        updateCallbackSummaryDict(dictVO.getConsensusTypeList(), CallbackResultTypeEnum.CONSENSUS.getName());
        updateCallbackSummaryDict(dictVO.getNegotiationFailedTypeList(), CallbackResultTypeEnum.NEGOTIATION_FAILED.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCallbackSummaryDict2(Map<String, List<CallbackTypeVO>> map) {
        // 去除不需要更新的字段
        map.remove(CALL_BACK_RESULT);
        map.forEach((k, v) -> updateCallbackSummaryDict(v, k));

    }

    @Override
    public void addCallbackSummary(CallbackSummarySaveDTO dto) {
        if (StringUtils.isBlank(dto.getCdrMainUniqueId())) {
            log.warn("addCallbackSummary 无关联通话记录:{}", JSON.toJSONString(dto));
        }

        if (StringUtils.isBlank(dto.getUuid())) {
            log.warn("addCallbackSummary 客户uuid为空:{}", JSON.toJSONString(dto));
        }
        CallbackSummary summary = new CallbackSummary();
        buildSummary(dto, summary);
        callbackSummaryMapper.insert(summary);
        dto.getOrderNoList().forEach(orderNo -> {
            WoTask woTask = new WoTask();
            woTask.setOrderNo(orderNo);
            woTask.setResponseTimeOut(Boolean.FALSE);
            woTask.setResolveContent(dto.getResolveContent());
            woTaskMapper.updateOrder(woTask);
        });
        // 同步催收
        final CallbackSummary s = summary;
        phoneSummaryExecutor.execute(()->pushSummaryToCollection(s));
    }

    private void pushSummaryToCollection(CallbackSummary summary) {
        try {
            LambdaQueryWrapper<CallbackSummary> wrapper = Wrappers.<CallbackSummary>lambdaQuery().eq(CallbackSummary::getCdrMainUniqueId, summary.getCdrMainUniqueId());
            List<CallbackSummary> infoList = callbackSummaryMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(infoList)) {
                log.error("pushSummaryToCollection get callbackInfo null: {}", summary);
                return;
            }
            LambdaQueryWrapper<ConPhoneCallInfo> cpc = Wrappers.<ConPhoneCallInfo>lambdaQuery().eq(ConPhoneCallInfo::getCdrMainUniqueId, summary.getCdrMainUniqueId());
            List<ConPhoneCallInfo> info = conPhoneCallInfoMapper.selectList(cpc);
            if (CollectionUtils.isEmpty(info)) {
                log.error("pushSummaryToCollection get callback info null: {}", summary);
                return;
            }
            CallbackSummary callbackInfo = infoList.get(0);
            ConPhoneSummaryDTO dto = new ConPhoneSummaryDTO();
            if (!CallTypeEnum.IN_CALL.getValue().equals(info.get(0).getCdrCallType())) {
                dto.setCdrCalleeCno(info.get(0).getCdrCno());
            } else {
                dto.setCdrCalleeCno(info.get(0).getCdrCalleeCno());
            }
            dto.setMainId(callbackInfo.getId());
            dto.setCdrStartTime(info.get(0).getCdrStartTime());
            dto.setSaveSummaryTime(callbackInfo.getGmtCreate());
            dto.setCallSummary(callbackInfo.getBusinessType() + "," + callbackInfo.getContactResult());
            dto.setCallComment(callbackInfo.getReasonType() + "," + callbackInfo.getCallbackComment());
            dto.setGmtCreate(callbackInfo.getGmtCreate());
            dto.setCreateUser(callbackInfo.getStaffId().toString());
            dto.setGmtModify(callbackInfo.getGmtModify());
            dto.setLstUpdUser(callbackInfo.getStaffId().toString());
            LambdaQueryWrapper<DataCustomer> customerWrapper = Wrappers.<DataCustomer>lambdaQuery()
                    .eq(DataCustomer::getUuid, callbackInfo.getUuid()).orderByDesc(DataCustomer::getGmtCreate);
            List<DataCustomer> dataCustomers = customerMapper.selectList(customerWrapper);
            if (CollectionUtils.isEmpty(dataCustomers)) {
                log.info("pushSummaryToCollection return,  cause userId null, param: {}", summary);
                return;
            } else {
                DataCustomer customer = dataCustomers.get(0);
                dto.setUserId(customer.getUserId());
                dto.setCustomerId(customer.getId());
                // 取实际拨打的号码
                dto.setMobile(info.get(0).getCdrCustomerNumber());
                //dto.setMobile(customer.getMobile());
            }
            conPhoneSummaryService.savePhoneSummary(dto);
        } catch (Exception e) {
            log.error("pushSummaryToCollection exception: {}, param: {}", e.getMessage(), summary, e);
        }
    }

    @Override
    public Page<CallbackSummaryReportVO> queryCallbackSummaryReport(CallbackSummaryReportQueryDTO dto) {
        // 校验时间跨度不超过3个月
        tmkReportService.validTime(dto.getStartTime(), dto.getEndTime());

        Page<CallbackSummaryReportVO> resultPage =
            callbackSummaryMapper.queryReportPage(new Page<>(dto.getCurrentPage(), dto.getRowsPerPage()), dto);

        if (Objects.nonNull(resultPage) && CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            resultPage.getRecords().forEach(this::fillReportVO);
        }
        return resultPage;
    }

    @Override
    public List<WorkOrderLogVO> queryCallbackSummaryLogList(String orderNo) {
        return callbackSummaryMapper.querySummaryLogByOrderNo(orderNo);
    }

    private void fillReportVO(CallbackSummaryReportVO summaryVO) {
        // 回电结果类型是在数据库中用json字符串保存的，上面sql查询的时候暂存在 contactResult 字段
        String typeJson = summaryVO.getContactResult();
        summaryVO.setContactResult(null);
        if (StringUtils.isBlank(typeJson)) {
            return;
        }

        // 解析json，key 是回电结果类型，value 是回电结果类型对应的备注(邮箱、黑产等)
        JSONObject typeJsonObject = JSON.parseObject(typeJson);
        String keyStr = Joiner.on("/").join(typeJsonObject.keySet());

        // 根据中文名，获取对应的枚举对象
        CallbackResultTypeEnum typeEnum = CallbackResultTypeEnum.getEnumByDesc(summaryVO.getCallbackResult());
        if (Objects.isNull(typeEnum)) {
            return;
        }

        /*
        不同的回电结果对应不同的类型
        未接通-联系结果
        协商一致-处理进度
        协商中-投诉原因
        协商失败-失败原因
        */
        switch (typeEnum) {
            case NOT_CONNECTED:
                summaryVO.setContactResult(keyStr);
                break;
            case UNDER_NEGOTIATION:
                summaryVO.setComplaintReason(keyStr);
                break;
            case CONSENSUS:
                summaryVO.setProcessingProgress(keyStr);
                break;
            case NEGOTIATION_FAILED:
                summaryVO.setFailReason(keyStr);
                break;
            default:
        }

        if (typeJsonObject.containsKey("邮箱")) {
            summaryVO.setEmail(typeJsonObject.getString("邮箱"));
        }

        if (typeJsonObject.containsKey("黑产")) {
            summaryVO.setBlackIndustry("是");
            summaryVO.setBlackIndustryComment(typeJsonObject.getString("黑产"));
        } else {
            summaryVO.setBlackIndustry("否");
        }
    }

    private void buildSummary(CallbackSummarySaveDTO dto, CallbackSummary summary) {
        BeanUtils.copyProperties(dto, summary);
        summary.setContactResult(dto.getCallbackResult());
        summary.setStaffId(CommonUtils.getCurrentloggedStaffId());
        List<ReasonTypeVO> reasonTypeList = dto.getReasonTypeList();
        JSONObject jObject = new JSONObject();
        for (ReasonTypeVO vo : reasonTypeList) {
            jObject.put(vo.getName(), StringUtils.isBlank(vo.getRemark()) ? "" : vo.getRemark());
        }
        summary.setReasonType(jObject.toJSONString());
        summary.setOrderNo(String.join("," , dto.getOrderNoList()));
        summary.setResolveContent(dto.getResolveContent());
    }

    private List<CallbackTypeVO> getContentListByCategory(String category) {
        List<OpDictInfo> dictList = CommonUtils.getDict(category, null);
        if (CollectionUtils.isEmpty(dictList)) {
            return Collections.emptyList();
        }
        return dictList.stream().map(dict -> {
            CallbackTypeVO vo = new CallbackTypeVO();
            vo.setName(dict.getContent());
            vo.setRemark(StringUtils.isBlank(dict.getDetail()) ? 0 : Integer.parseInt(dict.getDetail()));
            return vo;
        }).collect(Collectors.toList());
    }

    private void updateCallbackSummaryDict(List<CallbackTypeVO> voList, String category) {
        String joinStr = voList.stream().map(CallbackTypeVO::concatStr).sorted().collect(Collectors.joining());
        List<CallbackTypeVO> dbList = getContentListByCategory(category);
        String dbStr = dbList.stream().map(CallbackTypeVO::concatStr).sorted().collect(Collectors.joining());
        if (joinStr.equals(dbStr)) {
            return;
        }
        delAndInsertSummary(category, voList);

    }

    private void delAndInsertSummary(String category, List<CallbackTypeVO> voList) {
        opDictInfoMapper.delete(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, category));
        opDictInfoMapper.insertBatchSummaryDict(voList, category);
    }

}
