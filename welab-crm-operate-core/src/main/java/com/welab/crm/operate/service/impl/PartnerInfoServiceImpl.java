package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.OpPartnerInfo;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.partner.PartnerInfoImportDTO;
import com.welab.crm.operate.mapper.OpPartnerInfoMapper;
import com.welab.crm.operate.service.PartnerInfoService;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资金方信息维护
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class PartnerInfoServiceImpl implements PartnerInfoService {

    @Resource
    private OpPartnerInfoMapper opPartnerInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importPartnerInfo(List<PartnerInfoImportDTO> list) {
        List<OpPartnerInfo> insertList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();
        for (PartnerInfoImportDTO importDTO : list) {
            insertList.add(convertDtoToDomain(importDTO));
            OpPartnerInfo existInfo = queryPartnerInfoByPartnerName(importDTO.getPartnerName());
            if (Objects.nonNull(existInfo)) {
                deleteList.add(existInfo.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            opPartnerInfoMapper.deleteBatchIds(deleteList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            opPartnerInfoMapper.insertBatchSomeColumn(insertList);
        }
    }

    private OpPartnerInfo convertDtoToDomain(PartnerInfoImportDTO importDTO) {
        OpPartnerInfo partnerInfo = new OpPartnerInfo();
        BeanUtils.copyProperties(importDTO, partnerInfo);
        partnerInfo.setCreateTime(new Date());
        partnerInfo.setUpdateTime(new Date());
        return partnerInfo;
    }

    private OpPartnerInfo queryPartnerInfoByPartnerName(String partnerName) {
        return opPartnerInfoMapper
            .selectOne(Wrappers.lambdaQuery(OpPartnerInfo.class).eq(OpPartnerInfo::getPartnerName, partnerName));
    }

    @Override
    public void addPartnerInfo(PartnerInfoImportDTO dto) {
        OpPartnerInfo existInfo = queryPartnerInfoByPartnerName(dto.getPartnerName());
        if (Objects.nonNull(existInfo)) {
            throw new FastRuntimeException("存在同名资金方");
        }
        OpPartnerInfo partnerInfo = new OpPartnerInfo();
        BeanUtils.copyProperties(dto, partnerInfo);
        partnerInfo.setCreateTime(new Date());
        partnerInfo.setUpdateTime(new Date());
        opPartnerInfoMapper.insert(partnerInfo);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePartnerInfo(List<PartnerInfoImportDTO> list) {
        List<OpPartnerInfo> insertList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();
        for (PartnerInfoImportDTO dto : list) {
            if (Objects.nonNull(dto.getId())) {
                deleteList.add(dto.getId());
            }
            insertList.add(convertDtoToDomain(dto));

        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            opPartnerInfoMapper.deleteBatchIds(deleteList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            opPartnerInfoMapper.insertBatchSomeColumn(insertList);
        }
    }

    @Override
    public PartnerInfoImportDTO queryPartnerInfoByNameAndCode(String partnerName) {
        OpPartnerInfo partnerInfo = opPartnerInfoMapper.selectOne(Wrappers.lambdaQuery(OpPartnerInfo.class)
            .eq(OpPartnerInfo::getPartnerName, partnerName));
        if (Objects.isNull(partnerInfo)){
            log.warn("没查到资金方信息,partnerName:{}", partnerName);
            throw new FastRuntimeException("没查到资金方信息");
        }
        return convertDomainToDto(partnerInfo);
    }

    private PartnerInfoImportDTO convertDomainToDto(OpPartnerInfo partnerInfo) {
        PartnerInfoImportDTO dto = new PartnerInfoImportDTO();
        BeanUtils.copyProperties(partnerInfo, dto);
        return dto;
    }

    @Override
    public Page<PartnerInfoImportDTO> queryPartnerInfo(BaseReqDTO dto) {
        Page<OpPartnerInfo> page = opPartnerInfoMapper.selectPage(new Page<>(dto.getCurPage(), dto.getPageSize()),
            Wrappers.lambdaQuery(OpPartnerInfo.class));
        Page<PartnerInfoImportDTO> resultPage = new Page<>();
        BeanUtils.copyProperties(page, resultPage);
        List<PartnerInfoImportDTO> resultList = new ArrayList<>();
        page.getRecords().forEach(item -> {
            PartnerInfoImportDTO importVO = new PartnerInfoImportDTO();
            BeanUtils.copyProperties(importVO, importVO);
            resultList.add(importVO);

        });
        resultPage.setRecords(resultList);
        return resultPage;
    }

    @Override
    public List<PartnerInfoImportDTO> queryPartnerInfo() {
        List<OpPartnerInfo> partnerInfoList = opPartnerInfoMapper.selectList(null);
        return partnerInfoList.stream().map(this::convertDomainToDto).collect(Collectors.toList());
    }
    

}
