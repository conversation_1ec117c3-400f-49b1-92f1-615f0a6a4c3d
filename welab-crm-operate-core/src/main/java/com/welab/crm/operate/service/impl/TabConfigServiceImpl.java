package com.welab.crm.operate.service.impl;

import com.welab.common.keygen.KeyGenerator;
import com.welab.crm.operate.domain.CustTabColumnConfig;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.CustTabColumnConfigMapper;
import com.welab.crm.operate.service.TabConfigService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/1
 */
@Slf4j
@Service
public class TabConfigServiceImpl implements TabConfigService {

    @Resource
    private CustTabColumnConfigMapper configMapper;

    @Resource
    private KeyGenerator keyGenerator;

    @Override
    public List<String> getNotDisplayedColNames(String staffId, String tabName) {
        List<CustTabColumnConfig> configs = configMapper.selectByStaffIdAndTabName(staffId, tabName);
        if (CollectionUtils.isEmpty(configs)) {
            // 若未配置过，则无不显示的列，即展示所有的列
            return new ArrayList<>();
        }
        return configs.stream().map(config -> {
            return config.getTabColumn();
        }).collect(Collectors.toList());
    }

    @Override
    public void updateNotDisplayedColNames(String staffId, String tabName, List<String> colNames) {
        configMapper.deleteByStaffIdAndTabName(staffId, tabName);
        List<CustTabColumnConfig> configs = new ArrayList<>();
        for (String colName : colNames) {
            CustTabColumnConfig config = new CustTabColumnConfig();
            config.setId(keyGenerator.generateKey());
            config.setStaffId(staffId);
            config.setTabName(tabName);
            config.setTabColumn(colName);
            configs.add(config);
        }
        configMapper.insertBatch(configs);
    }
}
