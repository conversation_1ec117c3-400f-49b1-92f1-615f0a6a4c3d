/**
 * @Title: CrmColOrgStaffServiceImpl.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.domain.InPhoneLoginInfo;
import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.enums.LoginTypeEnum;
import com.welab.crm.operate.enums.WebSocketMsgTypeEnum;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.mapper.InPhoneLoginInfoMapper;
import com.welab.crm.operate.model.ColStaffModel;
import com.welab.crm.operate.service.ICrmOrgStaffService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.websocket.util.WebSocketUtil;
import com.welab.crm.operate.ws.SendMsgDTO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 员工service
 * <AUTHOR>
 * @date 2021-10-28 16:16:15
 * @version v1.0
 */
@Service
@Slf4j
public class CrmOrgStaffServiceImpl implements ICrmOrgStaffService{
    
    @Autowired
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Value("${exam.system.url.pre}")
    private String examUrlPre;


    private static final String ADD = "add";
    private static final String MODIFY = "modify";
    private static final String DELETE = "delete";
	@Autowired
	private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;

    @Override
    public ColStaffResVO getColStaff(String mobile) {
        try {
            ColStaffModel colStaffModel = new ColStaffModel();
            if (StringUtils.isEmpty(mobile)) {
                throw new FastRuntimeException("system.query.error.default", "非法请求");
            }
            colStaffModel.setStaffMobile(mobile);
            colStaffModel.setIsStatus(1);
            InAuthCrmStaff staff = findStaffByFilter(colStaffModel);
            if (null == staff) {
                throw new FastRuntimeException("system.query.error.default", "用户不存在");
            }
            ColStaffResVO vo = new ColStaffResVO();
            BeanUtils.copyProperties(staff, vo, "list");
            return vo;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public ColStaffResVO getColStaffByLoginName(String loginName) {
        try {
            ColStaffModel colStaffModel = new ColStaffModel();
            if (StringUtils.isEmpty(loginName)) {
                throw new FastRuntimeException("system.query.error.default", "非法请求");
            }
            colStaffModel.setLoginName(loginName);
            colStaffModel.setIsStatus(1);
            InAuthCrmStaff staff = findStaffByFilter(colStaffModel);
            if (null == staff) {
                throw new FastRuntimeException("system.query.error.default", "用户不存在");
            }
            ColStaffResVO vo = new ColStaffResVO();
            BeanUtils.copyProperties(staff, vo, "list");
            return vo;
        } catch (Exception e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    private InAuthCrmStaff findStaffByFilter(ColStaffModel colStaffModel) {
        List<InAuthCrmStaff> list = findStaffList(colStaffModel);
        if(CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private List<InAuthCrmStaff> findStaffList(ColStaffModel colStaffModel) {
        QueryWrapper<InAuthCrmStaff> queryWrapper = getQueryWrapper(colStaffModel);
        return inAuthCrmStaffMapper.selectList(queryWrapper);
    }
    
    private QueryWrapper<InAuthCrmStaff> getQueryWrapper(ColStaffModel colStaffModel) {
        QueryWrapper<InAuthCrmStaff> queryWrapper = new QueryWrapper<InAuthCrmStaff>();
        queryWrapper.eq(Objects.nonNull(colStaffModel.getIsStatus()), "is_status", colStaffModel.getIsStatus());
        if (StringUtils.isNotBlank(colStaffModel.getGroupCode())) {
            List<String> groups = Arrays.asList(colStaffModel.getGroupCode().split(","));
            queryWrapper.in("group_code", groups);
        }
        //queryWrapper.eq(StringUtils.isNotEmpty(colStaffModel.getGroupCode()), "group_code", colStaffModel.getGroupCode());
        queryWrapper.eq(StringUtils.isNotBlank(colStaffModel.getLoginName()), "login_name", colStaffModel.getLoginName());
        queryWrapper.eq(StringUtils.isNotBlank(colStaffModel.getStaffMobile()), "staff_mobile", colStaffModel.getStaffMobile());
        queryWrapper.like(StringUtils.isNotBlank(colStaffModel.getStaffName()), "staff_name", colStaffModel.getStaffName());
        return queryWrapper;
    }

    @Override
    public void deleteColStaff(BatchDictInfoReqDTO reqDTO) {
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();
        for (Long id : reqDTO.getNo()) {
            InAuthCrmStaff staff = getStaffById(id);
            staff.setIsStatus(0);
            staff.setGmtModify(new Date());
            updateStaffById(staff);
            ColStaffReqDTO dto = new ColStaffReqDTO();
            BeanUtils.copyProperties(staff, dto);
            dto.setId(staff.getId());
            syncUserToExamSystem(xUserId, xUserToken, DELETE, dto);
        }
    }
    
    private InAuthCrmStaff getStaffById(Long id) {
        return inAuthCrmStaffMapper.selectById(id);
    }
    
    private void updateStaffById(InAuthCrmStaff staff) {
        inAuthCrmStaffMapper.updateById(staff);
    }

    @Override
    public void addColStaff(ColStaffReqDTO reqDTO) {
        if (Boolean.FALSE.equals(CommonUtil.isMobile(reqDTO.getStaffMobile()))) {
            throw new FastRuntimeException("手机号不合法:" + reqDTO.getStaffMobile());
        }
        ColStaffModel colStaffModel = new ColStaffModel();
        colStaffModel.setStaffMobile(reqDTO.getStaffMobile());
        if (null != findStaffByFilter(colStaffModel)) {
            throw new FastRuntimeException("crm.colorg.recard.duplicate", "手机号已存在");
        }
        colStaffModel.setStaffMobile(null).setLoginName(reqDTO.getLoginName());
        if (null != findStaffByFilter(colStaffModel)) {
            throw new FastRuntimeException("crm.colorg.recard.duplicate", "登陆名称已存在");
        }
        colStaffModel.setLoginName(null).setStaffName(reqDTO.getStaffName());
        if (null != findStaffByFilter(colStaffModel)) {
            throw new FastRuntimeException("crm.colorg.recard.duplicate", "用户名已存在");
        }
        InAuthCrmStaff staff = new InAuthCrmStaff();
        try {
            
            BeanUtils.copyProperties(reqDTO, staff, "status","password");
            staff.setIsStatus(1);
            if (StringUtils.isNotBlank(reqDTO.getPassword())){
                staff.setPassword(passwordEncoder.encode(reqDTO.getPassword()));
            }
            inAuthCrmStaffMapper.insert(staff);
        } catch (Exception e) {
            log.error("添加记录失败:{}", e.getMessage());
            throw new FastRuntimeException("添加记录失败");
        }
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();
        reqDTO.setId(staff.getId());
        syncUserToExamSystem(xUserId, xUserToken, ADD, reqDTO);
        syncUserDeptToExamSystem(xUserId,xUserToken,ADD, reqDTO);

    }

    private void syncUserDeptToExamSystem(String xUserId, String xUserToken, String operation, ColStaffReqDTO reqDTO) {
        try {

            JSONObject params = new JSONObject();
            params.put("extraDeptId", reqDTO.getGroupCode());
            params.put("source", "crm");
            params.put("operation", operation);
            List<JSONObject> users = new ArrayList<>();
            JSONObject user = new JSONObject();
            user.put("extraUserid", reqDTO.getId().toString());
            user.put("phone", reqDTO.getStaffMobile());
            user.put("email", reqDTO.getEmail());
            user.put("name", reqDTO.getStaffName());
            user.put("crmUsername", reqDTO.getLoginName());
            users.add(user);
            params.put("users", users);

            String result = HttpClientUtil.post(HttpConfig.custom().url(examUrlPre + "/dept/syncUserDept")
                .headers(HttpHeader.custom().contentType("application/json").other("x-user-id", xUserId)
                    .other("x-user-token", xUserToken).build())
                .json(params.toJSONString()));
            log.info("syncUserDeptToExamSystem ,params:{},res:{},x-user-id:{}", params.toJSONString(), result, xUserId);

        } catch (Exception e) {
            log.warn("syncUserDeptToExamSystem exception,params:{},x-user-id:{}", JSON.toJSONString(reqDTO), xUserId,
                e);
        }
    }

    private void syncUserToExamSystem(String xUserId, String xUserToken, String operation, ColStaffReqDTO reqDTO) {
        try {

            JSONObject params = new JSONObject();
            params.put("extraUserid", reqDTO.getId().toString());
            params.put("phone", reqDTO.getStaffMobile());
            params.put("email", reqDTO.getEmail());
            params.put("name", reqDTO.getStaffName());
            params.put("source", "crm");
            params.put("operation", operation);
            params.put("crmUsername", reqDTO.getLoginName());

            String result = HttpClientUtil.post(HttpConfig
                .custom().url(examUrlPre + "/dept/syncUser").headers(HttpHeader.custom().contentType("application/json")
                    .other("x-user-id", xUserId).other("x-user-token", xUserToken).build())
                .json(params.toJSONString()));
            log.info("syncUserToExamSystem ,params:{},res:{},x-user-id:{}", params.toJSONString(), result, xUserId);
        } catch (Exception e) {
            log.warn("syncUserToExamSystem exception,params:{},x-user-id:{}", JSON.toJSONString(reqDTO), xUserId, e);
        }
    }

    @Override
    public List<ColStaffResVO> getColStaffList(ColStaffReqDTO reqDTO) {
        try {
            Page<ColStaffResVO> page = new Page<>();
            ColStaffModel model = new ColStaffModel();
            BeanUtils.copyProperties(reqDTO, model);
            List<InAuthCrmStaff> result = findStaffList(model);
            BeanUtils.copyProperties(result, page, "list");
            List<ColStaffResVO> datas = null;
            Map<String, String> loginNameCnoMap = inPhoneLoginInfoMapper.queryAll()
                    .stream().filter(item -> StringUtils.isNotBlank(item.getUserTel()) && StringUtils.isNotBlank(item.getIdNo()))
                    .collect(Collectors.toMap(InPhoneLoginInfo::getUserTel, InPhoneLoginInfo::getIdNo, (oldValue, newValue) -> oldValue));
            if (null != result) {
                datas = result.parallelStream().map(item -> {
                    try {
                        ColStaffResVO res = new ColStaffResVO();
                        BeanUtils.copyProperties(item, res);
                        res.setIsDisplayUploadFaceMenu(res.translateIsDisplayUploadFaceMenu(item.getIsDisplayUploadFaceMenu()));
                        res.setCno(loginNameCnoMap.get(item.getLoginName()));
                        return res;
                    } catch (BeansException e) {
                    }
                    return null;
                }).filter(item -> null != item).collect(Collectors.toList());
            }
            return datas;
        } catch (BeansException e) {
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
    }

    @Override
    public Page<ColStaffResVO> getStaffsByPage(ColStaffReqDTO reqDTO) {
        try {
            ColStaffModel model = new ColStaffModel();
            BeanUtils.copyProperties(reqDTO, model);
            QueryWrapper<InAuthCrmStaff> queryWrapper = getQueryWrapper(model);
            queryWrapper.orderByDesc("gmt_modify");
            Page<InAuthCrmStaff> staffs = inAuthCrmStaffMapper.selectPage(new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), queryWrapper);
            Page<ColStaffResVO> page = new Page<ColStaffResVO>();
            BeanUtils.copyProperties(staffs, page, "list");
            if (null != staffs && null != staffs.getRecords()) {
                List<ColStaffResVO> datas = staffs.getRecords().stream().map(item -> convertToColStaffResVO(item)).collect(Collectors.toList());
                page.setRecords(datas);
                return page;
            }
        } catch (Exception e) {
            log.warn("staff查询列表失败,出现异常:{}", ExceptionUtils.getStackTrace(e));
            throw new FastRuntimeException("system.query.error.default", e.getMessage());
        }
        return null;
    }
    
    private ColStaffResVO convertToColStaffResVO(InAuthCrmStaff item) {
        ColStaffResVO res = new ColStaffResVO();
        BeanUtils.copyProperties(item, res, "list");
        res.setStaffMobile(SecurityUtil.maskMobile(res.getStaffMobile()));
        res.setLoginType(LoginTypeEnum.getNameByCode(res.getLoginType()));
        if (Objects.isNull(item.getIsUploadBasePhoto()) || !item.getIsUploadBasePhoto()){
            res.setIsUploadBasePhoto(Constant.NOT_COMPLETE);
        } else {
            res.setIsUploadBasePhoto(Constant.COMPLETE);
        }
        if (Objects.isNull(item.getIsDisplayUploadFaceMenu()) || item.getIsDisplayUploadFaceMenu()){
            res.setIsDisplayUploadFaceMenu("是");
        } else {
            res.setIsDisplayUploadFaceMenu("否");
        }
        return res;
    }

    @Override
    public void updateColOrgStaff(ColStaffReqDTO reqDTO) {

        if (StringUtils.isNotBlank(reqDTO.getStaffMobile()) && Boolean.FALSE.equals(CommonUtil.isMobile(reqDTO.getStaffMobile()))){
            throw new FastRuntimeException("手机号不合法:" + reqDTO.getStaffMobile());
        }
        InAuthCrmStaff staff = getStaff(reqDTO);
        updateStaffById(staff);
        reqDTO.setId(staff.getId());
        if (StringUtils.isBlank(reqDTO.getStaffMobile())) {
            reqDTO.setStaffMobile(staff.getStaffMobile());
        }
        syncUserToExamSystem(CommonUtils.getXUserId(), CommonUtils.getXUserToken(), MODIFY, reqDTO);
        syncUserDeptToExamSystem(CommonUtils.getXUserId(), CommonUtils.getXUserToken(), MODIFY, reqDTO);
    }
    
    /*private InAuthCrmStaff getInAuthCollectionStaff(StaffReqDTO reqDTO, String id) {
        InAuthCrmStaff staff = getStaffById(Long.valueOf(id));
        if (null == staff) {
            throw new FastRuntimeException("crm.colorg.recard.notexist");
        }
        staff.setGmtModify(new Date());
        staff.setGroupCode(reqDTO.getGroupCode());
        if (StringUtils.isNotEmpty(reqDTO.getIsAdmin())) {
            staff.setIsManager(!"1".equals(reqDTO.getIsAdmin()));
        }
        return staff;
    }*/
    
    /**
     * 发送人脸菜单更新的WebSocket通知
     * @param staffId 员工ID
     * @param staffMobile 员工手机号
     * @param isDisplayUploadFaceMenu 是否显示上传人脸菜单
     */
    private void sendFaceMenuUpdateNotification(Long staffId, String staffMobile, String isDisplayUploadFaceMenu) {
        JSONObject message = new JSONObject();
        message.put("type", WebSocketMsgTypeEnum.FACE_MENU_UPDATE.getCode());
        message.put("staffId", staffId);
        message.put("isDisplayUploadFaceMenu", isDisplayUploadFaceMenu);
        
        SendMsgDTO dto = new SendMsgDTO();
        dto.setMobile(staffMobile);
        dto.setMessageJson(message);
        WebSocketUtil.sendByMobile(dto);
    }
    
    private InAuthCrmStaff getStaff(ColStaffReqDTO reqDTO) {
        InAuthCrmStaff staff = getStaffById(reqDTO.getId());
        if (null == staff) {
            throw new FastRuntimeException("crm.colorg.recard.notexist");
        }
        
        // 记录更新前的isDisplayUploadFaceMenu值
        Boolean oldIsDisplayUploadFaceMenu = staff.getIsDisplayUploadFaceMenu();
        
        BeanUtils.copyProperties(reqDTO, staff, CommonUtils.getNullPropertyNames(reqDTO));
        if (StringUtils.isNotBlank(reqDTO.getLoginType())) {
            staff.setLoginType(LoginTypeEnum.getCodeByName(reqDTO.getLoginType()));
        }
        if (StringUtils.isNotBlank(reqDTO.getIsDisplayUploadFaceMenu())) {
            Boolean newIsDisplayUploadFaceMenu = "是".equals(reqDTO.getIsDisplayUploadFaceMenu());
            staff.setIsDisplayUploadFaceMenu(newIsDisplayUploadFaceMenu);
            
            // 如果isDisplayUploadFaceMenu发生变化,发送WebSocket通知
            if(!Objects.equals(oldIsDisplayUploadFaceMenu, newIsDisplayUploadFaceMenu)) {
                sendFaceMenuUpdateNotification(staff.getId(), staff.getStaffMobile(), reqDTO.getIsDisplayUploadFaceMenu());
            }
        }
        
        if (StringUtils.isNotBlank(reqDTO.getPassword())) {
            staff.setPassword(passwordEncoder.encode(reqDTO.getPassword()));
        }
        return staff;
    }

    @Override
    public List<ColStaffResVO> getOutCaseColStaffList(ColStaffReqDTO reqDTO) {
        //外包暂不实现
        return null;
    }

    @Override
    public void updateColOrgStaff(List<ColStaffReqDTO> reqDTO) {
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();
        for (ColStaffReqDTO dto : reqDTO) {
            dto.setStaffMobile(null);
            InAuthCrmStaff staff = getStaff(dto);
            updateStaffById(staff);
            dto.setId(staff.getId());
            dto.setStaffMobile(staff.getStaffMobile());
            syncUserToExamSystem(xUserId, xUserToken, MODIFY, dto);
            syncUserDeptToExamSystem(xUserId,xUserToken,MODIFY,dto);

        }
    }

    @Override
    public void syncAllStaffToExamSystem() {
        String xUserId = CommonUtils.getXUserId();
        String xUserToken = CommonUtils.getXUserToken();
        ColStaffReqDTO reqDTO = new ColStaffReqDTO();
        reqDTO.setIsStatus(1);
        List<ColStaffResVO> staffList = getColStaffList(reqDTO);
        staffList.stream().map(item -> {
            ColStaffReqDTO dto = new ColStaffReqDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).forEach(dto -> {
            syncUserToExamSystem(xUserId, xUserToken, ADD, dto);
            syncUserDeptToExamSystem(xUserId, xUserToken, ADD, dto);
        });
    }
    
}
