package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电销-话务小结表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_summary_dict")
public class TmkSummaryDict implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 营销小结编号
     */
    private String summaryCode;

    /**
     * 业务产品类型:1.进件模式 2.额度模式 3.超级会员 4.钱夹谷谷 5.uuid(待定)
     */
    private String businessType;

    /**
     * 营销小结
     */
    private String summaryContent;

    /**
     * 联系结果对应的编码(与业务字典中配置的对应,联系结果: 0.失败 1.成功 2.未联系上 3.无需联系)
     */
    private String resultCode;

    /**
     * 优先级(此字段表示的优先级低于result_code字段)
     */
    private Integer sort;

    /**
     * 状态: 0-关闭 1.开启
     */
    private Boolean state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String lstUpdUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
