package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ConPhoneCallInfo;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.dto.phone.SoftPhoneReqDTO;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.vo.phone.CallInPhoneDetailVO;
import com.welab.crm.operate.vo.phone.CallInfoStatsVO;
import com.welab.crm.operate.vo.phone.IsSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneVO;
import com.welab.crm.operate.vo.screen.CountVO;
import com.welab.crm.operate.vo.woReport.ReportDupCallDetailVO;
import com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import com.welab.crm.operate.vo.woReport.ReportWorkTimeSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 软电话通话记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2021-10-21
 */
public interface ConPhoneCallInfoMapper extends BaseMapper<ConPhoneCallInfo> {

    Page<ConPhoneCallInfo> selectListByPage(Page<ConPhoneCallInfo> conPhoneCallInfoPage, @Param("filter") SoftPhoneReqDTO reqDTO);

    /**
     * 根据staffId查询通话记录
     */
    List<IsSummaryVO> selectSummaryByStaffId(Long staffId);

    Page<ReportDupCallDetailVO> selectDetailByPage(Page<ReportDupCallDetailVO> page, @Param("start") Date start,
                                                   @Param("end") Date end, @Param("lines") List<String> hotlines, @Param("staffIds") List<Long> staffIds);

    Page<ReportPhoneVO> queryReportDetail(Page<ReportPhoneVO> page, @Param("filter") ReportPhoneResultDTO reqDTO);

    List<ReportPhoneVO> queryReportDetail(@Param("filter") ReportPhoneResultDTO reqDTO);

    Page<CallInPhoneDetailVO> queryDetail(Page<CallInPhoneDetailVO> page, @Param("filter") PhoneSundRecordingDTO dto);
    
    List<CountVO> queryCallInfoGroupCount(@Param("filter") PhoneSundRecordingDTO dto);

    Page<ReportDupCallTotalVO> countCustomerCall(Page<ReportDupCallTotalVO> page, @Param("filter") ReportDupCallDTO callDTO);

    Page<ReportDupCallTotalVO> countCustomerCallByDay(Page<ReportDupCallTotalVO> page, @Param("filter") ReportDupCallDTO callDTO);

    List<CallInfoStatsVO> selectByPeriodTime(ReportWorkStatusDTO dto);

    List<ReportWorkTimeSummaryVO> selectCallInTotal(ReportWorkStatusDTO dto);

    List<ReportWorkTimeSummaryVO> selectCallOutTotal(ReportWorkStatusDTO dto);

    int selectCallInNumber(ReportWorkStatusDTO dto);

    /**
     * 查询当天各省来电数
     * @return
     */
    List<CountVO> queryProvinceCount();

    /**
     * 查询重复来电数
     * @return
     */
    List<CountVO> queryRepeatCount();

    /**
     * 分时查询重复来电数量
     */
    List<CountVO> queryRepeatCountHour();


    /**
     * 查询小组当天出勤人数
     * @param groupCodeList 组列表
     * @return 当天出勤人数
     */
    Integer queryTodayAttendNumber(@Param("groupCodeList") List<String> groupCodeList);

}
