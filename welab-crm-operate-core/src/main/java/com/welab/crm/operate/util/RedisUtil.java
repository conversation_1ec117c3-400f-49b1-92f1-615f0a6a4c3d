package com.welab.crm.operate.util;

import redis.clients.jedis.JedisCommands;

public class RedisUtil {
 
    private static final String LOCK_SUCCESS = "OK";

    /**
     * NX 标识不存在时set
     */
    private static final String SET_IF_NOT_EXIST = "NX";

    /**
     * 秒
     */
    private static final String SET_WITH_EXPIRE_TIME = "EX";
 
 
 
    /**
     * 尝试获取分布式锁
     * @param jedis Redis客户端
     * @param lockKey 锁
     * @param requestId 请求标识
     * @param expireTime 超期时间
     * @return 是否获取成功
     */
 
    public static boolean tryGetDistributedLock(JedisCommands jedis, String lockKey, String requestId, int expireTime) {
 
        String result = jedis.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
 
        if (LOCK_SUCCESS.equals(result)) {
 
            return true;
 
        }
 
        return false;
 
    }

    public static void releaseDistributedLock(JedisCommands jedisCommands, String key) {
        jedisCommands.del(key);
    }
}