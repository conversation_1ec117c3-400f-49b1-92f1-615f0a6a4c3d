package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 贷款提前结清记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_early_settled_record")
public class OpEarlySettledRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 对应客服系统的客服id
     */
    private Long customerId;

    /**
     * 贷款号
     */
    private String appNo;

    /**
     * 费率
     */
    private String loanRate;

    /**
     * 资金方
     */
    private String partnerCode;

    /**
     * 渠道来源
     */
    private String sourceCode;

    /**
     * 当前期数
     */
    private Integer currentTenor;

    /**
     * 客服id
     */
    private Long staffId;

    /**
     * 组别
     */
    private String groupCode;

    /**
     * 结清原因
     */
    private String reason;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否提前结清
     */
    private Boolean isEarlyClosed;

    /**
     * 贷款类型；cash-现金贷；wallet-钱包
     */
    private String loanType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;


}
