package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.operate.domain.WoTask;
import com.welab.crm.operate.dto.report.WoReportDTO;
import com.welab.crm.operate.dto.workorder.*;
import com.welab.crm.operate.model.WorkOrderModel;
import com.welab.crm.operate.vo.monitor.OrderCallbackMonitorVO;
import com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO;
import com.welab.crm.operate.vo.workorder.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface WoTaskMapper extends BaseMapper<WoTask> {
	Page<WorkOrderInfoVO> queryWorkOrderByPage(Page<WorkOrderInfoVO> page, @Param("reqDTO") WorkOrderModel reqDTO);
	
	List<WorkOrderCountVO> queryWorkCountList(@Param("reqDTO") WorkOrderSearchReqDTO reqDTO);
	
	Integer totalWorkOrder(@Param("reqDTO") WorkOrderTotalReqDTO reqDTO);
	
	WorkOrderDetailVO queryWorkOrderDetail(@Param("id")Long id, @Param("staffId") String staffId);
	
	List<WorkOrderLogVO> queryWorkOrderLogList(String executionId);
	
	List<WorkOrderLoanReqDTO> queryWorkOrderLoanList(Long id);

	/**
	 * 查询工单历史
	 * @param custId
	 * @param orderNo
	 * @return
	 */
	List<WorkOrderHisVO> queryWorkOrderHistory(@Param("ids")List<Long> custIdList, @Param("orderNo")String orderNo);

	/**
	 * 根据工单号查询工单流水
	 * @param busiKey
	 * @return
	 */
	List<WorkOrderLogVO> queryWorkOrderLogListByBusiKey(String busiKey);

	/**
	 * 根据工单号查询工单流水
	 * @param busiKey
	 * @return
	 */
	List<WorkOrderLogVO> queryWorkOrderLogListByBusiKeyList(@Param("list") List<String> busiKeyList);


	/**
	 * 根据工单三类跟客户Id查询工单
	 * @param reqDTO
	 * @return
	 */
	List<WoTask> queryWorkOrderHistoryByCondition(@Param("reqDTO") WorkOrderModel reqDTO);

	/**
	 * 资金投诉工单统计查询
	 * @param dto
	 * @return
	 */
	List<FundNamesDTO> queryFundName(@Param("dto") FundAndRegulatoryComplaintDTO dto);

	/**
	 * 监管投诉工单统计查询
	 * @param dto
	 * @return
	 */
	List<RegulatoryComplaintResDTO> queryComplaint(@Param("dto") FundAndRegulatoryComplaintDTO dto);

	/**
	 * 查询该客户全部的uuid
	 * @param uuid
	 * @return
	 */
	List<String> queryAllStatusByUuid(@Param("uuid") String uuid);

	/**
	 * 根据工单号查询流程信息
	 * @param orderNo
	 * @return
	 */
	WFRunExecutionDTO selectRunExecutionByOrderNo(@Param("orderNo") String orderNo);

	/**
	 * 查询催单情况
	 * @return
	 */
	List<OrderUrgeMonitorVO> queryOrderUrgeList();

	/**
	 * 查询工单回访情况
	 * @return
	 */
	List<OrderCallbackMonitorVO> queryOrderCallbackList();

	/**
	 * 查询升级投诉工单
	 */
	List<EscalationOrderVo> selectEscalationOrder(@Param("dto") WoReportDTO dto, @Param("nids") List<Long> normalOrderIds, @Param("sids") List<Long> specialOrderIds);


	/**
	 * 更新超时未联系的工单
	 */
	void updateOrder(@Param("woTask") WoTask woTask);


	/**
	 * 查询工单日志列表
	 * @param executionId
	 * @return
	 */
	List<WorkOrderLogVO> selectLogListByExecutionId(@Param("executionId") String executionId);
}
