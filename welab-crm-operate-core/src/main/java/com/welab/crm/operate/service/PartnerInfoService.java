package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.partner.PartnerInfoImportDTO;

import java.util.List;

/**
 * 资金方信息维护
 * <AUTHOR>
 */
public interface PartnerInfoService {

	/**
	 * 导入资金方信息
	 * @param list 导入列表
	 */
	void importPartnerInfo(List<PartnerInfoImportDTO> list);

	/**
	 * 添加资金方信息
	 * @param dto
	 */
	void addPartnerInfo(PartnerInfoImportDTO dto);

	/**
	 * 更新资金方信息
	 * @param list 已更新的全部数据
	 */
	void updatePartnerInfo(List<PartnerInfoImportDTO> list);

	/**
	 * 根据资金方名称跟资金方编码查询资金方信息
	 * @param partnerName 名称
	 * @return 资金方信息
	 */
	PartnerInfoImportDTO queryPartnerInfoByNameAndCode(String partnerName);

	/**
	 * 查询全部资金方信息
	 * @param dto 分页参数
	 * @return
	 */
	Page<PartnerInfoImportDTO> queryPartnerInfo(BaseReqDTO dto);


	/**
	 * 查询全部资金方信息
	 * @return 全部的资金方信息
	 */
	List<PartnerInfoImportDTO> queryPartnerInfo();
}
