/**
 * @Title: ICrmColOrgStaffService.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * @description 员工服务
 * <AUTHOR>
 * @date 2021-10-22 14:58:50
 * @version v1.0
 */
public interface ICrmOrgStaffService {
    
    /**  
     * 获取员工 
     * @param mobile
     * @return  
     */
    public ColStaffResVO getColStaff(String mobile);


    /**
     * 根据登录名获取员工信息
     * @param loginName
     * @return
     */
    public ColStaffResVO getColStaffByLoginName(String loginName);


    /**  
     * 删除员工 
     * @param reqDTO  
     */
    public void deleteColStaff(BatchDictInfoReqDTO reqDTO);

    /**  
     * 添加员工
     * @param reqDTO
     * @return  
     */
    public void addColStaff(ColStaffReqDTO reqDTO);

    /**  
     * 获取员工集合
     * @param reqDTO
     * @return  
     */
    public List<ColStaffResVO> getColStaffList(ColStaffReqDTO reqDTO);

    /**  
     * 分页获取员工 
     * @param reqDTO
     * @return  
     */
    public Page<ColStaffResVO> getStaffsByPage(ColStaffReqDTO reqDTO);

    /**  
     * 更新员工
     * @param reqDTO  
     */
    public void updateColOrgStaff(ColStaffReqDTO reqDTO);
    
    /**  
     * 获取委外员工集合
     * @param reqDTO
     * @return  
     */
    public List<ColStaffResVO> getOutCaseColStaffList(ColStaffReqDTO reqDTO);

    /**
     * 批量更新员工
     * @param reqDTO
     */
    public void updateColOrgStaff(List<ColStaffReqDTO> reqDTO);

    /**
     * 同步全部staff到考试系统
     */
    void syncAllStaffToExamSystem();
}
