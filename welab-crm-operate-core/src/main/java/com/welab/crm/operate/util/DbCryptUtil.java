package com.welab.crm.operate.util;

import com.welab.privacy.crypto.CryptoHelper;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 数据库字段加解密工具类
 * @date 2022/5/20 14:08
 */
@Component
@Slf4j
public class DbCryptUtil {

    @Value("${welab.privacy.root.url}")
    private String privacyUrl;

    @Value("${welab.privacy.secret.key}")
    private String secretKey;

    @Value("${appId}")
    private String appId;

    private static final String DATA_BASE = "welab_crm";
    private static final String TABLE = "tmk_loan_invite";
    public static final String CNID = "cnid";
    public static final String MOBILE = "mobile";
    public static final String NAME = "username";

    @PostConstruct
    public void init() {
        log.info("初始化加密工具类, url:{},  key:{} ,appId:{}", privacyUrl, secretKey, appId);
        try {
            CryptoHelper.getInstance().init(privacyUrl, secretKey, appId);
        } catch (Exception e) {
            log.error("初始化加密工具类异常", e);
        }
    }

    /**
     * 加密字符串，type加密类型
     * @param str
     * @param type
     * @return
     */
    public static String encrypt(String str, String type) {
        switch (type) {
            case CNID:
                return CryptoHelper.getInstance().encrypt(DATA_BASE, TABLE, CNID, str);
            case NAME:
                return CryptoHelper.getInstance().encrypt(DATA_BASE, TABLE, NAME, str);
            case MOBILE:
                return CryptoHelper.getInstance().encrypt(DATA_BASE, TABLE, MOBILE, str);
            default:
                return str;
        }
    }

    /**
     * 加密字符串，type加密类型
     * @param str
     * @param type
     * @return
     */
    public static String decrypt(String str, String type) {
        switch (type) {
            case CNID:
                return CryptoHelper.getInstance().decrypt(DATA_BASE, TABLE, CNID, str);
            case NAME:
                return CryptoHelper.getInstance().decrypt(DATA_BASE, TABLE, NAME, str);
            case MOBILE:
                return CryptoHelper.getInstance().decrypt(DATA_BASE, TABLE, MOBILE, str);
            default:
                return str;
        }
    }

}
