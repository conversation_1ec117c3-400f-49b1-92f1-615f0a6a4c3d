package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ContractSendRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.dto.ContractSendReqDTO;
import com.welab.crm.operate.vo.ContractSendVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合同发送记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
public interface ContractSendRecordMapper extends BaseMapper<ContractSendRecord> {

	Page<ContractSendVO> queryContractSendPage(Page<ContractSendVO> page, @Param("dto") ContractSendReqDTO reqDTO);

	List<ContractSendVO> queryContractSendList(@Param("dto") ContractSendReqDTO reqDTO);
}
