package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.service.ProductService;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.domain.OpEarlySettledRecord;
import com.welab.crm.operate.dto.earlySettle.EarlySettledReqDTO;
import com.welab.crm.operate.dto.loan.OutstandingEarlySettleDTO;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import com.welab.crm.operate.dto.wallet.WalletEarlySettleDTO;
import com.welab.crm.operate.mapper.OpEarlySettledRecordMapper;
import com.welab.crm.operate.model.EarlySettledOriginPartnerModel;
import com.welab.crm.operate.model.EarlySettledReasonTenorModel;
import com.welab.crm.operate.service.EarlySettledService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO;
import com.welab.crm.interview.enums.PartnerCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EarlySettledServiceImpl implements EarlySettledService {

    @Resource
    private OpEarlySettledRecordMapper opEarlySettledRecordMapper;

    @Resource
    private ProductService productService;

    @Override
    public void saveEarlySettledRecord(HistoryOperationDTO dto, StaffVO staffVO) {
        List<OpEarlySettledRecord> list = opEarlySettledRecordMapper.selectList(Wrappers.lambdaQuery(OpEarlySettledRecord.class).eq(OpEarlySettledRecord::getAppNo, dto.getApplicationId()));
        if (CollectionUtils.isNotEmpty(list)){
            log.warn("saveEarlySettledRecord 提前结清已有贷款号,appNo:{}", dto.getApplicationId());
            return;
        }
        OpEarlySettledRecord record = new OpEarlySettledRecord();
        record.setGroupCode(staffVO.getGroupCode());
        record.setStaffId(staffVO.getId());
        record.setCustomerId(dto.getCustomerId());
        record.setIsEarlyClosed(false);
        record.setComments(dto.getComment());
        record.setUserId(dto.getUserId());
        record.setAppNo(dto.getApplicationId());
        if (dto instanceof OutstandingEarlySettleDTO) {
            record.setReason(((OutstandingEarlySettleDTO) dto).getReason());
            record.setCurrentTenor(getCurrentTenor(((OutstandingEarlySettleDTO) dto).getTenor(), ((OutstandingEarlySettleDTO) dto).getDueDate()));
            record.setLoanRate(((OutstandingEarlySettleDTO) dto).getLoanRate());
            record.setLoanType("cash");
            record.setPartnerCode(((OutstandingEarlySettleDTO) dto).getPartnerCode());
            record.setSourceCode(((OutstandingEarlySettleDTO) dto).getSource());
        } else if (dto instanceof WalletEarlySettleDTO) {
            record.setReason(((WalletEarlySettleDTO) dto).getReason());
            record.setCurrentTenor(Integer.valueOf(((WalletEarlySettleDTO) dto).getTenor()));
            record.setLoanRate(((WalletEarlySettleDTO) dto).getLoanRate());
            record.setLoanType("wallet");
            record.setPartnerCode(((WalletEarlySettleDTO) dto).getPartnerCode());
        }
        opEarlySettledRecordMapper.insert(record);
    }

    @Override
    public List<EarlySettledRecordUserSummaryVO> querySettleUserSummary(EarlySettledReqDTO dto) {
        // 查询客服维度统计数据
        List<EarlySettledRecordUserSummaryVO> voList = opEarlySettledRecordMapper.queryUserSummary(dto);
        // 查询合计
        EarlySettledRecordUserSummaryVO totalVO = opEarlySettledRecordMapper.queryTotalSummary(dto);
        totalVO.setGroupName("合计");
        voList.add(totalVO);
        // 查询环比时间内的数据
        // 查询环比时间为空，默认查询上个月的数据
        if (StringUtils.isBlank(dto.getRoundStartTime())) {
            dto.setStartTime(DateUtil.plusMonths(DateUtil.stringToDate(dto.getStartTime()), -1, DateUtils.DATE_FORMAT));
        } else {
            dto.setStartTime(dto.getRoundStartTime());
        }
        if (StringUtils.isBlank(dto.getRoundEndTime())) {
            dto.setEndTime(DateUtil.plusMonths(DateUtil.stringToDate(dto.getEndTime()), -1, DateUtils.DATE_FORMAT));
        } else {
            dto.setEndTime(dto.getRoundEndTime());
        }
        EarlySettledRecordUserSummaryVO roundVO = opEarlySettledRecordMapper.queryTotalSummary(dto);
        roundVO.setGroupName(dto.getStartTime() + " - " + dto.getEndTime());
        voList.add(roundVO);

        // 计算环比
        EarlySettledRecordUserSummaryVO roundCompareVO = new EarlySettledRecordUserSummaryVO();
        roundCompareVO.setGroupName("环比");
        roundCompareVO.setUserCount(CommonUtil.calcPercentage(
            Integer.parseInt(totalVO.getUserCount()) - Integer.parseInt(roundVO.getUserCount()),
            Integer.parseInt(roundVO.getUserCount())));
        roundCompareVO.setOrderCount(CommonUtil.calcPercentage(
            Integer.parseInt(totalVO.getOrderCount()) - Integer.parseInt(roundVO.getOrderCount()),
            Integer.parseInt(roundVO.getOrderCount())));
        roundCompareVO.setEarlySettledOrderCount(CommonUtil.calcPercentage(
                Integer.parseInt(totalVO.getEarlySettledOrderCount()) - Integer.parseInt(roundVO.getEarlySettledOrderCount()),
                Integer.parseInt(roundVO.getOrderCount())));
        roundCompareVO.setEarlySettledRate(CommonUtil.calcPercentageDouble(
            Double.parseDouble(totalVO.getEarlySettledRate()) - Double.parseDouble(roundVO.getEarlySettledRate()),
            Double.parseDouble(roundVO.getEarlySettledRate())));
        roundCompareVO.setDebitAmount(CommonUtil.calcPercentageDouble(
                Double.parseDouble(totalVO.getDebitAmount().toString()) - Double.parseDouble(roundVO.getDebitAmount().toString()),
                Double.parseDouble(roundVO.getDebitAmount().toString())));
        // 将结清率转化为百分比
        for (EarlySettledRecordUserSummaryVO vo : voList) {
            vo.setEarlySettledRate(CommonUtil.calcPercentageDouble(Double.parseDouble(vo.getEarlySettledOrderCount()),
                Double.parseDouble(vo.getOrderCount())));
        }
        voList.add(roundCompareVO);

        return voList;

    }

    @Override
    public JSONObject queryReasonTenorSummary(String startTime, String endTime) {
        JSONObject reasonTenorSummary = new JSONObject();
        
        // 先查询合计列，合计列不区分原因，只根据期数分组
        // 合计行总数量
        Integer sumRowTotalCount = 0;
        List<EarlySettledReasonTenorModel> tenorSummaryList =
            opEarlySettledRecordMapper.queryTotalCountGroupByTenor(startTime, endTime);
        JSONObject sumRow = new JSONObject();
        for (EarlySettledReasonTenorModel model : tenorSummaryList) {
            sumRowTotalCount += model.getCount();
            tenorSpecialDeal(model,sumRow);
        }
        sumRow.put("total", sumRowTotalCount);
        sumRow.put("rate", "100.00%");
        reasonTenorSummary.put("合计", sumRow);

        List<EarlySettledReasonTenorModel> list =
            opEarlySettledRecordMapper.queryReasonTenorSummary(startTime, endTime);
        Integer finalSumTotal = sumRowTotalCount;
        list.stream().collect(Collectors.groupingBy(EarlySettledReasonTenorModel::getReason)).forEach((reason, earlySettledReasonTenorModels) -> {
            // 一个原因一行
            JSONObject reasonRow = new JSONObject();
            Integer totalCount = 0;
            for (EarlySettledReasonTenorModel model : earlySettledReasonTenorModels) {
                totalCount += model.getCount();
                tenorSpecialDeal(model,reasonRow);
            }
            reasonRow.put("rate",CommonUtil.calcPercentage(totalCount, finalSumTotal));
            reasonRow.put("total", totalCount);
            reasonTenorSummary.put(reason,reasonRow);
        });

        return reasonTenorSummary;
    }

    private void tenorSpecialDeal(EarlySettledReasonTenorModel model, JSONObject row) {
        Integer tenor = model.getTenor();
        if (tenor >= 7 && tenor <= 12) {
            Integer count = row.getInteger("7-12");
            if (Objects.isNull(count)){
                row.put("7-12", model.getCount());
            } else {
                row.put("7-12", count + model.getCount());
            }
        } else if (tenor >= 13 && tenor <= 18){
            Integer count = row.getInteger("13-18");
            if (Objects.isNull(count)){
                row.put("13-18", model.getCount());
            } else {
                row.put("13-18", count + model.getCount());
            }
        } else if (tenor >= 19){
            Integer count = row.getInteger("19-24");
            if (Objects.isNull(count)){
                row.put("19-24", model.getCount());
            } else {
                row.put("19-24", count + model.getCount());
            }
        } else {
            row.put(String.valueOf(tenor), model.getCount());
        }
    }


    @Override
    public JSONObject queryOriginPartnerCodeSummary(String startTime, String endTime) {

        JSONObject jsonObject = new JSONObject();

        // 先查询合计列，合计列不区分原因，只根据资金方分组
        // 合计行总数量
        Integer sumRowTotalCount = 0;
        List<EarlySettledOriginPartnerModel> totalDataList =
            opEarlySettledRecordMapper.queryTotalCountGroupByPartnerCode(startTime, endTime);
        JSONObject sumRow = new JSONObject();
        for (EarlySettledOriginPartnerModel model : totalDataList) {
            sumRowTotalCount += model.getCount();
            sumRow.put(getPartnerNameByCode(model.getPartner()), model.getCount());
        }
        sumRow.put("total", sumRowTotalCount);
        jsonObject.put("合计", sumRow);


        List<EarlySettledOriginPartnerModel> list = opEarlySettledRecordMapper.queryOriginPartnerSummary(startTime,endTime);

        if (CollectionUtils.isNotEmpty(list)){
            // 查询出全部的渠道
            List<String> originList = list.stream().map(EarlySettledOriginPartnerModel::getOrigin).distinct().collect(Collectors.toList());
            String origins = StringUtils.join(originList, ",");
            // 查询渠道号和名称的字典
            Map<String, String> originNameMap = productService.listChannelNameByCodes(origins);

            // 先把code翻译成中文
            for (EarlySettledOriginPartnerModel model : list) {
                if (StringUtils.isBlank(model.getOrigin())) {
                    model.setOrigin("");
                } else if (StringUtils.isNotBlank(originNameMap.get(model.getOrigin()))) {
                    model.setOrigin(originNameMap.get(model.getOrigin()));
                }
                model.setPartner(
                    StringUtils.isBlank(model.getPartner()) ? "" : getPartnerNameByCode(model.getPartner()));
            }

            list.stream().collect(Collectors.groupingBy(EarlySettledOriginPartnerModel::getOrigin)).forEach((origin, models) ->{
                JSONObject partnerCountJson = new JSONObject();
                Integer totalCount = 0;
                for (EarlySettledOriginPartnerModel model : models) {
                    partnerCountJson.put(model.getPartner(),model.getCount());
                    totalCount += model.getCount();
                }
                partnerCountJson.put("total",totalCount);
                jsonObject.put(origin,partnerCountJson);
            } );
        }

        return jsonObject;
    }

    private String getPartnerNameByCode(String partner) {
        String partnerName = PartnerCodeEnum.getPartnerNameByCode(partner);
        if (StringUtils.isBlank(partnerName)){
            return "我来贷";
        } else {
            return partnerName;
        }
    }

    /**
     * 根据期数跟到期日，计算出当前还款期次
     * 
     * @param tenor 格式 5/12 ,第一个数字是未还期次，第二个数字是总期次
     * @param dueDate 还款日 eg:假设 tenor = 5/12,due_date = 2023-04-22,今天是 2023-05-23 首先 5/12
     *            表示已经还了7期，剩余5期，所以当前应还第8期，due_date = 2023-04-22 表示4月22号时就应还第8期了但是还没还，但是现在已经是5月23号了，
     *            按照时间推，5月22号应该还第9期，所以现在5月23号应该处于第10期 所以公式就是 12 - 5 + 差几个月(now - due_date) + 2
     *            但是如果今天刚好是5月22日或者5月22日之前，则还处于第9期，所以公式是 12 - 5 + 差几个月(now - due_date) + 1
     */
    private Integer getCurrentTenor(String tenor, String dueDate) {

        String[] tenorArray = tenor.split("/");
        int currentTenor = Integer.parseInt(tenorArray[1]) - Integer.parseInt(tenorArray[0])
            + DateUtils.getMonthInterval(LocalDate.parse(dueDate), LocalDate.now()) + 2;

        Date dueDateD = DateUtil.stringToDate(dueDate);
        // 如果当天跟还款日是同一天 或者 当天在还款日之前
        if (DateUtil.getDay(dueDateD) == DateUtil.getDay() || DateUtil.isBefore(new Date(), dueDateD)) {
            return currentTenor - 1;
        } else {
            return currentTenor;
        }
    }

}
