package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电销放款邀约临时表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tmk_loan_invite_temp")
public class TmkLoanInviteTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电销唯一任务Id
     */
    private String tmkTaskId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 身份证号
     */
    private String cnid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private String gender;

    /**
     * 申请时间
     */
    private Date appliedAt;

    /**
     * 申请金额
     */
    private String appliedAmount;

    /**
     * 申请期数
     */
    private String appliedTenor;

    /**
     * 审批金额
     */
    private String amount;

    /**
     * 审批期数
     */
    private String tenor;

    /**
     * 审批时间
     */
    private Date approvedAt;

    /**
     * 确认时间
     */
    private Date confirmedAt;

    /**
     * 产品号
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 贷款号
     */
    private String applicationId;

    /**
     * 进件渠道
     */
    private String applyOrigin;

    /**
     * 用户等级
     */
    private String finalClass;

    /**
     * 贷款状态
     */
    private String state;

    /**
     * 注册时间
     */
    private Date registerAt;

    /**
     * 注册渠道
     */
    private String regOrigin;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 用户UUID
     */
    private String uuid;

    /**
     * 状态标识(0:未分配；1：已分配；7:被拦截8：已废弃)
     */
    private String flag;

    /**
     * 电销类型(loan:进件模式；credit:额度模式)
     */
    private String type;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 员工主键Id
     */
    private Long staffId;

    /**
     * 推送状态(1:已推送;2:未推送;3:被屏蔽)
     */
    private String pushFlag;

    /**
     * 拦截规则Id
     */
    private Long ruleId;

    /**
     * 是否注销
     */
    private Boolean blocked;

    /**
     * 上次登陆时间
     */
    private Date lastSignInAt;

    /**
     * 1:需要更新 0:不需要更新
     */
    private String updateFlag;

    /**
     * 分流标志
     */
    private String diversionTag;

}
