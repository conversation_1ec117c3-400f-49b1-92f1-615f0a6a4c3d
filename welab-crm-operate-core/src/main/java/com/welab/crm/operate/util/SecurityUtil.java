package com.welab.crm.operate.util;


import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.enums.FileType;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 安全相关工具类
 * @date 2022/3/25 17:18
 */
@Slf4j
@Component
public class SecurityUtil {

    @Autowired
    private ApplicationContext applicationContext;


    public static final String CNID = "cnid";
    public static final String NAME = "name";
    public static final String MOBILE = "mobile";
    /**
     * 不安全的文件后缀
     */
    public static final List<String> UNSAFE_SUFFIX_LIST = Arrays.asList("exe", "bat",  "vbe", "vbs", "js", "dll", "sh",
            "ini", "keystore", "p12");


    /**
     * 身份证替换正则,保留前8位和后2位
     */
    private static final String CNID_REGULAR = "(?<=\\d{8})\\d(?=\\d{2})";

    /**
     * 身份证替换正则,保留前6位和后4位
     */
    private static final String CNID_REGULAR_2 = "(?<=\\d{6})\\d(?=\\d{4})";

    /**
     * 手机号替换正则
     */
    private static final String MOBILE_REGULAR = "(?<=\\d{4})\\d(?=\\d{2})";

    /**
     * 手机号替换正则2
     */
    private static final String MOBILE_REGULAR_2 = "(?<=\\d{3})\\d(?=\\d{4})";


    /**
     * 手机号替换正则
     */
    private static final String MOBILE_REGULAR_ADD = "(?<=\\d{3})\\d(?=\\d{0})";


    public static String maskData(String data, String type){
        if (StringUtils.isBlank(data)){
            log.warn("敏感字段掩码处理,data字段不能为空");
            return data;
        }
        switch (type){
            case NAME:
                return maskName(data);
            case CNID:
                return maskCnid(data);
            case MOBILE:
                return maskMobile(data);
            default:
                log.warn("type异常，掩码处理失败");
                return data;
        }
    }

    public static String maskName(String name){
        if (StringUtils.isBlank(name)) {
            return name;
        }
        int length = name.length();
        // 两个字与三个字的姓名打码姓(*三/*伯虎)
        if (2 == length || 3 == length) {
            StringBuilder builder = new StringBuilder(name);
            builder.setCharAt(0,'*');
            return builder.toString();
        }
        // 四个字及以上的姓名只显示最后两个字，掩码*符号显示二个(欧阳德仁，打码成**德仁)（宇妥·去丹贡布，打码成**贡布）
        if (length > 3){
            String last2Char = name.substring(length - 2);
            return "**" + last2Char;
        }
        return name;
    }

    /**
     * 屏蔽名字中间的字
     * @param name
     * @return
     */
    public static String maskNameMidChar(String name){
        if (StringUtils.isBlank(name)) {
            return name;
        }
        int length = name.length();
        
        if (2 == length || 3 == length) {
            StringBuilder builder = new StringBuilder(name);
            builder.setCharAt(1,'*');
            return builder.toString();
        }
        
        if (length > 3){
            String first2Char = name.substring(0, 2);
            return first2Char + "**";
        }
        return name;
    }

    public static String maskMobile(String mobile){
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        return mobile.replaceAll(MOBILE_REGULAR, "*");
    }

    public static String maskCnid(String cnid){
        if (StringUtils.isBlank(cnid)) {
            return cnid;
        }
        return cnid.replaceAll(CNID_REGULAR,"*");
    }


    public static String maskCnid2(String cnid){
        if (StringUtils.isBlank(cnid)) {
            return cnid;
        }
        return cnid.replaceAll(CNID_REGULAR_2,"*");
    }

    public static String maskMobilePro(String mobile){
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        return mobile.replaceAll(MOBILE_REGULAR_ADD, "*");
    }

    public static String maskMobile2(String mobile){
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        return mobile.replaceAll(MOBILE_REGULAR_2, "*");
    }

    public static void main(String[] args) throws Exception {
//        String maskName = maskName("李廷伟大我认为人");
//        System.out.println("maskName = " + maskName);
//
//        String maskCnid = maskCnid("123656789123881235541");
//        System.out.println("maskCnid = " + maskCnid);
//
        String maskMobile = maskMobile("6399");
        System.out.println("maskMobile = " + maskMobile);

//        FileInputStream inputStream = new FileInputStream("E:\\测试下载文件夹\\画图.jpg");
//        byte[] b = IOUtils.toByteArray(inputStream);
//        System.out.println("getFileType(b) = " + getFileType(b));
    }


    /**
     * 根据文件字节数组，获取文件类型
     * @param fileBytes
     * @return
     */
    public static String getFileType(byte[] fileBytes){
        // 取前14个字节
        byte[] src = Arrays.copyOf(fileBytes, 14);
        StringBuilder code = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) {
                code.append(0);
            }
            code.append(hv);
        }
        return getFileTypeByCode(code.toString());
    }

    /**
     * 根据16进制编码，从枚举类中查询出文件类型
     * @param code
     * @return
     */
    public static String getFileTypeByCode(String code){
        FileType[] fileTypes = FileType.values();
        for (FileType fileType : fileTypes) {
            String[] codeArray = fileType.getValue().split("\\|");
            for (String c : codeArray) {
                if (code.startsWith(c)){
                    return fileType.name();
                }
            }
        }
        throw new FastRuntimeException("获取文件类型异常，无此文件类型");
    }

    /**
     * 查询该文件类型是否在字典中已配置，没配置的不允许上传
     * @param fileType
     * @return
     */
    public static boolean checkFileType(String fileType) {
        List<OpDictInfo> dict = CommonUtils.getDict("fileType", null);
        if (CollectionUtils.isEmpty(dict)) {
            return false;
        }
        OpDictInfo info = dict.get(0);
        if (Objects.isNull(info)){
            return false;
        }
        if (UNSAFE_SUFFIX_LIST.contains(fileType.toLowerCase())){
            throw new FastRuntimeException("文件类型不安全,不允许上传");
        }
        String[] fileTypeArr = info.getDetail().split(",");
        if (ArrayUtils.contains(fileTypeArr, fileType.toLowerCase())) {
            return true;
        } else {
            throw new FastRuntimeException("文件类型:[" + fileType + "],不允许上传");
        }
    }

    /**
     * 判断文件类型是否可以上传
     * @param originalFileName 用户上传的原始文件名称
     */
    public static boolean checkFileTypeByFileName(String originalFileName){
       if (StringUtils.isBlank(originalFileName) || !originalFileName.contains(".") || originalFileName.endsWith(".")) {
           throw new FastRuntimeException("文件类型必须包含有效的后缀名");
       }
        int indexPos = originalFileName.lastIndexOf(".");
        String fileType = originalFileName.substring(indexPos + 1);
        return checkFileType(fileType);
    }
}
