package com.welab.crm.operate.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/7/5
 */
@Data
public class LogOutModel implements Serializable {

    private static final long serialVersionUID = -6041084796947500158L;

    private Boolean agent;

    private Boolean blocked;

    private String cnid;

    private Long id;

    private String mobile;

    private String name;

    private String origin;

    private Date createdAt;
    
    private Integer userId;
}
