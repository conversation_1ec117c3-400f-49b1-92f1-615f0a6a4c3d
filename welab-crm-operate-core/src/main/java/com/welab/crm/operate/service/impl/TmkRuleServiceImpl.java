package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.workflow.busi.service.WFRunBusiService;
import com.welab.crm.operate.domain.TmkAssignHistory;
import com.welab.crm.operate.dto.tmkRule.TmkInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustInfo;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustReqDTO;
import com.welab.crm.operate.dto.workorder.AdjustWorkOrderInfo;
import com.welab.crm.operate.enums.TmkDistributionTypeEnum;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.mapper.OpOrderConfigMapper;
import com.welab.crm.operate.mapper.TmkAssignHistoryMapper;
import com.welab.crm.operate.mapper.TmkRuleInfoMapper;
import com.welab.crm.operate.service.ICrmOrgStaffService;
import com.welab.crm.operate.service.TmkRuleService;
import com.welab.crm.operate.service.TmkTaskService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.tmkRule.TmkInfoVO;
import com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 电销数据分配规则service服务
 * <AUTHOR>
 * @date 2022-02-24
 */
@Slf4j
@Service
public class TmkRuleServiceImpl implements TmkRuleService {

    @Resource
    private TmkRuleInfoMapper tmkRuleInfoMapper;

    @Resource
    private OpOrderConfigMapper opOrderConfigMapper;
    
    @Resource
    private WFRunBusiService runBusiService;
    
    @Resource
    private ICrmOrgStaffService crmOrgStaffService;

    @Resource
	private TmkAssignHistoryMapper tmkAssignHistoryMapper;
    
    @Resource
	private TmkTaskService tmkTaskService;

	@Resource
	private NoticeMsgServiceImpl noticeMsgService;

	@Resource
	private InAuthCrmStaffMapper inAuthCrmStaffMapper;

	@Override
	public Page<TmkInfoVO> queryTmkInfoList(TmkInfoReqDTO reqDTO) {
		log.info("queryTmkInfoList,reqDTO:{}", JSON.toJSONString(reqDTO));
		if (StringUtils.isNotBlank(reqDTO.getApprovedAtEnd())) {
			reqDTO.setApprovedAtEnd(plusEndDateParam(reqDTO.getApprovedAtEnd()));
		}
		if (StringUtils.isNotBlank(reqDTO.getEndCreateTime())) {
			reqDTO.setEndCreateTime(plusEndDateParam(reqDTO.getEndCreateTime()));
		}
		Page<TmkInfoVO> page = tmkRuleInfoMapper.queryTmkInfoByPage(
				new Page<>(reqDTO.getCurPage(), reqDTO.getPageSize()), reqDTO);
		page.getRecords().forEach(item -> item.setMobile(AesUtils.encrypt(item.getMobile())));
		
		return page;
	}
    
    @Override
    public List<TypeTotalTmkInfoVO> typeTotalTmkInfo(TmkInfoReqDTO reqDTO) {
        return tmkRuleInfoMapper.typeTotalTmkInfo(reqDTO);
    }
    
    @Override
    public void adjustTmkRule(TmkRuleAdjustReqDTO reqDTO) {
		//指定要分配的工单
		List<TmkRuleAdjustInfo> taskList = reqDTO.getTaskList();
		if (CollectionUtils.isEmpty(taskList)) {
			return;
		}
		log.info("adjustTmkRule taskList size:{}", taskList.size());
		//当前是第几条工单
		int curOrderNum = 1;
		String operatorId = reqDTO.getOperatorId();
		List<AdjustWorkOrderInfo> adjustList = reqDTO.getAssignList();
		// 保存分配的业务类型的list
		Set<String> tmkTypeSet = new HashSet<>();
		for (AdjustWorkOrderInfo adjustInfo : adjustList) {
			// 业务代码分配坐席 与任务
			while (taskList.size() > 0) {
				TmkRuleAdjustInfo taskInfo = taskList.get(0);
				
				//调用流程接口完成分单操作
				tmkTaskService.updateLatestResultCode(taskInfo.getTmkType(), taskInfo.getTmkTaskId(), 
						null, Long.parseLong(adjustInfo.getAssignStaffId()), "1");
				// 记录分单历史
				Date now = new Date();
				TmkAssignHistory tmkAssignHistory = new TmkAssignHistory();
				tmkAssignHistory.setTmkTaskId(taskInfo.getTmkTaskId());
				tmkAssignHistory.setGroupCode(adjustInfo.getAssignGroupCode());
				tmkAssignHistory.setStaffId(adjustInfo.getAssignStaffId());
				tmkAssignHistory.setCreateStaffId(operatorId);
				tmkAssignHistory.setDistributionTime(now);
				tmkAssignHistory.setGmtCreate(now);
				tmkAssignHistory.setGmtModify(now);
				tmkAssignHistory.setDistributionType(TmkDistributionTypeEnum.MANUAL.getValue());
				tmkAssignHistory.setMobile(AesUtils.getRealMobile(taskInfo.getMobile()));
				tmkAssignHistory.setUsername(taskInfo.getCustomerName());
				tmkAssignHistory.setApplicationId(taskInfo.getApplicationId());
				tmkAssignHistory.setCreateGroupCode(reqDTO.getOperatorGroupCode());
				// 手动调剂为第一次分配,所以之前组为系统组，之前staff为空
				tmkAssignHistory.setPreGroupCode("XT");
				tmkAssignHistory.setPreStaffId("");
				tmkAssignHistory.setUuid(taskInfo.getUuid());
				tmkAssignHistory.setUserId(taskInfo.getUserId());
				tmkAssignHistory.setProductName(taskInfo.getProductName());
				tmkAssignHistory.setApplyOrigin(taskInfo.getApplyOrigin());
				tmkAssignHistoryMapper.insert(tmkAssignHistory);
				taskList.remove(0);
				curOrderNum++;
				tmkTypeSet.add(taskInfo.getTmkType());
				if (curOrderNum > adjustInfo.getAssignNum()) {
					log.info("adjustTmkRule curOrderNum:{}", curOrderNum);
					// 发送调剂通知
					noticeMsgService.publishTmkMsg(tmkTypeSet, curOrderNum - 1, adjustInfo.getAssignStaffId());
					curOrderNum = 1;
					tmkTypeSet.clear();
					break;
				}
			}
		}
	}
	
	private String plusEndDateParam(String timeParam){
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
		LocalDate endDate = LocalDate.parse(timeParam, pattern);
		return pattern.format(endDate.plusDays(1));
	}
}
