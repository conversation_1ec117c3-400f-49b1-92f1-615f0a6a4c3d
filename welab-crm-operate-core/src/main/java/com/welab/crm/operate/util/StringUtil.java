package com.welab.crm.operate.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/30
 */
public class StringUtil {

    /**
     * 手机号替换规则
     */
    private static final String MOBILE_REGULAR = "(?<=\\d{3})\\d(?=\\d{4})";

    /**
     * 身份证号替换规则
     */
    private static final String CNID_REGULAR = "(?<=\\d{6})\\d(?=\\d{4})";

    /**
     * 银行卡替换规则
     */
    private static final String BANKCARD_REGULAR = "(?<=\\d{4})\\d(?=\\d{4})";

    /**
     * 手机号匹配规则
     */
    public static final Pattern MOBILE_PATTERN = Pattern.compile("^1(3|4|5|6|7|8|9)\\d{9}$");

    /**
     * 身份证号匹配规则，粗略的校验
     */
    public static final Pattern CNID_PATTERN = Pattern
        .compile("^(\\d{6})(19|20)(\\d{2})(1[0-2]|0[1-9])(0[1-9]|[1-2][0-9]|3[0-1])(\\d{3})(\\d|X|x)?$");

    public static String hideMobile(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(MOBILE_REGULAR, "*");
        }
        return "";
    }

    public static String hideCnid(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(CNID_REGULAR, "*");
        }
        return "";
    }

    public static String hideBankCard(String str) {
        if(StringUtils.isNotBlank(str)){
            return str.replaceAll(BANKCARD_REGULAR, "*");
        }
        return "";
    }

    /**
     * 判断传入的字符串是否是手机号
     */
    public static boolean isMobile(String mobile) {
        Matcher m = MOBILE_PATTERN.matcher(mobile);
        return m.matches();
    }

    /**
     * 18位身份证校验,粗略的校验
     *
     * @param idCard
     * @return
     */
    public static boolean is18ByteIdCard(String idCard) {
        Matcher matcher = CNID_PATTERN.matcher(idCard);
        return matcher.matches();
    }

    /**
     * 校验银行卡卡号
     *
     * @param bankCard
     * @return
     */
    public static boolean checkBankCard(String bankCard) {
        if (bankCard.length() < 15 || bankCard.length() > 19) {
            return false;
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        if (bit == 'N') {
            return false;
        }
        return bankCard.charAt(bankCard.length() - 1) == bit;
    }

    /**
     * 从不含校验位的银行卡卡号采用 Luhm 校验算法获得校验位
     *
     * @param nonCheckCodeBankCard
     * @return
     */
    public static char getBankCardCheckCode(String nonCheckCodeBankCard) {
        if (nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
            || !nonCheckCodeBankCard.matches("\\d+")) {
            // 如果传的不是数据返回N
            return 'N';
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for (int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - '0';
            if (j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? '0' : (char) ((10 - luhmSum % 10) + '0');
    }
}
