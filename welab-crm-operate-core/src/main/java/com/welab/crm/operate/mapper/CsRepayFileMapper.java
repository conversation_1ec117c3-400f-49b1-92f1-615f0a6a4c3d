package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.CsRepayFile;
import com.welab.crm.operate.dto.repay.RepayQueryDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 客户还款明细查询表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2022-11-05
 */
public interface CsRepayFileMapper extends BaseMapper<CsRepayFile> {

    Page<CsRepayFile> listRepayDetailPage(Page<CsRepayFile> page, @Param("filter") RepayQueryDTO queryDTO);

    CsRepayFile selectWalletUnComplete(@Param("filter") CsRepayFile file);
}
