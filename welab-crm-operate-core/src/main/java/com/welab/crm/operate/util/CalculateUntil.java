package com.welab.crm.operate.util;

import com.google.common.base.Preconditions;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class CalculateUntil {

    public static BigDecimal calculateIRR24Amount(int tenor, BigDecimal principal, BigDecimal interestRate){
        return setRound(getAmountPerInstallment(tenor, principal, interestRate)).multiply(BigDecimal.valueOf(tenor));
    }

    /**
     * 设置金额小数位
     */
    public static BigDecimal setRound(BigDecimal amount) {
        Preconditions.checkNotNull(amount);
        return amount.setScale(2,  BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 每期还款金额
     */
    public static BigDecimal getAmountPerInstallment(int tenor, BigDecimal principal, BigDecimal interestRate) {
        BigDecimal monthlyInterestRate = divide(interestRate, new BigDecimal("12"));
        return pmt(monthlyInterestRate, tenor, principal.negate(), BigDecimal.ZERO, 0);
    }

    public static BigDecimal divide(BigDecimal amount, BigDecimal index) {
        return amount.divide(index, 10, RoundingMode.HALF_UP);
    }

    public static BigDecimal pmt(BigDecimal r, int nper, BigDecimal pv, BigDecimal fv, int type) {
        if (r.compareTo(BigDecimal.ZERO) == 0) {
            return pv.add(fv).negate().divide(BigDecimal.valueOf(nper), 2,  BigDecimal.ROUND_HALF_UP);
        }
        BigDecimal pmt = r.negate().multiply(pv.multiply(onePlusPrPowNper(r, nper)).add(fv)).divide(
                (prMultiplyTypePlusone(r, type).multiply(onePlusPrPowNper(r, nper).subtract(BigDecimal.ONE))), BigDecimal.ROUND_HALF_UP);
        return pmt;
    }

    private static BigDecimal onePlusPrPowNper(BigDecimal r, int nper) {
        return r.add(BigDecimal.ONE).pow(nper);
    }

    private static BigDecimal prMultiplyTypePlusone(BigDecimal r, int type) {
        return r.multiply(BigDecimal.valueOf(type)).add(BigDecimal.ONE);
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = calculateIRR24Amount(12, new BigDecimal("12200"), new BigDecimal("0.36"));
        
        System.out.println("bigDecimal = " + bigDecimal);

    }
}