-- 为喵达投诉单本地查询添加性能优化索引
-- 作者: 系统生成
-- 日期: 2025-01-24

-- 检查并删除可能存在的旧索引（避免重复创建）
DROP INDEX IF EXISTS idx_miaoda_sn ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_work_order_no ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_status_assigned_at ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_sync_auto_reply_status ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_status_sync_assigned ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_nickname ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_phone ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_comp_phone ON miaoda_complaint;
DROP INDEX IF EXISTS idx_miaoda_gmt_create ON miaoda_complaint;

-- 1. 单字段索引 - 用于精确查询
-- 投诉单号索引（高频精确查询）
CREATE INDEX idx_miaoda_sn ON miaoda_complaint (sn);

-- 工单编号索引（关联查询）
CREATE INDEX idx_miaoda_work_order_no ON miaoda_complaint (work_order_no);

-- 投诉人昵称索引（模糊查询优化）
CREATE INDEX idx_miaoda_nickname ON miaoda_complaint (nickname);

-- 联系方式索引（模糊查询优化）
CREATE INDEX idx_miaoda_phone ON miaoda_complaint (phone);
CREATE INDEX idx_miaoda_comp_phone ON miaoda_complaint (comp_phone);

-- 创建时间索引（时间范围查询）
CREATE INDEX idx_miaoda_gmt_create ON miaoda_complaint (gmt_create);

-- 2. 复合索引 - 用于组合查询优化
-- 状态 + 分配时间索引（最常用的组合查询）
CREATE INDEX idx_miaoda_status_assigned_at ON miaoda_complaint (status, assigned_at);

-- 同步状态 + 自动回复状态索引（系统状态查询）
CREATE INDEX idx_miaoda_sync_auto_reply_status ON miaoda_complaint (sync_status, auto_reply_status);

-- 状态 + 同步状态 + 分配时间索引（综合查询优化）
CREATE INDEX idx_miaoda_status_sync_assigned ON miaoda_complaint (status, sync_status, assigned_at);

-- 分配时间 + 同步状态索引（时间范围 + 状态查询）
CREATE INDEX idx_miaoda_assigned_sync ON miaoda_complaint (assigned_at, sync_status);

-- 自动回复状态 + 分配时间索引（自动回复监控查询）
CREATE INDEX idx_miaoda_auto_reply_assigned ON miaoda_complaint (auto_reply_status, assigned_at);

-- 3. 覆盖索引 - 用于特定查询场景
-- 投诉单基本信息查询覆盖索引
CREATE INDEX idx_miaoda_basic_info ON miaoda_complaint (sn, status, nickname, phone, assigned_at);

-- 系统状态监控覆盖索引
CREATE INDEX idx_miaoda_system_status ON miaoda_complaint (sync_status, auto_reply_status, status, assigned_at);

-- 4. 分析查询性能的索引
-- 用于统计分析的索引
CREATE INDEX idx_miaoda_stats ON miaoda_complaint (status, assigned_at, gmt_create);

-- 添加索引使用说明注释
-- 索引使用场景说明：
-- 
-- idx_miaoda_sn: 
--   - 用于: WHERE sn = ?
--   - 场景: 精确查询投诉单
-- 
-- idx_miaoda_work_order_no:
--   - 用于: WHERE work_order_no = ?
--   - 场景: 根据工单号查询投诉单
-- 
-- idx_miaoda_status_assigned_at:
--   - 用于: WHERE status = ? AND assigned_at BETWEEN ? AND ?
--   - 场景: 按状态和时间范围查询
-- 
-- idx_miaoda_sync_auto_reply_status:
--   - 用于: WHERE sync_status = ? AND auto_reply_status = ?
--   - 场景: 系统状态监控查询
-- 
-- idx_miaoda_status_sync_assigned:
--   - 用于: WHERE status = ? AND sync_status = ? ORDER BY assigned_at
--   - 场景: 综合状态查询和排序
-- 
-- idx_miaoda_nickname:
--   - 用于: WHERE nickname LIKE ?
--   - 场景: 按用户昵称模糊查询
-- 
-- idx_miaoda_phone:
--   - 用于: WHERE phone LIKE ? OR comp_phone LIKE ?
--   - 场景: 按联系方式模糊查询
-- 
-- idx_miaoda_basic_info:
--   - 用于: 覆盖基本信息查询，减少回表
--   - 场景: 列表页面的基本信息展示
-- 
-- idx_miaoda_system_status:
--   - 用于: 系统状态监控和统计
--   - 场景: 运维监控和数据分析

-- 更新表统计信息（MySQL）
ANALYZE TABLE miaoda_complaint;

-- 添加表注释更新
ALTER TABLE miaoda_complaint COMMENT = '喵达投诉单表，包含JSON字段存储回复、结案和附件信息，以及自动回复状态跟踪。已优化索引支持高效的本地查询。';
