-- 为miaoda_complaint表添加自动回复相关字段
-- 作者: 系统生成
-- 日期: 2025-01-24

-- 添加自动回复状态字段
ALTER TABLE miaoda_complaint 
ADD COLUMN auto_reply_status TINYINT COMMENT '自动回复状态：NULL-未处理，1-成功，2-失败';

-- 添加自动回复时间字段
ALTER TABLE miaoda_complaint 
ADD COLUMN auto_reply_time DATETIME COMMENT '自动回复时间';

-- 添加索引优化查询性能
CREATE INDEX idx_auto_reply_query ON miaoda_complaint (status, assigned_at, auto_reply_status, sync_status);

-- 为op_dict_info表添加自动回复配置数据
INSERT INTO op_dict_info (id, category, type, content, detail, status, sort, gmt_create, gmt_modify) VALUES
(9000000001, 'miaoda_auto_reply', 'enabled', 'true', '自动回复功能开关：true-开启，false-关闭', 0, 1, NOW(), NOW()),
(9000000002, 'miaoda_auto_reply', 'content', '您好，我们已收到您的投诉，正在处理中，请耐心等待。如有疑问，请联系客服。', '自动回复内容模板', 0, 2, NOW(), NOW()),
(9000000003, 'miaoda_auto_reply', 'time_limit', '3', '自动回复时限（小时）', 0, 3, NOW(), NOW()),
(9000000004, 'miaoda_auto_reply', 'sync_interval', '30', '同步任务执行频率（分钟）', 0, 4, NOW(), NOW()),
(9000000005, 'miaoda_auto_reply', 'hide_content', '0', '是否隐藏回复内容：0-不隐藏，1-隐藏', 0, 5, NOW(), NOW()),
(9000000006, 'miaoda_auto_reply', 'hide_attach', '0', '是否隐藏回复附件：0-不隐藏，1-隐藏', 0, 6, NOW(), NOW());

-- 更新表注释
ALTER TABLE miaoda_complaint COMMENT = '喵达投诉单表，包含JSON字段存储回复、结案和附件信息，以及自动回复状态跟踪';
