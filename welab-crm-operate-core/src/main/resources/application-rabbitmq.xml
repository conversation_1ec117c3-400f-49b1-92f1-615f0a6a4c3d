<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit-1.7.xsd">
	<!-- mq连接池工厂 -->
<!-- 	<bean id="mqConnectionFactory"
		class="org.springframework.amqp.rabbit.connection.CachingConnectionFactory">
		<property name="uri" value="${welab.mq.uri}" />
		<property name="publisherConfirms" value="true" />
	</bean> -->

	<!-- listen监听容器 -->
<!-- 	<bean id="container"
		class="org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer">
		<property name="acknowledgeMode" value="MANUAL" />
		<property name="connectionFactory" ref="mqConnectionFactory" />
		<property name="messageListener" ref="messageListener" />
	</bean>
	<bean id="messageListener" class="com.welab.rabbitmq.impl.MessageDefaultListenerImpl">
		<property name="messageConverter" ref="messageConverter" />
	</bean>
	<bean id="messageConverter"
		class="org.springframework.amqp.support.converter.Jackson2JsonMessageConverter" />
	<bean class="com.welab.rabbitmq.MessageListenerAnnotationBeanPostProcessor"/> -->

	<!-- mq访问类builder，暴露给外界使用 -->
<!-- 	<bean id="mqAccessBuilder" class="com.welab.rabbitmq.MqAccessBuilder">
		<property name="connectionFactory" ref="mqConnectionFactory" />
		<property name="container" ref="container" />
	</bean> -->

</beans>