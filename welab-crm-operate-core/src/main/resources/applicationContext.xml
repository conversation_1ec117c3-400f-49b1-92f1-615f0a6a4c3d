<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:lang="http://www.springframework.org/schema/lang" xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:oxm="http://www.springframework.org/schema/oxm" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
		http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/oxm http://www.springframework.org/schema/oxm/spring-oxm.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<!-- 开启注解 -->
	<context:annotation-config />

	<!-- 启动AspectJ支持,代理实现采用CGLIB,而不是JDK -->
	<aop:aspectj-autoproxy proxy-target-class="true" />

	<!-- 扫描com.*.*包下所有标注服务组件 -->
	<context:component-scan base-package="com.welab">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
		<context:exclude-filter type="regex" expression="com.welab.authority.cache.provider.*" />

	</context:component-scan>

	<!-- 加载datasource配置 -->
	<import resource="classpath:/application-datasource.xml" />

	<!-- 加载dubbo配置 -->
	<import resource="classpath:/dubbo/dubbo-consumer.xml" />
	<import resource="classpath:/dubbo/dubbo-provider.xml" />

	<!-- 加载job配置 -->
	<import resource="classpath*:job/elasticJob.xml" />
	<!-- 自定义配置文件 appId:应用的名称,fileName:配置文件的名称 -->
	<bean id="defaultConfig" class="com.welab.common.config.DefaultConfigService">
		<property name="appId" value="welab-crm-operate"/>
		<property name="fileNames" value="server.properties"/>
	</bean>

	<!-- 生成分布式唯一ID,全局唯一 -->
	<bean id="keyGenerator" class="com.welab.common.keygen.KeyGeneratorFactory">
		<property name="zookeeperUrl" value="${zookeeper.url}" />
	</bean>
	
</beans>
