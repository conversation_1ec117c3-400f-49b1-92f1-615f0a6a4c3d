<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.InLenderWithholdRecordMapper">


    <select id="queryWithholdRecordByCondition" resultType="com.welab.crm.operate.vo.withhold.WithholdVO">
        select
            t0.gmt_create as applyTime,
            t1.group_name,
            t1.staff_name,
            t2.customer_name,
            t0.application_id,
            t0.repay_type as repaymentMode,
            t0.amount,
            t0.gmt_modify as updateTime,
            t0.pay_code as repayOrigin,
            t0.callback_result as result,
            t0.fail_reason,
            t1.id as staffId,
            t2.id as customerId,
            t2.uuid,
            t2.user_id,
            case
            when t0.consultation_status = 1 then '已逾期'
            when t0.consultation_status = 2 then '待还款' end
            as consultationStatus
        from
        in_lender_withhold_record t0
        left join in_auth_crm_staff t1 on t0.staff_id = t1.id
        left join data_customer t2 on t0.user_id = t2.id
        left join in_phone_login_info t3 on t1.login_name = t3.user_tel
        where 1=1
        <if test="dto.startTime != null">
            and date_format(t0.gmt_create,'%Y-%m-%d') &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            and date_format(t0.gmt_create,'%Y-%m-%d') &lt;= #{dto.endTime}
        </if>
        <if test='dto.isWallet != null and dto.isWallet == 0'>
            and t0.application_id not like 'WL%'
        </if>
        <if test='dto.isWallet != null and dto.isWallet == 1'>
            and t0.application_id like 'WL%'
        </if>
        <if test="dto.applicationId != null and dto.applicationId != ''">
            and t0.application_id = #{dto.applicationId}
        </if>
        <if test="dto.repaymentMode != null and dto.repaymentMode != ''">
            and t0.repay_type = #{dto.repaymentMode}
        </if>
        <if test="dto.result != null and dto.result != ''">
            and t0.callback_result = #{dto.result}
        </if>
        <if test="dto.cno != null and dto.cno != ''">
            and t3.id_no = #{dto.cno}
        </if>
        <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
            and t1.group_code in
            <foreach collection="dto.groupCodeList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
            and t1.id in
            <foreach collection="dto.staffIdList" open="(" close=")" index="index" item="item" separator=",">
                #{item}
            </foreach>
        </if>

        order by t0.gmt_create desc
    </select>

    <select id="queryWithholdRecordByConditionGroups" resultType="java.lang.String">
        select t1.group_name from in_lender_withhold_record t0
        left join in_auth_crm_staff t1 on t0.staff_id = t1.id
        left join data_customer t2 on t0.user_id = t2.id
        left join in_phone_login_info t3 on t1.login_name = t3.user_tel
        group by t1.group_name
    </select>

    <select id="queryWithholdReport" resultType="com.welab.crm.operate.dto.withhold.WithholdReportVO">
            select
                <if test="dto.period != null and dto.period != ''">
                    ${dto.period} as time,
                </if>
                <if test="dto.period = ''">
                    concat(#{dto.startTime},'-',#{dto.endTime}) as time,
                </if>
                count(distinct service_no) as orderCount,
                count(distinct user_id) as customerCount,
                count(callback_result = 'success' or null) as successCount,
                count(callback_result = 'fail' or null) as failCount,
                sum(if(callback_result = 'success',amount,0)) as successAmount,
                sum(if(callback_result = 'fail',amount,0) ) as failAmount
            from in_lender_withhold_record t0
            where 1=1
            and t0.gmt_create &gt;= #{dto.startTime}
            and t0.gmt_create &lt;= #{dto.endTime}
            <if test='dto.isWallet != null and dto.isWallet == 0'>
                and t0.application_id not like 'WL%'
            </if>
            <if test='dto.isWallet != null and dto.isWallet == 1'>
                and t0.application_id like 'WL%'
            </if>
            group by time
    </select>

</mapper>
