<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ContractSendRecordMapper">
    
    <sql id="query">
        SELECT
        csr.id,
        csr.app_no,
        csr.uuid,
        csr.customer_name,
        csr.status,
        csr.gmt_create as createTime,
        csr.group_code,
        csr.group_name,
        csr.staff_id,
        csr.staff_name,
        csr.partner_code,
        csr.partner_name,
        csr.send_type,
        csr.gmt_modify,
        csr.mobile,
        csr.send_time,
        customer_id,
        message_id
        FROM
        contract_send_record csr
        WHERE
        1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            AND csr.gmt_create >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND csr.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.groupCodes != null and dto.groupCodes.size() > 0">
            and csr.group_code in
            <foreach collection="dto.groupCodes" open="(" close=")" item="item" separator="," index="index">
                #{item}
            </foreach>
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and csr.uuid = #{dto.uuid}
        </if>
        <if test="dto.appNo != null and dto.appNo != ''">
            and csr.app_no = #{dto.appNo}
        </if>
        <if test="dto.partnerName != null and dto.partnerName != ''">
            and csr.partner_name = #{dto.partnerName}
        </if>
        <if test="dto.mobile != null and dto.mobile != ''">
            and csr.mobile = #{dto.mobile}
        </if>
        <if test="dto.sendType != null">
            and csr.send_type = #{dto.sendType}
        </if>
        <if test="dto.status != null ">
            <choose>
                <when test="dto.status == 1 and dto.queryFlag == 2">
                    and csr.status in (1,3,4)
                </when>
                <otherwise>
                    and csr.status = #{dto.status}
                </otherwise>
            </choose>
        </if>
        order by csr.gmt_create desc
    </sql>
    <select id="queryContractSendPage" resultType="com.welab.crm.operate.vo.ContractSendVO">

        <include refid="query"/>
    </select>
    <select id="queryContractSendList" resultType="com.welab.crm.operate.vo.ContractSendVO">
        <include refid="query"/>
    </select>

</mapper>
