<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.WoTask">
        <id column="id" property="id" />
        <result column="distribute_time" property="distributeTime" />
        <result column="submit_time" property="submitTime" />
        <result column="cust_id" property="custId" />
        <result column="cnid" property="cnid" />
        <result column="customer_name" property="customerName" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="mobile" property="mobile" />
        <result column="mobile_bak" property="mobileBak" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="order_one_class" property="orderOneClass" />
        <result column="order_two_class" property="orderTwoClass" />
        <result column="order_three_class" property="orderThreeClass" />
        <result column="order_case" property="orderCase" />
        <result column="urgent_flag" property="urgentFlag" />
        <result column="callback_flag" property="callbackFlag" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="create_group_code" property="createGroupCode" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="modify_staff_id" property="modifyStaffId" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="description" property="description" />
        <result column="opinion" property="opinion" />
        <result column="callback_note" property="callbackNote" />
        <result column="remark" property="remark" />
        <result column="reminder_flag" property="reminderFlag" />
        <result column="suc_loan_count" property="sucLoanCount" />
        <result column="complaints_channel" property="complaintsChannel" />
        <result column="fund_name" property="fundName" />
    </resultMap>
    <update id="updateOrder">
        update wo_task
        set response_time_out = #{woTask.responseTimeOut}
        , resolve_content = #{woTask.resolveContent}
        where order_no = #{woTask.orderNo}
    </update>

    <select id="queryWorkOrderByPage" resultType="com.welab.crm.operate.vo.workorder.WorkOrderInfoVO">
        select 
        a.id,
        a.order_no,
        a.reminder_flag,
        a.distribute_time,
        a.submit_time,
        a.orderType,
        a.orderOneClass,
        a.orderTwoClass,
        a.orderThreeClass,
        a.cust_type,
        a.customer_name,
        a.mobile,
        a.task_id,
        a.execution_id,
        a.vip,
        a.node_code,
        a.sign,
        a.user_id,
        a.uuid,
        a.urgent_flag,
        a.response_time_out
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            , case when b.id is null then 1 else 0 end as newFlag
        </if>
        from (select distinct
        wt.id,
        wt.order_no,
        wt.reminder_flag,
        wt.distribute_time,
        wt.submit_time,
        o1.content as orderType,
        o2.content as orderOneClass,
        o3.content as orderTwoClass,
        o4.content as orderThreeClass,
        dc.cust_type,
        wt.customer_name,
        wt.mobile,
        wrt.node_code,
        wrt.task_id,
        wre.execution_id,
        dc.vip,
        ifnull(wts.sign,'0') as sign,
        wt.gmt_create,
        dc.user_id,
        dc.uuid,
        wt.urgent_flag,
        wt.response_time_out
        <if test="reqDTO.complainStatus != null">
            , occ.approve_status, urge_status
        </if>
        from wo_task wt 
        inner join data_customer dc on wt.cust_id = dc.id
        <if test="reqDTO.complainStatus != null">
            inner join op_customer_complain occ on occ.work_order_no = wt.order_no
        </if>
        left join data_loan_application dla on dla.wo_task_id = wt.id
		left join wf_ru_execution wre on wre.busi_key = wt.order_no
        left join wf_ru_task wrt on wrt.execution_id = wre.execution_id
        <if test="reqDTO.noContactTimeStart != null">
            left join callback_summary cs on
            cs.id = (
            select
            cs2.id
            from
            callback_summary cs2
            where
            cs2.uuid = dc.uuid
            and cs2.staff_id = wrt.staff_id
            order by
            cs2.id desc
            limit 1 )
        </if>
        left join op_dict_info o1 on o1.id = wt.type
        left join op_dict_info o2 on o2.id = wt.order_one_class
        left join op_dict_info o3 on o3.id = wt.order_two_class
        left join op_dict_info o4 on o4.id = wt.order_three_class
        left join wo_task_sign wts on (wt.order_no = wts.order_no and wrt.staff_id = wts.staff_id)
        where wrt.status in ('ready','unassigned')
        <if test="reqDTO.orderType != null and reqDTO.orderType != ''">
            and wt.type = #{reqDTO.orderType}
        </if>
        <if test="reqDTO.orderOneClass != null and reqDTO.orderOneClass != ''">
            and wt.order_one_class = #{reqDTO.orderOneClass}
        </if>
        <if test="reqDTO.orderTwoClass != null and reqDTO.orderTwoClass != ''">
            and wt.order_two_class = #{reqDTO.orderTwoClass}
        </if>
        <if test="reqDTO.orderThreeClass != null and reqDTO.orderThreeClass != ''">
            and wt.order_three_class = #{reqDTO.orderThreeClass}
        </if>
        <if test="reqDTO.customerName != null and reqDTO.customerName != ''">
            and wt.customer_name = #{reqDTO.customerName}
        </if>
        <if test="reqDTO.mobile != null and reqDTO.mobile != ''">
            and wt.mobile = #{reqDTO.mobile}
        </if>
        <if test="reqDTO.cnid != null and reqDTO.cnid != ''">
            and wt.cnid = #{reqDTO.cnid}
        </if>
        <if test="reqDTO.custType != null and reqDTO.custType != ''">
            and dc.cust_type = #{reqDTO.custType}
        </if>
        <if test="reqDTO.reminderFlag != null and reqDTO.reminderFlag != ''">
            and wt.reminder_flag = #{reqDTO.reminderFlag}
        </if>
        <if test="reqDTO.orderNo != null and reqDTO.orderNo != ''">
            and wt.order_no = #{reqDTO.orderNo}
        </if>
        <if test="reqDTO.groupCodeList != null and reqDTO.groupCodeList.size() > 0">
            and wrt.group_code in
            <foreach collection="reqDTO.groupCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            and wrt.staff_id = #{reqDTO.staffId}
        </if>
        <if test="reqDTO.staffId == null or reqDTO.staffId == ''">
            and (wrt.staff_id is null or wrt.staff_id = '')
        </if>
        <if test="reqDTO.distributeStartDate != null">
            and wt.distribute_time &gt;= #{reqDTO.distributeStartDate}
        </if>
        <if test="reqDTO.distributeEndDate != null">
            and wt.distribute_time &lt; #{reqDTO.distributeEndDate}
        </if>
        <if test="reqDTO.submitStartDate != null">
            and wt.submit_time &gt;= #{reqDTO.submitStartDate}
        </if>
        <if test="reqDTO.submitEndDate != null">
            and wt.submit_time &lt;= #{reqDTO.submitEndDate}
        </if>
        <if test="reqDTO.urgentFlag != null and reqDTO.urgentFlag != ''">
            and wt.urgent_flag = #{reqDTO.urgentFlag}
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and wre.status = #{reqDTO.status}
        </if>
        <if test="reqDTO.vip != null and reqDTO.vip != ''">
            and dc.vip = #{reqDTO.vip}
        </if>
        <if test="reqDTO.productName != null and reqDTO.productName != ''">
            and dla.product_name = #{reqDTO.productName}
        </if>
        <if test="reqDTO.channelCode != null and reqDTO.channelCode != ''">
            and dla.channel_code = #{reqDTO.channelCode}
        </if>
        <if test="reqDTO.partnerCode != null and reqDTO.partnerCode != ''">
            and dla.partner_code = #{reqDTO.partnerCode}
        </if>
        <if test="reqDTO.applyStartDate != null">
            and dla.apply_time &gt;= #{reqDTO.applyStartDate}
        </if>
        <if test="reqDTO.applyEndDate != null">
            and dla.apply_time &lt;= #{reqDTO.applyEndDate}
        </if>
        <if test="reqDTO.approvalStartDate != null">
            and dla.approval_time &gt;= #{reqDTO.approvalStartDate}
        </if>
        <if test="reqDTO.approvalEndDate != null">
            and dla.approval_time &lt;= #{reqDTO.approvalEndDate}
        </if>
        <if test="reqDTO.confirmStartDate != null">
            and dla.confirm_time &gt;= #{reqDTO.confirmStartDate}
        </if>
        <if test="reqDTO.confirmEndDate != null">
            and dla.confirm_time &lt;= #{reqDTO.confirmEndDate}
        </if>
        <if test="reqDTO.loanStartDate != null">
            and dla.loan_time &gt;= #{reqDTO.loanStartDate}
        </if>
        <if test="reqDTO.loanEndDate != null">
            and dla.loan_time &lt;= #{reqDTO.loanEndDate}
        </if>
        <if test="reqDTO.complainStatus != null">
            and occ.approve_status = #{reqDTO.complainStatus}
        </if>
        <if test="reqDTO.userId != null and reqDTO.userId != ''">
            and dc.user_id = #{reqDTO.userId}
        </if>
        <if test="reqDTO.uuid != null and reqDTO.uuid != ''">
            and dc.uuid = #{reqDTO.uuid}
        </if>
        <if test="reqDTO.noContactTimeStart != null">
            and TIMESTAMPDIFF(HOUR,
            ifnull(GREATEST(cs.gmt_create, wrt.gmt_modify), wrt.gmt_modify),
            NOW()) &gt;= #{reqDTO.noContactTimeStart}
        </if>
        <if test="reqDTO.noContactTimeEnd != null">
            and TIMESTAMPDIFF(HOUR,
            ifnull(GREATEST(cs.gmt_create, wrt.gmt_modify), wrt.gmt_modify),
            NOW()) &lt;= #{reqDTO.noContactTimeEnd}
        </if>
        ) a
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            left join
            (select * from wf_ru_task wrt2 where id in (select max(id)  from wf_ru_task wrt group by execution_id )) c
            on c.execution_id = a.execution_id
            left join (select wrl2.* from wf_ru_log wrl2 where wrl2.id in (
            select max(id) from wf_ru_log wrl where JSON_EXTRACT(comment, '$.opinion') != '' and operate_type not in ('催单', '手动调剂', '提单') group by wrl.execution_id
            )
            )b on c.task_id = b.task_id and c.staff_id = b.staff_id
        </if>
        where 1 = 1
        <if test="reqDTO.sign != null and reqDTO.sign != ''">
            and a.sign = #{reqDTO.sign}
        </if>
        order by
        a.response_time_out desc,
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            newFlag desc,
        </if>
        a.sign desc,
        a.reminder_flag desc,
        a.urgent_flag desc,
        a.gmt_create desc
    </select>
    
    <select id="queryWorkCountList" resultType="com.welab.crm.operate.vo.workorder.WorkOrderCountVO">
        SELECT
        <if test="reqDTO.orderType == null or reqDTO.orderType == ''">
            "工单总量" as orderType,
        </if>
        <if test="reqDTO.orderType == '工单总量'">
            t0.type as orderType,
        </if>
        <if test="reqDTO.orderType != null and reqDTO.orderType != ''
        		and reqDTO.orderType != '工单总量'">
            t0.order_one_class as orderType,
        </if>
            count(1) as count
        FROM wo_task t0
        WHERE 1 = 1 
        <if test="reqDTO.orderType != null and reqDTO.orderType != ''
        		and reqDTO.orderType != '工单总量'">
            and t0.type = #{reqDTO.orderType}
        </if>
        group by orderType
        order by orderType
    </select>
    <select id="totalWorkOrder" resultType="java.lang.Integer">
        SELECT
            count(1) as count
        from wf_ru_task wrt
        inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
		INNER JOIN wo_task wt on wre.busi_key = wt.order_no 
        WHERE 1 = 1 
        <if test="reqDTO.orderOneClass != null and reqDTO.orderOneClass != ''">
            and wt.order_one_class = #{reqDTO.orderOneClass}
        </if>
        <if test="reqDTO.groupCode != null and reqDTO.groupCode != ''">
            and wrt.group_code = #{reqDTO.groupCode}
        </if>
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            and wrt.staff_id = #{reqDTO.staffId}
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and wt.status = #{reqDTO.status}
        </if>
    </select>
    
    <select id="queryWorkOrderDetail" resultType="com.welab.crm.operate.vo.workorder.WorkOrderDetailVO">
        SELECT
        	wt.id,
        	wt.cust_id,
        	wt.cnid,
        	wt.customer_name,
        	wt.age,
        	wt.gender,
        	wt.mobile,
        	wt.mobile_bak,
        	wt.remark,
        	wt.order_no,
        	wt.type,
        	wt.status,
        	wt.order_one_class,
        	wt.order_two_class,
        	wt.order_three_class,
        	wt.urgent_flag,
        	wt.callback_flag,
        	wt.description,
        	wt.opinion,
        	wt.callback_note,
            wt.complaints_channel,
            wt.fund_name,
            wt.match_up,
        	wts.sign,
        	wac.appoint_time,
        	wac.appoint_title,
        	wac.status as appointStatus,
        	dc.user_id,
        	dc.uuid,
        	wt.mobile_baks,
        	wt.email
        FROM wo_task wt
        left join wo_task_sign wts on (wt.order_no = wts.order_no and wts.staff_id = #{staffId})
        left join wo_appoint_callback wac on (wt.order_no = wac.order_no and wac.staff_id = #{staffId})
        left join data_customer dc on wt.cust_id = dc.id
        WHERE wt.id = #{id}
    </select>
    
    <select id="queryWorkOrderLogList" resultType="com.welab.crm.operate.vo.workorder.WorkOrderLogVO">
        SELECT
        	iacs.staff_name as staff_id,
        	wrl.gmt_create,
        	wrl.comment,
        	iacs.group_code as groupCode,
        	wdt.show_name as operate
        FROM wf_ru_log wrl 
        inner join wf_ru_task wrt on wrt.task_id = wrl.task_id
        inner join in_auth_crm_staff iacs on wrt.staff_id = iacs.id
        left join wf_de_transition wdt on wrl.trans_cde = wdt.trans_code
        WHERE wrl.execution_id = #{executionId}
        order by wrl.gmt_create desc
    </select>
    
    <select id="queryWorkOrderLoanList" resultType="com.welab.crm.operate.dto.workorder.WorkOrderLoanReqDTO">
        SELECT
		  	dla.application_id,
		  	dla.product_name,
		  	dla.apply_Time,
		  	dla.approval_time,
		  	dla.confirm_time,
		  	dla.loan_time,
		  	dla.channel_code,
		  	dla.partner_code,
		  	dla.apply_amount,
			dla.approval_amount,
			dla.apply_tenor,
			dla.approval_tenor,
			dla.status,
			dla.user_level
        FROM data_loan_application dla 
        inner join wo_task wt on dla.wo_task_id = wt.id
        WHERE wt.id = #{id}
    </select>

    <select id="queryWorkOrderHistory" resultType="com.welab.crm.operate.vo.workorder.WorkOrderHisVO">
        select wt.id,wt.order_no, odic.wo_type_detail as type,odic.wo_type_thir_detail as thirdType,
          wt.customer_name,wt.gmt_create,wt.opinion,wt.resolve_content
        from wo_task wt
        inner join op_dict_info_conf odic on odic.wo_type_id=wt.type
          and odic.wo_type_fir_id=wt.order_one_class
          and odic.wo_type_sec_id=wt.order_two_class
          and odic.wo_type_thir_id=wt.order_three_class
        where 1=1
          <if test="ids != null and ids.size() > 0">
            and wt.cust_id in
            <foreach collection="ids" separator="," item="item" open="(" close=")" index="">
                #{item}
            </foreach>
          </if>
          <if test="orderNo!=null and orderNo!=''">
            and wt.order_no=#{orderNo}
          </if>
           order by wt.gmt_create desc
    </select>
    <select id="queryWorkOrderLogListByBusiKey" resultType="com.welab.crm.operate.vo.workorder.WorkOrderLogVO">
            select
            iacs.group_name ,
            iacs.staff_name as staff_id,
            whl.comment ,
            whl.gmt_create
            from wf_his_execution whe
            left join wf_his_log whl on whe.execution_id = whl.execution_id
            left join in_auth_crm_staff iacs on whl.staff_id = iacs.id
            where whe.busi_key = #{busiKey}
            union all
            select
            iacs.group_name ,
            iacs.staff_name as staff_id,
            whl.comment ,
            whl.gmt_create
            from wf_ru_execution whe
            left join wf_ru_log whl on whe.execution_id = whl.execution_id
            left join in_auth_crm_staff iacs on whl.staff_id = iacs.id
            where whe.busi_key = #{busiKey}
            order by gmt_create desc
    </select>
    <select id="queryWorkOrderHistoryByCondition"
        resultType="com.welab.crm.operate.domain.WoTask">
        select wt.*
        from wo_task wt
        join wf_ru_execution wre on wre.busi_key = wt.order_no
        join data_customer dc on wt.cust_id = dc.id
        where 1=1
        <if test="reqDTO.orderType != null and reqDTO.orderType != ''">
            and wt.type = #{reqDTO.orderType}
        </if>
        <if test="reqDTO.orderOneClass != null">
            and wt.order_one_class = #{reqDTO.orderOneClass}
        </if>
        <if test="reqDTO.orderTwoClass != null">
            and wt.order_two_class = #{reqDTO.orderTwoClass}
        </if>
        <if test="reqDTO.orderThreeClass != null">
            and wt.order_three_class = #{reqDTO.orderThreeClass}
        </if>
        <if test="reqDTO.custId != null">
            and wt.cust_id = #{reqDTO.custId}
        </if>
        <if test="reqDTO.uuid != null and reqDTO.uuid != ''">
            and dc.uuid = #{reqDTO.uuid}
        </if>
        <if test="reqDTO.notInStatusList != null and reqDTO.notInStatusList.size() > 0">
            and wre.status not in
            <foreach collection="reqDTO.notInStatusList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        order by wt.gmt_create desc
    </select>
    <select id="queryWorkOrderLogListByBusiKeyList"
            resultType="com.welab.crm.operate.vo.workorder.WorkOrderLogVO">

            select
            iacs.group_name ,
            iacs.staff_name as staff_id,
            whl.comment ,
            whl.gmt_create,
            whe.busi_key as orderNo
            from wf_his_execution whe
            left join wf_his_log whl on whe.execution_id = whl.execution_id
            left join in_auth_crm_staff iacs on whl.staff_id = iacs.id
            where whe.busi_key in
            <foreach collection="list" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
            union all
            select
            iacs.group_name ,
            iacs.staff_name as staff_id,
            whl.comment ,
            whl.gmt_create,
            whe.busi_key as orderNo
            from wf_ru_execution whe
            left join wf_ru_log whl on whe.execution_id = whl.execution_id
            left join in_auth_crm_staff iacs on whl.staff_id = iacs.id
            where whe.busi_key in
            <foreach collection="list" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
            order by gmt_create desc
    </select>

    <select id="queryFundName" resultType="com.welab.crm.operate.dto.workorder.FundNamesDTO">
        select
            wt.match_up as matchUp,
            case when wre.status = 'resolved_close' then 1
                 when whe.status = 'resolved_close' then 1
            else 0 end as 'jieJueLiang'
        from wo_task wt
        left join wf_ru_execution wre on wt.order_no = wre.busi_key
        left join wf_his_execution whe on wt.order_no = whe.busi_key
        where wt.`type` = 1522029642925539330
        and wt.complaints_channel is not null
        and wt.fund_name is not null
        <if test="dto.startTime != null and dto.startTime != ''">
            and wt.gmt_create between #{dto.startTime} and #{dto.endTime}
        </if>
    </select>

    <select id="queryComplaint" resultType="com.welab.crm.operate.dto.workorder.RegulatoryComplaintResDTO">
        select
            wt.times,
            sum(case when wt.complaints_channel = '12345' then 1 else 0 end) as 'reXian',
            sum(case when wt.complaints_channel = '国满件' then 1 else 0 end) as 'guoManJian',
            sum(case when wt.complaints_channel = '初件-普通件' then 1 else 0 end) as 'puTongJian',
            sum(case when wt.complaints_channel = '重复件' then 1 else 0 end) as 'chongFuJian',
            sum(case when wt.complaints_channel = '税务局' then 1 else 0 end) as 'shuiWuJu',
            sum(case when wt.complaints_channel = '派出所' then 1 else 0 end) as 'paiChuSuo',
            sum(case when wt.complaints_channel = '南山金融专班' then 1 else 0 end) as 'nanShan',
            sum(case when wt.complaints_channel = '北海金融办' then 1 else 0 end) as 'beiHai',
            sum(case when wt.complaints_channel = '质量监督管理局' then 1 else 0 end) as 'zhiLiang',
            sum(case when wt.complaints_channel = '基金小镇前台' then 1 else 0 end) as 'xiaoZhen',
            sum(case when wt.complaints_channel = '市政一体化' then 1 else 0 end) as 'shiZheng',
            sum(case when wt.complaints_channel = '诉讼/举报' then 1 else 0 end) as 'suSong',
            sum(case when wt.status = 'close' then 1 else 0 end) as 'jieJueLiang'
        from
        (
            select
                substring_index(substring_index(w.complaints_channel, ',', b.help_topic_id + 1), ',', - 1) as complaints_channel,
                date_format(w.gmt_create, '%Y-%m-%d') as times,
                case
                    when wre.status = 'resolved_close' then 'close'
                    when whe.status = 'resolved_close' then 'close'
                    else 'unknown'
                end as 'status'
            from wo_task w
            left join wf_ru_execution wre on w.order_no = wre.busi_key
            left join wf_his_execution whe on w.order_no = whe.busi_key
            inner join help_topic b on b.help_topic_id &lt; (length(w.complaints_channel) - length(replace(w.complaints_channel, ',', '')) + 1)
            where w.`type` = 1458710884927254529
                and w.complaints_channel is not null
                <if test="dto.startTime != null and dto.startTime != ''">
                    and w.gmt_create between #{dto.startTime} and #{dto.endTime}
                </if>
        )wt group by wt.times
    </select>
    <select id="queryAllStatusByUuid" resultType="java.lang.String">
        select
            wre.status
        from
            wf_ru_execution wre
                join wo_task wt on
                wre.busi_key = wt.order_no
                join data_customer dc on
                wt.cust_id = dc.id
        where
            uuid = #{uuid}
        group by
            wre.status
        union all
        select
            wre.status
        from
            wf_his_execution wre
                join wo_task wt on
                wre.busi_key = wt.order_no
                join data_customer dc on
                wt.cust_id = dc.id
        where
            uuid = #{uuid}
    </select>
    <select id="selectRunExecutionByOrderNo"
            resultType="com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO">
        select *
        from wf_ru_execution
        where busi_key = #{orderNo}
    </select>
    <select id="queryOrderUrgeList" resultType="com.welab.crm.operate.vo.monitor.OrderUrgeMonitorVO">
        SELECT iacs.staff_mobile,
               wt.order_no,
               o1.content as orderType,
               o2.content as orderThreeType,
               wrt.urge_times
        from wf_ru_execution wre
                 join wf_ru_task wrt on
            wrt.id = (select wrt2.id
                      from wf_ru_task wrt2
                      where wrt2.execution_id = wre.execution_id
                      order by wrt2.id desc
            limit 1 )
            join wo_task wt
        on
            wt.order_no = wre.busi_key
            join data_customer dc on
            wt.cust_id = dc.id
            left join op_dict_info o1 on
            wt.`type` = o1.id
            left join op_dict_info o2 on
            wt.order_three_class = o2.id
            left join in_auth_crm_staff iacs on
            wrt.staff_id = iacs.id
            left join callback_summary cs on
            cs.id = (
            select
            cs2.id
            from
            callback_summary cs2
            where
            cs2.uuid = dc.uuid
            and cs2.staff_id = wrt.staff_id
            order by
            cs2.id desc
            limit 1 )
            left join wf_ru_log wrl on
            wrl.id = (
            select
            wrl2.id
            from
            wf_ru_log wrl2
            where
            wrl2.execution_id = wre.execution_id
            order by
            wrl2.id desc
            limit 1 )
        where wt.reminder_flag = 1
        and (cs.id is null or cs.gmt_create &lt; wrl.gmt_create)
    </select>
    <select id="queryOrderCallbackList" resultType="com.welab.crm.operate.vo.monitor.OrderCallbackMonitorVO">
        SELECT
            ifnull(GREATEST(cs.gmt_create, wrt.gmt_modify), wrt.gmt_modify) as lastContactTime,
            odi.content as orderType,
            odi2.content as orderThreeType,
            iacs.login_name,
            TIMESTAMPDIFF(HOUR,
                    ifnull(GREATEST(cs.gmt_create, wrt.gmt_modify), wrt.gmt_modify),
                    NOW()) as hoursBetweenContactTimeAndNow
        from
            wf_ru_execution wre
                join wf_ru_task wrt on
                    wrt.id = (
                    select
                        wrt2.id
                    from
                        wf_ru_task wrt2
                    where
                        wrt2.execution_id = wre.execution_id
                    order by
                        wrt2.id desc
            limit 1 )
            join wo_task wt on
            wt.order_no = wre.busi_key
            join data_customer dc on
            wt.cust_id = dc.id
            join op_dict_info odi on
            wt.`type` = odi.id
            join op_dict_info odi2 on
            wt.order_three_class = odi2.id
            join in_auth_crm_staff iacs on
            iacs.id = wrt.staff_id
            left join callback_summary cs on
            cs.id = (
            select
            cs2.id
            from
            callback_summary cs2
            where
            cs2.uuid = dc.uuid
            and cs2.staff_id = wrt.staff_id
            order by
            cs2.id desc
            limit 1 )
        where
            wre.status not like '%end'
          and wrt.group_code = 'tsz'
    </select>
    <select id="selectEscalationOrder" resultType="com.welab.crm.operate.vo.workorder.EscalationOrderVo">

        select
        *,
        (select odi.content from wo_task wt
        join op_dict_info odi on wt.order_three_class = odi.id
        where wt.id = ordinaryId ) as ordinaryOrderThreeType
        from
        (
        select
        p1.cust_id custId,
        max(p1.ordinary_id) ordinaryId,
        max(p1.ordinary_date) ordinaryDate,
        min(p1.escalated_id) escalatedId,
        min(p1.escalated_date) escalatedDate
        from
        (
        select
        o.cust_id as cust_id,
        o.id as ordinary_id,
        o.gmt_create as ordinary_date,
        e.id as escalated_id,
        e.gmt_create as escalated_date
        from
        wo_task o
        join wo_task e on
        o.cust_id = e.cust_id
        and e.type in 
        <foreach collection="sids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and e.gmt_create >= o.gmt_create
        and e.gmt_create &lt;= DATE_ADD(o.gmt_create, interval 30 day)
        where
        o.type in 
        <foreach collection="nids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and o.gmt_create >= #{dto.startTime}
        and o.gmt_create &lt;= #{dto.endTime}
        ) p1
        group by
        p1.cust_id
        )p2
        order by
        p2.ordinaryDate
    </select>
    <select id="selectLogListByExecutionId" resultType="com.welab.crm.operate.vo.workorder.WorkOrderLogVO">
        select
            iacs.group_code ,
            iacs.group_name ,
            iacs.staff_name as staff_id ,
            iacs.login_name ,
            wrl.gmt_create ,
            wrl.comment ,
            (select id_no from in_phone_login_info ipli where ipli.user_tel = iacs.login_name order by id desc limit 1) as cno,
            (case
                 when wrl.operate_type is not null then wrl.operate_type
                 else wdt.show_name
                end) as operate
        from
            wf_ru_log wrl
                left join wf_de_transition wdt
                          on
                              wrl.trans_code = wdt.trans_code
                join in_auth_crm_staff iacs on
                wrl.staff_id = iacs.id
        where
            execution_id = #{executionId}
        union all
        select
            iacs.group_code ,
            iacs.group_name ,
            iacs.staff_name as staff_id ,
            iacs.login_name ,
            wrl.gmt_create ,
            wrl.comment ,
            (select id_no from in_phone_login_info ipli where ipli.user_tel = iacs.login_name order by id desc limit 1) as cno,
            (case
                 when wrl.operate_type is not null then wrl.operate_type
                 else wdt.show_name
                end) as operate
        from
            wf_his_log wrl
                left join wf_de_transition wdt
                          on
                              wrl.trans_code = wdt.trans_code
                join in_auth_crm_staff iacs on
                wrl.staff_id = iacs.id
        where
            execution_id = #{executionId}
        order by gmt_create desc
    </select>


</mapper>
