<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkLoanInviteTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.TmkLoanInviteTemp">
        <id column="id" property="id" />
        <result column="tmk_task_id" property="tmkTaskId" />
        <result column="username" property="username" />
        <result column="cnid" property="cnid" />
        <result column="mobile" property="mobile" />
        <result column="gender" property="gender" />
        <result column="applied_at" property="appliedAt" />
        <result column="applied_amount" property="appliedAmount" />
        <result column="applied_tenor" property="appliedTenor" />
        <result column="amount" property="amount" />
        <result column="tenor" property="tenor" />
        <result column="approved_at" property="approvedAt" />
        <result column="confirmed_at" property="confirmedAt" />
        <result column="product_code" property="productCode" />
        <result column="product_name" property="productName" />
        <result column="application_id" property="applicationId" />
        <result column="apply_origin" property="applyOrigin" />
        <result column="final_class" property="finalClass" />
        <result column="state" property="state" />
        <result column="register_at" property="registerAt" />
        <result column="reg_origin" property="regOrigin" />
        <result column="user_id" property="userId" />
        <result column="uuid" property="uuid" />
        <result column="flag" property="flag" />
        <result column="type" property="type" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="staff_id" property="staffId" />
        <result column="push_flag" property="pushFlag" />
        <result column="rule_id" property="ruleId" />
        <result column="blocked" property="blocked" />
        <result column="last_sign_in_at" property="lastSignInAt" />
        <result column="update_flag" property="updateFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tmk_task_id, username, cnid, mobile, gender, applied_at, applied_amount, applied_tenor, amount, tenor, approved_at, confirmed_at, product_code, product_name, application_id, apply_origin, final_class, state, register_at, reg_origin, user_id, uuid, flag, type, gmt_create, gmt_modify, staff_id, push_flag, rule_id, blocked, last_sign_in_at, update_flag
    </sql>

</mapper>
