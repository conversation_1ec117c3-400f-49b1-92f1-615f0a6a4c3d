<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.EliteWorkorderLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.EliteWorkorderLogs">
        <id column="id" property="id" />
        <result column="objective_guid" property="objectiveGuid" />
        <result column="submit_time" property="submitTime" />
        <result column="submit_group" property="submitGroup" />
        <result column="submit_staff" property="submitStaff" />
        <result column="deal_time" property="dealTime" />
        <result column="from_step" property="fromStep" />
        <result column="to_step" property="toStep" />
        <result column="deal_group" property="dealGroup" />
        <result column="deal_staff" property="dealStaff" />
        <result column="operate_type" property="operateType" />
        <result column="order_status" property="orderStatus" />
        <result column="comments" property="comments" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, objective_guid, submit_time, submit_group, submit_staff, deal_time, from_step, to_step, deal_group, deal_staff, operate_type, order_status, comments
    </sql>

</mapper>
