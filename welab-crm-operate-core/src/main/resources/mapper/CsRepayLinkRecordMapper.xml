<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsRepayLinkRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsRepayLinkRecord">
        <id column="id" property="id" />
        <result column="customer_name" property="customerName" />
        <result column="uuid" property="uuid" />
        <result column="application_id" property="applicationId" />
        <result column="send_mobile" property="sendMobile" />
        <result column="repay_mode" property="repayMode" />
        <result column="repay_amount" property="repayAmount" />
        <result column="send_time" property="sendTime" />
        <result column="send_staff_id" property="sendStaffId" />
        <result column="send_staff_name" property="sendStaffName" />
        <result column="send_staff_group" property="sendStaffGroup" />
        <result column="customer_id" property="customerId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_name, uuid, application_id, send_mobile, repay_mode, repay_amount, send_time, send_staff_id, send_staff_name, send_staff_group, customer_id, gmt_create, gmt_modify
    </sql>
    <select id="queryRecord" resultType="com.welab.crm.operate.vo.repaylink.H5RepayLinkVO">
        select
        send_time,
        send_staff_name as staffName,
        send_staff_group as groupName,
        customer_name,
        uuid,
        send_mobile as mobile,
        repay_mode,
        repay_amount,
        application_id,
        user_id,
        case 
            when consultation_status = 1 then '已逾期'
            when consultation_status = 2 then '待还款' end
            as consultationStatus,
        payment_channel,
        repay_time,
        repay_result,
        case when repay_method='helpPayment' then '线上还款'
             when repay_method='offlinePayment' then '线下还款'
        else repay_method end as repayMethod,
        partner_name as partnerName,
        partner_code as partnerCode
        from  cs_repay_link_record
        where 1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            and send_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and send_time &lt;= #{dto.endTime} + interval 1 day
        </if>
        <if test="dto.mobile != null and dto.mobile != ''">
            and send_mobile = #{dto.mobile}
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and uuid = #{dto.uuid}
        </if>
        <if test="dto.appNo != null and dto.appNo != ''">
            and application_id = #{dto.appNo}
        </if>
        <if test="dto.repayMethod != null and dto.repayMethod != ''">
            and repay_method = #{dto.repayMethod}
        </if>
        <if test="dto.partnerName != null and dto.partnerName != ''">
            and partner_name like concat('%', #{dto.partnerName}, '%')
        </if>
        <if test="dto.groupName != null and dto.groupName != ''">
            and send_staff_group = #{dto.groupName}
        </if>
        order by id desc
    </select>

    <select id="queryRecordByGroups" resultType="java.lang.String">
        select send_staff_group as groupName from  cs_repay_link_record group by send_staff_group
    </select>

    <select id="queryRepaySummary" resultType="com.welab.crm.operate.vo.repaylink.H5RepaySummaryReportVO">
        select
        <if test="period != null and period != ''">
            ${period} as time,
        </if>
        <if test="period = ''">
            concat(#{startTime},'-',#{endTime}) as time,
        </if>
        count(distinct id) as orderCount,
        count(distinct uuid) as customerCount,
        count(repay_result = 'succeed' or null) as successCount,
        count(repay_result = 'failed' or null) as failCount,
        sum(if(repay_result = 'succeed',repay_amount,0)) as successAmount,
        sum(if(repay_result = 'failed',repay_amount,0) ) as failAmount,
        case when repay_method='helpPayment' then '线上还款'
             when repay_method='offlinePayment' then '线下还款'
             else repay_method end as repayMethod
        from cs_repay_link_record t0
        where 1=1
        <if test="repayMethod != null and repayMethod != ''">
            and t0.repay_method = #{repayMethod}
        </if>
        and t0.gmt_create &gt;= #{startTime}
        and t0.gmt_create &lt;= #{endTime}
        group by time,repay_method
    </select>


</mapper>
