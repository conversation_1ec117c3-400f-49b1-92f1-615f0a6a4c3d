<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsLoginRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsLoginRecord">
        <id column="id" property="id" />
        <result column="staff_id" property="staffId" />
        <result column="login_type" property="loginType" />
        <result column="result" property="result" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, login_type, result, gmt_create, gmt_modify
    </sql>
    
    <sql id="queryDetail">
        select
        clr.id as id,
        iacs.staff_name ,
        ipli.id_no as cno,
        clr.login_type ,
        date_format(clr.gmt_create,'%Y-%m-%d %H:%i:%s') as loginTime,
        clr.`result` as loginResult,
        iacs.group_name
        from
        cs_login_record clr
        left join in_auth_crm_staff iacs on
        clr.staff_id = iacs.id
        left join in_phone_login_info ipli on
        iacs.login_name = ipli.user_tel

        where
        clr.gmt_create &gt;= #{dto.startTime}
        and clr.gmt_create &lt;= #{dto.endTime}
        <if test="dto.groupCodes != null and dto.groupCodes.size() > 0">
            and iacs.group_code in
            <foreach collection="dto.groupCodes" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffNames != null and dto.staffNames.size() > 0">
            and iacs.id in
            <foreach collection="dto.staffNames" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by clr.gmt_create desc
    </sql>
    <select id="queryLoginReportDetailPage"
            resultType="com.welab.crm.operate.vo.loginReport.LoginReportDetailVO">
        <include refid="queryDetail"/>
    </select>
    <select id="queryLoginReportDetailList"
            resultType="com.welab.crm.operate.vo.loginReport.LoginReportDetailVO">
        <include refid="queryDetail"/>
    </select>

</mapper>
