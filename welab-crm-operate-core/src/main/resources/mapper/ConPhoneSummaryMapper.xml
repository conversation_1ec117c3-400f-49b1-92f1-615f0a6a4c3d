<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConPhoneSummaryMapper">


    <select id="selectSummary" resultType="com.welab.crm.operate.vo.phone.PhoneSummaryVO">
        select
        t1.customer_id,
        t2.task_id,
        t1.call_summary,
        t1.call_summary_code,
        t1.save_summary_time,
        t1.call_comment,
        t1.map_name,
        t1.over_due_label,
        t2.cdr_call_type as callType,
        t2.cdr_start_time as callTime,
        t2.cdr_customer_number as phoneNumber,
        t3.staff_name,
        t3.group_name,
        t1.cdr_main_unique_id,
        t2.gmt_create,
        (case when t2.cdr_call_type = '4' then t2.cdr_cno
         else t2.cdr_callee_cno end) as cdr_cno
        from con_phone_summary t1
        left join con_phone_call_info t2
        on t1.cdr_main_unique_id = t2.cdr_main_unique_id
        left join in_auth_crm_staff t3
        on t1.staff_id = t3.id
        left join data_customer t4
        on t1.customer_id = t4.id
        where 1=1
        and t1.staff_id = t2.staff_id
        <if test="filter.id != null">
            and t1.id = #{filter.id}
        </if>
        <if test="filter.customerIdList != null and filter.customerIdList.size() > 0">
            and t1.customer_id in
            <foreach collection="filter.customerIdList" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="filter.taskId != null and filter.taskId != ''">
            and t2.task_id = #{filter.taskId}
        </if>
        <if test="filter.callSummary != null and filter.callSummary != ''">
            and t1.call_summary = #{filter.callSummary}
        </if>
        <if test="filter.callSummaryCode != null and filter.callSummaryCode != ''">
            and t1.call_summary_code = #{filter.callSummaryCode}
        </if>
        <if test="filter.callComment != null and filter.callComment != ''">
            and t1.callComment = #{filter.callComment}
        </if>
        <if test="filter.mapName != null and filter.mapName != ''">
            and t1.map_name = #{filter.mapName}
        </if>
        <if test="filter.staffId != null and filter.staffId != ''">
            and t1.staff_id = #{filter.staffId}
        </if>

        union all
        select
        t1.customer_id,
        t1.tmk_task_id as task_id,
        t5.summary_content as call_summary,
        t1.contact_code as call_summary_code,
        t1.gmt_create as save_summary_time,
        t1.comment as call_comment,
        '' as map_name,
        '' as over_due_label,
        t2.cdr_call_type as callType,
        t2.cdr_start_time as callTime,
        t2.cdr_customer_number as phoneNumber,
        t3.staff_name,
        t3.group_name,
        t1.cdr_main_unique_id,
        t2.gmt_create,
        t2.cdr_cno
        from tmk_summary t1
        left join con_phone_call_info t2
        on t1.cdr_main_unique_id = t2.cdr_main_unique_id
        left join in_auth_crm_staff t3
        on t1.staff_id = t3.id
        left join data_customer t4
        on t1.customer_id = t4.id
        left join tmk_summary_dict t5
        on t1.contact_code = t5.summary_code
        where 1=1
        <if test="filter.customerIdList != null and filter.customerIdList.size() > 0">
            and t1.customer_id in
            <foreach collection="filter.customerIdList" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="filter.staffId != null and filter.staffId != ''">
            and t1.staff_id = #{filter.staffId}
        </if>

        union all
        select
        '',
        '',
        e.summary_content as call_summary,
        '',
        e.save_summary_time,
        e.summary_remark as call_comment,
        '',
        '',
        e.call_type as callType,
        e.call_time as callTime,
        '' as phoneNumber,
        e.staff_name,
        e.staff_group as group_name,
        '',
        null,
        ''
        from elite_contact_history e
        where e.phone_number = #{filter.mobile}
        order by save_summary_time desc

    </select>


    <select id="selectSummaryByCdrMainUniqueId" resultType="com.welab.crm.operate.vo.phone.PhoneSummaryVO">
        select
        t1.customer_id,
        t2.task_id,
        t1.call_summary,
        t1.call_summary_code,
        t1.save_summary_time,
        t1.call_comment,
        t1.map_name,
        t1.over_due_label,
        t2.cdr_call_type as callType,
        t2.cdr_start_time as callTime,
        t2.cdr_customer_number as phoneNumber,
        t3.staff_name,
        t3.group_name,
        t1.cdr_main_unique_id,
        t2.gmt_create,
        (case when t2.cdr_call_type = '4' then t2.cdr_cno
        else t2.cdr_callee_cno end) as cdr_cno
        from con_phone_summary t1
        left join con_phone_call_info t2 on t1.cdr_main_unique_id = t2.cdr_main_unique_id
        left join in_auth_crm_staff t3 on t1.staff_id = t3.id
        left join data_customer t4 on t1.customer_id = t4.id
        where 1=1
        and t1.staff_id = t2.staff_id
        and t1.cdr_main_unique_id = #{cdrMainUniqueId}
    </select>


    <select id="queryReportSummaryDetail" resultType="com.welab.crm.operate.vo.phone.ReportPhoneResultVO">
        SELECT
        t2.cdr_hotline,
        t2.cdr_start_time,
        t2.cdr_end_time,
        t2.cdr_end_bridge_time,
        t3.uuid,
        t3.user_id,
        t3.customer_name,
        t2.cdr_cno AS staff_id,
        t4.staff_name,
        t1.call_summary,
        t1.call_comment,
        t5.summary as aiSummary,
        t1.over_due_label,
        case
        when t1.consultation_status = 1 then '已逾期'
        when t1.consultation_status = 2 then '待还款' end
        as consultationStatus
        FROM
        con_phone_summary t1
        JOIN con_phone_call_info t2 force index (con_phone_call_info_cdr_start_time_IDX) ON t1.cdr_main_unique_id = t2.cdr_main_unique_id
        JOIN data_customer t3 ON t1.customer_id = t3.id
        JOIN in_auth_crm_staff t4 ON t1.staff_id = t4.id
        JOIN call_info_ai_summary t5 ON t1.cdr_main_unique_id =t5.cdr_main_unique_id
        WHERE 1 = 1
        <if test="filter.startTime != null and filter.startTime != ''">
            AND t2.cdr_start_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND t2.cdr_start_time &lt;= #{filter.endTime}
        </if>
        <if test="filter.cdrHotlines != null  and filter.cdrHotlines.size()>0">
            AND t2.cdr_hotline IN
            <foreach item="item" index="index" collection="filter.cdrHotlines" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="filter.summaryThree != null and filter.summaryThree != ''">
                AND t1.call_summary = concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'#',#{filter.summaryThree})
            </when>
            <when test="filter.summaryTwo != null and filter.summaryTwo != ''">
                AND t1.call_summary like concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'%')
            </when>
            <when test="filter.summaryOne != null and filter.summaryOne != ''">
                AND t1.call_summary like concat(#{filter.summaryOne},'%')
            </when>
        </choose>
    </select>

    <select id="queryReportSummary" resultType="com.welab.crm.operate.vo.phone.ReportPhoneResultSummaryVO">
        SELECT
        call_summary_code,
        call_summary,
        count(t1.id) as num
        FROM
        con_phone_summary t1
        JOIN con_phone_call_info t2 force index (con_phone_call_info_cdr_start_time_IDX) ON t1.cdr_main_unique_id = t2.cdr_main_unique_id
        WHERE 1 = 1
        <if test="filter.startTime != null and filter.startTime != ''">
            AND t2.cdr_start_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND t2.cdr_start_time &lt;= #{filter.endTime}
        </if>
        <choose>
            <when test="filter.summaryThree != null and filter.summaryThree != ''">
                AND t1.call_summary = concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'#',#{filter.summaryThree})
            </when>
            <when test="filter.summaryTwo != null and filter.summaryTwo != ''">
                AND t1.call_summary like concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'%')
            </when>
            <when test="filter.summaryOne != null and filter.summaryOne != ''">
                AND t1.call_summary like concat(#{filter.summaryOne},'%')
            </when>
        </choose>
        <if test="filter.cdrHotlines != null  and filter.cdrHotlines.size()>0">
            AND t2.cdr_hotline IN
            <foreach item="item" index="index" collection="filter.cdrHotlines" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY call_summary_code, call_summary
    </select>

    <select id="queryReportSummaryCount" resultType="java.math.BigDecimal">
        SELECT
        SUM(t.num) AS summaryCount
        FROM
        (
            SELECT
                call_summary_code,
                call_summary,
                count(t1.id) AS num
            FROM con_phone_summary t1
            JOIN con_phone_call_info t2 force index(con_phone_call_info_cdr_start_time_IDX) ON t1.cdr_main_unique_id = t2.cdr_main_unique_id
            WHERE 1 = 1
            <if test="filter.startTime != null and filter.startTime != ''">
                AND t2.cdr_start_time &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null and filter.endTime != ''">
                AND t2.cdr_start_time &lt;= #{filter.endTime}
            </if>
            <choose>
                <when test="filter.summaryThree != null and filter.summaryThree != ''">
                    AND t1.call_summary = concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'#',#{filter.summaryThree})
                </when>
                <when test="filter.summaryTwo != null and filter.summaryTwo != ''">
                    AND t1.call_summary like concat(#{filter.summaryOne},'#',#{filter.summaryTwo},'%')
                </when>
                <when test="filter.summaryOne != null and filter.summaryOne != ''">
                    AND t1.call_summary like concat(#{filter.summaryOne},'%')
                </when>
            </choose>
            <if test="filter.cdrHotlines != null  and filter.cdrHotlines.size()>0">
                AND t2.cdr_hotline IN
                <foreach item="item" index="index" collection="filter.cdrHotlines" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY call_summary_code, call_summary
        ) t
    </select>
    <select id="selectCallInSummaryByCustomerIdAndStaffIdAndTime"
        resultType="com.welab.crm.operate.vo.phone.PhoneSummaryVO">
            select t1.cdr_start_time as callTime
            from
            con_phone_summary t0
            join con_phone_call_info t1 on t0.cdr_main_unique_id = t1.cdr_main_unique_id
            where t0.customer_id = #{cid}
            and t0.staff_id = #{sid}
            and t1.cdr_start_time &lt; #{callTime}
            order by t1.cdr_start_time desc
    </select>
    <select id="queryTop10Question" resultType="com.welab.crm.operate.vo.screen.CountVO">
        SELECT * from (
        SELECT
        odi.content as name,
        COUNT(1) as cnt
        from con_phone_summary cps
        join con_phone_call_info cpci force index(con_phone_call_info_cdr_start_time_IDX) on cps.cdr_main_unique_id = cpci.cdr_main_unique_id
        join op_dict_info odi on cps.call_summary_code = odi.id
        where cpci.cdr_start_time >= current_date()
        group by odi.id ) t
        order by t.cnt desc
        limit 10
    </select>


</mapper>
