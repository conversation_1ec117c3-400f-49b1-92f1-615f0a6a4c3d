<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ExternalComplaintOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ExternalComplaintOrder">
        <id column="id" property="id" />
        <result column="login_name" property="loginName" />
        <result column="channel_order_no" property="channelOrderNo" />
        <result column="debt_order_no" property="debtOrderNo" />
        <result column="id_no" property="idNo" />
        <result column="name" property="name" />
        <result column="mobile" property="mobile" />
        <result column="mobile_bak" property="mobileBak" />
        <result column="email" property="email" />
        <result column="order_no" property="orderNo" />
        <result column="urgent_flag" property="urgentFlag" />
        <result column="callback_flag" property="callbackFlag" />
        <result column="quick_order_id" property="quickOrderId" />
        <result column="quick_order" property="quickOrder" />
        <result column="complaints_channel" property="complaintsChannel" />
        <result column="fund_name" property="fundName" />
        <result column="type" property="type" />
        <result column="order_one_class" property="orderOneClass" />
        <result column="order_two_class" property="orderTwoClass" />
        <result column="order_three_class" property="orderThreeClass" />
        <result column="order_case" property="orderCase" />
        <result column="description" property="description" />
        <result column="user_type" property="userType" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, login_name, channel_order_no, debt_order_no, id_no, name, mobile, mobile_bak, email, order_no, urgent_flag, callback_flag, quick_order_id, quick_order, complaints_channel, fund_name, type, order_one_class, order_two_class, order_three_class, order_case, description, user_type, gmt_create, gmt_modify
    </sql>
    
    <sql id="querySql">
        select
        eco.id ,
        eco.order_no ,
        COALESCE(eco.channel_order_no,eco.debt_order_no) as adaptiveOrderNo,
        eco.quick_order,
        eco.name ,
        COALESCE(wre.status,whe.status) as orderStatus ,
        eco.gmt_create as createTime,
        whe.gmt_modify as completeTime,
        cs.resolve_content ,
        ipli.id_no as cno
        from external_complaint_order eco
        left join wo_task wt on eco.order_no = wt.order_no
        left join wf_ru_execution wre on eco.order_no = wre.busi_key
        left join wf_his_execution whe on eco.order_no = whe.busi_key
        left join data_customer dc on wt.cust_id = dc.id
        left join callback_summary cs on cs.id = (
        select cs2.id from callback_summary cs2 where cs2.uuid = dc.uuid order by id desc limit 1)
        left join in_auth_crm_staff iacs on cs.staff_id = iacs.id
        left join in_phone_login_info ipli on iacs.login_name = ipli.user_tel
        where eco.login_name = #{dto.loginName}
        <if test="dto.startTime != null and dto.startTime != ''">
            and eco.gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and eco.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.mobile != null and dto.mobile != ''">
            and eco.mobile = #{dto.mobile}
        </if>
        <if test="dto.idNo != null and dto.idNo != ''">
            and eco.id_no = #{dto.idNo}
        </if>
        <if test="dto.channelOrderNo != null and dto.channelOrderNo != ''">
            and eco.channel_order_no like concat('%',#{dto.channelOrderNo},'%')
        </if>
        <if test="dto.debtOrderNo != null and dto.debtOrderNo != ''">
            and eco.debt_order_no like concat('%',#{dto.debtOrderNo},'%')
        </if>
        <if test="dto.ids != null and dto.ids.size() > 0">
            and eco.id in
            <foreach collection="dto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by eco.gmt_create desc
    </sql>
    <select id="selectOrderHistoryPage"
            resultType="com.welab.crm.operate.vo.workorder.ExternalComplaintListVO">
        <include refid="querySql"/>
    </select>
    <select id="selectOrderHistoryList" resultType="com.welab.crm.operate.vo.workorder.ExternalComplaintListVO">
        <include refid="querySql"/>
    </select>
    <select id="selectOrderContactList" resultType="com.welab.crm.operate.vo.workorder.OrderContactVO">
        select
            cs.id,
            cpci.cdr_start_time as callTime,
            cs.gmt_create as saveSummaryTime,
            concat(cs.business_type, '#', contact_result) as callSummary,
            cs.reason_type,
            cpci.cdr_customer_number as mobile
        from
            external_complaint_order eco
                left join wo_task wt on
                eco.order_no = wt.order_no
                left join data_customer dc on
                wt.cust_id = dc.id
                join callback_summary cs on
                cs.uuid = dc.uuid
                    and cs.order_no like concat('%', eco.order_no, '%')
                left join con_phone_call_info cpci on
                cpci.cdr_main_unique_id = cs.cdr_main_unique_id
        where eco.login_name = #{loginName}
        and eco.id = #{id}
    </select>
    <select id="selectExecutionById" resultType="com.welab.crm.operate.vo.workorder.ExecutionVO">
        select
            COALESCE(wre.execution_id, whe.execution_id) as executionId,
            eco.order_no,
            wt.id as woTaskId,
            COALESCE(wre.status, whe.status) as orderStatus
        from
            external_complaint_order eco
                left join wo_task wt on
                eco.order_no = wt.order_no
                left join wf_ru_execution wre on
                eco.order_no = wre.busi_key
                left join wf_his_execution whe on
                eco.order_no = whe.busi_key
        where eco.id = #{id}
        and eco.login_name = #{loginName}
    </select>


</mapper>
