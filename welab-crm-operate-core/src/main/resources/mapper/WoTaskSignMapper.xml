<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoTaskSignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.WoTaskSign">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="staff_id" property="staffId" />
        <result column="sign" property="sign" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, staff_id, sign, gmt_create, gmt_modify
    </sql>

	<select id="queryWorkOrderSign" resultType="com.welab.crm.operate.domain.WoTaskSign">
        SELECT
            *
        FROM wo_task_sign t
        WHERE t.order_no = #{reqDTO.orderNo}
            and t.staff_id = #{reqDTO.staffId}
            limit 1
    </select>
</mapper>
