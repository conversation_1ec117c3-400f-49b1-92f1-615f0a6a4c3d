<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsCalloutInterceptionRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsCalloutInterceptionRule">
        <id column="id" property="id" />
        <result column="order_status" property="orderStatus" />
        <result column="label_name" property="labelName" />
        <result column="group_codes" property="groupCodes" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="remark" property="remark" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_status, label_name, group_codes, is_deleted, create_staff_id, remark, gmt_create, gmt_modify
    </sql>

</mapper>
