<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoTaskTempMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.WoTaskTemp">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="wo_id" property="woId" />
        <result column="handler_group_id" property="handlerGroupId" />
        <result column="assign_date" property="assignDate" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="desc" property="desc" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="create_staff_id" property="createStaffId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, wo_id, handler_group_id, assign_date, start_time, end_time, desc, gmt_create, create_staff_id
    </sql>

</mapper>
