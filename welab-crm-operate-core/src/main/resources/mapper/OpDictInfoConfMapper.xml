<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpDictInfoConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpDictInfoConf">
        <id column="id" property="id" />
        <result column="wo_type_id" property="woTypeId" />
        <result column="wo_type_detail" property="woTypeDetail" />
        <result column="wo_type_fir_id" property="woTypeFirId" />
        <result column="wo_type_fir_detail" property="woTypeFirDetail" />
        <result column="wo_type_sec_id" property="woTypeSecId" />
        <result column="wo_type_sec_detail" property="woTypeSecDetail" />
        <result column="wo_type_thir_id" property="woTypeThirId" />
        <result column="wo_type_thir_detail" property="woTypeThirDetail" />
        <result column="wo_type_child_id" property="woTypeChildId" />
        <result column="wo_type_child_detail" property="woTypeChildDetail" />
        <result column="description" property="description" />
        <result column="is_status" property="isStatus" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wo_type_id, wo_type_detail, wo_type_fir_id, wo_type_fir_detail, wo_type_sec_id, wo_type_sec_detail, wo_type_thir_id, wo_type_thir_detail, wo_type_child_id, wo_type_child_detail, description, is_status, gmt_create, gmt_modify
    </sql>

    <select id="selectByWoTypeId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from op_dict_info_conf
        where wo_type_id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by wo_type_id
    </select>

</mapper>
