<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpStaffExtraStatusLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpStaffExtraStatusLog">
        <id column="id" property="id" />
        <result column="staff_id" property="staffId" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, status, start_time, end_time, duration, gmt_create, gmt_modify
    </sql>
    <select id="selectSubmitOrderTimeGroupByDayAndStaff"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        <if test="dto.period=='day'">
            date_format(osesl.gmt_create,'%Y-%m-%d') as date,
        </if>
        <if test="dto.period=='range'">
            concat(left(#{dto.startTime},10),'~',left(#{dto.endTime},10)) as date,
        </if>
            iacs.id as staff_id ,
            sum(osesl.duration) as submitOrderDuration
        from op_staff_extra_status_log osesl force index(op_staff_extra_status_log_gmt_create_IDX)
            left join in_auth_crm_staff iacs on osesl.staff_id = iacs.login_name
        where 
            osesl.gmt_create between #{dto.startTime} and #{dto.endTime}
            <if test="dto.groupCode != null and dto.groupCode != ''">
                and iacs.group_code = #{dto.groupCode}
            </if>
            <if test="dto.staffId != null">
                and iacs.id = #{dto.staffId}
            </if>
        group by  1, 2
    </select>
    <select id="selectSubmitOrderTimeGroupByStaff"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        iacs.id as staff_id,
        sum(osesl.duration) as submitOrderDuration,
        ipli.id_no as cno
        from op_staff_extra_status_log osesl force index(op_staff_extra_status_log_gmt_create_IDX)
        left join in_auth_crm_staff iacs on osesl.staff_id = iacs.login_name
        left join in_phone_login_info ipli on
        osesl.staff_id = ipli.user_tel
        where
        osesl.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.groupCode != null and dto.groupCode != ''">
            and iacs.group_code = #{dto.groupCode}
        </if>
        <if test="dto.staffId != null">
            and iacs.id = #{dto.staffId}
        </if>
        group by  1
    </select>
    <select id="selectSubmitOrderTimeGroupByStaffs"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        iacs.id as staff_id,
        sum(osesl.duration) as submitOrderDuration,
        ipli.id_no as cno
        from op_staff_extra_status_log osesl
        left join in_auth_crm_staff iacs on osesl.staff_id = iacs.login_name
        left join in_phone_login_info ipli on
        osesl.staff_id = ipli.user_tel
        where
        osesl.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.groupListCodes != null and dto.groupListCodes.size() > 0">
            and iacs.group_code in
            <foreach collection="dto.groupListCodes" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffId != null">
            and iacs.id = #{dto.staffId}
        </if>
        group by  1
    </select>
    <select id="selectSubmitOrderTime"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        ifnull(sum(osesl.duration) ,0) as submitOrderDuration
        from op_staff_extra_status_log osesl
        left join in_auth_crm_staff iacs on osesl.staff_id = iacs.login_name
        where
        osesl.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.groupCode != null and dto.groupCode != ''">
            and iacs.group_code = #{dto.groupCode}
        </if>
        <if test="dto.staffId != null">
            and iacs.id = #{dto.staffId}
        </if>
    </select>

</mapper>
