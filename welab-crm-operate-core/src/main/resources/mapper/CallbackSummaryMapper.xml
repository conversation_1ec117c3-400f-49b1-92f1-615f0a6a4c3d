<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CallbackSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CallbackSummary">
        <id column="id" property="id" />
        <result column="uuid" property="uuid" />
        <result column="cdr_main_unique_id" property="cdrMainUniqueId" />
        <result column="business_type" property="businessType" />
        <result column="contact_result" property="contactResult" />
        <result column="reason_type" property="reasonType" />
        <result column="callback_comment" property="callbackComment" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uuid, cdr_main_unique_id, business_type, contact_result, reason_type, callback_comment, gmt_create, gmt_modify
    </sql>
    <select id="querySummaryByUuid" resultType="com.welab.crm.operate.vo.phone.PhoneSummaryVO">
        select
       		cpci.cdr_customer_number as phoneNumber,
       		cpci.cdr_start_time as callTime,
       		cs.gmt_create as saveSummaryTime,
       		'外呼' as callType,
       		iacs.staff_name as staffName,
       		ipli.id_no as cdrCno,
       		iacs.group_name as groupName,
       		concat(cs.business_type,'#',cs.contact_result) as taskId,
       		cs.reason_type as callSummary,
       		cs.callback_comment as callComment,
       		cs.cdr_main_unique_id,
       		cs.resolve_content
       from callback_summary cs
       left join con_phone_call_info cpci on cs.cdr_main_unique_id = cpci.cdr_main_unique_id
       left join in_auth_crm_staff iacs on cs.staff_id = iacs.id
       left join in_phone_login_info ipli on iacs.login_name = ipli.user_tel
       where cs.uuid = #{uuid}

    </select>
    <select id="queryReportPage" resultType="com.welab.crm.operate.vo.callbackSummary.CallbackSummaryReportVO">
        select
        cs.business_type ,
        dc.customer_name,
        dc.uuid,
        dc.user_id,
        iacs.staff_name ,
        cpci.cdr_start_time as callTime ,
        cs.contact_result as callbackResult,
        cs.reason_type as contactResult,
        cs.callback_comment as contactComment
        from callback_summary cs
        left join (select * from data_customer d1 where d1.id in (select max(id) from data_customer d2 group by d2.uuid)) dc on cs.uuid = dc.uuid
        left join in_auth_crm_staff iacs on cs.staff_id = iacs.id
        left join con_phone_call_info cpci on cs.cdr_main_unique_id = cpci.cdr_main_unique_id
        where cs.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.businessTypeList != null and dto.businessTypeList.size() > 0">
            and cs.business_type in
            <foreach collection="dto.businessTypeList" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        order by cpci.cdr_start_time desc
    </select>
    <select id="querySummaryLogByOrderNo" resultType="com.welab.crm.operate.vo.workorder.WorkOrderLogVO">
        select
            iacs.group_code ,
            iacs.group_name ,
            iacs.staff_name as staffId ,
            cs.gmt_create ,
            cs.resolve_content,
            (select id_no from in_phone_login_info ipli where ipli.user_tel = iacs.login_name order by id desc limit 1) as cno
        from wo_task wt
                 join data_customer dc on wt.cust_id = dc.id
                 join callback_summary cs on dc.uuid = cs.uuid and cs.order_no like CONCAT('%',wt.order_no,'%')
                 join in_auth_crm_staff iacs on cs.staff_id = iacs.id
        where wt.order_no = #{orderNo}
        group by cs.id
    </select>

</mapper>
