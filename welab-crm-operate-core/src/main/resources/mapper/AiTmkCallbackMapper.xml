<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.AiTmkCallbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.AiTmkCallback">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="tmk_task_id" property="tmkTaskId" />
        <result column="channel_name" property="channelName" />
        <result column="phone_no" property="phoneNo" />
        <result column="call_num" property="callNum" />
        <result column="call_time" property="callTime" />
        <result column="answer_time" property="answerTime" />
        <result column="end_time" property="endTime" />
        <result column="bill_sec" property="billSec" />
        <result column="phone_status" property="phoneStatus" />
        <result column="call_result" property="callResult" />
        <result column="willing_col" property="willingCol" />
        <result column="sys_call_status" property="sysCallStatus" />
        <result column="call_back_date" property="callBackDate" />
        <result column="record_url" property="recordUrl" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, tmk_task_id, channel_name, phone_no, call_num, call_time, answer_time, end_time, bill_sec, phone_status, call_result, willing_col, sys_call_status, call_back_date, record_url, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>
    
    <select id="queryCallBack" resultType="com.welab.crm.operate.vo.ai.AiTmkCallBackVO">
        SELECT
        t2.mobile,
        t3.call_time,
        t3.end_time,
        t3.phone_status,
        CASE t2.type
        WHEN 'loan' THEN
        '进件模式'
        WHEN 'credit' THEN
        '额度模式'
        END AS type
        FROM
        ai_tmk_callback t3
        INNER JOIN tmk_loan_invite t2 ON t2.tmk_task_id = t3.tmk_task_id
        WHERE t2.user_id = #{id}
    </select>

    <select id="queryUuidCallBack" resultType="com.welab.crm.operate.vo.ai.AiTmkCallBackVO">
        SELECT t2.mobile,
               t3.call_time,
               t3.end_time,
               t3.phone_status
        FROM ai_tmk_uuid_callback t3
                 JOIN tmk_uuid t2 ON t2.tmk_task_id = t3.tmk_task_id
                WHERE t2.user_id = #{id}
    </select>

    <select id="queryAiTransformData" resultType="com.welab.crm.operate.vo.telemarketing.AiTransformReportVO">
        select
        ifnull(count(answer_time),0) as connectedNum,
        sum(t1.bill_sec) as callTime,
        sum((case when t3.state = 'confirmed' and t1.call_time &lt; t3.confirmed_at and TO_DAYS(t1.call_time) = TO_DAYS(t3.confirmed_at) then 1 else 0 end)) as transformNum,
        sum((case when t3.state = 'confirmed' and t1.call_time &lt; t3.confirmed_at and TO_DAYS(t1.call_time) = TO_DAYS(t3.confirmed_at) then t3.confirmed_amount else 0 end)) as transformAmount
        from ai_tmk_callback t1
        join ai_tmk_config t2 on t1.config_id = t2.id
        join tmk_loan_invite t3 on t1.tmk_task_id = t3.tmk_task_id
        where t1.import_time between #{starTime} and #{endTime}
        and t2.id = #{ruleId}
    </select>
</mapper>
