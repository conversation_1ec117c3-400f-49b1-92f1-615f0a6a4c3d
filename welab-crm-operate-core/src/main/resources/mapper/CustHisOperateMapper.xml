<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CustHisOperateMapper">

    <select id="selectHis" resultType="com.welab.crm.operate.vo.operate.CustHisOperateVO">
        select
        t1.user_id,
        t1.customer_id,
        t2.staff_name,
        t1.group_code,
        t1.operate_type,
        t1.operate_time,
        t1.loan_id,
        t1.old_mobile,
        t1.comment
        from cust_his_operate t1
        join in_auth_crm_staff t2
        on t1.staff_id = t2.login_name
        where 1=1
        <if test="filter.userId !=null">
            and (
                t1.user_id = #{filter.userId}
                <if test="filter.customerId !=null">
                    or t1.customer_id = #{filter.customerId}
                </if>
            )
        </if>

        <if test="filter.applicationId !=null and filter.applicationId != ''">
            and t1.loan_id = #{filter.applicationId}
        </if>
        <if test="filter.type !=null and filter.type != ''">
            and t1.type = #{filter.type}
        </if>
        <if test="filter.staffId !=null and filter.staffId != ''">
            and t1.staff_id = #{filter.staffId}
        </if>
        order by t1.gmt_create desc
    </select>
</mapper>
