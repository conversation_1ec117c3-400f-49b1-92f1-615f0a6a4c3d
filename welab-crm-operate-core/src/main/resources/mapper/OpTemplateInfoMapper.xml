<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpTemplateInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpTemplateInfo">
        <id column="id" property="id" />
        <result column="case_id" property="caseId" />
        <result column="staff_id" property="staffId" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="keyword" property="keyword" />
        <result column="type" property="type" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, case_id, staff_id, title, content, keyword, type, gmt_create, gmt_modify
    </sql>

	<select id="queryTemplateInfoByPage" resultType="com.welab.crm.operate.vo.workorder.TemplateInfoVO">
        SELECT
            *
        FROM op_template_info t0
        WHERE 1 = 1 
        <if test="reqDTO.caseId != null and reqDTO.caseId != ''">
            and t0.case_id = #{reqDTO.caseId}
        </if>
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            and t0.staff_id = #{reqDTO.staffId}
        </if>
        <if test="reqDTO.title != null and reqDTO.title != ''">
            and t0.title like concat('%',#{reqDTO.title},'%') 
        </if>
        <if test="reqDTO.keyword != null and reqDTO.keyword != ''">
            and t0.keyword like concat('%',#{reqDTO.keyword},'%') 
        </if>
        <if test="reqDTO.type != null and reqDTO.type != ''">
            and t0.type = #{reqDTO.type}
        </if>
        ORDER BY t0.gmt_create desc
    </select>
</mapper>
