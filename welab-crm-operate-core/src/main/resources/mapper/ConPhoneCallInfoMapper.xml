<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConPhoneCallInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ConPhoneCallInfo">
        <id column="id" property="id" />
        <result column="call_detail_guid" property="callDetailGuid" />
        <result column="staff_id" property="staffId" />
        <result column="group_code" property="groupCode" />
        <result column="cdr_enterprise_id" property="cdrEnterpriseId" />
        <result column="cdr_number_trunk" property="cdrNumberTrunk" />
        <result column="cdr_hotline" property="cdrHotline" />
        <result column="cdr_main_unique_id" property="cdrMainUniqueId" />
        <result column="cdr_customer_number" property="cdrCustomerNumber" />
        <result column="cdr_customer_area_code" property="cdrCustomerAreaCode" />
        <result column="cdr_customer_city" property="cdrCustomerCity" />
        <result column="cdr_customer_province" property="cdrCustomerProvince" />
        <result column="cdr_customer_number_type" property="cdrCustomerNumberType" />
        <result column="cdr_status" property="cdrStatus" />
        <result column="cdr_call_type" property="cdrCallType" />
        <result column="cdr_callee_cno" property="cdrCalleeCno" />
        <result column="cdr_join_queue_time" property="cdrJoinQueueTime" />
        <result column="cdr_bridge_time" property="cdrBridgeTime" />
        <result column="cdr_start_time" property="cdrStartTime" />
        <result column="cdr_answer_time" property="cdrAnswerTime" />
        <result column="cdr_end_time" property="cdrEndTime" />
        <result column="cdr_record_file" property="cdrRecordFile" />
        <result column="cdr_end_reason" property="cdrEndReason" />
        <result column="cdr_end_bridge_time" property="cdrEndBridgeTime" />
        <result column="cdr_queue" property="cdrQueue" />
        <result column="cdr_detail_sip_cause" property="cdrDetailSipCause" />
        <result column="cdr_investigation" property="cdrInvestigation" />
        <result column="cdr_cno" property="cdrCno" />
        <result column="cdr_x_number" property="cdrXNumber" />
        <result column="callee_ringing_time" property="calleeRingingTime" />
        <result column="cdr_request_unique_id" property="cdrRequestUniqueId" />
        <result column="cdr_agent_number" property="cdrAgentNumber" />
        <result column="cust_callee_clid" property="custCalleeClid" />
        <result column="cdr_clid" property="cdrClid" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, call_detail_guid, staff_id, group_code, cdr_enterprise_id, cdr_number_trunk, cdr_hotline, cdr_main_unique_id, cdr_customer_number, cdr_customer_area_code, cdr_customer_city, cdr_customer_province, cdr_customer_number_type, cdr_status, cdr_call_type, cdr_callee_cno, cdr_join_queue_time, cdr_bridge_time, cdr_start_time, cdr_answer_time, cdr_end_time, cdr_record_file, cdr_end_reason, cdr_end_bridge_time, cdr_queue, cdr_detail_sip_cause, cdr_investigation, cdr_cno, cdr_x_number, callee_ringing_time, cdr_request_unique_id, cdr_agent_number, cust_callee_clid, cdr_clid, gmt_create, gmt_modify
    </sql>
    <select id="selectListByPage" resultType="com.welab.crm.operate.domain.ConPhoneCallInfo">
        select
        <include refid="Base_Column_List"></include>
        from con_phone_call_info i
        where 1=1
        <if test="filter.callDetailGuid != null">
            and i.call_detail_guid = #{filter.callDetailGuid}
        </if>
        <if test="filter.staffId != null and filter.staffId != ''">
            and i.staff_id = #{filter.staffId}
        </if>
        <if test="filter.groupCode != null and filter.groupCode != ''">
            and i.group_code = #{filter.groupCode}
        </if>
        <if test="filter.cdrCustomerNumber != null and filter.cdrCustomerNumber != ''">
            and i.cdr_customer_number = #{filter.cdrCustomerNumber}
        </if>
        <if test="filter.cdrRequestUniqueId != null and filter.cdrRequestUniqueId != ''">
            and i.cdr_request_unique_id = #{filter.cdrRequestUniqueId}
        </if>
        <if test="filter.cdrStatus != null and filter.cdrStatus != ''">
            and i.cdr_status = #{filter.cdrStatus}
        </if>
        <if test="filter.cdrCallType != null and filter.cdrCallType != ''">
            and i.cdr_call_type = #{filter.cdrCallType}
        </if>
        order by i.gmt_create desc
    </select>
    <select id="selectSummaryByStaffId" resultType="com.welab.crm.operate.vo.phone.IsSummaryVO">
        select task_id,
        cdr_main_unique_id as mainUniqueId,
        cdr_customer_number as customerNumber,
        cdr_call_type as callType
        from con_phone_call_info
        where staff_id = #{staffId}
        order by cdr_start_time desc
        limit 1
    </select>

    <select id="selectDetailByPage" resultType="com.welab.crm.operate.vo.woReport.ReportDupCallDetailVO">
        select cpc.cdr_start_time,
        cpc.cdr_hotline as hotline,
        cpc.cdr_answer_time,
        cpc.cdr_join_queue_time,
        cpc.cdr_end_time,
        cpc.cdr_end_bridge_time,
        cpc.cdr_status,
        cpc.cdr_callee_cno as cdrCno,
        cpc.cdr_customer_number,
        dc.customer_name,
        cpc.cdr_customer_number as mobile,
        dc.vip as iVip,
        iacs.staff_name,
        cps.call_summary,
        cps.call_comment,
        dc.user_id,
        dc.uuid
        from con_phone_call_info cpc
        left join con_phone_summary cps on cpc.cdr_main_unique_id = cps.cdr_main_unique_id
        left join data_customer dc on dc.id = cps.customer_id
        left join in_auth_crm_staff iacs on cpc.staff_id = iacs.id
        where
        cpc.cdr_call_type = '1'
        and cpc.cdr_start_time between #{start} and #{end}
        and (cpc.cdr_status = '1' or cpc.cdr_status = '2')
        and cpc.cdr_hotline in
        <foreach collection="lines" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="staffIds != null and staffIds.size() > 0">
            and iacs.id in
            <foreach collection="staffIds" item="staffId" index="index" open="(" close=")" separator=",">
                #{staffId}
            </foreach>
        </if>
        order by cpc.cdr_start_time asc, cpc.cdr_hotline asc
    </select>

    <select id="queryReportDetail" resultType="com.welab.crm.operate.vo.phone.ReportPhoneVO">
        SELECT
        t1.cdr_customer_number,
        concat_ws('/', t1.cdr_customer_city, t1.cdr_customer_province) as area,
        t2.uuid,
        t2.user_id,
        t2.customer_name,
        t1.gmt_create,
        CASE t1.cdr_call_type
        WHEN '1' THEN
        '呼入'
        WHEN '4' THEN
        '预览外呼'
        WHEN '6' THEN
        '主叫外呼'
        WHEN '9' THEN
        '内部呼叫'
        END AS cdr_call_type,
        t1.cdr_start_time,
        t1.cdr_bridge_time,
        t1.cdr_end_time,
        CASE
        WHEN t1.cdr_end_reason = '1000' THEN
        '是'
        ELSE
        '否'
        END AS cdr_end_reason,
        t1.cdr_end_bridge_time,
        t1.staff_id,
        t1.cdr_callee_cno,
        t3.staff_name,
        t1.cdr_cno,
        CASE cdr_status
        WHEN '1' THEN
        '座席接听'
        WHEN '2' THEN
        '已呼叫座席,座席未接听'
        WHEN '3' THEN
        '系统接听'
        WHEN '4' THEN
        '系统未接-IVR配置错误'
        WHEN '5' THEN
        '系统未接-停机'
        WHEN '30' THEN
        '座席未接听'
        WHEN '31' THEN
        '座席接听,未呼叫客户'
        WHEN '32' THEN
        '座席接听,客户未接听'
        WHEN '33' THEN
        '双方接听'
        WHEN '50' THEN
        '主叫外呼接听'
        WHEN '51' THEN
        '主叫外呼,客户未接听'
        WHEN '52' THEN
        '主叫外呼,双方接听'
        ELSE
        '未知'
        END AS cdr_status,
        t3.group_name as group_code,
        t1.cdr_hotline,
        CASE t2.vip
        WHEN 1 THEN
        '是'
        ELSE
        '否'
        END AS vip
        FROM
        con_phone_call_info t1
        LEFT JOIN (SELECT t.customer_name, t.mobile, t.uuid, t.user_id, t.vip FROM data_customer t GROUP BY t.customer_name, t.mobile) t2 ON t1.cdr_customer_number = t2.mobile
        LEFT JOIN in_auth_crm_staff t3 ON t1.staff_id = t3.id
        WHERE
        1 = 1
        <if test="filter.startTime != null and filter.startTime != ''">
            AND t1.cdr_start_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND t1.cdr_start_time &lt;= #{filter.endTime}
        </if>
        <!--<if test="filter.cdrHotline != null and filter.cdrHotline != ''">
            AND t1.cdr_hotline = #{filter.cdrHotline}
        </if>-->
        <if test="filter.cdrHotlines != null  and filter.cdrHotlines.size()>0">
            AND t1.cdr_hotline IN
            <foreach item="item" index="index" collection="filter.cdrHotlines" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="filter.cdrCno != null and filter.cdrCno != ''">
            AND t1.cdr_callee_cno = #{filter.cdrCno}
        </if>
        <if test="filter.cdrCustomerNumber != null and filter.cdrCustomerNumber != ''">
            AND t1.cdr_customer_number = #{filter.cdrCustomerNumber}
        </if>
        <if test="filter.cdrCallType != null and filter.cdrCallType != ''">
            AND t1.cdr_call_type = #{filter.cdrCallType}
        </if>
    </select>

    <select id="queryDetail" resultType="com.welab.crm.operate.vo.phone.CallInPhoneDetailVO">
        SELECT
        t1.id,
        t1.cdr_record_file,
        t1.cdr_cno,
        t1.staff_id,
        t1.cdr_callee_cno,
        t3.staff_name,
        CASE t1.cdr_call_type
        WHEN '1' THEN '呼入'
        WHEN '4' THEN '预览外呼'
        WHEN '6' THEN '主叫外呼'
        WHEN '9' THEN '内部呼叫'
        END AS cdr_call_type,
        t1.cdr_start_time,
        t1.cdr_end_time,
        t1.cdr_end_bridge_time,
        CASE t1.cdr_customer_number_type
        WHEN '1' THEN '固话'
        WHEN '2' THEN '手机'
        END AS cdr_customer_number_type,
        t1.cdr_agent_number,
        t1.cdr_clid,
        t1.cdr_customer_number,
        CASE WHEN t1.cdr_end_reason = '1000' AND t1.cdr_call_type='1' THEN '客户挂机'
        WHEN t1.cdr_end_reason = '1001' AND t1.cdr_call_type='1' THEN '座席挂机'
        WHEN t1.cdr_end_reason = '1000' AND t1.cdr_call_type='4' THEN '座席挂机'
        WHEN t1.cdr_end_reason = '1001' AND t1.cdr_call_type='4' THEN '客户挂机'
        WHEN t1.cdr_end_reason = '1002' THEN '被强拆'
        END AS cdr_end_reason,
        t2.customer_name,
        t2.user_id,
        t2.uuid,
        t1.group_code,
        t4.sv_keys as satisfactionEvaluation
        FROM
        con_phone_call_info t1 force index (con_phone_call_info_cdr_start_time_IDX)
        LEFT JOIN data_customer t2 ON t1.cdr_customer_number = t2.mobile
        LEFT JOIN in_auth_crm_staff t3 ON t1.staff_id = t3.id
        left join con_satisfaction t4 on t1.cdr_main_unique_id = t4.cdr_main_unique_id
        left join con_phone_summary t5 on t1.cdr_main_unique_id = t5.cdr_main_unique_id
        WHERE 1 = 1
        <if test="filter.groupCode != null and filter.groupCode != ''">
            AND t1.group_code = #{filter.groupCode}
        </if>
        <if test="filter.recordFile != null and filter.recordFile != ''">
            AND t1.cdr_record_file = #{filter.recordFile}
        </if>
        <if test="filter.staffNo != null and filter.staffNo != ''">
            AND (t1.cdr_cno = #{filter.staffNo} OR t1.cdr_callee_cno = #{filter.staffNo})
        </if>
        <if test="filter.staffId != null">
            AND t1.staff_id = #{filter.staffId}
        </if>
        <if test="filter.id != null">
            AND t1.id = #{filter.id}
        </if>
        <if test="filter.customerName != null and filter.customerName != ''">
            AND t2.customer_name = #{filter.customerName}
        </if>
        <if test="filter.cdrCustomerNumberType != null and filter.cdrCustomerNumberType != ''">
            AND t1.cdr_call_type = #{filter.cdrCustomerNumberType}
            <choose>
                <when test="filter.cdrCustomerNumberType == 1">
                    AND t1.cdr_end_bridge_time is not null AND t1.cdr_end_bridge_time != '0'
                </when>
            </choose>
        </if>
        <if test="filter.cdrClid != null and filter.cdrClid != ''">
            AND t1.cdr_clid = #{filter.cdrClid}
        </if>
        <if test="filter.cdrCustomerNumber != null and filter.cdrCustomerNumber != ''">
            AND t1.cdr_customer_number = #{filter.cdrCustomerNumber}
        </if>
        <if test="filter.recordFile != null and filter.recordFile != ''">
            AND t1.cdr_record_file = #{filter.recordFile}
        </if>
        <if test="filter.startTime != null and filter.startTime != ''">
            AND t1.cdr_start_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND t1.cdr_start_time &lt;= #{filter.endTime}
        </if>
        <if test="filter.startCdrEndBridgeTime != null and filter.startCdrEndBridgeTime != ''">
            AND TIME_TO_SEC(t1.cdr_end_bridge_time) &gt;= #{filter.startCdrEndBridgeTime}
        </if>
        <if test="filter.endCdrEndBridgeTime != null and filter.endCdrEndBridgeTime != ''">
            AND TIME_TO_SEC(t1.cdr_end_bridge_time) &lt;= #{filter.endCdrEndBridgeTime}
        </if>
        <if test="filter.userId != null and filter.userId != ''">
            AND t2.user_id = #{filter.userId}
        </if>
        <if test="filter.uuid != null and filter.uuid != ''">
            AND t2.uuid = #{filter.uuid}
        </if>
        <if test="filter.contactResult != null and filter.contactResult == 0">
            AND t1.cdr_end_bridge_time = '0'
        </if>
        <if test="filter.contactResult != null and filter.contactResult == 1">
            AND t1.cdr_end_bridge_time != '0'
        </if>
        <if test="filter.satisfactionLevel != null">
            AND LEFT(t4.sv_keys,1) = #{filter.satisfactionLevel}
        </if>
        <if test="filter.dissatisfiedReason != null">
            AND SUBSTR(t4.sv_keys,2,1) = #{filter.dissatisfiedReason}
        </if>
        <if test="filter.isEvaluate != null and filter.isEvaluate == 0">
            AND t4.sv_keys is null
        </if>
        <if test="filter.isEvaluate != null and filter.isEvaluate == 1">
            AND t4.sv_keys is not null
        </if>
        <if test="filter.callSummary != null and filter.callSummary != ''">
            AND t5.call_summary like concat('%', #{filter.callSummary}, '%')
        </if>
        <if test="filter.type != null and filter.type == 0">
            AND ((t1.cdr_end_reason ='1000' AND t1.cdr_call_type =4) OR (t1.cdr_end_reason ='1001' AND t1.cdr_call_type =1))
        </if>
        <if test="filter.type != null and filter.type == 1">
            AND ((t1.cdr_end_reason ='1000' AND t1.cdr_call_type =1) or (t1.cdr_end_reason ='1001' AND t1.cdr_call_type =4))
        </if>
        GROUP BY t1.id
        ORDER BY t1.cdr_start_time DESC
    </select>

    <select id="countCustomerCall" resultType="com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO">
        select total.hotline, callQuantities, realCustomers, manServiceCounts, manRealCounts
        from (select cdr_hotline as hotline, count(*) as callQuantities
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        and cdr_call_type = '1'
        group by cdr_hotline) total
        left join
        (select a.hotline, count(*) as realCustomers
        from (select cdr_hotline as hotline,
        cdr_customer_number
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_customer_number, cdr_hotline) as a
        group by a.hotline) rc on total.hotline = rc.hotline
        left join
        (select cdr_hotline as hotline,
        count(*) as manServiceCounts
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and (cdr_status = '1' or cdr_status = '2')
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_hotline) ms on ms.hotline = total.hotline
        left join
        (select a.hotline,
        count(*) as manRealCounts
        from (select cdr_hotline as hotline,
        cdr_customer_number
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and (cdr_status = '1' or cdr_status = '2')
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_customer_number, cdr_hotline) as a
        group by a.hotline) mr on mr.hotline = total.hotline
    </select>

    <select id="countCustomerCallByDay" resultType="com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO">
        select total.hotline, total.callDay, callQuantities, realCustomers, manServiceCounts, manRealCounts
        from (select cdr_hotline as hotline, date_format(cdr_start_time,'%Y-%m-%d') as callDay, count(*) as callQuantities
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        and cdr_call_type = '1'
        group by cdr_hotline, date_format(cdr_start_time,'%Y-%m-%d')) total
        left join
        (select a.hotline, a.callDay, count(*) as realCustomers
        from (select cdr_hotline as hotline, date_format(cdr_start_time,'%Y-%m-%d') as callDay
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_customer_number, cdr_hotline, date_format(cdr_start_time,'%Y-%m-%d')) as a
        group by a.hotline,a.callDay) rc on total.hotline = rc.hotline and total.callDay=rc.callDay
        left join
        (select cdr_hotline as hotline,
        count(*) as manServiceCounts,
        date_format(cdr_start_time,'%Y-%m-%d') as callDay
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and (cdr_status = '1' or cdr_status = '2')
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_hotline, date_format(cdr_start_time,'%Y-%m-%d')) ms on ms.hotline=total.hotline and total.callDay=ms.callDay
        left join
        (select a.hotline, a.callDay,
        count(*) as manRealCounts
        from (select cdr_hotline as hotline,date_format(cdr_start_time,'%Y-%m-%d') as callDay
        from con_phone_call_info
        where cdr_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        and (cdr_status = '1' or cdr_status = '2')
        and cdr_hotline in
        <foreach collection="filter.hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by cdr_customer_number, cdr_hotline, date_format(cdr_start_time,'%Y-%m-%d')) as a
        group by a.hotline, a.callDay) mr on mr.hotline = total.hotline and total.callDay=mr.callDay
    </select>

    <select id="selectByPeriodTime" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.vo.phone.CallInfoStatsVO">
        select
        <if test="period=='day'">
            date_format(gmt_create, '%Y-%m-%d') as day,
        </if>
        <if test="period=='range'">
            concat(left(#{startTime},10),'~',left(#{endTime},10)) as day,
        </if>
        cdr_status,
        cdr_call_type,
        cdr_callee_cno,
        cdr_cno,
        cdr_end_bridge_time
        from con_phone_call_info force index(con_phone_call_info_cdr_start_time_IDX)
        where cdr_start_time between #{startTime} and #{endTime}
        and cdr_call_type in ('1','4')
        and (cdr_cno is not null or cdr_callee_cno is not null)
        <if test="groupCode!=null and groupCode!=''">
            and group_code = #{groupCode}
        </if>
        <if test="staffId!=null and staffId!=''">
            and staff_id = #{staffId}
        </if>
    </select>

    <select id="selectCallInTotal" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkTimeSummaryVO">
        select cno,
        sum(callInCount)                 as callInCount,
        sum(callInConnectedNumber)       as callInConnectedNumber,
        sum(callInDuration) as callInDuration
        from (select
        cdr_callee_cno as cno,
        count(*) as callInCount,
        (case when cdr_status='1' then count(1) else 0 end) as callInConnectedNumber,
        (case when cdr_end_bridge_time is not null then sum(time_to_sec(cdr_end_bridge_time)) else 0 end) as callInDuration
        from con_phone_call_info force index(con_phone_call_info_cdr_start_time_IDX)
        where cdr_start_time between #{startTime} and #{endTime}
        and cdr_call_type ='1'
        and cdr_callee_cno is not null
        <if test="groupCode!=null and groupCode!=''">
            and group_code = #{groupCode}
        </if>
        <if test="staffId!=null and staffId!=''">
            and staff_id = #{staffId}
        </if>
        group by cdr_callee_cno,cdr_status) t group by t.cno
    </select>

    <select id="selectCallOutTotal" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkTimeSummaryVO">
        select cno,
        sum(outboundCount) as outboundCount,
        sum(outboundConnectedNumber) as outboundConnectedNumber,
        sum(outboundDuration) as outboundDuration
        from (select
        cdr_cno as cno,
        count(*) as outboundCount,
        (case when cdr_status='33' then count(1) else 0 end) as outboundConnectedNumber,
        (case when cdr_end_bridge_time is not null then sum(time_to_sec(cdr_end_bridge_time)) else 0 end)
        as outboundDuration
        from con_phone_call_info force index(con_phone_call_info_cdr_start_time_IDX)
        where cdr_start_time between #{startTime} and #{endTime}
        and cdr_call_type ='4'
        and cdr_cno is not null
        <if test="groupCode!=null and groupCode!=''">
            and group_code = #{groupCode}
        </if>
        <if test="staffId!=null and staffId!=''">
            and staff_id = #{staffId}
        </if>
        group by cdr_cno, cdr_status) t group by t.cno
    </select>

    <select id="selectCallInNumber" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO" resultType="int">
        select
        count(1) as call_in_number
        from
        con_phone_call_info force index(con_phone_call_info_cdr_start_time_IDX)
        where
        cdr_call_type = '1'
        and cdr_callee_cno is not null
        and cdr_start_time between #{startTime} and #{endTime}
        <if test="groupCode!=null and groupCode!=''">
            and group_code = #{groupCode}
        </if>
        <if test="staffId!=null and staffId!=''">
            and staff_id = #{staffId}
        </if>
    </select>
    <select id="queryProvinceCount" resultType="com.welab.crm.operate.vo.screen.CountVO">
        SELECT
        cdr_customer_province as name ,
        COUNT(1) as cnt
        from con_phone_call_info cpci
        where
        cdr_start_time >= CURRENT_DATE()
        and cdr_call_type = '1'
        and cdr_enterprise_id = '7600088'
        group by cdr_customer_province
    </select>
    <select id="queryRepeatCount" resultType="com.welab.crm.operate.vo.screen.CountVO">
        SELECT
        'repeatCount' as name,
        ifnull(SUM(repeat_number),0) as cnt
        from con_repeat_call crc
        where gmt_create >= CURRENT_DATE()
        union all
        SELECT
        'totalCount' as name,
        COUNT(1) as cnt
        from con_phone_call_info cpci
        where cdr_start_time >= CURRENT_DATE()
        and cdr_enterprise_id = '7600088'
        and cdr_call_type = '1'
        and cdr_bridge_time is not null
    </select>
    <select id="queryRepeatCountHour" resultType="com.welab.crm.operate.vo.screen.CountVO">
        SELECT DATE_FORMAT(gmt_create, '%H') as name, SUM(repeat_number) as cnt
        from con_repeat_call crc
        where gmt_create BETWEEN CURRENT_DATE() and DATE_ADD(CURRENT_DATE(), INTERVAL 1 day)
        group by 1
    </select>
    <select id="queryCallInfoGroupCount" resultType="com.welab.crm.operate.vo.screen.CountVO">
    select
        name,
        sum(case when cdr_end_bridge_time != '0' then 1 else 0 end) as cnt
    from(
        SELECT
        t1.group_code as name,
        t1.cdr_end_bridge_time
        FROM
        con_phone_call_info t1
        LEFT JOIN data_customer t2 ON t1.cdr_customer_number = t2.mobile
        LEFT JOIN in_auth_crm_staff t3 ON t1.staff_id = t3.id
        left join con_satisfaction t4 on t1.cdr_main_unique_id = t4.cdr_main_unique_id
        WHERE 1 = 1
        <if test="filter.groupCode != null and filter.groupCode != ''">
            AND t1.group_code = #{filter.groupCode}
        </if>
        <if test="filter.recordFile != null and filter.recordFile != ''">
            AND t1.cdr_record_file = #{filter.recordFile}
        </if>
        <if test="filter.staffNo != null and filter.staffNo != ''">
            AND (t1.cdr_cno = #{filter.staffNo} OR t1.cdr_callee_cno = #{filter.staffNo})
        </if>
        <if test="filter.staffId != null">
            AND t1.staff_id = #{filter.staffId}
        </if>
        <if test="filter.id != null">
            AND t1.id = #{filter.id}
        </if>
        <if test="filter.customerName != null and filter.customerName != ''">
            AND t2.customer_name = #{filter.customerName}
        </if>
        <if test="filter.cdrCustomerNumberType != null and filter.cdrCustomerNumberType != ''">
            AND t1.cdr_call_type = #{filter.cdrCustomerNumberType}
        </if>
        <if test="filter.cdrClid != null and filter.cdrClid != ''">
            AND t1.cdr_clid = #{filter.cdrClid}
        </if>
        <if test="filter.cdrCustomerNumber != null and filter.cdrCustomerNumber != ''">
            AND t1.cdr_customer_number = #{filter.cdrCustomerNumber}
        </if>
        <if test="filter.recordFile != null and filter.recordFile != ''">
            AND t1.cdr_record_file = #{filter.recordFile}
        </if>
        <if test="filter.startTime != null and filter.startTime != ''">
            AND t1.cdr_start_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND t1.cdr_start_time &lt;= #{filter.endTime}
        </if>
        <if test="filter.startCdrEndBridgeTime != null">
            AND t1.cdr_end_bridge_time &gt;= #{filter.startCdrEndBridgeTime}
        </if>
        <if test="filter.endCdrEndBridgeTime != null">
            AND t1.cdr_end_bridge_time &lt;= #{filter.endCdrEndBridgeTime}
        </if>
        <if test="filter.userId != null and filter.userId != ''">
            AND t2.user_id = #{filter.userId}
        </if>
        <if test="filter.uuid != null and filter.uuid != ''">
            AND t2.uuid = #{filter.uuid}
        </if>
        <if test="filter.contactResult != null and filter.contactResult == 0">
            AND t1.cdr_end_bridge_time = '0'
        </if>
        <if test="filter.contactResult != null and filter.contactResult == 1">
            AND t1.cdr_end_bridge_time != '0'
        </if>
        <if test="filter.satisfactionLevel != null">
            AND LEFT(t4.sv_keys,1) = #{filter.satisfactionLevel}
        </if>
        <if test="filter.dissatisfiedReason != null">
            AND SUBSTR(t4.sv_keys,2,1) = #{filter.dissatisfiedReason}
        </if>
        <if test="filter.isEvaluate != null and filter.isEvaluate == 0">
            AND t4.sv_keys is null
        </if>
        <if test="filter.isEvaluate != null and filter.isEvaluate == 1">
            AND t4.sv_keys is not null
        </if>
        GROUP BY t1.id
        )t group by name
    </select>
    <select id="queryTodayAttendNumber" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT staff_id)
        from con_phone_call_info cpci
        where
            cdr_start_time >= CURRENT_DATE()
            and group_code in 
            <foreach collection="groupCodeList" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>

    </select>

</mapper>
