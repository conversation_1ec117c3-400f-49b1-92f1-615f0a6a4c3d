<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpEarlySettledRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpEarlySettledRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="customer_id" property="customerId" />
        <result column="app_no" property="appNo" />
        <result column="loan_rate" property="loanRate" />
        <result column="partner_code" property="partnerCode" />
        <result column="source_code" property="sourceCode" />
        <result column="current_tenor" property="currentTenor" />
        <result column="staff_id" property="staffId" />
        <result column="group_code" property="groupCode" />
        <result column="reason" property="reason" />
        <result column="comments" property="comments" />
        <result column="is_early_closed" property="isEarlyClosed" />
        <result column="loan_type" property="loanType" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, customer_id, app_no, loan_rate, partner_code, source_code, current_tenor, staff_id, group_code, reason, comments, is_early_closed, loan_type, gmt_create, gmt_modify
    </sql>
    <select id="queryUserSummary"
            resultType="com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO">
    select staffName, groupName, userCount, orderCount, debitAmount, earlySettledOrderCount,ifnull(round(earlySettledOrderCount/orderCount,2),0) as earlySettledRate
    from(
        select
        t1.staff_name as staffName,
        t2.name as groupName,
        ifnull(count(distinct t0.user_id),0) as userCount,
        ifnull(count(distinct t0.app_no),0) as orderCount,
        ifnull(sum(case when t0.is_early_closed = 1 then 1 else 0 end),0) as earlySettledOrderCount,
        ifnull(sum(t0.debit_amount), 0) as debitAmount
        from op_early_settled_record t0
        join in_auth_crm_staff t1 on t0.staff_id = t1.id
        join in_auth_crm_org t2 on t0.group_code = t2.code
        where 1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            and t0.gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and t0.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.loanType != null and dto.loanType.size() >0">
            and t0.loan_type in
            <foreach collection="dto.loanType" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.groupCode != null and dto.groupCode.size() >0">
            and t0.group_code in
            <foreach collection="dto.groupCode" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffIds != null and dto.staffIds.size() >0">
            and t0.staff_id in
            <foreach collection="dto.staffIds" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        group by t0.group_code,t0.staff_id
        )t


    </select>
    <select id="queryTotalSummary"
            resultType="com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO">
        select userCount,orderCount, earlySettledOrderCount,debitAmount,ifnull(round(earlySettledOrderCount/orderCount,2),0) as earlySettledRate
        from (
        select
        ifnull(count(distinct t0.user_id),0) as userCount,
        ifnull(count(distinct t0.app_no),0) as orderCount,
        ifnull(sum(case when t0.is_early_closed = 1 then 1 else 0 end),0) as earlySettledOrderCount,
        ifnull(sum(t0.debit_amount), 0) as debitAmount
        from op_early_settled_record t0
        join in_auth_crm_staff t1 on t0.staff_id = t1.id
        join in_auth_crm_org t2 on t0.group_code = t2.code
        where 1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            and t0.gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and t0.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.loanType != null and dto.loanType.size() >0">
            and t0.loan_type in
            <foreach collection="dto.loanType" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.groupCode != null and dto.groupCode.size() >0">
            and t0.group_code in
            <foreach collection="dto.groupCode" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffIds != null and dto.staffIds.size() >0">
            and t0.staff_id in
            <foreach collection="dto.staffIds" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        )t
    </select>
    <select id="queryReasonTenorSummary" resultType="com.welab.crm.operate.model.EarlySettledReasonTenorModel">
        select
            reason,
            current_tenor as tenor,
            count(1) as count
        from op_early_settled_record oesr
        where gmt_create between #{startTime} and #{endTime}
        group by reason,current_tenor
    </select>
    <select id="queryOriginPartnerSummary"
            resultType="com.welab.crm.operate.model.EarlySettledOriginPartnerModel">
        select
            source_code as origin,
            partner_code as partner,
            count(1) as count
        from op_early_settled_record oesr
        where gmt_create between #{startTime} and #{endTime}
        group by source_code ,partner_code
    </select>
    <select id="queryTotalCountGroupByTenor"
            resultType="com.welab.crm.operate.model.EarlySettledReasonTenorModel">
        select
            current_tenor as tenor,
            count(1) as count
        from op_early_settled_record oesr
        where gmt_create between #{startTime} and #{endTime}
        group by current_tenor
    </select>
    <select id="queryTotalCountGroupByPartnerCode"
            resultType="com.welab.crm.operate.model.EarlySettledOriginPartnerModel">
        select
            partner_code as partner,
            count(1) as count
        from op_early_settled_record oesr
        where gmt_create between #{startTime} and #{endTime}
        group by partner_code
    </select>

</mapper>
