<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.LoanTransferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.LoanTransfer">
        <id column="id" property="id" />
        <result column="file_name" property="fileName" />
        <result column="quantities" property="quantities" />
        <result column="with_attachment" property="withAttachment" />
        <result column="approve_state" property="approveState" />
        <result column="approve_time" property="approveTime" />
        <result column="auditor" property="auditor" />
        <result column="remark" property="remark" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_name, quantities, with_attachment, approve_state, approve_time, auditor, create_user, lst_upd_user, gmt_create, gmt_modify, remark
    </sql>
    <select id="selectPageByCondition" resultType="com.welab.crm.operate.domain.LoanTransfer">

        SELECT distinct clt.* from cs_loan_transfer clt
        join cs_loan_transfer_contract cltc on clt.id = cltc.transfer_id
        where 1=1
        <if test="dto.fileName != null and  dto.fileName != ''">
            and clt.file_name = #{dto.fileName}
        </if>
        <if test="dto.approveState != null">
            and clt.approve_state = #{dto.approveState}
        </if>
        <if test="dto.startCreateTime != null">
            and clt.gmt_create &gt;= #{dto.startCreateTime}
        </if>

        <if test="dto.endCreateTime != null">
            and clt.gmt_create &lt;= #{dto.endCreateTime}
        </if>
        <if test="dto.applicationId != null and  dto.applicationId != ''">
            and cltc.contract_no = #{dto.applicationId}
        </if>
        order by clt.id desc
    </select>
    <select id="selectOrderLoanTransferList" resultType="com.welab.crm.operate.vo.loan.OrderLoanTransferVO">
        SELECT
            dla.id,
            wt.gmt_create as applyTime,
            wt.customer_name as name,
            dla.application_id as applicationId,
            dla.status as orderState,
            '债转客户已结清订单要求更细状态' as description,
            case
                when cltc.push_state = 1 then '已推送'
                else '未推送'
                end as pushState ,
            case
                when cltc.process_status = 1 then '处理成功'
                when cltc.process_status = 0 then '处理失败'
                else ''
                end as processState,
            case
                when cltc.sms_state = 1 then '发送成功'
                when cltc.sms_state = 0 then '发送失败'
                else ''
                end as smsState
        from
            wo_task wt
                left join op_dict_info odi4 on
                wt.order_three_class = odi4.id
                left join data_loan_application dla on
                wt.id = dla.wo_task_id
                left join cs_loan_transfer_contract cltc on
                dla.application_id = cltc.contract_no
        where
            odi4.content = '债转用户更新订单'
            <if test="dto.startTime != null and dto.startTime != ''">
                and wt.gmt_create &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and wt.gmt_create &lt;= #{dto.endTime}
            </if>
            <if test="dto.applicationId != null and dto.applicationId != ''">
                and dla.application_id = #{dto.applicationId}
            </if>
            <if test="dto.processState != null">
                and cltc.process_status = #{dto.processState}
            </if>
            order by wt.gmt_create desc
    </select>
</mapper>
