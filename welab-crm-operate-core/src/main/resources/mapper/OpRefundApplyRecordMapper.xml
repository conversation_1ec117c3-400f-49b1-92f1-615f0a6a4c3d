<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpRefundApplyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpRefundApplyRecord">
        <id column="id" property="id" />
        <result column="application_id" property="applicationId" />
        <result column="customer_name" property="customerName" />
        <result column="user_id" property="userId" />
        <result column="amount" property="amount" />
        <result column="refund_days" property="refundDays" />
        <result column="refund_start_time" property="refundStartTime" />
        <result column="approval_status" property="approvalStatus" />
        <result column="push_flag" property="pushFlag" />
        <result column="staff_id" property="staffId" />
        <result column="request_no" property="requestNo" />
        <result column="result" property="result" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_id, customer_name, user_id, amount, refund_days, refund_start_time, approval_status, push_flag, staff_id, request_no, result, gmt_create, gmt_modify
    </sql>

</mapper>
