<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.AiTmkPushMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.AiTmkPush">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="quantities" property="quantities" />
        <result column="push_day" property="pushDay" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, quantities, push_day, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>
    <select id="queryTotalPushCount" resultType="java.lang.Integer">

        select sum(quantities)
        from ai_tmk_push
        where push_day between #{startTime} and #{endTime}
        and config_id = #{configId}
    </select>

</mapper>
