<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.InPhoneLoginInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.InPhoneLoginInfo">
        <id column="id" property="id" />
        <result column="org" property="org" />
        <result column="id_no" property="idNo" />
        <result column="pwd" property="pwd" />
        <result column="tel" property="tel" />
        <result column="tel_type" property="telType" />
        <result column="init_state" property="initState" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="seating_no" property="seatingNo" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="user_tel" property="userTel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org, id_no, pwd, tel, tel_type, init_state, status, create_user, lst_upd_user, gmt_create, seating_no, gmt_modify, user_tel
    </sql>
    <select id="queryCnoListByStaffId" resultType="java.lang.String">
        select t0.id_no
        from in_phone_login_info t0
        join in_auth_crm_staff t1 on t0.user_tel = t1.login_name
        where t1.id in(
        <foreach collection="list" open="" close="" separator="," index="index" item="item">
            #{item}
        </foreach>
        )
    </select>
    <select id="queryCnoListByGroupCode" resultType="java.lang.String">
        select t0.id_no
        from in_phone_login_info t0
        join in_auth_crm_staff t1 on t0.user_tel = t1.login_name
        where t1.group_code in(
        <foreach collection="list" open="" close="" separator="," index="index" item="item">
            #{item}
        </foreach>
        )
    </select>
    <select id="queryGroupCodeByCno" resultType="com.welab.crm.operate.domain.InAuthCrmStaff">
        select t1.group_name, t1.staff_name
        from in_phone_login_info t0
        join in_auth_crm_staff t1 on t0.user_tel = t1.login_name
        where t0.id_no = #{cno}
    </select>
    <select id="queryStaffEfficiency" resultType="com.welab.crm.operate.vo.tmkReport.StaffEfficiencyVO">
            SELECT
            r5.name as groupName,
            r3.staff_name,
            r4.id_no as cno,
            ifnull(r2.call_avg,'0') as avgCalloutTime,
            ifnull(r1.acw_time,'0') as acwTime,
            ifnull(r1.busy_time,'0') as busyTime,
            ifnull(r1.break_time,'0') as breakTime,
            case when r1.login_time = 0 then 0 else ifnull((r1.acw_time+r2.ob_time+r2.ib_time)/r1.login_time,0) end as workTimeRate,
            case when r1.login_time = 0 then 0 else ifnull((r2.ob_time+r2.ib_time)/r1.login_time,0) end as callTimeRate,
            ifnull(r2.call_in,'0') as avgCalloutCount,
            ifnull(r2.call_num,'0') as avgBridgeCount,
            case when r2.call_in = 0 then 0 else ifnull((r2.call_num)/r2.call_in,0) end as bridgeRate,
            ifnull(r1.login_time,'0') as loginTime,
            ifnull(r2.ob_time, '0') as callTime,
            ifnull(r2.ib_time, '0') as ibTime
            from
            (SELECT
            SUM(case when t1.status = 'login' and t1.duration is not null then t1.duration
                    when t1.status = 'login' and t1.duration is null then UNIX_TIMESTAMP(now()) - UNIX_TIMESTAMP(t1.start_time) else 0 end) as login_time,
            sum(case when t1.status = 'busy' then t1.duration else 0 end) as busy_time,
            SUM(case when t1.status = 'ACW' then t1.duration else 0 end) as acw_time,
            sum(case when t1.status = 'break' then t1.duration else 0 end) as break_time,
            t2.id as staff_id
            from op_staff_status_his t1 force index(op_staff_status_his_gmt_create_IDX)
            inner join in_auth_crm_staff t2 on t1.staff_id = t2.login_name
            where t1.gmt_create BETWEEN #{startTime} and #{endTime}
            group by t1.staff_id)r1
            inner join
            (
                SELECT
                sum(case when t1.cdr_call_type = '4' then 1 else 0 end) as call_in,
                sum(case when t1.cdr_call_type = '4' and t1.cdr_bridge_time is not null then 1 else 0 end) as call_num,
                sum(case when t1.cdr_call_type = '4' then TIME_TO_SEC(t1.cdr_end_bridge_time ) else 0 end) as ob_time,
                sum(case when t1.cdr_call_type = '1' then TIME_TO_SEC(t1.cdr_end_bridge_time ) else 0 end) as ib_time,
                sum(case when t1.cdr_call_type = '4' then TIME_TO_SEC(t1.cdr_end_bridge_time ) else 0 end)/sum(case when t1.cdr_call_type = '4' and t1.cdr_bridge_time is not null then 1 else 0 end) as call_avg,
                t1.staff_id
                from con_phone_call_info t1
                where t1.cdr_start_time BETWEEN #{startTime} and #{endTime}
                and t1.staff_id is not null
                group by t1.staff_id
                )r2 on r1.staff_id = r2.staff_id
            inner join in_auth_crm_staff r3 on r2.staff_id = r3.id
            inner join in_phone_login_info r4 on r3.login_name = r4.user_tel
            inner join in_auth_crm_org r5 on r3.group_code = r5.code
            <if test="groupCodeList != null and groupCodeList.size() > 0">
            where r3.group_code in
            <foreach collection="groupCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            </if>
            order by r3.staff_name
    </select>
    <select id="queryAllAgentList" resultType="com.welab.crm.operate.vo.agent.AgentVO">
        SELECT
            ipli.id_no as cno,
            iacs.group_name as groupName
        from
            in_phone_login_info ipli
                join in_auth_crm_staff iacs on
                ipli.user_tel = iacs.login_name
        where
            iacs.is_status = 1
    </select>
    <select id="queryAll" resultType="com.welab.crm.operate.domain.InPhoneLoginInfo">
        select *
        from in_phone_login_info
        where status = 1
    </select>

</mapper>
