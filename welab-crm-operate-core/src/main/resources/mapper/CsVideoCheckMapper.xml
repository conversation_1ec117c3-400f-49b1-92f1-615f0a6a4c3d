<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsVideoCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsVideoCheck">
        <id column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="mobile" property="mobile" />
        <result column="customer_name" property="customerName" />
        <result column="staff_id" property="staffId" />
        <result column="img_code" property="imgCode" />
        <result column="voice_code" property="voiceCode" />
        <result column="token" property="token" />
        <result column="video_name" property="videoName" />
        <result column="is_collect" property="isCollect" />
        <result column="sms_log_id" property="smsLogId" />
        <result column="result_msg" property="resultMsg" />
        <result column="img_score" property="imgScore" />
        <result column="voice_score" property="voiceScore" />
        <result column="start_record_time" property="startRecordTime" />
        <result column="end_record_time" property="endRecordTime" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="read_msg" property="readMsg" />
        <result column="user_id" property="userId" />
        <result column="uuid" property="uuid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, mobile, customer_name, staff_id, img_code, voice_code, token, video_name, is_collect, sms_log_id, result_msg, img_score, voice_score, start_record_time, end_record_time, gmt_create, gmt_modify, read_msg, user_id, uuid
    </sql>
    <select id="queryVideoTask" resultType="com.welab.crm.operate.vo.video.VideoTaskVO">
        select
        id,
        user_id,
        uuid,
        customer_name,
        start_record_time as startTime,
        end_record_time as endTime,
        (case when video_name is not null then 1 else 0 end) as isVideo,
        img_code as faceCheckResult,
        voice_code as voiceCheckResult,
        is_collect,
        SEC_TO_TIME(TIMESTAMPDIFF(SECOND, start_record_time,end_record_time)) as videoTime,
        video_name as fileName,
        source_video_name as sourceFileName,
        staff_id as staffId,
        gmt_create as gmtCreate
        from cs_video_check
        where 1=1
        <if test="dto.userId != null">
            and user_id = #{dto.userId}
        </if>
        <if test="dto.imgCode != null">
            and img_code = #{dto.imgCode}
        </if>
        <if test="dto.voiceCode != null">
            and voice_code = #{dto.voiceCode}
        </if>
        <if test="dto.uuid != '' and dto.uuid != null">
            and uuid = #{dto.uuid}
        </if>
        <if test="dto.staffId != '' and dto.staffId != null">
            and staff_id = #{dto.staffId}
        </if>
        <if test="dto.startTime != '' and dto.startTime != null">
            and gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != '' and dto.endTime != null">
            and gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.callStartTime != '' and dto.callStartTime != null">
            and start_record_time &gt;= #{dto.callStartTime}
        </if>
        <if test="dto.callEndTime != '' and dto.callEndTime != null">
            and start_record_time &lt;= #{dto.callEndTime}
        </if>
        <if test="dto.isCollect != null">
            and is_collect = #{dto.isCollect}
        </if>
        <if test="dto.isVideo != null">
            <choose>
                <when test="dto.isVideo == 1">
                    and video_name is not null
                </when>
                <otherwise>
                    and video_name is null
                </otherwise>
            </choose>
        </if>
        order by id desc
    </select>

</mapper>
