<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkAssignHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.TmkAssignHistory">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="group_code" property="groupCode" />
        <result column="staff_id" property="staffId" />
        <result column="tmk_task_id" property="tmkTaskId" />
        <result column="distribution_time" property="distributionTime" />
        <result column="distribution_type" property="distributionType" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, group_code, staff_id, tmk_task_id, distribution_time, distribution_type, create_staff_id, gmt_create, gmt_modify
    </sql>

    <select id="selectRecycleByTime" resultType="com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO">
        select tah.pre_staff_id as staffId, count(1) as recoveryNum
        from tmk_assign_history tah join tmk_loan_invite tli on tah.tmk_task_id=tli.tmk_task_id
        where tah.gmt_create between #{filter.startTime} and #{filter.endTime}
        and tah.pre_staff_id is not null and tah.distribution_type='retrieve'
        <if test="typeList != null and typeList.size() > 0">
            and tli.type in
            <foreach collection="typeList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="codeList != null and codeList.size() > 0">
            and tah.pre_group_code in
            <foreach collection="codeList" separator="," item="item" index="index" open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by tah.pre_staff_id
    </select>

</mapper>
