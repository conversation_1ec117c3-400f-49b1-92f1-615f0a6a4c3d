<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoReturnLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.WoReturnLog">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="staff_id" property="staffId" />
        <result column="remark" property="remark" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, staff_id, remark, create_staff_id, gmt_create, gmt_modify
    </sql>

</mapper>
