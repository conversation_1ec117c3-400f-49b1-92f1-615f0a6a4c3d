<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoTypeMapper">
	
	<select id="queryWorkOrderTypeByPage" resultType="com.welab.crm.operate.vo.workorder.WorkOrderTypeVO">
        SELECT
            *
        FROM wo_type t0
        WHERE 1 = 1 
        <if test="reqDTO.code != null and reqDTO.code != ''">
            and t0.code = #{reqDTO.code}
        </if>
        <if test="reqDTO.name != null and reqDTO.name != ''">
            and t0.name = #{reqDTO.name}
        </if>
        <if test="reqDTO.userflag != null and reqDTO.userflag != ''">
            and t0.user_flag = #{reqDTO.userflag}
        </if>
        <if test="reqDTO.type != null and reqDTO.type != ''">
            and t0.type = #{reqDTO.type}
        </if>
        <if test="reqDTO.level != null and reqDTO.level != ''">
            and t0.level = #{reqDTO.level}
        </if>
        ORDER BY t0.code asc
    </select>
</mapper>
