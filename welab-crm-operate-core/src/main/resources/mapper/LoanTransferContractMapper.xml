<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.LoanTransferContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.LoanTransferContract">
        <id column="id" property="id" />
        <result column="transfer_id" property="transferId" />
        <result column="contract_no" property="contractNo" />
        <result column="push_state" property="pushState" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transfer_id, contract_no, push_state, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>
    <update id="updateSmsState">
        update cs_loan_transfer_contract
            set sms_state = #{state}
            where transfer_id = #{id}
            and contract_no in 
            <foreach collection="list" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
    </update>
    <select id="selectPageByTransferId" resultType="com.welab.crm.operate.vo.loan.LoanContactVO">

        SELECT
            cltc.id ,
            cltc.contract_no ,
            case when cltc.push_state = 1 then '已推送' else '未推送' end as pushState ,
            date_format(cltc.gmt_create,'%Y-%m-%d %H:%i:%s') as gmtCreate ,
            case when cltc.process_status = 1 then '处理成功' when cltc.process_status = 0 then '处理失败' else '' end as processStatus,
            cltc.fail_reason,
            case
                when cltc.sms_state = 1 then '发送成功'
                when cltc.sms_state = 0 then '发送失败'
                else ''
                end as smsState
            from cs_loan_transfer_contract cltc
            where cltc.transfer_id = #{transferId}
            order by cltc.process_status
    </select>


</mapper>
