<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkReportMapper">

  <select id="selectReassignData"
          parameterType="com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO"
          resultType="com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO">
    select tli.staff_id, iacs.staff_name, count(1) as assignNum
    from tmk_loan_invite tli
    join in_auth_crm_staff iacs on tli.staff_id = iacs.id
    where tli.assign_date between #{filter.startTime} and #{filter.endTime}
    and tli.flag = '2'
    <if test="typeList != null and typeList.size() > 0">
      and tli.type in
      <foreach collection="typeList" separator="," item="item" index="index" open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="codeList != null and codeList.size() > 0">
      and iacs.group_code in
      <foreach collection="codeList" separator="," item="item" index="index" open="(" close=")">
        #{item}
      </foreach>
    </if>
    group by tli.staff_id
  </select>

  <select id="selectReassignOutboundData"
          parameterType="com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO"
          resultType="com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO">

    select
    tt.staff_id,
    tt.group_code,
    count(1) as outboundNum,
    count(case when tt.callestablished = 1 then 1 end) as connectedNum,
    count(case when tt.callestablished = 1 and tt.confirmed_at &gt; #{filter.startTime} and tt.confirmed_at &lt; #{filter.endTime} and tt.conver_time > 0 then 1 end) as conversionNum
    from (
    select
    t.*
    from
    (
    select
    cpci.staff_id,
    cpci.group_code,
    tli.confirmed_at,
    tli.assign_date,
    tli.type as businessType,
    tli.flag,
    tli.state,
    tli.application_id,
    timestampdiff(second,cpci.cdr_start_time,tli.confirmed_at) as conver_time,
    (case when cpci.cdr_bridge_time is not null then 1 else 0 end) as callestablished
    from tmk_loan_invite tli
    join tmk_summary ts on tli.tmk_task_id = ts.tmk_task_id
    join con_phone_call_info cpci on ts.cdr_main_unique_id = cpci.cdr_main_unique_id
    where 1=1
    <if test="typeList != null and typeList.size() > 0">
      and tli.type in
      <foreach collection="typeList" separator="," item="item" index="index" open="(" close=")">
          #{item}
      </foreach>
    </if>
    and cpci.cdr_call_type = '4' and tli.flag = '2'
    and cpci.cdr_start_time between #{filter.startTime} and #{filter.endTime}
    and tli.assign_date between #{filter.startTime} and #{filter.endTime}
    <if test="codeList != null and codeList.size() > 0">
      and cpci.group_code in
      <foreach collection="codeList" separator="," item="item" index="index" open="(" close=")">
          #{item}
      </foreach>
    </if>
    order by tli.application_id, callestablished desc, cpci.cdr_start_time limit 100000
    ) t group by t.application_id
    ) tt
    group by tt.staff_id
    order by tt.staff_id
  </select>

</mapper>
