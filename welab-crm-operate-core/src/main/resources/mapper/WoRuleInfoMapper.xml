<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoRuleInfoMapper">

   <select id="queryWorkOrderRuleByPage" resultType="com.welab.crm.operate.vo.workorder.WorkOrderRuleVO">
        SELECT
            t.id,
            t.name,
            t.work_order_type,
            t.order_case,
            t.handle_group_id,
            t.handle_staff_id,
            t.assign_date,
            t.start_time,
            t.end_time,
            t.assign_interval,
            t.status,
            t.level,
            t.gmt_create,
            t.gmt_modify,
            t.gmt_handle,
            iacs.staff_name as createStaffId,
            t.modify_staff_id,
            t.rule_type,
            t.assign_type,
            t.assign_num,
            t.apply_origin
        FROM wo_rule_info t 
        left JOIN in_auth_crm_staff iacs on iacs.id = t.create_staff_id
        WHERE 1 = 1 
        <if test="reqDTO.workOrderType != null  and reqDTO.workOrderType != ''">
            and t.work_order_type like concat('%',#{reqDTO.workOrderType},'%')
        </if>
        <if test="reqDTO.orderCase != null  and reqDTO.orderCase != ''">
            and t.order_case like concat('%',#{reqDTO.orderCase},'%')
        </if>
        <if test="reqDTO.name != null and reqDTO.name != ''">
            and t.name = #{reqDTO.name}
        </if>
        <if test="reqDTO.assignDate != null and reqDTO.assignDate != ''">
            and t.assign_date = #{reqDTO.assignDate}
        </if>
        <if test="reqDTO.handleGroupId != null and reqDTO.handleGroupId != ''">
            and t.handle_group_id like concat('%',#{reqDTO.handleGroupId},'%')
        </if>
        <if test="reqDTO.ruleType != null and reqDTO.ruleType != ''">
            and t.rule_type = #{reqDTO.ruleType}
        </if>
        <if test="reqDTO.assignType != null and reqDTO.assignType != ''">
            and t.assign_type = #{reqDTO.assignType}
        </if>
        ORDER BY t.status desc, t.level asc
    </select>
    
    <update id="updateWorkOrderRuleAll">
        UPDATE wo_rule_info
           SET status = #{status}
        where rule_type = #{ruleType}
    </update>
    
    <select id="queryWorkOrderAdjustCountByPage" resultType="com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO">
        select
        wt.type,
        wrt.group_code,
        wrt.staff_id,
        count(1) as number,
        iacs.staff_name,
        iaco.name as groupName
        from wf_ru_task wrt 
		inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
		INNER JOIN wo_task wt on wre.busi_key = wt.order_no 
		LEFT JOIN in_auth_crm_staff iacs on iacs.id = wrt.staff_id
		LEFT JOIN in_auth_crm_org iaco on iaco.code = wrt.group_code  
        where wre.status not in ('close','waste') 
        and wrt.status in ('ready','unassigned') 
        <if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
            AND wrt.group_code in
            <foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
            AND wrt.staff_id in
            <foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and wt.status = #{reqDTO.status}
        </if>
        <if test="reqDTO.type != null  and reqDTO.type.size()>0">
            AND wt.type in
            <foreach item="item" index="index" collection="reqDTO.type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.orderCase != null  and reqDTO.orderCase.size()>0">
            AND wt.order_case in
            <foreach item="item" index="index" collection="reqDTO.orderCase" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.distributeStartDate != null">
            and wt.distribute_time &gt;= #{reqDTO.distributeStartDate}
        </if>
        <if test="reqDTO.distributeEndDate != null">
            and wt.distribute_time &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.submitStartDate != null">
            and wt.submit_time &gt;= #{reqDTO.submitStartDate}
        </if>
        <if test="reqDTO.submitEndDate != null">
            and wt.submit_time &lt; date_add(#{reqDTO.submitEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.mobile != null and reqDTO.mobile != ''">
            and wt.mobile = #{reqDTO.mobile}
        </if>
        <if test="reqDTO.customerName != null and reqDTO.customerName != ''">
            and wt.customer_name = #{reqDTO.customerName}
        </if>
        <if test="reqDTO.isAssigned != null and reqDTO.isAssigned == 1">
            and wrt.staff_id is not null and wrt.staff_id != ""
        </if>
        <if test="reqDTO.isAssigned != null and reqDTO.isAssigned == 0">
            and (wrt.staff_id is null or wrt.staff_id = "")
        </if>
        group by wt.type,wrt.group_code,wrt.staff_id,iacs.staff_name,groupName
        order by wt.type
    </select>
    
    <select id="queryWorkOrderAdjustListByPage" resultType="com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO">
        select
        wt.order_no,
        wt.submit_time,
        wt.customer_name,
        wt.mobile,
        wt.type,
        wt.order_case,
        wt.status,
        wt.distribute_time,
        wrt.group_code,
        wrt.staff_id,
        wrt.task_id,
        iacs.staff_name,
        iaco.name as groupName,
        dc.user_id,
        dc.uuid
        from wf_ru_task wrt 
		inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
		INNER JOIN wo_task wt on wre.busi_key = wt.order_no
		inner join data_customer dc on wt.cust_id = dc.id
		LEFT JOIN in_auth_crm_staff iacs on iacs.id = wrt.staff_id 
		LEFT JOIN in_auth_crm_org iaco on iaco.code = wrt.group_code 
        where wre.complete_flag != '1'
        and wrt.status in ('ready','unassigned') 
        <if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
            AND wrt.group_code in
            <foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
            AND wrt.staff_id in
            <foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and wt.status = #{reqDTO.status}
        </if>
        <if test="reqDTO.type != null  and reqDTO.type.size()>0">
            AND wt.type in
            <foreach item="item" index="index" collection="reqDTO.type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.orderCase != null  and reqDTO.orderCase.size()>0">
            AND wt.order_case in
            <foreach item="item" index="index" collection="reqDTO.orderCase" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.distributeStartDate != null">
            and wt.distribute_time &gt;= #{reqDTO.distributeStartDate}
        </if>
        <if test="reqDTO.distributeEndDate != null">
            and wt.distribute_time &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.submitStartDate != null">
            and wt.submit_time &gt;= #{reqDTO.submitStartDate}
        </if>
        <if test="reqDTO.submitEndDate != null">
            and wt.submit_time &lt; date_add(#{reqDTO.submitEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.mobile != null and reqDTO.mobile != ''">
            and wt.mobile = #{reqDTO.mobile}
        </if>
        <if test="reqDTO.customerName != null and reqDTO.customerName != ''">
            and wt.customer_name = #{reqDTO.customerName}
        </if>
        <if test="reqDTO.isAssigned != null and reqDTO.isAssigned == 1">
            and wrt.staff_id is not null and wrt.staff_id != ""
        </if>
        <if test="reqDTO.isAssigned != null and reqDTO.isAssigned == 0">
            and (wrt.staff_id is null or wrt.staff_id = "")
        </if>
        <if test="reqDTO.orderNo != null and reqDTO.orderNo != ''">
            and wt.order_no = #{reqDTO.orderNo}
        </if>
        <if test="reqDTO.userId != null and reqDTO.userId != ''">
            and dc.user_id = #{reqDTO.userId}
        </if>
        <if test="reqDTO.uuid != null and reqDTO.uuid != ''">
            and dc.uuid = #{reqDTO.uuid}
        </if>
        order by wt.gmt_create desc
    </select>
    
    <select id="queryWorkOrderAdjustList" resultType="com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO">
        select
        wt.order_no,
        wt.submit_time,
        wt.customer_name,
        wt.mobile,
        wt.type,
        wt.order_case,
        wt.status,
        wt.distribute_time,
        wrt.group_code,
        wrt.staff_id,
        wrt.task_id,
        iacs.staff_name,
        iaco.name as groupName
        from wf_ru_task wrt 
		inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
		INNER JOIN wo_task wt on wre.busi_key = wt.order_no 
		LEFT JOIN in_auth_crm_staff iacs on iacs.id = wrt.staff_id 
		LEFT JOIN in_auth_crm_org iaco on iaco.code = wrt.group_code 
        where wre.complete_flag != '1'
        and wrt.status in ('ready','unassigned') 
        <if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
            AND wrt.group_code in
            <foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
            AND wrt.staff_id in
            <foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and wt.status = #{reqDTO.status}
        </if>
       <if test="reqDTO.type != null  and reqDTO.type.size()>0">
            AND wt.type in
            <foreach item="item" index="index" collection="reqDTO.type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.orderCase != null  and reqDTO.orderCase.size()>0">
            AND wt.order_case in
            <foreach item="item" index="index" collection="reqDTO.orderCase" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqDTO.distributeStartDate != null">
            and wt.distribute_time &gt;= #{reqDTO.distributeStartDate}
        </if>
        <if test="reqDTO.distributeEndDate != null">
            and wt.distribute_time &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.submitStartDate != null">
            and wt.submit_time &gt;= #{reqDTO.submitStartDate}
        </if>
        <if test="reqDTO.submitEndDate != null">
            and wt.submit_time &lt; date_add(#{reqDTO.submitEndDate}, interval 1 day)
        </if>
        <if test="reqDTO.mobile != null and reqDTO.mobile != ''">
            and wt.mobile = #{reqDTO.mobile}
        </if>
        <if test="reqDTO.customerName != null and reqDTO.customerName != ''">
            and wt.customer_name = #{reqDTO.customerName}
        </if>
        order by wt.gmt_create desc
    </select>
    
    <select id="typeTotalWorkOrder" resultType="com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO">
        SELECT
        	odi.content as orderType,
            count(1) as totalCount
			  from wf_ru_task wrt 
				inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
        inner join wo_task wt on wre.busi_key = wt.order_no
        left join op_dict_info odi on odi.id = wt.type
        WHERE wre.status not in ('close','waste') 
				and wrt.status in ('ready','unassigned') 
        group by orderType
    </select>
    
    <update id="updateWorkOrderInfo">
        UPDATE wo_task
           SET distribute_time = now()
        where order_no = #{orderNo}
    </update>
    
    <select id="queryWorkOrderNoByTaskId" resultType="java.lang.String">
        select
        wt.order_no
        from wf_ru_task wrt 
		inner join wf_ru_execution wre on wrt.execution_id = wre.execution_id
		INNER JOIN wo_task wt on wre.busi_key = wt.order_no 
		where wrt.task_id = #{taskId}
    </select>
    <select id="queryExeIdByOrderNo" resultType="java.lang.String">
        select execution_id
        from wf_ru_execution wre
        where busi_key = #{orderNo};
    </select>
</mapper>
