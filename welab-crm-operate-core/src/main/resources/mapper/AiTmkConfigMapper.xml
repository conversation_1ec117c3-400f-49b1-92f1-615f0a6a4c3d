<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.AiTmkConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.AiTmkConfig">
        <id column="id" property="id" />
        <result column="product_names" property="productNames" />
        <result column="loan_channels" property="loanChannels" />
        <result column="ca_company" property="caCompany" />
        <result column="speech_id" property="speechId" />
        <result column="task_id" property="taskId" />
        <result column="push_way" property="pushWay" />
        <result column="number" property="number" />
        <result column="rule_name" property="ruleName" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="max_amount" property="maxAmount" />
        <result column="min_amount" property="minAmount" />
        <result column="approved_at" property="approvedAt" />
        <result column="flag" property="flag" />
        <result column="state" property="state" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_names, loan_channels, ca_company, speech_id, task_id, push_way, number, rule_name, start_time, end_time, max_amount, min_amount, approved_at, flag, state, create_user, lst_upd_user, gmt_create, gmt_modify, delete_flag
    </sql>

</mapper>
