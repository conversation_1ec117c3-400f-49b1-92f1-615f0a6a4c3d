<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.InAuthCrmOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.InAuthCrmOrg">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="pcode" property="pcode" />
        <result column="level" property="level" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="create_user" property="createUser" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="lst_upd_user" property="lstUpdUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, pcode, level, gmt_create, create_user, gmt_modify, lst_upd_user
    </sql>

</mapper>
