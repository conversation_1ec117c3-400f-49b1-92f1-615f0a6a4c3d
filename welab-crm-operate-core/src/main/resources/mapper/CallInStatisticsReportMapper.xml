<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CallInStatisticsReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CallInStatisticsReport">
        <id column="id" property="id" />
        <result column="hotline" property="hotline" />
        <result column="call_in_num" property="callInNum" />
        <result column="to_manual_num" property="toManualNum" />
        <result column="answered_num" property="answeredNum" />
        <result column="total_bridge_time" property="totalBridgeTime" />
        <result column="avg_bridge_time" property="avgBridgeTime" />
        <result column="queue_give_up_num" property="queueGiveUpNum" />
        <result column="count_day" property="countDay" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, hotline, call_in_num, to_manual_num, answered_num, total_bridge_time, avg_bridge_time, queue_give_up_num, count_day
    </sql>

</mapper>
