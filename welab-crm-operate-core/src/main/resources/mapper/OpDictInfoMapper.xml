<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpDictInfoMapper">

    <insert id="insertBatchSummaryDict">
        INSERT INTO welab_crm.op_dict_info
        (id,category, content, detail, status)
        VALUES
        <foreach collection="list" item="item" separator="," open="" close="" index="index">
            (
            FLOOR(RAND() * 1000000000), #{category}, #{item.name}, #{item.remark}, 1
            )
        </foreach>
    </insert>
    <update id="increaseSort">
        update op_dict_info set sort = sort + 1 where id = #{id}
    </update>
</mapper>
