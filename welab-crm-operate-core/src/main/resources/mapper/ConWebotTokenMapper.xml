<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConWebotTokenMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ConWebotToken">
        <id column="id" property="id" />
        <result column="mobile" property="mobile" />
        <result column="validate_type" property="validateType" />
        <result column="token" property="token" />
        <result column="result_code" property="resultCode" />
        <result column="result_message" property="resultMessage" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, validate_type, valid_url, result_code, result_message, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>

</mapper>
