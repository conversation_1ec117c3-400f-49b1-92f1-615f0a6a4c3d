<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpOrderConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpOrderConfig">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="order_one_class" property="orderOneClass" />
        <result column="get_num" property="getNum" />
        <result column="get_count" property="getCount" />
        <result column="get_group_code" property="getGroupCode" />
        <result column="status" property="status" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, order_one_class, get_num, get_count, get_group_code, status, gmt_create, gmt_modify
    </sql>

	<select id="queryOrderRuleConfig" resultType="com.welab.crm.operate.vo.workorder.OpOrderConfigVO">
        SELECT
        	*
        FROM op_order_config
        limit 1
    </select>
    
    <update id="updateOrderRuleConfig" parameterType="com.welab.crm.operate.dto.workorder.OpOrderConfigDTO">
        update op_order_config
        set 
        <if test="reqDTO.type != null and reqDTO.type != ''">
            type = #{reqDTO.type},
        </if>
        <if test="reqDTO.orderOneClass != null and reqDTO.orderOneClass != ''">
            order_one_class = #{reqDTO.orderOneClass},
        </if>
        <if test="reqDTO.getGroupCode != null and reqDTO.getGroupCode != ''">
            get_group_code = #{reqDTO.getGroupCode},
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            status = #{reqDTO.status},
        </if>
        <if test="reqDTO.getNum != null">
            get_num = #{reqDTO.getNum},
        </if>
        <if test="reqDTO.getCount != null">
            get_count = #{reqDTO.getCount},
        </if>
        gmt_modify = now()
    </update>
</mapper>
