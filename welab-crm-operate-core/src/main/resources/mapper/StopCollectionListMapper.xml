<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.StopCollectionListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.StopCollectionList">
        <id column="id" property="id" />
        <result column="id_no" property="idNo" />
        <result column="mobile" property="mobile" />
        <result column="uuid" property="uuid" />
        <result column="user_id" property="userId" />
        <result column="cust_name" property="custName" />
        <result column="valid_start_time" property="validStartTime" />
        <result column="valid_end_time" property="validEndTime" />
        <result column="add_reason" property="addReason" />
        <result column="black_type" property="blackType" />
        <result column="staff_name" property="staffName" />
        <result column="group_name" property="groupName" />
        <result column="operate_type" property="operateType" />
        <result column="comment" property="comment" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, id_no, mobile, uuid, user_id, cust_name, valid_start_time, valid_end_time, add_reason, black_type, staff_name, group_name, operate_type, comment, gmt_create, gmt_modify
    </sql>
    <select id="queryUuidByUserIdList" resultType="com.welab.crm.operate.domain.StopCollectionList">
        select user_id,uuid from stop_collection_list scl
        where user_id in
        <foreach collection="list" item="item" open="(" close=")" index="index" separator=",">
            #{item}
        </foreach>
        group by user_id
    </select>
    <select id="selectDetailReportPage" resultType="com.welab.crm.operate.vo.blacklist.BlackDetailReportVO">
        select
        scl.staff_name as createUser,
        scl.gmt_create as createTime,
        cust_name,
        id_no,
        scl.mobile,
        uuid,
        user_id,
        valid_start_time,
        valid_end_time,
        add_reason as reason,
        black_type,
        scl.group_name,
        operate_type,
        comment,
        black_day,
        operate_user,
        scl.gmt_modify as updateTime
        from stop_collection_list scl
        left join in_auth_crm_staff iacs on scl.staff_name = iacs.login_name
        where 1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            and scl.gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and scl.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.groupCode != null and dto.groupCode.size() > 0">
            and iacs.group_code in
            <foreach collection="dto.groupCode" item="item" close=")" open="(" index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffId != null and dto.staffId.size() > 0">
            and iacs.id in
            <foreach collection="dto.staffId" item="item" close=")" open="(" index="index" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="queryDetail" resultType="com.welab.crm.operate.domain.StopCollectionList">
        select scl.* from stop_collection_list scl
        left join in_auth_crm_staff iacs on scl.staff_name = iacs.login_name
        where 1=1
        and scl.operate_type = 'insert'
        <if test="dto.startTime != null and dto.startTime != ''">
            and scl.gmt_create &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and scl.gmt_create &lt;= #{dto.endTime}
        </if>
        <if test="dto.groupCode != null and dto.groupCode.size() > 0">
            and iacs.group_code in
            <foreach collection="dto.groupCode" separator="," index="index" open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.staffId != null and dto.staffId.size() > 0">
            and iacs.id in
            <foreach collection="dto.staffId" separator="," index="index" open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>



</mapper>
