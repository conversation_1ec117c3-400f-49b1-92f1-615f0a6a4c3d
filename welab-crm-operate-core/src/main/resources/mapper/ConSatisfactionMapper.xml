<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConSatisfactionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ConSatisfaction">
        <id column="id" property="id" />
        <result column="cdr_enterprise_id" property="cdrEnterpriseId" />
        <result column="cdr_number_trunk" property="cdrNumberTrunk" />
        <result column="cdr_hotline" property="cdrHotline" />
        <result column="cdr_main_unique_id" property="cdrMainUniqueId" />
        <result column="cdr_customer_number" property="cdrCustomerNumber" />
        <result column="cdr_call_type" property="cdrCallType" />
        <result column="sv_start_time" property="svStartTime" />
        <result column="sv_end_time" property="svEndTime" />
        <result column="bridged_cno" property="bridgedCno" />
        <result column="cdr_transfer" property="cdrTransfer" />
        <result column="sv_keys" property="svKeys" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cdr_enterprise_id, cdr_number_trunk, cdr_hotline, cdr_main_unique_id, cdr_customer_number, cdr_call_type, sv_start_time, sv_end_time, bridged_cno, cdr_transfer, sv_keys, gmt_create, gmt_modify
    </sql>

    <select id="selectSatisfactionRate" resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        t.date,
        t.bridged_cno as cno,
        concat(ifnull(round((s_num + vs_num)/ part_num * 100, 2), '0.00'), '%') as satisfiedRate
        from
        (
        select
        bridged_cno,
        <if test="filter.period=='day'">
            date_format(sv_start_time, '%Y-%m-%d') as date,
        </if>
        <if test="filter.period=='range'">
            concat(left(#{filter.startTime},10),'~',left(#{filter.endTime},10)) as date,
        </if>
        count(case when length(sv_keys)>= 1 then 1 else null end) as part_num,
        count(case when substr(sv_keys, 1) = '1' then 1 else null end) as vs_num,
        count(case when substr(sv_keys, 1) = '2' then 1 else null end) as s_num
        from
        con_satisfaction
        where
        sv_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        group by
        2,1) t
    </select>

    <select id="selectSatisfactionTotalRate" resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select
        concat(ifnull(round((s_num + vs_num)/ part_num * 100, 2), '0.00'), '%') as satisfiedRate
        from
            (
            select
            count(case when length(sv_keys)>= 1 then 1 else null end) as part_num,
            count(case when substr(sv_keys, 1) = '1' then 1 else null end) as vs_num,
            count(case when substr(sv_keys, 1) = '2' then 1 else null end) as s_num
            from
            con_satisfaction cs force index(idx_satisfaction_start_end_time) join in_phone_login_info ipli on ipli.id_no = cs.bridged_cno
            join in_auth_crm_staff iacs on iacs.login_name = ipli.user_tel and iacs.is_status = 1
            where
            sv_start_time between #{filter.startTime} and #{filter.endTime}
            and cdr_call_type = '1'
            <if test="filter.groupCode!=null and filter.groupCode!=''">
                and iacs.group_code=#{filter.groupCode}
            </if>
            <if test="filter.staffId!=null and filter.staffId!=''">
                and iacs.id=#{filter.staffId}
            </if>
        ) t
    </select>
</mapper>
