<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.NoticeMsgMapper">


    <select id="selectNoticeSendByPage" resultType="com.welab.crm.operate.vo.notice.NoticeMsgVO">
        select
            msg_id,
            title,
            sender,
            file_name,
            type,
            gmt_create as publish_time
        from notice_msg
        where 1=1
        and notice_type = 'system'
        <if test="filter.sender != null and filter.sender != ''">
            and sender = #{filter.sender}
        </if>
        <if test="filter.type != null and filter.type != ''">
            and type = #{filter.type}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and title like concat('%',#{filter.title},'%') 
        </if>
            and status = 1
        group by gmt_create
        order by gmt_create desc
    </select>
    <select id="selectNoticeReceiveByPage" resultType="com.welab.crm.operate.vo.notice.NoticeMsgVO">
        select
            msg_id,
            title,
            sender,
            file_name,
            type,
            gmt_create as publish_time
        from notice_msg
        where 1=1
        and notice_type = 'system'
        <if test="filter.receiver != null and filter.receiver != ''">
            and receiver = #{filter.receiver}
        </if>
        <if test="filter.type != null and filter.type != ''">
            and type = #{filter.type}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and title like concat('%',#{filter.title},'%')
        </if>
        and status = 1
        order by gmt_create
    </select>
    <select id="selectReadNoticeByPage" resultType="com.welab.crm.operate.vo.notice.NoticeMsgVO">
        select
        t1.msg_id,
        t1.title,
        t1.sender,
        t1.file_name,
        t1.type,
        t1.gmt_create as publish_time,
        t1.work_order_no,
        t1.json_detail
        from notice_msg t1
        where 1=1
        and notice_type = 'system'
        and exists (
            select 1 from notice_read_log t2
            where t1.msg_id = t2.msg_id and t2.staff_id = #{filter.receiver}
        )
        <if test="filter.receiver != null and filter.receiver != ''">
            and t1.receiver = #{filter.receiver}
        </if>
        <if test="filter.type != null and filter.type != ''">
            and t1.type = #{filter.type}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t1.title like concat('%',#{filter.title},'%')
        </if>
        and t1.status = 1
        order by t1.gmt_create ${filter.sort}
    </select>
    <select id="selectUnReadNoticeByPage" resultType="com.welab.crm.operate.vo.notice.NoticeMsgVO">
        select
        t1.msg_id,
        t1.title,
        t1.sender,
        t1.file_name,
        t1.type,
        t1.gmt_create as publish_time,
        t1.work_order_no,
        t1.json_detail
        from notice_msg t1
        where 1=1
        and notice_type = 'system'
        and not exists (
        select 1 from notice_read_log t2
        where t1.msg_id = t2.msg_id and t2.staff_id = #{filter.receiver}
        )
        <if test="filter.receiver != null and filter.receiver != ''">
            and t1.receiver = #{filter.receiver}
        </if>
        <if test="filter.type != null and filter.type != ''">
            and t1.type = #{filter.type}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t1.title like concat('%',#{filter.title},'%')
        </if>
        and t1.status = 1
        order by t1.gmt_create ${filter.sort}
    </select>
    <select id="selectAllUnReadNotice" resultType="com.welab.crm.operate.domain.NoticeMsg">
        select
        *
        from notice_msg t1
        where 1=1
        and notice_type = 'system'
        and not exists (
        select 1 from notice_read_log t2
        where t1.msg_id = t2.msg_id and t2.staff_id = #{staffId}
        )
        <if test="staffId != null and staffId != ''">
            and t1.receiver = #{staffId}
        </if>
        <if test="type != null and type != ''">
            and t1.type = #{type}
        </if>
        and t1.status = 1
    </select>
    <select id="selectUnReadNoticeByTmkTaskId" resultType="java.lang.Integer">

        select count(*)
        from notice_msg t1
            left join notice_read_log t2 on t1.msg_id = t2.msg_id and t1.receiver = t2.staff_id
        where t1.work_order_no = #{tmkTaskId}
        and t2.id is null
    </select>

</mapper>
