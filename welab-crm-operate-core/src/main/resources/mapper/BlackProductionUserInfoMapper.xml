<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.BlackProductionUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.BlackProductionUserInfo">
        <id column="id" property="id" />
        <result column="uuid" property="uuid" />
        <result column="user_mobile" property="userMobile" />
        <result column="id_no" property="idNo" />
        <result column="apply_staff_id" property="applyStaffId" />
        <result column="apply_time" property="applyTime" />
        <result column="black_production_mobile" property="blackProductionMobile" />
        <result column="black_production_email" property="blackProductionEmail" />
        <result column="remark" property="remark" />
        <result column="approval_staff_id" property="approvalStaffId" />
        <result column="approval_time" property="approvalTime" />
        <result column="user_type" property="userType" />
        <result column="source" property="source" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="name" property="name" />
        <result column="approval_status" property="approvalStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uuid, user_mobile, id_no, apply_staff_id, apply_time, black_production_mobile, black_production_email, remark, approval_staff_id, approval_time, user_type, source, gmt_create, gmt_modify, name, approval_status
    </sql>
    <select id="queryBlackProductionPage"
            resultType="com.welab.crm.operate.vo.blackProduction.BlackProductionVO">
        select
        bpui.id,
        bpui.uuid,
        bpui.name,
        bpui.user_mobile ,
        iacs.login_name as applyStaff,
        bpui.apply_time ,
        bpui.black_production_mobile ,
        bpui.black_production_email ,
        (case
        when user_type = 1 then '确认黑产'
        else '疑似黑产'
        end) as userType,
        bpui.remark ,
        (case
        when source = 1 then 'AIF联盟'
        else '客服系统'
        end) as source,
        group_concat(bpa.file_name) as attachmentNames,
        bpui.id_no
        from
        black_production_user_info bpui
        left join in_auth_crm_staff iacs on
        bpui.apply_staff_id = iacs.id
        left join black_production_attachment bpa on
        bpui.id = bpa.user_info_id
        where
        1 = 1
        <if test="reqDTO.applyStart != null and reqDTO.applyStart != ''">
            and bpui.apply_time &gt;= #{reqDTO.applyStart}
        </if>
        <if test="reqDTO.applyEnd != null and reqDTO.applyEnd != ''">
            and bpui.apply_time &lt;= #{reqDTO.applyEnd}
        </if>
        <if test="reqDTO.uuid != null and reqDTO.uuid != ''">
            and bpui.uuid = #{reqDTO.uuid}
        </if>
        <if test="reqDTO.userMobile != null and reqDTO.userMobile != ''">
            and bpui.user_mobile = #{reqDTO.userMobile}
        </if>
        <if test="reqDTO.idNo != null and reqDTO.idNo != ''">
            and bpui.id_no = #{reqDTO.idNo}
        </if>
        <if test="reqDTO.blackProductionMobile != null and reqDTO.blackProductionMobile != ''">
            and bpui.black_production_mobile = #{reqDTO.blackProductionMobile}
        </if>
        <if test="reqDTO.blackProductionEmail != null and reqDTO.blackProductionEmail != ''">
            and bpui.black_production_email = #{reqDTO.blackProductionEmail}
        </if>
        <if test="reqDTO.userType != null">
            and bpui.user_type = #{reqDTO.userType}
        </if>
        <if test="reqDTO.source != null">
            and bpui.source = #{reqDTO.source}
        </if>
        <if test="reqDTO.approvalStatus != null">
            and bpui.approval_status = #{reqDTO.approvalStatus}
        </if>
        group by
        bpui.id
        order by bpui.id desc
    </select>

</mapper>
