<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkVipMapper">

    <select id="queryTmkVipPage" resultType="com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO">
        select t.* from
        (
        select
        t0.tmk_task_id,
        t0.username,
        t0.mobile,
        t0.member_pay_mode,
        t0.uuid,
        t0.order_no,
        t0.order_create_time as gmt_create,
        t0.source,
        (case
        when t0.order_status = '4' then '支付成功'
        when t0.order_status = '5' then '支付失败'
        when t0.order_status = '6' then '支付中'
        when t0.order_status = '-2' then '未下单'
        when t0.order_status = '0' then '未支付'
        end
        ) as order_status,
        t0.order_status as paymentStatus,
        t0.repay_message,
        c.cdr_start_time as lastCalledAt,
        tsd.summary_content as contact_result,
        tsd.result_code,
        ts.comment,
        t0.user_id,
        t0.assign_date as distributedAt,
        t0.payment_amount as orderAmount,
        t0.call_num
        from tmk_vip t0
        left join tmk_summary ts on t0.summary_id = ts.id and t0.staff_id = ts.staff_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where 1=1
        <if test="dto.username != null and dto.username != ''">
            and t0.username = #{dto.username}
        </if>
        <if test="dto.mobile != null and dto.mobile != ''">
            and t0.mobile = #{dto.mobile}
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and t0.uuid = #{dto.uuid}
        </if>
        <if test="dto.memberPayMode != null and dto.memberPayMode == 'pay_later'">
            and t0.member_pay_mode = #{dto.memberPayMode}
        </if>
        <if test="dto.memberPayMode != null and dto.memberPayMode == 'pay_first'">
            and (t0.member_pay_mode is null or t0.member_pay_mode = #{dto.memberPayMode})
        </if>
        <if test="dto.applicationId != null and dto.applicationId != ''">
            and t0.order_no = #{dto.applicationId}
        </if>
        <if test="dto.orderAmount != null and dto.orderAmount != ''">
            and t0.payment_amount = #{dto.orderAmount}
        </if>
        <if test="dto.gmtCreateStart != null and dto.gmtCreateStart != ''">
            and t0.gmt_create &gt;= #{dto.gmtCreateStart}
        </if>
        <if test="dto.gmtCreateEnd != null and dto.gmtCreateEnd != ''">
            and t0.gmt_create &lt;= #{dto.gmtCreateEnd}
        </if>
        <if test="dto.source != null and dto.source != ''">
            and t0.source like concat('%',#{dto.source},'%')
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            and t0.order_status = #{dto.orderStatus}
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and t0.uuid = #{dto.uuid}
        </if>
        <if test="dto.userId != null and dto.userId !=''">
            and t0.user_id = #{dto.userId}
        </if>
        <if test="dto.tmkTaskId != null and dto.tmkTaskId != ''">
            and t0.tmk_task_id = #{dto.tmkTaskId}
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "1"'>
            and ts.id is not null and ts.gmt_create &gt; t0.assign_date
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "0"'>
            and (ts.id is null or ts.gmt_create &lt; t0.assign_date)
        </if>
        <if test="dto.contactResultList != null and dto.contactResultList.size() >0">
            and tsd.result_code in
            <foreach collection="dto.contactResultList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.contactCode != null and dto.contactCode != ''">
            and tsd.summary_code = #{dto.contactCode}
        </if>
        <if test="dto.lastCalledAtStart != null and dto.lastCalledAtStart != ''">
            and c.cdr_start_time &gt;= #{dto.lastCalledAtStart}
        </if>
        <if test="dto.lastCalledAtEnd != null and dto.lastCalledAtEnd != ''">
            and c.cdr_start_time &lt;= #{dto.lastCalledAtEnd}
        </if>
        <if test="dto.distributedAtStart != null and dto.distributedAtStart != ''">
            and t0.assign_date &gt;= #{dto.distributedAtStart}
        </if>
        <if test="dto.distributedAtEnd != null and dto.distributedAtEnd != ''">
            and t0.assign_date &lt;= #{dto.distributedAtEnd}
        </if>
        <if test="dto.staffId != null and dto.staffId != ''">
            and t0.staff_id = #{dto.staffId}
        </if>
        <if test="dto.callNum != null and dto.callNum != ''">
            and t0.call_num = #{dto.callNum}
        </if>
        order by ts.gmt_create desc
        limit 100000
        ) t
        group by tmk_task_id
    </select>
    
    <select id="queryVipTransformData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO">
        select
        iacs.staff_name,
        iacs.group_name as groupCode,
        count(case when tt.month_diff &gt;= 0 then 1 else null end) as callOutCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 then 1 else null end) as answerCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 and tt.payment_at &gt;= #{dto.startTime} and
        tt.payment_at &lt; #{dto.endTime} and tt.conver_time > 0 and tt.order_status = '4' then 1 else null end) as transformCount,
        count(case when tt.month_diff &lt; 0 then 1 else null end) as hisCallOutCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 then 1 else null end) as hisAnswerCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 and tt.payment_at &gt;= #{dto.startTime} and
        tt.payment_at &lt; #{dto.endTime} and tt.conver_time > 0 and tt.order_status = '4' then 1 else null end) as hisTransformCount
        from (
        select
        t.*
        from
        (
        select
        ts.staff_id,
        cpci.cdr_start_time,
        tv.gmt_create,
        tv.order_status,
        tv.payment_at,
        tv.uuid,
        timestampdiff(second,cpci.cdr_start_time,tv.payment_at) as conver_time,
        (DATE_FORMAT(tv.assign_date, '%Y%m')-DATE_FORMAT(#{dto.endTime},'%Y%m')) as month_diff,
        (case when cpci.cdr_bridge_time is not null then 1 else 0 end) as callestablished
        from tmk_vip tv
        left join tmk_summary ts on tv.tmk_task_id = ts.tmk_task_id
        left join con_phone_call_info cpci on ts.cdr_main_unique_id = cpci.cdr_main_unique_id
        where 1 = 1
        and cpci.cdr_call_type = '4'
        and cpci.cdr_start_time between #{dto.startTime} and #{dto.endTime}
        and ts.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
            and cpci.staff_id in (
            <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
        and cpci.group_code in (
            <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        order by tv.uuid, callestablished desc, cpci.cdr_start_time
        limit 100000
        ) t group by t.uuid
        ) tt
        left join in_auth_crm_staff iacs on tt.staff_id = iacs.id
        group by tt.staff_id
        order by tt.staff_id
    </select>
	
	<select id="queryTmkCjhyData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkCjhyReportVO">
        select
        tv.user_id,
        tv.uuid,
        '超级会员' as tmkType,
        iacs.staff_name as staff_id,
        iacs.group_name as group_code,
        tsd.result_code,
        tsd.summary_content,
        ts.comment,
        ts.gmt_create as summaryAt,
        (case when tv.order_status = 4 then tv.payment_at end) as orderdAt,
        tv.order_status as orderdStatus,
        tv.init_order_status as pushStatus,
        tv.repay_message,
        tv.gmt_create,
        tv.member_pay_mode,
        tv.payment_amount,
        c.cdr_bridge_time as callStartTime,
        c.cdr_end_time as callEndTime,
        TIME_TO_SEC(c.cdr_end_bridge_time) as callTime
        from tmk_summary ts
        join tmk_vip tv on tv.tmk_task_id = ts.tmk_task_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join in_auth_crm_staff iacs on ts.staff_id = iacs.id
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where ts.gmt_create between #{dto.startTime} and #{dto.endTime}
            <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                and ts.staff_id in (
                <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
            <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                and iacs.group_code, in (
                <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
    </select>
</mapper>
