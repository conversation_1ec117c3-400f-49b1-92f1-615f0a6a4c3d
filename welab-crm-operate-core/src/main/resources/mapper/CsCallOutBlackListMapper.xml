<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsCallOutBlackListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsCallOutBlackList">
        <id column="id" property="id" />
        <result column="uuid" property="uuid" />
        <result column="cust_name" property="custName" />
        <result column="apply_staff_id" property="applyStaffId" />
        <result column="apply_time" property="applyTime" />
        <result column="apply_desc" property="applyDesc" />
        <result column="approval_status" property="approvalStatus" />
        <result column="valid_time" property="validTime" />
        <result column="valid_start_time" property="validStartTime" />
        <result column="valid_end_time" property="validEndTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="approval_staff_id" property="approvalStaffId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uuid, cust_name, apply_staff_id, apply_time, apply_desc, approval_status, valid_time, valid_start_time, valid_end_time, is_deleted, gmt_create, gmt_modify, approval_staff_id
    </sql>

</mapper>
