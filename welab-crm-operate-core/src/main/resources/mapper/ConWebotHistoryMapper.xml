<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConWebotHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ConWebotHistory">
        <id column="id" property="id" />
        <result column="uuid" property="uuid" />
        <result column="user_id" property="userId" />
        <result column="customer_name" property="customerName" />
        <result column="mobile" property="mobile" />
        <result column="validate_type" property="validateType" />
        <result column="token" property="token" />
        <result column="send_group" property="sendGroup" />
        <result column="send_user" property="sendUser" />
        <result column="send_time" property="sendTime" />
        <result column="validate_count" property="validateCount" />
        <result column="final_code" property="finalCode" />
        <result column="final_msg" property="finalMsg" />
        <result column="validate_time" property="validateTime" />
        <result column="vendor" property="vendor" />
        <result column="validate_code" property="validateCode" />
        <result column="validate_msg" property="validateMsg" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uuid, user_id, customer_name, mobile, validate_type, token, send_group, send_user, send_time, validate_count, final_code, final_msg, validate_time, vendor, validate_code, validate_msg, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>

    <select id="getDetailsByPage" resultType="com.welab.crm.operate.domain.ConWebotHistory">
        select <include refid="Base_Column_List"/>
        from con_webot_history
        where send_time &gt;= #{filter.startTime} and send_time &lt;= #{filter.endTime}
        <if test="filter.vendor != null and filter.vendor != ''">
            and vendor=#{filter.vendor}
        </if>
        <if test="filter.userId">
            and user_id=#{filter.userId}
        </if>
        <if test="filter.uuid != null and filter.uuid != ''">
            and uuid=#{filter.uuid}
        </if>
        <if test="filter.sendUser != null and filter.sendUser != ''">
            and send_user=#{filter.sendUser}
        </if>
        <if test="filter.sendGroup != null and filter.sendGroup != ''">
            and send_group=#{filter.sendGroup}
        </if>
    </select>

    <select id="getDayDataByPage" resultType="com.welab.crm.operate.vo.face.FaceDayVO">
        select f.sendTime as day,
        f.sendCount,
        f.validateCount,
        f.unValidateCount,
        f.successCount,
        f.avgValidateCount,
        (case when t.firstSuccessCount is null then 0 else t.firstSuccessCount end) as firstSuccessCount
        from (
        select date_format(send_time, '%Y-%m-%d') as sendTime ,
        count(distinct token) as sendCount,
        sum(case when final_code is not null then 1 else 0 end) as validateCount,
        sum(case when final_code is null then 1 else 0 end) as unValidateCount,
        sum(case when final_code = 0 and validate_code =0 then 1 else 0 end) as successCount,
        sum(case when final_code = 0 then 1 else 0 end) as avgValidateCount
        from con_webot_history
        where send_time &gt;= #{filter.startTime} and send_time &lt;= #{filter.endTime}
        <if test="filter.vendor != null and filter.vendor != ''">
            and vendor=#{filter.vendor}
        </if>
        group by date_format(send_time, '%Y-%m-%d') ) f left join
        (select s.sendTime , count(*) as firstSuccessCount from (
        select date_format(send_time, '%Y-%m-%d') as sendTime
        from con_webot_history
        where send_time &gt;= #{filter.startTime} and send_time &lt;= #{filter.endTime}
        <if test="filter.vendor != null and filter.vendor != ''">
            and vendor=#{filter.vendor}
        </if>
        and final_code =0
        group by date_format(send_time, '%Y-%m-%d'), token having count(*) = 1) s group by s.sendTime) t on f.sendTime=t.sendTime
    </select>

    <select id="getCauseByPage" resultType="com.welab.crm.operate.vo.face.FaceCauseVO">
        select validate_msg as failCause,
        count(1) as failCount
        from con_webot_history cwh
        where send_time &gt;= #{filter.startTime} and send_time &lt;= #{filter.endTime}
        <if test="filter.vendor != null and filter.vendor != ''">
            and vendor=#{filter.vendor}
        </if>
        and validate_code != 0 group by validate_msg
    </select>

    <select id="getCauseTotal" resultType="com.welab.crm.operate.vo.face.FaceCauseVO">
        select
        count(1) as failCount
        from con_webot_history cwh
        where send_time &gt;= #{filter.startTime} and send_time &lt;= #{filter.endTime}
        <if test="filter.vendor != null and filter.vendor != ''">
            and vendor=#{filter.vendor}
        </if>
        and validate_code != 0
    </select>

</mapper>
