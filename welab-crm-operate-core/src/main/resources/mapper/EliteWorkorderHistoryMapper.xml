<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.EliteWorkorderHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.EliteWorkorderHistory">
        <id column="id" property="id" />
        <result column="objective_guid" property="objectiveGuid" />
        <result column="customer_guid" property="customerGuid" />
        <result column="workorder_id" property="workorderId" />
        <result column="order_type" property="orderType" />
        <result column="step_name" property="stepName" />
        <result column="wo_type1" property="woType1" />
        <result column="wo_type2" property="woType2" />
        <result column="wo_type3" property="woType3" />
        <result column="wo_type4" property="woType4" />
        <result column="customer_name" property="customerName" />
        <result column="gender" property="gender" />
        <result column="mobile" property="mobile" />
        <result column="order_status" property="orderStatus" />
        <result column="create_time" property="createTime" />
        <result column="end_time" property="endTime" />
        <result column="comments" property="comments" />
        <result column="problem_desc" property="problemDesc" />
        <result column="remark" property="remark" />
        <result column="curr_group" property="currGroup" />
        <result column="curr_staff" property="currStaff" />
        <result column="last_staff" property="lastStaff" />
        <result column="last_deal_time" property="lastDealTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, objective_guid, customer_guid, workorder_id, order_type, step_name, wo_type1, wo_type2, wo_type3, wo_type4, customer_name, gender, mobile, order_status, create_time, end_time, comments, problem_desc, remark, curr_group, curr_staff, last_staff, last_deal_time
    </sql>

</mapper>
