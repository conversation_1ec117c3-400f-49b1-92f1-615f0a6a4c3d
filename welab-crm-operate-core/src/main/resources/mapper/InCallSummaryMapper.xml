<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.InCallSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.InCallSummary">
        <id column="id" property="id" />
        <result column="staff_mobile" property="staffMobile" />
        <result column="description" property="description" />
        <result column="last_dict_id" property="lastDictId" />
        <result column="top_status" property="topStatus" />
        <result column="top_time" property="topTime" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_mobile, description, last_dict_id, top_status, top_time, gmt_create, gmt_modify
    </sql>

</mapper>
