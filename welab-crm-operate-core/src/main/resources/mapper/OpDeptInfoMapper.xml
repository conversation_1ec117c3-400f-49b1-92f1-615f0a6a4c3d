<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpDeptInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpDeptInfo">
        <id column="id" property="id" />
        <result column="application_id" property="applicationId" />
        <result column="company_name" property="companyName" />
        <result column="company_tel" property="companyTel" />
        <result column="dept_date" property="deptDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_id, company_name, company_tel, dept_date
    </sql>

</mapper>
