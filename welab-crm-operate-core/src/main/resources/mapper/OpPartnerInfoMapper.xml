<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpPartnerInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpPartnerInfo">
        <id column="id" property="id" />
        <result column="partner_name" property="partnerName" />
        <result column="institution_number" property="institutionNumber" />
        <result column="loan_card" property="loanCard" />
        <result column="system_repay_time_normal" property="systemRepayTimeNormal" />
        <result column="grace_period" property="gracePeriod" />
        <result column="h5_repay" property="h5Repay" />
        <result column="early_repay_each_installment" property="earlyRepayEachInstallment" />
        <result column="early_settlement_msg" property="earlySettlementMsg" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="settlement_proof" property="settlementProof" />
        <result column="quota_status" property="quotaStatus" />
        <result column="send_status" property="sendStatus" />
        <result column="invoice_issue" property="invoiceIssue" />
        <result column="close_get" property="closeGet" />
        <result column="invoice_get" property="invoiceGet" />
        <result column="close_quota" property="closeQuota" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, partner_name, institution_number, loan_card, system_repay_time_normal, grace_period, h5_repay, early_repay_each_installment, early_settlement_msg, create_time, update_time, settlement_proof, quota_status, send_status, invoice_issue, close_get, invoice_get, close_quota
    </sql>

</mapper>
