<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.LoanCancelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.LoanCancel">
        <id column="id" property="id" />
        <result column="contract_no" property="contractNo" />
        <result column="approve_comment" property="approveComment" />
        <result column="approve_state" property="approveState" />
        <result column="auditor" property="auditor" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_no, approve_comment, approve_state, auditor, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>

</mapper>
