<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.IvrSummaryMapper">





    <select id="getIvrDetailByDict" resultType="com.welab.crm.operate.vo.ivr.IvrKeyDetailVOPlus">
        select
        t1.id as recordId,
        date_format(t0.cdr_start_time , '%Y-%m-%d') as callDate,
        t4.customer_name as username,
        t0.cdr_customer_number as mobile,
        t4.uuid,
        t4.user_id,
        time_format(t0.cdr_start_time, '%H:%i:%s') as callTime,
        time_format(t1.start_time, '%H:%i:%s') as keyStartTime,
        time_format(t0.cdr_bridge_time, '%H:%i:%s') as csAnswerTime,
        time_format(t0.cdr_end_time, '%H:%i:%s')as hangUpTime,
        TIMESTAMPDIFF(SECOND,t0.cdr_bridge_time, t0.cdr_end_time) as talkDuration,
        t2.staff_name,
        t5.id_no as cno,
        <if test="list != null and list.size() > 0">
            <foreach collection="list" index="index" separator="," open="" close="" item="item">
                sum(Case When t1.press_key = #{item.pressKey} and t0.cdr_hotline = #{item.hotline} Then 1 else 0 End) as ${item.code}
            </foreach>
        </if>
        from con_phone_call_info t0
        left join ivr_summary t1 on t0.cdr_main_unique_id = t1.cdr_main_unique_id
        left join in_auth_crm_staff t2 on t0.staff_id = t2.id
        left join in_phone_login_info t5 on t2.login_name = t5.user_tel
        left join (
            select dc.uuid,dc.customer_name,dc.user_id,dc.mobile from data_customer dc
            group by mobile
        ) t4 on t4.mobile = t0.cdr_customer_number
        where 1=1
        and t0.cdr_call_type = '1'
        and t0.cdr_start_time &gt;= #{dto.startTime}
        and t0.cdr_start_time &lt;= #{dto.endTime}
        and t0.cdr_hotline = #{dto.hotline}
        and t1.gmt_create &gt;= #{dto.startTime}
        and t1.gmt_create &lt;= #{dto.endTime}
        group by t1.cdr_main_unique_id
        order by t1.start_time
    </select>
    <select id="countIvrDetailByDict" resultType="com.welab.crm.operate.vo.ivr.IvrKeyDetailVOPlus">
        select
        '合计' as recordId,
        <if test="list != null and list.size() > 0">
            <foreach collection="list" item="item" index="index" open="" close="" separator=",">
                count(Case When t1.press_key = #{item.pressKey}Then 1 End) as ${item.code}
            </foreach>
        </if>
        from ivr_summary t1
        where 1=1
        and t1.gmt_create &gt;= #{dto.startTime}
        and t1.gmt_create &lt;= #{dto.endTime}
    </select>
    <select id="getIvrKeyTotalByDict" resultType="com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVOPlus">

        select t.*,
        <if test="list != null and list.size() > 0">
            <foreach collection="list" index="index" separator="+" item="item" open="(" close=")">
                    t.${item.code}
            </foreach>
            as total
        </if>
        from
        (select
        <if test="dto.period == 'range'">
            '统计' as callTime,
        </if>
        <if test="dto.period == 'day'">
            date_format(t1.cdr_start_time,'%Y-%m-%d') as callTime,
        </if>
        <if test="list != null and list.size() > 0">
            <foreach collection="list" index="index" separator="," item="item" open="" close="">
                ifnull(sum(case when t0.press_key = #{item.pressKey} and t1.cdr_hotline = #{item.hotline} then 1 else 0 end),0) as ${item.code}
            </foreach>
        </if>
        from ivr_summary t0
        left join con_phone_call_info t1 on t0.cdr_main_unique_id = t1.cdr_main_unique_id
        where 1=1
        and t1.cdr_call_type = '1'
        and t1.cdr_start_time &gt;= #{dto.startTime}
        and t1.cdr_start_time &lt;= #{dto.endTime}
        and t0.gmt_create &gt;= #{dto.startTime}
        and t0.gmt_create &lt;= #{dto.endTime}
        and t1.cdr_hotline in
        (
        <foreach collection="dto.hotlineList" item="item" close="" open="" index="index" separator=",">#{item}
        </foreach>
        )
        group by callTime
        ) t
    </select>
    <select id="getIvrArtificialPro" resultType="com.welab.crm.operate.vo.ivr.IvrCountVO">
        select ifnull(count(*),0) as ivrCount,
        <if test = "dto.period == 'range'">
            '统计' as callTime
        </if>
        <if test = "dto.period == 'day'">
            date_format(tt.cdr_start_time,'%Y-%m-%d') as callTime
        </if>
        from (
        select t.* from
        (select
          t2.press_key,t1.cdr_main_unique_id,t1.cdr_start_time
        from
        ivr_summary t0 force index (ivr_summary_gmt_create_IDX)
        left join con_phone_call_info t1 on t0.cdr_main_unique_id = t1.cdr_main_unique_id
        left join ivr_summary t2 on t0.cdr_main_unique_id = t2.cdr_main_unique_id
        where 1=1
        and t1.cdr_start_time between #{dto.startTime} and #{dto.endTime}
        and t0.gmt_create between #{dto.startTime} and #{dto.endTime}
        and t2.gmt_create between #{dto.startTime} and #{dto.endTime}
        and t1.cdr_call_type = '1'
        and t0.start_time > t2.start_time
        and t1.cdr_hotline = #{dto.hotline}
        and t0.press_key in
        (
            <foreach collection="dto.artificialKey" item="item" open="" close="" separator="," index="index">
                #{item}
            </foreach>
        )
        order by t2.start_time desc
        limit 1000000 )t group by t.cdr_main_unique_id
        ) tt where tt.press_key = #{dto.notArtificialKey} group by callTime
    </select>
    <select id="queryDhfwArtificialCount" resultType="com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVO">
        select
        <if test = "dto.period == 'range'">
            '统计' as callTime,
        </if>
        <if test = "dto.period == 'day'">
            date_format(cpci.cdr_start_time,'%Y-%m-%d') as callTime,
        </if>
            ifnull(sum(case when cpci.cdr_bridge_time is not null then 1 else 0 end), 0) as dhfw
        from
            ivr_summary is2
                join con_phone_call_info cpci on
                is2.cdr_main_unique_id = cpci.cdr_main_unique_id
        where
            is2.gmt_create between #{dto.startTime} and #{dto.endTime}
          and cpci.cdr_start_time between #{dto.startTime} and #{dto.endTime}
          and is2.press_key = '1.1.2'
          and cpci.cdr_hotline = '10100518'
        group by
            1;
    </select>
    <select id="getPhoneInfo" resultType="com.welab.crm.operate.vo.ivr.IvrKeyDetailVOPlus">

        select
        t0.cdr_main_unique_id as recordId,
        date_format(t0.cdr_start_time , '%Y-%m-%d') as callDate,
        t4.customer_name as username,
        t0.cdr_customer_number as mobile,
        t4.uuid,
        t4.user_id,
        time_format(t0.cdr_start_time, '%H:%i:%s') as callTime,
        time_format(t0.cdr_bridge_time, '%H:%i:%s') as csAnswerTime,
        time_format(t0.cdr_end_time, '%H:%i:%s')as hangUpTime,
        TIMESTAMPDIFF(SECOND,t0.cdr_bridge_time, t0.cdr_end_time) as talkDuration,
        t2.staff_name,
        t5.id_no as cno
        from con_phone_call_info t0
        left join in_auth_crm_staff t2 on t0.staff_id = t2.id
        left join in_phone_login_info t5 on t2.login_name = t5.user_tel
        left join data_customer t4 on t4.id = (
        select dc.id from data_customer dc where dc.mobile = t0.cdr_customer_number order by gmt_modify desc limit 1
        )
        where 1=1
        and t0.cdr_call_type = '1'
        and t0.cdr_start_time &gt;= #{dto.startTime}
        and t0.cdr_start_time &lt;= #{dto.endTime}
        and t0.cdr_hotline = #{dto.hotline}
        group by t0.cdr_main_unique_id
        order by t0.cdr_start_time
    </select>
    <select id="queryIvrDetailByCondition" resultType="com.welab.crm.operate.vo.ivr.IvrKeyDetailVOPlus">
        select
        t1.cdr_main_unique_id as recordId,
        time_format(t1.start_time, '%H:%i:%s') as keyStartTime,
        <if test="list != null and list.size() > 0">
            <foreach collection="list" index="index" separator="," open="" close="" item="item">
                sum(Case When t1.press_key = #{item.pressKey} Then 1 else 0 End) as ${item.code}
            </foreach>
        </if>
        from ivr_summary t1 
        where 1=1
        and t1.gmt_create &gt;= #{dto.startTime}
        and t1.gmt_create &lt;= #{dto.endTime}
        group by t1.cdr_main_unique_id

    </select>
    <select id="queryIvrDetailByMainId" resultType="com.welab.crm.operate.vo.ivr.IvrKeyDetailVOPlus">
        select
        t1.cdr_main_unique_id as recordId,
        time_format(t1.start_time, '%H:%i:%s') as keyStartTime,
        <if test="list != null and list.size() > 0">
            <foreach collection="list" index="index" separator="," open="" close="" item="item">
                sum(Case When t1.press_key = #{item.pressKey} Then 1 else 0 End) as ${item.code}
            </foreach>
        </if>
        from ivr_summary t1
        where 1=1
        and t1.cdr_main_unique_id in 
        <foreach collection="idList" item="item" open="(" close=")" separator="," index="index">
            #{item}
        </foreach>
        group by t1.cdr_main_unique_id
    </select>

</mapper>
