<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkInterceptHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.TmkInterceptHistory">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="intercept_date" property="interceptDate" />
        <result column="num" property="num" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, intercept_date, num, gmt_create, gmt_modify
    </sql>

</mapper>
