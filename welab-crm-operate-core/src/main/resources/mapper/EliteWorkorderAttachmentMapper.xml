<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.EliteWorkorderAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.EliteWorkorderAttachment">
        <id column="id" property="id" />
        <result column="customer_guid" property="customerGuid" />
        <result column="create_date" property="createDate" />
        <result column="file_name" property="fileName" />
        <result column="path" property="path" />
        <result column="file_desc" property="fileDesc" />
        <result column="staff_name" property="staffName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_guid, create_date, file_name, path, file_desc, staff_name
    </sql>

</mapper>
