<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkLoanInviteMapper">

    <!-- t0.ai_push_flag, -->
    <select id="queryLoanInviteTaskPage" resultType="com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO">
        select t.*
        from (
            select
            t0.tmk_task_id,
            t0.mobile,
            t0.username,
            t0.application_id,
            t0.product_code,
            t0.product_name,
            t0.apply_origin,
            t0.tenor,
            t0.approved_at,
            c.cdr_start_time as lastCalledAt,
            tsd.summary_content as contact_result,
            tsd.result_code,
            ts.comment,
            (case
                when t0.state = 'aip' then '未转化'
                when t0.state = 'confirmed' then '已转化'
            end
            ) as state,
            t0.blocked,
            t0.register_at,
            t0.reg_origin,
            t0.applied_amount,
            t0.applied_tenor,
            t0.applied_at,
            t0.amount,
            t0.user_id,
            t0.uuid,
            t0.assign_date as distributedAt,
            t0.last_sign_in_at,
            t0.avl_credit,
            t0.credit_line,
            t0.credit_state as creditStatus,
            t0.call_num,
            t0.diversion_tag,
            t0.ai_push_time
            from tmk_loan_invite t0
            left join tmk_summary ts on t0.summary_id = ts.id and t0.staff_id = ts.staff_id
            left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
            left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where 1=1
            <if test="dto.tmkType != null and dto.tmkType != ''">
                and t0.type = #{dto.tmkType}
            </if>
            <if test="dto.mobile != null and dto.mobile != ''">
                and t0.mobile = #{dto.mobile}
            </if>
            <if test="dto.username != null and dto.username != ''">
                and t0.username = #{dto.username}
            </if>
            <if test="dto.applicationId != null and dto.applicationId != ''">
                and t0.application_id = #{dto.applicationId}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and t0.product_name like concat('%',#{dto.productName},'%')
            </if>
            <if test="dto.approvedAtStart != null and dto.approvedAtStart != ''">
                and t0.approved_at &gt;= #{dto.approvedAtStart}
            </if>
            <if test="dto.approvedAtEnd != null and dto.approvedAtStart != ''">
                and t0.approved_at &lt;= #{dto.approvedAtEnd}
            </if>
            <if test="dto.source != null and dto.source != ''">
                and t0.apply_origin like concat('%',#{dto.source},'%')
            </if>
            <if test="dto.state != null and dto.state != ''">
                and t0.state = #{dto.state}
            </if>
            <if test="dto.userId != null and dto.userId !=''">
                and t0.user_id = #{dto.userId}
            </if>
            <if test="dto.uuid != null and dto.uuid != ''">
                and t0.uuid = #{dto.uuid}
            </if>
            <if test="dto.tmkTaskId != null and dto.tmkTaskId != ''">
                and t0.tmk_task_id = #{dto.tmkTaskId}
            </if>
            <if test='dto.callStatus != null and dto.callStatus == "1"'>
                and ts.id is not null and ts.gmt_create &gt; t0.assign_date
            </if>
            <if test='dto.callStatus != null and dto.callStatus == "0"'>
                and (ts.id is null or ts.gmt_create &lt; t0.assign_date)
            </if>
            <if test="dto.contactResultList != null and dto.contactResultList.size() >0">
                and tsd.result_code in
                <foreach collection="dto.contactResultList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.contactCode != null and dto.contactCode != ''">
                and tsd.summary_code = #{dto.contactCode}
            </if>
            <if test="dto.lastCalledAtStart != null and dto.lastCalledAtStart != ''">
                and c.cdr_start_time &gt;= #{dto.lastCalledAtStart}
            </if>
            <if test="dto.lastCalledAtEnd != null and dto.lastCalledAtEnd != ''">
                and c.cdr_start_time &lt;= #{dto.lastCalledAtEnd}
            </if>
            <if test="dto.distributedAtStart != null and dto.distributedAtStart != '' ">
                and t0.assign_date &gt;= #{dto.distributedAtStart}
            </if>
            <if test="dto.distributedAtEnd != null and dto.distributedAtEnd != ''">
                and t0.assign_date &lt;= #{dto.distributedAtEnd}
            </if>
            <if test="dto.staffId != null and dto.staffId != ''">
                and t0.staff_id = #{dto.staffId}
            </if>
            <if test="dto.creditStatus != null and dto.creditStatus != ''">
                and t0.credit_state = #{dto.creditStatus}
            </if>
            <if test="dto.avlCredit != null and dto.avlCredit != ''">
                and t0.avl_credit &gt;= #{dto.avlCredit}
            </if>
            <if test="dto.avlCreditStart != null and dto.avlCreditStart != ''">
                and t0.avl_credit &gt;= #{dto.avlCreditStart}
            </if>
            <if test="dto.avlCreditEnd != null and dto.avlCreditEnd != ''">
                and t0.avl_credit &lt;= #{dto.avlCreditEnd}
            </if>
            <if test="dto.callNum != null and dto.callNum != ''">
                and t0.call_num = #{dto.callNum}
            </if>
            order by ts.gmt_create desc
            limit 100000
        )t
        group by tmk_task_id

    </select>
    <select id="queryLoanInviteTransformData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO">
        select
        iacs.staff_name,
        iacs.group_name as groupCode,
        count(case when tt.month_diff &gt;= 0 then 1  else null end) as callOutCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 then 1 else null end) as answerCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 and tt.confirmed_at &gt;= #{dto.startTime} and tt.confirmed_at &lt; #{dto.endTime} and tt.conver_time > 0 then 1  else null end) as transformCount,
        count(case when tt.month_diff &lt; 0 then 1  else null end) as hisCallOutCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 then 1 else null end) as hisAnswerCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 and tt.confirmed_at &gt;= #{dto.startTime} and tt.confirmed_at &lt; #{dto.endTime} and tt.conver_time > 0 then 1  else null end) as hisTransformCount
        from (
            select
            t.*
            from
            (
                select
                ts.staff_id,
                cpci.cdr_start_time,
                tli.gmt_create,
                tli.confirmed_at,
                tli.application_id,
                timestampdiff(second,
                cpci.cdr_start_time,
                tli.confirmed_at) as conver_time,
                (DATE_FORMAT(tli.assign_date, '%Y%m')-DATE_FORMAT(#{dto.endTime},'%Y%m')) as month_diff,
                (case when cpci.cdr_bridge_time is not null then 1 else 0 end) as callestablished
                from tmk_loan_invite tli
                left join tmk_summary ts on tli.tmk_task_id = ts.tmk_task_id
                left join con_phone_call_info cpci on ts.cdr_main_unique_id = cpci.cdr_main_unique_id
                where 1 = 1
                and tli.type = #{dto.tmkType}
                and cpci.cdr_call_type = '4'
                and cpci.cdr_start_time between #{dto.startTime} and #{dto.endTime}
                and ts.gmt_create between #{dto.startTime} and #{dto.endTime}
                <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                    and cpci.staff_id in (
                    <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">#{item}
                    </foreach>
                    )
                </if>
                <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                    and cpci.group_code in (
                    <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">#{item}
                    </foreach>
                    )
                </if>
                order by tli.
                application_id, callestablished desc, cpci.cdr_start_time
                limit 100000
            ) t group by t.application_id
        ) tt
        left join in_auth_crm_staff iacs on tt.staff_id = iacs.id
        group by tt.staff_id
        order by tt.staff_id

    </select>
    <select id="queryAiPushDetail" resultType="com.welab.crm.operate.vo.telemarketing.AiPushReportVO">
        select
        atc.id,
        tli.user_id,
        tli.uuid,
        atc2.rule_name as taskName,
        atc.call_time,
        atc.phone_status as contactResult,
        tli.applied_at,
        tli.confirmed_at,
        tli.confirmed_amount,
        atc.bill_sec,
        atc.import_time
        from tmk_loan_invite tli
        join ai_tmk_callback atc on tli.tmk_task_id = atc.tmk_task_id
        join ai_tmk_config atc2 on atc.config_id = atc2.id
        where atc.import_time between #{dto.startTime} and #{dto.endTime}

    </select>
	
	<select id="queryTmkLoanData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkLoanReportVO">
        select
        tli.user_id,
        tli.uuid,
        tli.application_id,
        tli.apply_origin,
        tli.product_name,
        tli.approved_at,
        tli.state,
        tli.amount,
        iacs.staff_name as staff_id,
        iacs.group_name as group_code,
        tsd.result_code,
        tsd.summary_content,
        ts.comment,
        ts.gmt_create as summaryAt,
        tli.confirmed_at,
        TIMESTAMPDIFF(second, ts.gmt_create, tli.confirmed_at) as timeDif,
        tli.gmt_create,
        tli.diversion_tag,
        tli.confirmed_amount,
        c.cdr_bridge_time as callStartTime,
        c.cdr_end_time as callEndTime,
        TIME_TO_SEC(c.cdr_end_bridge_time) as callTime
        from tmk_summary ts
        join tmk_loan_invite tli on tli.tmk_task_id = ts.tmk_task_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join in_auth_crm_staff iacs on ts.staff_id = iacs.id
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where tli.type = #{dto.tmkType}
            and ts.gmt_create between #{dto.startTime} and #{dto.endTime}
            <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                and ts.staff_id in (
                <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
            <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                and iacs.group_code in (
                <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
    </select>
    
    <select id="queryTmkAssignDetailData" resultType="com.welab.crm.operate.vo.telemarketing.TmkAssignDetailReportVO">
        select  t.user_id,
        		t.uuid,
        		t.product_name,
        		t.apply_origin,
        		t.gmt_create,
        		'已下发' as status,
        		t.distribution_type,
        		t.distribution_time,
        		s.group_name as group_code,
        		s.staff_name as staff_id,
        		t1.diversion_tag,
        		tu.package_define
	        FROM tmk_assign_history t
	        left join in_auth_crm_staff s on t.staff_id = s.id
            left join tmk_loan_invite t1 on t.tmk_task_id = t1.tmk_task_id
            left join tmk_uuid tu on t.tmk_task_id = tu.tmk_task_id
        where t.distribution_time between #{dto.startTime} and #{dto.endTime}
	        <if test="dto.tmkTypeList != null and dto.tmkTypeList.size() > 0">
                and t.distribution_type in (
                <foreach collection="dto.tmkTypeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
	        <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                and t.staff_id in (
                <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
            <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                and t.group_code in (
                <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
	    </select>
</mapper>
