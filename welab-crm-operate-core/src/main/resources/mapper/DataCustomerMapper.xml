<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.DataCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.DataCustomer">
        <id column="id" property="id" />
        <result column="cnid" property="cnid" />
        <result column="customer_name" property="customerName" />
        <result column="address" property="address" />
        <result column="family_address" property="familyAddress" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="mobile" property="mobile" />
        <result column="uuid" property="uuid" />
        <result column="user_id" property="userId" />
        <result column="company_name" property="companyName" />
        <result column="company_address" property="companyAddress" />
        <result column="creditline" property="creditline" />
        <result column="avl_creditline" property="avlCreditline" />
        <result column="create_by" property="createBy" />
        <result column="modify_by" property="modifyBy" />
        <result column="last_contact_date" property="lastContactDate" />
        <result column="degree" property="degree" />
        <result column="vip" property="vip" />
        <result column="register_time" property="registerTime" />
        <result column="register_origin" property="registerOrigin" />
        <result column="cust_type" property="custType" />
        <result column="bank_date" property="bankDate" />
        <result column="elec_card_no" property="elecCardNo" />
        <result column="is_alipay_auth" property="isAlipayAuth" />
        <result column="is_carrier_auth" property="isCarrierAuth" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="is_zx" property="isZx" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cnid, customer_name, address, family_address, age, gender, mobile, uuid, user_id, company_name, company_address, creditline, avl_creditline, create_by, modify_by, last_contact_date, degree, vip, register_time, register_origin, cust_type, bank_date, elec_card_no, is_alipay_auth, is_carrier_auth, gmt_create, gmt_modify, is_zx
    </sql>
    <select id="queryByUserIdList" resultType="com.welab.crm.operate.domain.DataCustomer">
        select user_id,uuid,customer_name from data_customer dc
        where user_id in
        <foreach collection="userIdList" item="item" open="(" close=")" index="index" separator=",">
            #{item}
        </foreach>
        group by user_id
    </select>

</mapper>
