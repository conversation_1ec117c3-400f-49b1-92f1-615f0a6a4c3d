<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpHttpLogRecordMapper">


    <select id="queryStaffMonitorReportPage"
            resultType="com.welab.crm.operate.vo.monitor.StaffMonitorReportVO">
        <include refid="queryReport"/>
    </select>
    <select id="queryAvgCount" resultType="com.welab.crm.operate.vo.monitor.StaffMonitorReportVO">
        select queryDate,
        groupCode,
        round(avg(userDetailQueryCount)) as userDetailQueryCount,
        round(avg(queryUserCount)) as queryUserCount,
        round(avg(mobileDecodeCount)) as mobileDecodeCount,
        round(avg(loanAgreementQueryCount)) as loanAgreementQueryCount,
        round(avg(soundQueryCount)) as soundQueryCount
        from (
        <include refid="queryReport"/>
        )t
        group by queryDate,groupCode
    </select>
    <select id="queryStaffMonitorReportList"
            resultType="com.welab.crm.operate.vo.monitor.StaffMonitorReportVO">
        <include refid="queryReport"/>
    </select>


    <sql id="queryReport">
        select
        <if test="dto.period == 'day'">
            date_format(l.create_time,'%Y-%m-%d') as queryDate,
        </if>
        <if test="dto.period == 'range'">
            concat(#{dto.startTime},' ~ ',#{dto.endTime}) as queryDate,
        </if>
        s.group_name as groupCode,
        ifnull(s.staff_name,l.login_name) as staffName,
        ifnull(sum(case when l.uuid is not null then 1 end),0) as userDetailQueryCount,
        ifnull(count(distinct l.uuid),0) as queryUserCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/user/decode' then 1 end ),0) as mobileDecodeCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/loan/application/agreement' then 1 end ),0) as
        loanAgreementQueryCount,
        ifnull(sum(case when request_path = '/welab-crm-operate/v1/file/query-by-id' then 1 end ),0) as soundQueryCount
        from
        op_http_log_record l
        left join in_auth_crm_staff s on
        l.login_name = s.login_name and s.is_status = 1
        where
        l.create_time between #{dto.startTime} and #{dto.endTime}
        <if test="dto.groupCodes != null and dto.groupCodes.size() > 0">
            and s.group_code in
            <foreach collection="dto.groupCodes" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
        group by
        queryDate,l.login_name
        order by queryDate,group_name
    </sql>
</mapper>
