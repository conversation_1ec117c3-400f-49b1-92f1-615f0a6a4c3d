<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoReportMapper">

    <select id="selectSummaryThirdType" parameterType="com.welab.crm.operate.dto.report.ReportSummaryTypeDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportSummaryTypeVO">
        select
            odic.wo_type_detail as type,
            odic.wo_type_fir_detail as firstType,
            odic.wo_type_sec_detail as secondType,
            odic.wo_type_thir_detail as thirdType,
            count(odic.wo_type_thir_id) as number,
            ifnull(sum((case when whe.complete_flag = '1' then 1 else 0 end)),0) as endCount,
            ifnull(sum((case when wre.complete_flag = '0' then 1 else 0 end)),0) as notEndCount
        from op_dict_info_conf odic
        inner join wo_task wt on
            odic.wo_type_id=wt.type
            and odic.wo_type_fir_id=wt.order_one_class
            and odic.wo_type_sec_id=wt.order_two_class
            and odic.wo_type_thir_id=wt.order_three_class
        left join wf_his_execution whe on wt.order_no = whe.busi_key
        left join wf_ru_execution wre on wt.order_no = wre.busi_key
        where 1=1
            and wt.gmt_create &gt;= #{startTime}
            and wt.gmt_create &lt;= #{endTime}
            <if test="type!=null and type!=''">
              and odic.wo_type_id=#{type}
            </if>
            <if test="firstType!=null and firstType!=''">
              and odic.wo_type_fir_id=#{firstType}
            </if>
            <if test="secondType!=null and secondType!=''">
              and odic.wo_type_sec_id=#{secondType}
            </if>
            <if test="thirdType!=null and thirdType!=''">
              and odic.wo_type_thir_id=#{thirdType}
            </if>
        group by odic.wo_type_id, odic.wo_type_fir_id, odic.wo_type_sec_id, odic.wo_type_thir_id
    </select>


    <select id="selectSummaryDetailsComplaint" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportSummaryDetailsVO">
    <foreach collection="list" open="" close="" item="item" separator="union all" index="index">
        select #{item.content} as type, ifnull(sum(number),0) as number, ifnull(sum(closed),0) as closedNumber,
        concat(ifnull(round((sum(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
        select count(1) as number,sum(case when whe.complete_flag=1 then 1 else 0 end) closed
        from wo_task wt inner join (
        select odic.wo_type_id,odic.wo_type_detail from op_dict_info_conf odic group by odic.wo_type_id
        ) t on t.wo_type_id=wt.type
        left join wf_his_execution  whe on whe.busi_key=wt.order_no
        where t.wo_type_detail= #{item.content}
        and wt.gmt_create &gt;= #{dto.startTime}
        and wt.gmt_create &lt;= #{dto.endTime}
        group by t.wo_type_detail
        ) t1
    </foreach>
    </select>
    <select id="selectTotalNumber" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportSummaryVO">
        select '工单总量' as type, ifnull(sum(number),0) as totalNumber, ifnull(sum(closed),0) as closedNumber,
            concat(ifnull(round((sum(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
            select count(1) as number,sum(case when whe.complete_flag=1 then 1 else 0 end) closed
            from wo_task wt inner join (
                select odic.wo_type_id,odic.wo_type_detail from op_dict_info_conf odic group by odic.wo_type_id
            ) t on t.wo_type_id=wt.type
            left join wf_his_execution whe on whe.busi_key=wt.order_no
            where 1=1
                and wt.gmt_create &gt;= #{startTime}
                and wt.gmt_create &lt;= #{endTime}
            group by t.wo_type_detail
        ) n
    </select>

    <select id="selectNumber4Type" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportSummaryVO">
        <foreach collection="list" index="index" item="item" open="" close="" separator="union all">
            select concat(#{item.content},'总量') as type, ifnull(sum(number),0) as totalNumber, ifnull(sum(closed),0) as closedNumber,
            concat(ifnull(round((sum(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
            from (
            select count(1) as number,sum(case when whe.complete_flag = '1' then 1 else 0 end) closed
            from wo_task wt inner join (
            select odic.wo_type_id,odic.wo_type_detail
            from op_dict_info_conf odic
            where odic.wo_type_detail=#{item.content}
            group by odic.wo_type_id
            ) t on t.wo_type_id=wt.type
            left join wf_his_execution whe on whe.busi_key=wt.order_no
            where 1=1
            and wt.gmt_create &gt;= #{dto.startTime}
            and wt.gmt_create &lt;= #{dto.endTime}
            group by t.wo_type_id
            ) t1
        </foreach>
    </select>

    <select id="selectSummaryDetailsProduct" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportSummaryDetailsVO">
        select '现金分期' as type, ifnull(sum(number),0) as number, ifnull(max(closed),0) as closedNumber,
          concat(ifnull(round((max(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
          select count(1) as number,(case when whe.complete_flag=1 then count(1) else 0 end) closed
          from wo_task wt inner join (
            select odic.wo_type_id,odic.wo_type_detail,odic.wo_type_fir_id,odic.wo_type_fir_detail
            from op_dict_info_conf odic
            where 1=1
            and odic.wo_type_fir_detail='现金分期'
            <if test="type!=null and type!=''">
                and odic.wo_type_detail=#{type}
            </if>
            group by odic.wo_type_id,odic.wo_type_fir_id
          ) t on t.wo_type_id=wt.type and t.wo_type_fir_id=wt.order_one_class
          left join wf_his_execution  whe on whe.busi_key=wt.order_no
          where 1=1
          and wt.gmt_create &gt;= #{cond.startTime}
          and wt.gmt_create &lt;= #{cond.endTime}
          group by t.wo_type_fir_id,whe.complete_flag
        ) t1
        union all
        select '钱夹谷谷' as type, ifnull(sum(number),0) as number, ifnull(max(closed),0) as closedNumber,
          concat(ifnull(round((max(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
          select count(1) as number,(case when whe.complete_flag=1 then count(1) else 0 end) closed
          from wo_task wt inner join (
            select odic.wo_type_id,odic.wo_type_detail,odic.wo_type_fir_id,odic.wo_type_fir_detail
            from op_dict_info_conf odic
            where 1=1
            and odic.wo_type_fir_detail='钱夹谷谷'
            <if test="type!=null and type!=''">
                and odic.wo_type_detail=#{type}
            </if>
            group by odic.wo_type_id,odic.wo_type_fir_id
          ) t on t.wo_type_id=wt.type and t.wo_type_fir_id=wt.order_one_class
          left join wf_his_execution  whe on whe.busi_key=wt.order_no
          where 1=1
          and wt.gmt_create &gt;= #{cond.startTime}
          and wt.gmt_create &lt;= #{cond.endTime}
          group by t.wo_type_fir_id,whe.complete_flag
        ) t2
        union all
        select '淘新机' as type, ifnull(sum(number),0) as number, ifnull(max(closed),0) as closedNumber,
        concat(ifnull(round((max(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
        select count(1) as number,(case when whe.complete_flag=1 then count(1) else 0 end) closed
        from wo_task wt inner join (
        select odic.wo_type_id,odic.wo_type_detail,odic.wo_type_fir_id,odic.wo_type_fir_detail
        from op_dict_info_conf odic
        where 1=1
        and odic.wo_type_fir_detail='淘新机'
        <if test="type!=null and type!=''">
            and odic.wo_type_detail=#{type}
        </if>
        group by odic.wo_type_id,odic.wo_type_fir_id
        ) t on t.wo_type_id=wt.type and t.wo_type_fir_id=wt.order_one_class
        left join wf_his_execution  whe on whe.busi_key=wt.order_no
        where 1=1
        and wt.gmt_create &gt;= #{cond.startTime}
        and wt.gmt_create &lt;= #{cond.endTime}
        group by t.wo_type_fir_id,whe.complete_flag
        ) t3
        union all
        select '电商业务' as type, ifnull(sum(number),0) as number, ifnull(max(closed),0) as closedNumber,
        concat(ifnull(round((max(closed)/sum(number) * 100),2), 0.00),'%') as closedRate
        from (
        select count(1) as number,(case when whe.complete_flag=1 then count(1) else 0 end) closed
        from wo_task wt inner join (
        select odic.wo_type_id,odic.wo_type_detail,odic.wo_type_fir_id,odic.wo_type_fir_detail
        from op_dict_info_conf odic
        where 1=1
        and odic.wo_type_fir_detail='电商业务'
        <if test="type!=null and type!=''">
            and odic.wo_type_detail=#{type}
        </if>
        group by odic.wo_type_id,odic.wo_type_fir_id
        ) t on t.wo_type_id=wt.type and t.wo_type_fir_id=wt.order_one_class
        left join wf_his_execution  whe on whe.busi_key=wt.order_no
        where 1=1
        and wt.gmt_create &gt;= #{cond.startTime}
        and wt.gmt_create &lt;= #{cond.endTime}
        group by t.wo_type_fir_id,whe.complete_flag
        ) t4
    </select>

    <select id="selectDetailsCount" parameterType="com.welab.crm.operate.dto.report.WoReportDTO" resultType="int">
        select count(1) from wo_task wt
        left join data_loan_application dla on dla.wo_task_id=wt.id
        <if test="(cond.orderStateList != null and cond.orderStateList.size() > 0) or (cond.groupCodeList != null and cond.groupCodeList.size() > 0)">
            left join (select wre.busi_key,wre.status,wrt.group_code from
            wf_ru_execution wre
            inner join wf_ru_task wrt on wrt.execution_id=wre.execution_id and wrt.status in('ready', 'unassigned')
            ) cur on wt.order_no=cur.busi_key
            left join (
            select whe.busi_key,whe.status as close_type,wht.group_code
            from wf_his_execution whe
            inner join wf_his_log whl on whl.execution_id=whe.execution_id
            inner join(
            select execution_id,max(gmt_create) gmt_create from wf_his_log group by execution_id
            ) t on whl.execution_id=t.execution_id and whl.gmt_create=t.gmt_create
            inner join wf_his_task wht on whl.task_id=wht.task_id
            ) prehis on wt.order_no=prehis.busi_key
        </if>
        where
        <choose>
            <when test="cond.orderIdList != null and cond.orderIdList.size() > 0">
                wt.id in
                <foreach collection="cond.orderIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                wt.gmt_create &gt;= #{cond.startTime}
                and wt.gmt_create &lt;= #{cond.endTime}
                <include refid="baseOrderQuery"/>
            </otherwise>
        </choose>
    </select>

    <sql id="baseOrderQuery">
        <if test="cond.orderTypeList != null and cond.orderTypeList.size() > 0">
            and wt.type in
            <foreach collection="cond.orderTypeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="cond.orderStateList != null and cond.orderStateList.size() > 0 and cond.groupCodeList == null">
                and ( cur.status in
                <foreach collection="cond.orderStateList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or
                prehis.close_type in
                <foreach collection="cond.orderStateList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="cond.groupCodeList != null and cond.groupCodeList.size() > 0 and cond.orderStateList == null">
                and (cur.group_code in
                <foreach collection="cond.groupCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or
                prehis.group_code in
                <foreach collection="cond.groupCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="cond.orderStateList != null and cond.orderStateList.size() > 0 and cond.groupCodeList != null
                and cond.groupCodeList.size() > 0">
                and (( cur.status in
                <foreach collection="cond.orderStateList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                and
                cur.group_code in
                <foreach collection="cond.groupCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>

                )
                or
                (
                prehis.close_type in
                <foreach collection="cond.orderStateList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                and
                prehis.group_code in
                <foreach collection="cond.groupCodeList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ))
            </when>
        </choose>
    </sql>

    <select id="selectWoIdsPage" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.domain.DataLoanApplication">
          select wt.id as wo_task_id,
          dla.application_id,
          dla.product_name,
          dla.apply_time,
          dla.approval_time,
          dla.confirm_time,
          dla.loan_time,
          dla.channel_code,
          dla.partner_code,
          dla.apply_tenor,
          dla.approval_tenor,
          dla.user_level,
          dla.status,
          dla.apply_amount,
          dla.approval_amount,
          dla.total_rate,
          dla.partner_code_new
          from wo_task wt
        left join data_loan_application dla on dla.wo_task_id=wt.id
        <if test="(cond.orderStateList != null and cond.orderStateList.size() > 0) or (cond.groupCodeList != null and cond.groupCodeList.size() > 0)">
            left join (select wre.busi_key,wre.status,wrt.group_code from
            wf_ru_execution wre
            inner join wf_ru_task wrt on wrt.execution_id=wre.execution_id and wrt.status in('ready', 'unassigned')
            ) cur on wt.order_no=cur.busi_key
            left join (
            select whe.busi_key,whe.status as close_type,wht.group_code
            from wf_his_execution whe
            inner join wf_his_log whl on whl.execution_id=whe.execution_id
            inner join(
            select execution_id,max(gmt_create) gmt_create from wf_his_log group by execution_id
            ) t on whl.execution_id=t.execution_id and whl.gmt_create=t.gmt_create
            inner join wf_his_task wht on whl.task_id=wht.task_id
            ) prehis on wt.order_no=prehis.busi_key
        </if>
        where
        <choose>
            <when test="cond.orderIdList != null and cond.orderIdList.size() > 0">
                wt.id in
                <foreach collection="cond.orderIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <include refid="baseOrderQuery"/>
            </when>
            <otherwise>
                wt.gmt_create &gt;= #{cond.startTime}
                and wt.gmt_create &lt;= #{cond.endTime}
                <include refid="baseOrderQuery"/>
            </otherwise>
        </choose>
      limit ${(cond.currentPage-1) * cond.rowsPerPage}, ${cond.rowsPerPage}
    </select>

    <select id="selectDetails" resultType="com.welab.crm.operate.vo.woReport.ReportDetailsVO">
        select wt.id,wt.order_no,wt.customer_name,wt.gender,wt.age,wt.cnid,wt.mobile,wt.mobile_bak,wt.mobile_baks,
               wt.remark,wt.suc_loan_count,wt.complaints_channel,wt.fund_name,wt.match_up as matchUp,
          odic.wo_type_detail as type, odic.wo_type_fir_detail as firstType,wt.current_loan_count,
          odic.wo_type_sec_detail as secondType,odic.wo_type_thir_detail as thirdType,
          iacs.group_name as createGroupName,iacs.staff_name as createStaffName,
          date_format(wt.gmt_create,'%Y-%m-%d %T') as gmt_create,
          wt.description,dc.uuid,dc.user_id,wt.callback_note,
          date_format(wah.distribution_time,'%Y-%m-%d %T') as assignTime,
          wah.distribution_type as assignType,
          cur.cur_group_name,cur.cur_staff_name,
          date_format(cur.cur_process_time,'%Y-%m-%d %T') as cur_process_time,
          cur.cur_comment,cur.complete_flag,cur.status,
          (case when pre.pre_group_name is null then prehis.prehis_group_name else pre.pre_group_name end) as pre_group_name,
          (case when pre.pre.pre_staff_name is null then prehis.prehis_staff_name else pre.pre.pre_staff_name end) as pre_staff_name,
          date_format((case when pre.pre_process_time is null then prehis.prehis_process_time else pre.pre_process_time end),'%Y-%m-%d %T') as pre_process_time,
          (case when pre.pre_comment is null then prehis.prehis_comment else pre.pre_comment end) as pre_comment,
          prehis.close_type
        from wo_task wt
        inner join op_dict_info_conf odic on odic.wo_type_id=wt.type
          and odic.wo_type_fir_id=wt.order_one_class
          and odic.wo_type_sec_id=wt.order_two_class
          and odic.wo_type_thir_id=wt.order_three_class
        left join in_auth_crm_staff iacs on wt.create_staff_id=iacs.id
        left join data_customer dc on wt.cust_id=dc.id
        left join (
          select wre.busi_key,wre.status,iacs.staff_name as cur_staff_name,iaco.name as cur_group_name,
            wrt.group_code,wrl.gmt_create as cur_process_time,wrl.comment as cur_comment,wre.complete_flag
          from wf_ru_execution wre
          inner join wf_ru_task wrt on wrt.execution_id=wre.execution_id and wrt.status in('ready', 'unassigned')
          inner join in_auth_crm_org iaco on wrt.group_code=iaco.code
          left join in_auth_crm_staff iacs on wrt.staff_id=iacs.id
          left join wf_ru_log wrl on wrl.execution_id=wre.execution_id
          inner join (
            select execution_id,task_id,max(gmt_create) gmt_create from wf_ru_log group by execution_id
          ) t on wrl.execution_id=t.execution_id and wrl.gmt_create=t.gmt_create
        ) cur on wt.order_no=cur.busi_key
        left join (
          select wre.busi_key,iacs.staff_name as pre_staff_name,iaco.name as pre_group_name,
            wrl.gmt_create as pre_process_time,wrl.comment as pre_comment
          from wf_ru_execution wre
          inner join wf_ru_task wrt on wre.execution_id=wrt.execution_id and wrt.status in('ready', 'unassigned')
          left join in_auth_crm_staff iacs on wrt.pre_staff_id=iacs.id
          inner join in_auth_crm_org iaco on wrt.pre_group_code=iaco.code
          inner join wf_ru_log wrl on wrt.pre_task_id=wrl.task_id and wrt.pre_staff_id=wrl.staff_id
          inner join(
            select execution_id,task_id,max(gmt_create) gmt_create from wf_ru_log group by execution_id,task_id
          ) t on wrl.task_id=t.task_id and wrl.gmt_create=t.gmt_create
        ) pre on wt.order_no=pre.busi_key
        left join (
          select whe.busi_key,whe.status as close_type,iacs.staff_name as prehis_staff_name,iaco.name as prehis_group_name,
          wht.group_code,whl.gmt_create as prehis_process_time,whl.comment as prehis_comment
          from wf_his_execution whe
          inner join wf_his_log whl on whl.execution_id=whe.execution_id
          inner join(
            select execution_id,max(gmt_create) gmt_create from wf_his_log group by execution_id
          ) t on whl.execution_id=t.execution_id and whl.gmt_create=t.gmt_create
          inner join wf_his_task wht on whl.task_id=wht.task_id
          left join in_auth_crm_staff iacs on wht.staff_id=iacs.id
          inner join in_auth_crm_org iaco on wht.group_code=iaco.code
        ) prehis on wt.order_no=prehis.busi_key
        left join(
          select wah.order_no,wah.distribution_time,wah.distribution_type
          from wo_assign_history wah
          inner join (
          select order_no, max(distribution_time) distribution_time from wo_assign_history group by order_no
          ) t on t.order_no=wah.order_no and t.distribution_time=wah.distribution_time
        ) wah on wah.order_no=wt.order_no
        where
        <choose>
            <when test="idList != null and idList.size() > 0">
                wt.id in
                <foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <include refid="baseOrderQuery"/>
            </when>
            <otherwise>
                wt.gmt_create &gt;= #{cond.startTime}
                and wt.gmt_create &lt;= #{cond.endTime}
                <include refid="baseOrderQuery"/>
            </otherwise>
        </choose>
        <choose>
            <when test="cond.orderIdList != null and cond.orderIdList.size() > 0">
                order by field(wt.id,
                <foreach collection="cond.orderIdList" index="index" item="item" separator="," open="" close="">
                    #{item}
                </foreach>
                )
            </when>
            <otherwise>
                order by wt.gmt_create asc
            </otherwise>
        </choose>
    </select>

    <select id="selectAssignmentSummary" parameterType="com.welab.crm.operate.dto.report.ReportAssignmentSummaryDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportAssignmentSummaryVO">
        select t.distribution_type as assignType, t.name as ruleName, t.group_name, t.staff_name, count(1) as count
        from(
            select wah.distribution_type, wri.name, iacs.group_name, wah.staff_id, iacs.staff_name, wah.order_no
            from wo_assign_history wah
            inner join in_auth_crm_staff iacs on wah.staff_id=iacs.id
            left join wo_rule_info wri on wri.id=wah.rule_id
            where 1=1
              and wah.distribution_time &gt;= #{startTime}
              and wah.distribution_time &lt;= #{endTime}
              <if test="ruleId!=null and ruleId!=''">
                and wah.rule_id=#{ruleId}
              </if>
            group by wah.staff_id,wah.distribution_type,wah.order_no
        ) t
        group by t.staff_id,t.distribution_type,t.name
    </select>

    <select id="selectAssignmentDetails" parameterType="com.welab.crm.operate.dto.report.WoReportDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportAssignmentDetailsVO">
        select wt.order_no, odic.wo_type_detail as orderType, odic.wo_type_fir_detail as firstType,
          odic.wo_type_sec_detail as secondType,odic.wo_type_thir_detail as thirdType, wt.description,
          wah.distribution_time as assignTime, wah.distribution_type as assignType,
          wri.name as ruleName, iacs.group_name, wah.staff_id, iacs.staff_name, dc.uuid, dc.user_id
        from wo_assign_history wah
        inner join in_auth_crm_staff iacs on wah.staff_id=iacs.id
        inner join wo_task wt on wt.order_no=wah.order_no
        inner join data_customer dc on dc.id=wt.cust_id
        inner join op_dict_info_conf odic on odic.wo_type_id=wt.type
          and odic.wo_type_fir_id=wt.order_one_class
          and odic.wo_type_sec_id=wt.order_two_class
          and odic.wo_type_thir_id=wt.order_three_class
        left join wo_rule_info wri on wri.id=wah.rule_id
        where 1=1
          and wah.distribution_time &gt;= #{startTime}
          and wah.distribution_time &lt;= #{endTime}
        group by wah.staff_id, wah.distribution_type, wah.order_no
    </select>

    <select id="selectOutboundEfficiencySummary"
      parameterType="com.welab.crm.operate.dto.report.OutboundEfficiencySummaryDTO"
      resultType="com.welab.crm.operate.vo.woReport.OutboundEfficiencySummaryVO">
        select iacs.id,iacs.staff_name,iacs.group_name,ipli.id_no,
            ifnull(whl.closedNum,0) + ifnull(wrl.closedNum,0) as closedNumber,
            ifnull(wrt.unclosedNum,0) as unclosedNumber
        from in_auth_crm_staff iacs
        left join in_phone_login_info ipli on ipli.user_tel=iacs.login_name
        left join (
          select staff_id, count(1) closedNum from
          wf_his_log force index (idx_gmt_create)
          where next_node_code='nwoEnd'
          and gmt_create &gt;= #{cond.startTime}
          and gmt_create &lt;= #{cond.endTime}
          group by staff_id
        ) whl on whl.staff_id=iacs.id
        left join (
          select staff_id, count(1) closedNum from
          wf_ru_log force index (idx_gmt_create)
          where next_node_code='nwoEnd'
          and gmt_create &gt;= #{cond.startTime}
          and gmt_create &lt;= #{cond.endTime}
          group by staff_id
        ) wrl on wrl.staff_id=iacs.id
        left join (
          select staff_id, count(1) unclosedNum from
          wf_ru_task where status='ready'
          group by staff_id
        ) wrt on wrt.staff_id=iacs.id
        where 1=1
        <choose>
            <when test="cond.groupCode!=null and cond.groupCode!=''">
              and iacs.group_code=#{cond.groupCode}
            </when>
            <otherwise>
              and group_code in
              <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
              </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="selectEfficiencySummary" parameterType="com.welab.crm.operate.dto.report.ReportEfficiencySummaryDTO"
      resultType="com.welab.crm.operate.vo.woReport.ReportEfficiencySummaryVO">
      select #{type} as type,iacs.group_name,iacs.staff_name,ipli.id_no,
        ifnull(t_create.createNum,0) as createNum,
        ifnull(t_return.returnNum,0) as returnNum,
        ifnull(t_assign.assignNum,0) as assignNum,
        ifnull(closed.closedNum,0) as closeNum,
        ifnull(resolved_close.resolvedNum,0) as resolvedNum,
        ifnull(unresolved_close.unresolvedNum,0) as unresolvedNum,
        ifnull(not_connected_close.notConnectedNum,0) as notConnectedNum,
        ifnull(processing.processingNum,0) as processingNum,
        ifnull(flowing.flowingNum,0) as flowingNum,
        ifnull(reminder.reminderNum,0) as reminderNum
      from in_auth_crm_staff iacs
      left join op_dict_info odi on odi.type=iacs.group_code and odi.category='workOrderReportGroup'
      left join in_phone_login_info ipli on ipli.user_tel=iacs.login_name
      left join(
        select create_staff_id,count(1) as createNum
        from wo_task wt
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
          and wt.gmt_create &gt;= #{cond.startTime}
          and wt.gmt_create &lt;= #{cond.endTime}
        group by create_staff_id
      ) t_create on t_create.create_staff_id=iacs.id
      left join(
        select staff_id,count(1) as returnNum
        from wo_return_log wrl
        inner join wo_task wt on wt.order_no=wrl.order_no
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
          and wrl.gmt_create &gt;= #{cond.startTime}
          and wrl.gmt_create &lt;= #{cond.endTime}
        group by staff_id
      ) t_return on t_return.staff_id=iacs.id
      left join(
        select staff_id,count(1) as assignNum
        from(
          select staff_id,wah.order_no
          from wo_assign_history wah
          inner join wo_task wt on wt.order_no=wah.order_no
          inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
          where 1=1
          and wah.distribution_time &gt;= #{cond.startTime}
          and wah.distribution_time &lt;= #{cond.endTime}
          group by staff_id,order_no
        ) t
        group by staff_id
      ) t_assign on t_assign.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as resolvedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after='resolved_close'
        group by whl.staff_id
      ) resolved_close on resolved_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as unresolvedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after='unresolved_close'
        group by whl.staff_id
      ) unresolved_close on unresolved_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as notConnectedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after='not_connected_close'
        group by whl.staff_id
      ) not_connected_close on not_connected_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as closedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after in ('resolved_close','unresolved_close','not_connected_close')
        group by whl.staff_id
      ) closed on closed.staff_id=iacs.id
      left join (
        select wrt.staff_id, count(1) as processingNum
        from wf_ru_task wrt
        inner join wf_ru_execution wre on wre.execution_id=wrt.execution_id
        inner join wo_task wt on wt.order_no=wre.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and wrt.gmt_create &gt;= #{cond.startTime}
        and wrt.gmt_create &lt;= #{cond.endTime}
        and wrt.status='ready'
        group by wrt.staff_id
      ) processing on processing.staff_id=iacs.id
      left join (
        select wrl.staff_id, count(1) as flowingNum
        from wf_ru_log wrl
        inner join wf_ru_execution wre on wre.execution_id=wrl.execution_id and wre.status='flow'
        inner join wo_task wt on wt.order_no=wre.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        where 1=1
        and wrl.gmt_create &gt;= #{cond.startTime}
        and wrl.gmt_create &lt;= #{cond.endTime}
        and wrl.status_before='process'
        and wrl.status_after='flow'
        group by wrl.staff_id
      ) flowing on flowing.staff_id=iacs.id
      left join (
        select wrt.staff_id, t.reminderNum
        from wf_ru_execution wre
        inner join wo_task wt on wt.order_no=wre.busi_key
        inner join op_dict_info odi on odi.id=wt.type and odi.content=#{type}
        inner join wf_ru_task wrt on wre.execution_id=wrt.execution_id
        inner join (
          select task_id, count(1) as reminderNum from wf_ru_log wrl
          where 1=1
          and wrl.gmt_create &gt;= #{cond.startTime}
          and wrl.gmt_create &lt;= #{cond.endTime}
          and wrl.operate_type='催单'
          group by task_id
        ) t on wrt.task_id=t.task_id
        group by wrt.staff_id
      ) reminder on reminder.staff_id=iacs.id
      where
        <if test="cond.groupCode != null and cond.groupCode!=''">
            iacs.group_code=#{cond.groupCode}
        </if>
        <if test="list != null and list.size()>0">
            iacs.group_code in
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
      order by odi.detail,ipli.id_no
    </select>

    <select id="selectCentralMonitoring" parameterType="com.welab.crm.operate.dto.report.CentralMonitoringDTO"
      resultType="com.welab.crm.operate.vo.woReport.CentralMonitoringVO">
      select
        <choose>
          <when test="cond.type!=null and cond.type!=''">
            odi.content
          </when>
          <otherwise>
            '普通，投诉，舆情，监管'
          </otherwise>
        </choose>
        as type,
        iacs.group_name,iacs.staff_name,ipli.id_no,
        ifnull(t_assign.assignNum,0) as assignNum,
        ifnull(closed.closedNum,0) as closedNum,
        ifnull(currentMonth.closedNum,0) as currentMonthClosedNum,
        ifnull(history.closedNum,0) as historyClosedNum,
        concat(ifnull(round(closed.closedNum/t_assign.assignNum * 100, 2), 0.00), '%') as closedRate,
        (ifnull(t_assign.assignNum,0) - ifnull(closed.closedNum,0))  as unclosedNum,
        ifnull(flowing.flowingNum,0) as flowingNum,
        ifnull(resolved_close.resolvedNum,0) as resolvedNum,
        ifnull(unresolved_close.unresolvedNum,0) as unresolvedNum,
        ifnull(not_connected_close.notConnectedNum,0) as notConnectedNum,
        concat(ifnull(round(resolved_close.resolvedNum/t_assign.assignNum*100, 2), 0.00), '%') as resolvedRate,
        concat(ifnull(round(unresolved_close.unresolvedNum/t_assign.assignNum*100, 2), 0.00), '%') as unresolvedRate,
        concat(ifnull(round(not_connected_close.notConnectedNum/t_assign.assignNum*100, 2), 0.00), '%') as notConnectedRate
      from in_auth_crm_staff iacs
      <if test="cond.type!=null and cond.type!=''">
        inner join op_dict_info odi on odi.id=#{cond.type}
      </if>
      left join in_phone_login_info ipli on ipli.user_tel=iacs.login_name
      left join(
        select staff_id,count(1) as assignNum
        from(
          select staff_id,wah.order_no
          from wo_assign_history wah
          inner join wo_task wt on wt.order_no=wah.order_no
          <if test="cond.type!=null and cond.type!=''">
            inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
          </if>
          where 1=1
          and wah.gmt_create &gt;= #{cond.startTime}
          and wah.gmt_create &lt;= #{cond.endTime}
          group by staff_id,order_no
        ) t
        group by staff_id
      ) t_assign on t_assign.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as resolvedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
          and whl.gmt_create &gt;= #{cond.startTime}
          and whl.gmt_create &lt;= #{cond.endTime}
          and whl.status_after='resolved_close'
        group by whl.staff_id
      ) resolved_close on resolved_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as unresolvedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after='unresolved_close'
        group by whl.staff_id
      ) unresolved_close on unresolved_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as notConnectedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after='not_connected_close'
        group by whl.staff_id
      ) not_connected_close on not_connected_close.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as closedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
        and whl.gmt_create &gt;= #{cond.startTime}
        and whl.gmt_create &lt;= #{cond.endTime}
        and whl.status_after in ('resolved_close','unresolved_close','not_connected_close')
        group by whl.staff_id
      ) closed on closed.staff_id=iacs.id
      left join (
        select wrl.staff_id, count(1) as flowingNum
        from wf_ru_log wrl
        inner join wf_ru_execution wre on wre.execution_id=wrl.execution_id and wre.status='flow'
        inner join wo_task wt on wt.order_no=wre.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
        and wrl.gmt_create &gt;= #{cond.startTime}
        and wrl.gmt_create &lt;= #{cond.endTime}
        and wrl.status_before='process'
        and wrl.status_after='flow'
        group by wrl.staff_id
      ) flowing on flowing.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as closedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
          and whe.gmt_create &gt;= #{cond.startTime}
          and whe.gmt_create &lt;= #{cond.endTime}
          and whl.gmt_create &gt;= #{cond.startTime}
          and whl.gmt_create &lt;= #{cond.endTime}
          and whl.status_after in ('resolved_close','unresolved_close','not_connected_close')
        group by whl.staff_id
      ) currentMonth on currentMonth.staff_id=iacs.id
      left join (
        select whl.staff_id,count(1) as closedNum
        from wf_his_log whl
        inner join wf_his_execution whe on whe.execution_id=whl.execution_id
        inner join wo_task wt on wt.order_no=whe.busi_key
        <if test="cond.type!=null and cond.type!=''">
          inner join op_dict_info odi on odi.id=wt.type and odi.id=#{cond.type}
        </if>
        where 1=1
          and whl.gmt_create &gt;= #{cond.startTime}
          and whl.gmt_create &lt;= #{cond.endTime}
          and whl.status_after in ('resolved_close','unresolved_close','not_connected_close')
          and whe.gmt_create &lt;= #{cond.startTime}
        group by whl.staff_id
      ) history on history.staff_id=iacs.id
      where 1=1
      <if test="cond.groupCode!=null and cond.groupCode!=''">
        and iacs.group_code in
        <foreach collection="cond.groupCodesList" index="index" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="cond.staffId!=null and cond.staffId!=''">
        and iacs.id in
        <foreach collection="cond.staffIds" index="index" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      order by iacs.group_code
    </select>
    <select id="selectRangeTransferReportPage"
            resultType="com.welab.crm.operate.vo.transferReport.RangeTransferReportVO">
        <include refid="transferRangeSql"/>
    </select>
    <select id="queryTotalGroupByTransferCompany"
            resultType="com.welab.crm.operate.vo.transferReport.RangeTransferReportVO">
        select
            dla.transfer_company,
            ifnull(count(1),0) as total
        from
            wo_task wt
                left join data_loan_application dla on
                wt.id = dla.wo_task_id
                join op_dict_info o1 on
                o1.id = wt.order_three_class
                join op_dict_info o2 on
                o2.id = wt.order_two_class
                join op_dict_info o3 on
                o3.id = wt.type
        where
            dla.transfer_company is not null
          and o2.content = '债转'
          and wt.gmt_create between #{startTime} and #{endTime}
        group by
            1
        order by 1;
    </select>
    <select id="selectRangeTransferReportList"
            resultType="com.welab.crm.operate.vo.transferReport.RangeTransferReportVO">
        <include refid="transferRangeSql"/>
    </select>
    <select id="selectMonthTransferList"
            resultType="com.welab.crm.operate.vo.transferReport.MonthTransferBaseVO">
        select
            dla.transfer_company,
            date_format(wt.gmt_create,'%Y-%m') as dateStr,
            count(1) as count
        from
            wo_task wt
                left join data_loan_application dla on
                wt.id = dla.wo_task_id
                join op_dict_info o2 on
                o2.id = wt.order_two_class
        where
            dla.transfer_company is not null
          and o2.content = '债转'
          and wt.gmt_create between #{dto.startTime} and #{dto.endTime}
        group by
            1,
            2
        order by 1

    </select>
    <select id="selectTransferDetailReportPage"
            resultType="com.welab.crm.operate.vo.transferReport.TransferReportDetailVO">
        <include refid="transferDetailSql"/>
    </select>
    <select id="selectTransferDetailReportList"
            resultType="com.welab.crm.operate.vo.transferReport.TransferReportDetailVO">
        <include refid="transferDetailSql"/>
    </select>
    <select id="selectComplaintOrderData"
            resultType="com.welab.crm.operate.vo.woReport.ComplaintEscalationStatisticsReportVO">
        select
            <choose>
                <when test="dto.period == 'day'">
                    date_format(gmt_create,'%Y-%m-%d') as time,
                </when>
                <otherwise>
                    '周期' as time,
                </otherwise>
            </choose>
            sum(case when `type` = #{fid} then 1 else 0 end) as fundComplaintCount,
            sum(case when `type` = #{mid} then 1 else 0 end) as monitorComplaintCount,
            count(distinct cust_id) as complaintUserCount
        from
            wo_task wt
        where gmt_create between #{dto.startTime} and #{dto.endTime}
        and type in (#{fid}, #{mid})
        group by 1

    </select>


    <sql id="transferDetailSql">
        select
        wt.order_no ,
        o0.content as type,
        o1.content as firstType,
        o2.content as secondType,
        o3.content as thirdType,
        wt.complaints_channel ,
        wt.fund_name,
        wt.customer_name ,
        wt.gender ,
        wt.age ,
        wt.cnid ,
        wt.mobile ,
        wt.description ,
        dc.uuid ,
        dc.user_id ,
        dla.application_id ,
        dla.transfer_company
        from wo_task wt
        join data_loan_application dla on wt.id = dla.wo_task_id
        join op_dict_info o0 on o0.id = wt.`type`
        join op_dict_info o1 on o1.id = wt.order_one_class
        join op_dict_info o2 on o2.id = wt.order_two_class
        join op_dict_info o3 on o3.id = wt.order_three_class
        join data_customer dc on wt.cust_id = dc.id
        where o2.content = '债转'
        and wt.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.transferCompany!=null and dto.transferCompany!=''">
            and dla.transfer_company like concat('%',#{dto.transferCompany},'%')
        </if>
        order by wt.gmt_create,wt.order_no
    </sql>

    <sql id="transferRangeSql">
        select
            dla.transfer_company,
            case when o3.content = '投诉工单' then '普通投诉'
                when o3.content = '舆情工单' then '舆情投诉'
                when o3.content = '资方工单' then '资方投诉'
                else o3.content end as complaintSource,
            ifnull(sum(case when o1.type = 'violentCollection' then 1 else 0 end), 0) as 'violentCollection',
            ifnull(sum(case when o1.type = 'useOurNominalCollection' then 1 else 0 end), 0) as 'useOurNominalCollection',
            ifnull(sum(case when o1.type = 'nonAcceptanceTransfer' then 1 else 0 end), 0) as 'nonAcceptanceTransfer',
            ifnull(sum(case when o1.type = 'verifyRepaymentIssues' then 1 else 0 end), 0) as 'verifyRepaymentIssues',
            ifnull(sum(case when o1.type = 'clearanceVerification' then 1 else 0 end), 0) as 'clearanceVerification',
            ifnull(sum(case when o1.type = 'unContactedTransfer' then 1 else 0 end), 0) as 'unContactedTransfer',
            ifnull(sum(case when o1.type = 'contactCompany' then 1 else 0 end), 0) as 'contactCompany',
            ifnull(sum(case when o1.type = 'contactThirdParties' then 1 else 0 end), 0) as 'contactThirdParties',
            ifnull(sum(case when o1.type = 'otherRepaymentIssues' then 1 else 0 end), 0) as 'otherRepaymentIssues',
            ifnull(sum(case when o1.type = 'negotiateRepayment' then 1 else 0 end), 0) as 'negotiateRepayment',
            ifnull(sum(case when o1.type = 'complaintsActionFrozen' then 1 else 0 end), 0) as 'complaintsActionFrozen',
            ifnull(sum(case when o1.type = 'deferredRepayment' then 1 else 0 end), 0) as 'deferredRepayment'
        from
            wo_task wt
                left join data_loan_application dla on
                wt.id = dla.wo_task_id
                join op_dict_info o1 on
                o1.id = wt.order_three_class
                join op_dict_info o2 on
                o2.id = wt.order_two_class
                join op_dict_info o3 on
                o3.id = wt.type
        where
            dla.transfer_company is not null
          and o2.content = '债转'
          and wt.gmt_create between #{dto.startTime} and #{dto.endTime}
        group by
            1,
            2
        order by 1
    </sql>

</mapper>
