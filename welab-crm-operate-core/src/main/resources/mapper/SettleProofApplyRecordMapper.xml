<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.SettleProofApplyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.SettleProofApplyRecord">
        <id column="id" property="id" />
        <result column="apply_time" property="applyTime" />
        <result column="staff_id" property="staffId" />
        <result column="application_id" property="applicationId" />
        <result column="partner_code" property="partnerCode" />
        <result column="state" property="state" />
        <result column="file_path" property="filePath" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, apply_time, staff_id, application_id, partner_code, state, file_path, gmt_create, gmt_modify
    </sql>

    <select id="query" resultType="com.welab.crm.operate.vo.settlement.SettleProofApplyRecordVO">
        SELECT
        t1.id,
        t1.apply_time,
        t1.staff_id,
        t2.staff_name,
        t1.application_id,
        t1.partner_code,
        t1.state
        FROM settle_proof_apply_record t1
        LEFT JOIN in_auth_crm_staff t2 ON t1.staff_id = t2.id
        WHERE 1 = 1
        <if test="filter.starApplyTime != null and filter.starApplyTime != ''">
            AND t1.apply_time &gt;= #{filter.starApplyTime}
        </if>
        <if test="filter.endApplyTime != null and filter.endApplyTime != ''">
            AND t1.apply_time &lt;= #{filter.endApplyTime}
        </if>
        <if test="filter.applicationId != null and filter.applicationId != ''">
            AND t1.application_id = #{filter.applicationId}
        </if>
        <if test="filter.staffId != null and filter.staffId != ''">
            AND t1.staff_id = #{filter.staffId}
        </if>
        <if test="filter.partnerCode != null and filter.partnerCode != ''">
            AND t1.partner_code = #{filter.partnerCode}
        </if>
        <if test="filter.state != null and filter.state != ''">
            AND t1.state = #{filter.state}
        </if>
        ORDER BY t1.gmt_create DESC
    </select>
</mapper>
