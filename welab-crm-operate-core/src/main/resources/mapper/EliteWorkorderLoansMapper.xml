<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.EliteWorkorderLoansMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.EliteWorkorderLoans">
        <id column="id" property="id" />
        <result column="flag" property="flag" />
        <result column="objective_guid" property="objectiveGuid" />
        <result column="customer_name" property="customerName" />
        <result column="mobile" property="mobile" />
        <result column="product_name" property="productName" />
        <result column="application_id" property="applicationId" />
        <result column="apply_time" property="applyTime" />
        <result column="order_status" property="orderStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, flag, objective_guid, customer_name, mobile, product_name, application_id, apply_time, order_status
    </sql>

</mapper>
