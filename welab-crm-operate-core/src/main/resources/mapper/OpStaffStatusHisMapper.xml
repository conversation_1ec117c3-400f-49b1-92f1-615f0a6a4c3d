<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpStaffStatusHisMapper">

    <select id="selectWorkStatusSummary" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select date,id_no as cno,staff_name, staff_id, group_code,
        date_format(max(loginTime),'%Y-%m-%d %T') as loginTime,
        date_format(max(logoutTime),'%Y-%m-%d %T') as logoutTime,
        max(loginTimes) as loginTimes,
        max(pauseTimes) as pauseTimes,
        max(restTimes) as restTimes,
        max(trainingTimes) as trainingTimes,
        max(eatingTimes) as eatingTimes,
        max(meetingTimes) as meetingTimes,
        concat( lpad(floor( max(idleDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(idleDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(idleDuration) % 60, 2, '0' ) ) as idleDuration,
        concat( lpad(floor( max(pauseDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(pauseDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(pauseDuration) % 60, 2, '0' ) ) as pauseDuration,
        concat( lpad(floor( max(wrapUpDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(wrapUpDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(wrapUpDuration) % 60, 2, '0' ) ) as wrapUpDuration,
        concat( lpad(floor( max(restDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(restDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(restDuration) % 60, 2, '0' ) ) as restDuration,
        concat( lpad(floor( max(trainingDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(trainingDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(trainingDuration) % 60, 2, '0' ) ) as trainingDuration,
        concat( lpad(floor( max(eatingDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(eatingDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(eatingDuration) % 60, 2, '0' ) ) as eatingDuration,
        concat( lpad(floor( max(meetingDuration) / 3600 ), 2, '0' ), ':',lpad( floor( (max(meetingDuration) % 3600) / 60 ), 2, '0' ), ':',lpad( max(meetingDuration) % 60, 2, '0' ) ) as meetingDuration
        from (
        select
        <if test="filter.period=='day'">
            date_format(ossh.gmt_create,'%Y-%m-%d') as date,
        </if>
        <if test="filter.period=='range'">
            concat(left(#{filter.startTime},10),'~',left(#{filter.endTime},10)) as date,
        </if>
        ipli.id_no, ossh.status,
        iacs.id as staff_id, iacs.staff_name, iacs.group_code,
        (case when ossh.`status`='checkIn' then min(ossh.start_time) else null end) as loginTime,
        (case when ossh.`status`='checkIn' then max(ossh.end_time) else null end) as logoutTime,
        (case when ossh.`status`='checkIn' then count(1) else null end) as loginTimes,
        (case when ossh.`status`='free' then sum(duration) else 0 end) as idleDuration,
        (case when ossh.`status`='ACW' then sum(duration) else 0 end) as wrapUpDuration,
        (case when ossh.`status`='busy' then sum(duration) else 0 end) as pauseDuration,
        (case when ossh.`status`='busy' then count(1) else null end) as pauseTimes,
        (case when ossh.`status`='break' then sum(duration) else 0 end) as restDuration,
        (case when ossh.`status`='break' then count(1) else null end) as restTimes,
        (case when ossh.`status`='training' then sum(duration) else 0 end) as trainingDuration,
        (case when ossh.`status`='training' then count(1) else null end) as trainingTimes,
        (case when ossh.`status`='lunch' then sum(duration) else 0 end) as eatingDuration,
        (case when ossh.`status`='lunch' then count(1) else null end) as eatingTimes,
        (case when ossh.`status`='meeting' then sum(duration) else 0 end) as meetingDuration,
        (case when ossh.`status`='meeting' then count(1) else null end) as meetingTimes
        from in_phone_login_info ipli
        inner join in_auth_crm_staff iacs on iacs.login_name=ipli.user_tel
        left join op_staff_status_his ossh force index(op_staff_status_his_gmt_create_IDX) on ossh.staff_id=iacs.login_name
        where 1=1
        and ossh.gmt_create &gt;= #{filter.startTime}
        and ossh.gmt_create &lt;= #{filter.endTime}
        <if test="filter.groupCode!=null and filter.groupCode!=''">
            and iacs.group_code = #{filter.groupCode}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and iacs.id = #{filter.staffId}
        </if>
        group by 1,2,3
        ) t
        group by 1,2
    </select>

    <select id="selectCallInWorkStatus" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.model.ReportCallInWorkStatusModel">
        select date_format(ossh.gmt_create,'%Y-%m-%d') as date, ipli.id_no as cno, iacs.id as staffId, iacs.staff_name,
        iacs.group_code, iacs.group_name, ossh.status,
        date_format(ossh.start_time,'%Y-%m-%d %T') as start_time,
        date_format(ossh.end_time,'%Y-%m-%d %T') as end_time,
        ossh.duration
        from in_phone_login_info ipli
        inner join in_auth_crm_staff iacs on iacs.login_name=ipli.user_tel
        left join op_staff_status_his ossh force index(op_staff_status_his_gmt_create_IDX) on ossh.staff_id=iacs.login_name
        and ossh.status in('break','busy','lunch','training','meeting')
        where 1=1
        and ossh.gmt_create &gt;= #{filter.startTime}
        and ossh.gmt_create &lt;= #{filter.endTime}
        <if test="filter.groupCode!=null and filter.groupCode!=''">
            and iacs.group_code = #{filter.groupCode}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and iacs.id = #{filter.staffId}
        </if>
        union all
        select
        date_format(osesl.gmt_create, '%Y-%m-%d') as date,
        ipli.id_no as cno,
        iacs.id as staffId ,
        iacs.staff_name ,
        iacs.group_code,
        iacs.group_name,
        osesl.status,
        date_format(osesl.start_time, '%Y-%m-%d %T') as start_time,
        date_format(osesl.end_time, '%Y-%m-%d %T') as end_time,
        osesl.duration
        from
        op_staff_extra_status_log osesl force index(op_staff_extra_status_log_gmt_create_IDX)
        left join in_auth_crm_staff iacs on
        osesl.staff_id = iacs.login_name
        left join in_phone_login_info ipli on
        osesl.staff_id = ipli.user_tel
        where 1=1
        and osesl.gmt_create &gt;= #{filter.startTime}
        and osesl.gmt_create &lt;= #{filter.endTime}
        <if test="filter.groupCode!=null and filter.groupCode!=''">
            and iacs.group_code = #{filter.groupCode}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and iacs.id = #{filter.staffId}
        </if>
    </select>

    <select id="selectStaffEfficiency" resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">

        select t.date, t.cno, t.staff_id, t.staff_name, t.group_code, t.group_name,
        concat(ifnull(round((call_in_number-repeat_number)/call_in_number*100,2),'0.00'),'%') as resolvedRate,
        concat(ifnull(round((s_num+vs_num)/part_num *100,2),'0.00'),'%') as satisfiedRate
        from (
        select ipli.id_no as cno, iacs.id as staff_id, iacs.staff_name, iacs.group_code, iacs.group_name
        <if test="filter.period=='day'">
            ,date_format(ossh.gmt_create,'%Y-%m-%d') as date
        </if>
        <if test="filter.period=='range'">
            ,concat(left(#{filter.startTime},10),'~',left(#{filter.endTime},10)) as date
        </if>
        from op_staff_status_his ossh force index(op_staff_status_his_gmt_create_IDX)
        inner join in_phone_login_info ipli on ipli.user_tel=ossh.staff_id
        inner join in_auth_crm_staff iacs on iacs.login_name=ipli.user_tel and iacs.is_status=1
        where
        ossh.gmt_create between #{filter.startTime} and #{filter.endTime}
        <if test="filter.groupCode!=null and filter.groupCode!=''">
            and iacs.group_code=#{filter.groupCode}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and iacs.id=#{filter.staffId}
        </if>
        group by
        <if test="filter.period=='day'">
            date_format(ossh.gmt_create,'%Y-%m-%d'),
        </if>
        ossh.staff_id
        ) t
        left join (
        select count(1) as call_in_number,cdr_callee_cno as cno
        <if test="filter.period=='day'">
            ,date_format(cpci.gmt_create,'%Y-%m-%d') as date
        </if>
        from con_phone_call_info cpci force index(con_phone_call_info_cdr_start_time_IDX)
        where
        cdr_call_type='1'
        and cdr_callee_cno is not null
        and cpci.cdr_start_time between #{filter.startTime} and #{filter.endTime}
        group by
        <if test="filter.period=='day'">
            date_format(cpci.gmt_create,'%Y-%m-%d'),
        </if>
        cdr_callee_cno
        ) t1 on t.cno=t1.cno
        <if test="filter.period=='day'">
            and t.date=t1.date
        </if>
        left join (
        select sum(crc.repeat_number) as repeat_number, crc.cno
        <if test="filter.period=='day'">
            ,date_format(crc.gmt_create,'%Y-%m-%d') as date
        </if>
        from con_repeat_call crc
        where
        crc.gmt_create between #{filter.startTime} and #{filter.endTime}
        group by
        <if test="filter.period=='day'">
            date_format(crc.gmt_create,'%Y-%m-%d'),
        </if>
        crc.cno
        ) t2 on t.cno=t2.cno
        <if test="filter.period=='day'">
            and t.date=t2.date
        </if>
        left join(
        select bridged_cno,
        <if test="filter.period=='day'">
            date_format(gmt_create,'%Y-%m-%d') as date,
        </if>
        count(case when length(sv_keys)>= 1 then 1 else null end) as part_num,
        count(case when substr(sv_keys,1) = '1' then 1 else null end) as vs_num,
        count(case when substr(sv_keys,1) = '2' then 1 else null end) as s_num
        from con_satisfaction
        where
        sv_start_time between #{filter.startTime} and #{filter.endTime}
        and cdr_call_type = '1'
        group by
        <if test="filter.period=='day'">
            date_format(gmt_create,'%Y-%m-%d'),
        </if>
        bridged_cno
        )t3 on t.cno = t3.bridged_cno
        <if test="filter.period=='day'">
            and t.date=t3.date
        </if>
    </select>

    <select id="selectLoginDurationByDay" parameterType="com.welab.crm.operate.dto.report.ReportWorkStatusDTO"
            resultType="com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO">
        select id_no as cno, t.date,
            date_format(max(loginTime),'%Y-%m-%d %T') as loginTime,
            date_format(max(logoutTime),'%Y-%m-%d %T') as logoutTime,
            date_format(max(wrapUpDuration),'%H:%i:%s') as wrapUpDuration,
            date_format(max(idleDuration),'%H:%i:%s') as idleDuration,
            date_format(max(trainingDuration),'%H:%i:%s') as trainingDuration,
            date_format(max(eatingDuration),'%H:%i:%s') as eatingDuration,
            date_format(max(meetingDuration),'%H:%i:%s') as meetingDuration
        from (
            select ipli.id_no,
            date_format(ossh.gmt_create, '%Y-%m-%d') as date,
            (case when ossh.`status`='checkIn' then min(ossh.start_time) end) as loginTime,
            (case when ossh.`status`='checkIn' then max(ossh.end_time) end) as logoutTime,
            (case when ossh.`status`='ACW' then sec_to_time(sum(duration)) end) as wrapUpDuration,
            (case when ossh.`status`='free' then sec_to_time(sum(duration)) else null end) as idleDuration,
            (case when ossh.`status`='lunch' then sec_to_time(sum(duration)) else null end) as eatingDuration,
            (case when ossh.`status`='meeting' then sec_to_time(sum(duration)) else null end) as meetingDuration,
            (case when ossh.`status`='training' then sec_to_time(sum(duration)) else null end) as trainingDuration
            from in_phone_login_info ipli
            inner join in_auth_crm_staff iacs on iacs.login_name=ipli.user_tel
            left join op_staff_status_his ossh force index(op_staff_status_his_gmt_create_IDX) on ossh.staff_id=iacs.login_name
            where
            ossh.status in ('checkIn', 'ACW', 'free', 'lunch', 'meeting', 'training') and
            ossh.gmt_create between #{startTime} and #{endTime}
            <if test="groupCode!=null and groupCode!=''">
                and iacs.group_code = #{groupCode}
            </if>
            <if test="staffId!=null and staffId!=''">
                and iacs.id = #{staffId}
            </if>
            group by ipli.id_no,ossh.status, date_format(ossh.gmt_create, '%Y-%m-%d')
        ) t
        group by t.id_no ,t.date
    </select>

</mapper>
