<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkUuidMapper">


    <select id="queryUuidTaskListPage" resultType="com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO">
        select
        tu.tmk_task_id,
        tu.username,
        tu.uuid,
        tu.application_id,
        tu.user_id,
        tu.avl_credit,
        tu.credit_status,
        tuc.num_package_def as packageDefine,
        tu.is_income,
        tu.is_withdrawal,
        tu.mobile,
        tu.gender,
        tu.gmt_create,
        c.cdr_start_time as lastCalledAt,
        tsd.summary_content as contact_result,
        tsd.result_code,
        ts.comment,
        tu.assign_date as distributedAt,
        tu.credit_line,
        tu.call_num,
        tuc.call_instruction,
        tu.label_name as uuidLabelName
        from tmk_uuid tu
        <if test="dto.queryType != null and dto.queryType == 'L'.toString()">
            force index (tmk_uuid_assign_date_IDX)
        </if>
        join tmk_uuid_config tuc on tu.num_package_id = tuc.num_package_id and tu.call_type = tuc.call_type and tuc.call_type='man' and tuc.delete_flag=0
        left join tmk_summary ts on tu.summary_id = ts.id and tu.staff_id = ts.staff_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where 1=1
        <if test="dto.tmkTaskId != null and dto.tmkTaskId != ''">
            and tu.tmk_task_id = #{dto.tmkTaskId}
        </if>
        <if test="dto.username != null and dto.username != ''">
            and tu.username = #{dto.username}
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and tu.uuid = #{dto.uuid}
        </if>
        <if test="dto.userId != null and dto.userId != ''">
            and tu.user_id = #{dto.userId}
        </if>
        <if test="dto.applicationId != null and dto.applicationId != ''">
            and tu.application_id = #{dto.applicationId}
        </if>
        <if test="dto.packageDefine != null and dto.packageDefine != ''">
            and tuc.num_package_def like concat('%',#{dto.packageDefine},'%')
        </if>
        <if test="dto.gmtCreateStart != null and dto.gmtCreateStart != ''">
            and tu.gmt_create &gt;= #{dto.gmtCreateStart}
        </if>
        <if test="dto.gmtCreateEnd != null and dto.gmtCreateEnd != ''">
            and tu.gmt_create &lt;= #{dto.gmtCreateEnd}
        </if>
        <if test="dto.avlCredit != null">
            and tu.avl_credit &gt;= #{dto.avlCredit}
        </if>
        <if test="dto.avlCreditStart != null and dto.avlCreditStart != ''">
            and tu.avl_credit &gt;= #{dto.avlCreditStart}
        </if>
        <if test="dto.avlCreditEnd != null and dto.avlCreditEnd != ''">
            and tu.avl_credit &lt;= #{dto.avlCreditEnd}
        </if>
        <if test="dto.creditStatus != null and dto.creditStatus != ''">
            and tu.credit_status = #{dto.creditStatus}
        </if>
        <if test="dto.isIncome != null and dto.isIncome != ''">
            and tu.is_income = #{dto.isIncome}
        </if>
        <if test="dto.isWithdrawal != null and dto.isWithdrawal != ''">
            and tu.is_withdrawal = #{dto.isWithdrawal}
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "1"'>
            and ts.id is not null and ts.gmt_create &gt; tu.assign_date
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "0"'>
            and (ts.id is null or ts.gmt_create &lt; tu.assign_date)
        </if>
        <if test="dto.contactResultList != null and dto.contactResultList.size() >0">
            and tsd.result_code in
            <foreach collection="dto.contactResultList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.contactCode != null and dto.contactCode != ''">
            and tsd.summary_code = #{dto.contactCode}
        </if>
        <if test="dto.lastCalledAtStart != null and dto.lastCalledAtStart != ''">
            and c.cdr_start_time &gt;= #{dto.lastCalledAtStart}
        </if>
        <if test="dto.lastCalledAtEnd != null and dto.lastCalledAtEnd != ''">
            and c.cdr_start_time &lt;= #{dto.lastCalledAtEnd}
        </if>
        <if test="dto.distributedAtStart != null and dto.distributedAtStart != ''">
            and tu.assign_date &gt;= #{dto.distributedAtStart}
        </if>
        <if test="dto.distributedAtEnd != null and dto.distributedAtEnd != ''">
            and tu.assign_date &lt;= #{dto.distributedAtEnd}
        </if>
        <if test="dto.staffId != null and dto.staffId != ''">
            and tu.staff_id = #{dto.staffId}
        </if>
        <if test="dto.callNum != null and dto.callNum != ''">
            and tu.call_num = #{dto.callNum}
        </if>
        <if test="dto.uuidLabelNames != null and dto.uuidLabelNames.size() >0">
            and tu.label_name in
            <foreach collection="dto.uuidLabelNames" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
	
	<select id="queryTmkUuidData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkUuidReportVO">
        select
        tu.user_id,
        tu.uuid,
        tu.package_define,
        tu.applied_at,
        tu.confirmed_at,
        iacs.staff_name as staff_id,
        iacs.group_name as group_code,
        tsd.result_code,
        tsd.summary_content,
        ts.comment,
        ts.gmt_create as summaryAt,
        tu.gmt_create,
        c.cdr_bridge_time as callStartTime,
        c.cdr_end_time as callEndTime,
        TIME_TO_SEC(c.cdr_end_bridge_time) as callTime
        from tmk_summary ts
        join tmk_uuid tu on tu.tmk_task_id = ts.tmk_task_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join in_auth_crm_staff iacs on ts.staff_id = iacs.id
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where ts.gmt_create between #{dto.startTime} and #{dto.endTime}
            <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                and ts.staff_id in (
                <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
            <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                and iacs.group_code in (
                <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
    </select>
    <select id="queryUuidTransformData"
        resultType="com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO">
        select
        iacs.staff_name,
        iacs.group_name as groupCode,
        count(case when tt.month_diff &gt;= 0 then 1  else null end) as callOutCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 then 1 else null end) as answerCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 and tt.is_transform = 1 then 1  else null end) as transformCount,
        count(case when tt.month_diff &lt; 0 then 1  else null end) as hisCallOutCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 then 1 else null end) as hisAnswerCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 and tt.is_transform = 1 then 1  else null end) as hisTransformCount
        from (
        select
        t.*
        from
        (
        select
        ts.staff_id,
        cpci.cdr_start_time,
        tu.applied_at,
        tu.confirmed_at,
        tu.uuid,
        (case when tu.type = 'jj' and (timestampdiff(second,cpci.cdr_start_time,tu.applied_at)) > 0 and tu.is_income =
        '1' and tu.applied_at between #{dto.startTime} and #{dto.endTime} then 1
            when tu.type = 'tx' and (timestampdiff(second,cpci.cdr_start_time,tu.confirmed_at)) > 0 and tu.is_withdrawal =
        '1' and tu.confirmed_at between #{dto.startTime} and #{dto.endTime} then 1
         else 0 end) as is_transform,
        (DATE_FORMAT(tu.assign_date, '%Y%m')-DATE_FORMAT(#{dto.endTime},'%Y%m')) as month_diff,
        (case when cpci.cdr_bridge_time is not null then 1 else 0 end) as callestablished
        from tmk_uuid tu
        left join tmk_summary ts on tu.tmk_task_id = ts.tmk_task_id
        left join con_phone_call_info cpci on ts.cdr_main_unique_id = cpci.cdr_main_unique_id
        where 1 = 1
        and cpci.cdr_call_type = '4'
        and cpci.cdr_start_time between #{dto.startTime} and #{dto.endTime}
        and ts.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
            and cpci.staff_id in (
            <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
            and cpci.group_code in (
            <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        order by tu.uuid, callestablished desc, cpci.cdr_start_time
        limit 100000
        ) t group by t.uuid
        ) tt
        left join in_auth_crm_staff iacs on tt.staff_id = iacs.id
        group by tt.staff_id
        order by tt.staff_id

    </select>

    <select id="selectPackageDefine" resultType="String">
        SELECT distinct num_package_def FROM tmk_uuid_config force index (idx_tmk_uuid_config_gmt_create) WHERE gmt_create &gt;= #{date}
    </select>
    <select id="queryTaskCount" resultType="java.lang.Long">
        select
        count(1)
        from tmk_uuid tu
        <if test="dto.queryType != null and dto.queryType == 'L'.toString()">
            force index (tmk_uuid_assign_date_IDX)
        </if>
        join tmk_uuid_config tuc on tu.num_package_id = tuc.num_package_id and tu.call_type = tuc.call_type and tuc.call_type='man' and tuc.delete_flag=0
        left join tmk_summary ts on tu.summary_id = ts.id and tu.staff_id = ts.staff_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where 1=1
        <if test="dto.tmkTaskId != null and dto.tmkTaskId != ''">
            and tu.tmk_task_id = #{dto.tmkTaskId}
        </if>
        <if test="dto.username != null and dto.username != ''">
            and tu.username = #{dto.username}
        </if>
        <if test="dto.uuid != null and dto.uuid != ''">
            and tu.uuid = #{dto.uuid}
        </if>
        <if test="dto.userId != null and dto.userId != ''">
            and tu.user_id = #{dto.userId}
        </if>
        <if test="dto.applicationId != null and dto.applicationId != ''">
            and tu.application_id = #{dto.applicationId}
        </if>
        <if test="dto.packageDefine != null and dto.packageDefine != ''">
            and tuc.num_package_def like concat('%',#{dto.packageDefine},'%')
        </if>
        <if test="dto.gmtCreateStart != null and dto.gmtCreateStart != ''">
            and tu.gmt_create &gt;= #{dto.gmtCreateStart}
        </if>
        <if test="dto.gmtCreateEnd != null and dto.gmtCreateEnd != ''">
            and tu.gmt_create &lt;= #{dto.gmtCreateEnd}
        </if>
        <if test="dto.avlCredit != null">
            and tu.avl_credit &gt;= #{dto.avlCredit}
        </if>
        <if test="dto.creditStatus != null and dto.creditStatus != ''">
            and tu.credit_status = #{dto.creditStatus}
        </if>
        <if test="dto.isIncome != null and dto.isIncome != ''">
            and tu.is_income = #{dto.isIncome}
        </if>
        <if test="dto.isWithdrawal != null and dto.isWithdrawal != ''">
            and tu.is_withdrawal = #{dto.isWithdrawal}
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "1"'>
            and ts.id is not null and ts.gmt_create &gt; tu.assign_date
        </if>
        <if test='dto.callStatus != null and dto.callStatus == "0"'>
            and (ts.id is null or ts.gmt_create &lt; tu.assign_date)
        </if>
        <if test="dto.contactResultList != null and dto.contactResultList.size() >0">
            and tsd.result_code in
            <foreach collection="dto.contactResultList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.contactCode != null and dto.contactCode != ''">
            and tsd.summary_code = #{dto.contactCode}
        </if>
        <if test="dto.lastCalledAtStart != null and dto.lastCalledAtStart != ''">
            and c.cdr_start_time &gt;= #{dto.lastCalledAtStart}
        </if>
        <if test="dto.lastCalledAtEnd != null and dto.lastCalledAtEnd != ''">
            and c.cdr_start_time &lt;= #{dto.lastCalledAtEnd}
        </if>
        <if test="dto.distributedAtStart != null and dto.distributedAtStart != ''">
            and tu.assign_date &gt;= #{dto.distributedAtStart}
        </if>
        <if test="dto.distributedAtEnd != null and dto.distributedAtEnd != ''">
            and tu.assign_date &lt;= #{dto.distributedAtEnd}
        </if>
        <if test="dto.staffId != null and dto.staffId != ''">
            and tu.staff_id = #{dto.staffId}
        </if>
        <if test="dto.callNum != null and dto.callNum != ''">
            and tu.call_num = #{dto.callNum}
        </if>
        <if test="dto.uuidLabelNames != null and dto.uuidLabelNames.size() >0">
            and tu.label_name in
            <foreach collection="dto.uuidLabelNames" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
