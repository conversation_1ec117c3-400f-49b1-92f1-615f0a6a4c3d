<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CsRepayFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CsRepayFile">
        <id column="id" property="id" />
        <result column="file_url" property="fileUrl" />
        <result column="user_count" property="userCount" />
        <result column="task_state" property="taskState" />
        <result column="approve_state" property="approveState" />
        <result column="approve_time" property="approveTime" />
        <result column="remark" property="remark" />
        <result column="auditor" property="auditor" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_url, user_count, task_state, approve_state, approve_time, remark, auditor, create_user, lst_upd_user, gmt_create, gmt_modify, loan_type
    </sql>

    <select id="listRepayDetailPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_repay_file
        <where>
            <if test="filter.startTime != null and filter.startTime != ''">
                and gmt_create &gt;= str_to_date(#{filter.startTime}, '%Y-%m-%d %H:%i')
            </if>
            <if test="filter.endTime != null and filter.endTime != ''">
                and gmt_create &lt;= str_to_date(#{filter.endTime}, '%Y-%m-%d %H:%i')
            </if>
            <if test="filter.taskState != null">
                and task_state = #{filter.taskState}
            </if>
            <if test="filter.createUser != null and filter.createUser != ''">
                and create_user like concat('%',#{filter.createUser},'%')
            </if>
            <if test="filter.loanType != null and filter.loanType != ''">
                and loan_type = #{filter.loanType}
            </if>
        </where>
        order by gmt_create desc
    </select>

    <select id="selectWalletUnComplete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_repay_file
        <where>
            <if test="filter.taskState != null">
                and task_state = #{filter.taskState}
            </if>
            and date_sub(now(),interval 1 minute) &lt; gmt_create
        </where>
    </select>

</mapper>
