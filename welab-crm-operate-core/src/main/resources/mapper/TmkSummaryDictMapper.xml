<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkSummaryDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.TmkSummaryDict">
        <id column="id" property="id" />
        <result column="summary_code" property="summaryCode" />
        <result column="business_type" property="businessType" />
        <result column="summary_content" property="summaryContent" />
        <result column="result_code" property="resultCode" />
        <result column="sort" property="sort" />
        <result column="state" property="state" />
        <result column="create_user" property="createUser" />
        <result column="lst_upd_user" property="lstUpdUser" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, summary_code, business_type, summary_content, result_code, sort, state, create_user, lst_upd_user, gmt_create, gmt_modify
    </sql>

</mapper>
