<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ConRepeatCallMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ConRepeatCall">
        <id column="id" property="id" />
        <result column="hotline" property="hotline" />
        <result column="cno" property="cno" />
        <result column="staff_id" property="staffId" />
        <result column="mobile" property="mobile" />
        <result column="repeat_number" property="repeatNumber" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, hotline, cno, staff_id, mobile, repeat_number, gmt_create, gmt_modify
    </sql>

    <select id="selectManDupCalls" resultType="com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO">
        select hotline, sum(repeat_number) as duplicateCalls
        <if test="period=='day'">
            ,date_format(gmt_create,'%Y-%m-%d') as callDay
        </if>
        from con_repeat_call
        where cno is not null
        and gmt_create between #{startTime} and #{endTime}
        and hotline in
        <foreach collection="hotline" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        group by hotline
        <if test="period=='day'">
            ,date_format(gmt_create,'%Y-%m-%d')
        </if>
    </select>

    <select id="selectRepeatNumber" resultType="int">
        select
        ifnull(sum(crc.repeat_number), 0)  as repeat_number
        from
        con_repeat_call crc
        join in_phone_login_info ipli on
        ipli.id_no = crc.cno
        join in_auth_crm_staff iacs on
        iacs.login_name = ipli.user_tel
        and iacs.is_status = 1
        where
        crc.gmt_create between #{filter.startTime} and #{filter.endTime}
        <if test="filter.groupCode!=null and filter.groupCode!=''">
            and iacs.group_code=#{filter.groupCode}
        </if>
        <if test="filter.staffId!=null and filter.staffId!=''">
            and iacs.id=#{filter.staffId}
        </if>
    </select>

</mapper>
