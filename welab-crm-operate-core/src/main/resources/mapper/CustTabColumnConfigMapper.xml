<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.CustTabColumnConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.CustTabColumnConfig">
        <id column="id" property="id" />
        <result column="tab_name" property="tabName" />
        <result column="tab_column" property="tabColumn" />
        <result column="staff_id" property="staffId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tab_name, tab_column, staff_id, gmt_create, gmt_modify
    </sql>

    <select id="selectByStaffIdAndTabName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cust_tab_column_config
        where staff_id = #{staffId, jdbcType=VARCHAR}
        and tab_name = #{tabName, jdbcType=VARCHAR}
    </select>

    <delete id="deleteByStaffIdAndTabName">
        delete from cust_tab_column_config
        where staff_id = #{staffId, jdbcType=VARCHAR}
        and tab_name = #{tabName, jdbcType=VARCHAR}
    </delete>

    <insert id="insertBatch">
        insert into cust_tab_column_config(
            id, tab_name, tab_column, staff_id
        ) values
        <foreach collection="list" index="index" item="item"  separator="," open="" close="">
            (#{item.id}, #{item.tabName}, #{item.tabColumn}, #{item.staffId})
        </foreach>
    </insert>
</mapper>
