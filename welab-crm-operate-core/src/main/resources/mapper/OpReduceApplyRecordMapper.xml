<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpReduceApplyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpReduceApplyRecord">
        <id column="id" property="id" />
        <result column="application_id" property="applicationId" />
        <result column="user_id" property="userId" />
        <result column="amount" property="amount" />
        <result column="reducible_amount" property="reducibleAmount" />
        <result column="rate_after_reduce" property="rateAfterReduce" />
        <result column="reduction_type" property="reductionType" />
        <result column="reduction_reason" property="reductionReason" />
        <result column="approval_status" property="approvalStatus" />
        <result column="push_flag" property="pushFlag" />
        <result column="staff_id" property="staffId" />
        <result column="request_no" property="requestNo" />
        <result column="result" property="result" />
        <result column="complaint_channel" property="complaintChannel" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="customer_name" property="customerName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_id, user_id, amount, reducible_amount, rate_after_reduce, reduction_type, reduction_reason, approval_status, push_flag, staff_id, request_no, result, complaint_channel, gmt_create, gmt_modify, customer_name
    </sql>
    <select id="selectApplyRecordPage" resultType="com.welab.crm.operate.vo.reduce.ReduceAndRefundVO">
        select
            customer_name as name,
            orar.complaint_channel ,
            user_id ,
            orar.gmt_create as applyTime,
            iacs.staff_name as applyName,
            orral1.gmt_create as processTime,
            orar.approval_status ,
            task_status ,
            orar.request_no
        from
            op_reduce_apply_record orar
                join in_auth_crm_staff iacs on
                orar.staff_id = iacs.id
                join op_reduce_refund_approval_log orral1 on
                    orral1.id =
                    (
                        select
                            id
                        from
                            op_reduce_refund_approval_log orral2
                        where
                            orral2.request_no = orar.request_no
                        order by
                            orral2.id desc
            limit 1
            )
        where
            1=1
            <if test="dto.applyTimeStart != null and dto.applyTimeStart != ''">
                and orar.gmt_create &gt;= #{dto.applyTimeStart}
            </if>
            <if test="dto.applyTimeEnd != null and dto.applyTimeEnd != ''">
                and orar.gmt_create &lt;= #{dto.applyTimeEnd}
            </if>
            <if test="dto.userId != null">
                and user_id = #{dto.userId}
            </if>
            <if test="dto.applicationId != null and dto.applicationId != ''">
                and application_id = #{dto.applicationId}
            </if>
            <if test="dto.approvalStatus != null and dto.approvalStatus != ''">
                and orar.approval_status = #{dto.approvalStatus}
            </if>
            <if test="dto.taskStatus != null and dto.taskStatus != ''">
                and task_status = #{dto.taskStatus}
            </if>
            <if test="dto.name != null and dto.name != ''">
                and customer_name = #{dto.name}
            </if>
            <if test="dto.applyName != null and dto.applyName != ''">
                and iacs.staff_name = #{dto.applyName}
            </if>
        union
        select
            customer_name as name,
            orar.complaint_channel ,
            user_id ,
            orar.gmt_create as applyTime,
            iacs.staff_name as applyName,
            orral1.gmt_create as processTime,
            orar.approval_status ,
            task_status ,
            orar.request_no
        from
            op_refund_apply_record orar
                join in_auth_crm_staff iacs on
                orar.staff_id = iacs.id
                join op_reduce_refund_approval_log orral1 on
                    orral1.id =
                    (
                        select
                            id
                        from
                            op_reduce_refund_approval_log orral2
                        where
                            orral2.request_no = orar.request_no
                        order by
                            orral2.id desc
            limit 1
            )
        where
            1=1
            <if test="dto.applyTimeStart != null and dto.applyTimeStart != ''">
                and orar.gmt_create &gt;= #{dto.applyTimeStart}
            </if>
            <if test="dto.applyTimeEnd != null and dto.applyTimeEnd != ''">
                and orar.gmt_create &lt;= #{dto.applyTimeEnd}
            </if>
            <if test="dto.userId != null">
                and user_id = #{dto.userId}
            </if>
            <if test="dto.applicationId != null and dto.applicationId != ''">
                and application_id = #{dto.applicationId}
            </if>
            <if test="dto.approvalStatus != null and dto.approvalStatus != ''">
                and orar.approval_status = #{dto.approvalStatus}
            </if>
            <if test="dto.taskStatus != null and dto.taskStatus != ''">
                and task_status = #{dto.taskStatus}
            </if>
            <if test="dto.name != null and dto.name != ''">
                and customer_name = #{dto.name}
            </if>
            <if test="dto.applyName != null and dto.applyName != ''">
                and iacs.staff_name = #{dto.applyName}
            </if>
        order by applyTime desc
    </select>

    <select id="selectApplyRecordApplyName" resultType="java.lang.String">
        select
        applyName
        from
        (
        select
        iacs.staff_name as applyName
        from
        op_reduce_apply_record orar
        join in_auth_crm_staff iacs on
        orar.staff_id = iacs.id
        join op_reduce_refund_approval_log orral1 on
        orral1.id =
        (
        select
        id
        from
        op_reduce_refund_approval_log orral2
        where
        orral2.request_no = orar.request_no
        order by
        orral2.id desc
        limit 1
        )
        union
        select
        iacs.staff_name as applyName
        from
        op_refund_apply_record orar
        join in_auth_crm_staff iacs on
        orar.staff_id = iacs.id
        join op_reduce_refund_approval_log orral1 on
        orral1.id =
        (
        select
        id
        from
        op_reduce_refund_approval_log orral2
        where
        orral2.request_no = orar.request_no
        order by
        orral2.id desc
        limit 1
        )) t
        group by t.applyName
    </select>

    <select id="selectReduceRefundDetailPage"
            resultType="com.welab.crm.operate.vo.reduce.ReduceDetailReportVO">
        select
        orral.gmt_create as applyTime,
        orar.amount as reduceAmount,
        orral.reduction_reason as reduceReason,
        orar2.amount as refundAmount,
        orar.rate_after_reduce as rateAfterReduce,
        iacs.login_name as applyStaff,
        orar.reduction_type as reduceType,
        CONCAT_WS(',', orar.result, orar2.result) as processStatus,
        CONCAT_WS(',', orar.fail_reason, orar2.fail_reason) as failReason,
        orar.id as reduceId,
        orar2.id as refundId,
        orral.request_no,
        orral.complaint_channel as complaintChannel,
        orar.product_name as productName,
        orar.customer_name as customerName,
        orar.uuid as uuid,
        orar.user_id as userId,
        orar.application_id as applicationId,
        orar.partner_code as partnerCode,
        orar.total_rate as actualRate,
        orar2.refund_type,
        case
        when orar.id is null then '退款'
        when orar2.id is null then '减免'
        else '减免,退款'
        end as applyType,
        case
            when orar.task_status = '1' then GREATEST(ifnull(orar.gmt_modify,'0'),ifnull(orar2.gmt_modify,'0')) 
            else '' end as processEndTime
        from
        op_reduce_refund_approval_log orral
        left join op_reduce_apply_record orar on
        orral.request_no = orar.request_no
        left join op_refund_apply_record orar2 on
        orral.request_no = orar2.request_no
        and orar.application_id = orar2.application_id
        left join in_auth_crm_staff iacs on
        orral.staff_id = iacs.id
        where
        orral.approval_status = '0'
        and (orar.id is not null
        or orar2.id is not null)
        and orral.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.applyTypeList.contains('reduce') and !dto.applyTypeList.contains('refund')">
            and orar.id is not null
        </if>
        <if test="dto.applyTypeList.contains('refund') and !dto.applyTypeList.contains('reduce') ">
            and orar2.id is not null
        </if>
        union
        select
        orral.gmt_create as applyTime,
        orar.amount as reduceAmount,
        orral.reduction_reason as reduceReason,
        orar2.amount as refundAmount,
        orar.rate_after_reduce as rateAfterReduce,
        iacs.login_name as applyStaff,
        orar.reduction_type as reduceType,
        CONCAT_WS(',', orar.result, orar2.result) as processStatus,
        CONCAT_WS(',', orar.fail_reason, orar2.fail_reason) as failReason,
        orar.id as reduceId,
        orar2.id as refundId,
        orral.request_no,
        orral.complaint_channel as complaintChannel,
        orar2.product_name as productName,
        orar2.customer_name as customerName,
        orar2.uuid as uuid,
        orar2.user_id as userId,
        orar2.application_id as applicationId,
        orar2.partner_code as partnerCode,
        orar2.total_rate as actualRate,
        orar2.refund_type,
        case
        when orar.id is null then '退款'
        when orar2.id is null then '减免'
        else '减免,退款'
        end as applyType,
        case
        when orar2.task_status = '1' then GREATEST(ifnull(orar.gmt_modify,'0'),ifnull(orar2.gmt_modify,'0'))
        else '' end as processEndTime
        from
        op_reduce_refund_approval_log orral
        left join op_refund_apply_record orar2 on
        orral.request_no = orar2.request_no
        left join op_reduce_apply_record orar on
        orral.request_no = orar.request_no
        and orar2.application_id = orar.application_id
        left join in_auth_crm_staff iacs on
        orral.staff_id = iacs.id
        where
        orral.approval_status = '0'
        and (orar.id is not null
        or orar2.id is not null)
        and orral.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.applyTypeList.contains('reduce') and !dto.applyTypeList.contains('refund')">
            and orar.id is not null
        </if>
        <if test="dto.applyTypeList.contains('refund') and !dto.applyTypeList.contains('reduce') ">
            and orar2.id is not null
        </if>
        order by request_no desc
    </select>
    <select id="queryStatisticData" resultType="com.welab.crm.operate.vo.reduce.ReduceStatisticsReportVO">
        select
        t1.reduceReason,
        t1.reduceAmount,
        t1.reduceCount,
        t2.refundAmount,
        t1.applyStaff,
        t1.reduceType,
        t2.refundType
        from
        (
        select
        orral.reduction_reason as reduceReason,
        sum(orar.amount) as reduceAmount,
        sum(case when orar.id is not null then 1 else 0 end) as reduceCount,
        ifnull(orar.reduction_type, '') as reduceType,
        ifnull(orar2.refund_type,'') as refundType,
        iacs.login_name as applyStaff,
        orar.request_no as requestNo
        from
        op_reduce_refund_approval_log orral
        left join op_reduce_apply_record orar
        on
        orral.request_no = orar.request_no
        left join op_refund_apply_record orar2 on
        orar2.id = (
        select
        id
        from
        op_refund_apply_record tt
        where
        tt.request_no = orar.request_no
        limit 1)
        join in_auth_crm_staff iacs on
        orral.staff_id = iacs.id
        where
        orral.approval_status = '0'
        and orral.gmt_create between #{dto.startTime} and #{dto.endTime}
        and orar.approval_status = '2'
        <if test="dto.reduceReasonList != null and dto.reduceReasonList.size() > 0">
            and orar.reduction_reason in
            <foreach collection="dto.reduceReasonList" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.reduceTypeList != null and dto.reduceTypeList.size() > 0">
            and orar.reduction_type in
            <foreach collection="dto.reduceTypeList" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.applyStaffIds != null and dto.applyStaffIds.size() > 0">
            and orar.staff_id in
            <foreach collection="dto.applyStaffIds" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by
        orar.reduction_reason ,
        orar.reduction_type,
        orar.staff_id,
        orar2.refund_type
        ) t1
        left join (
        select
        orral.reduction_reason as reduceReason,
        sum(orar2.amount) as refundAmount,
        iacs.login_name as applyStaff,
        orral.request_no as requestNo,
        ifnull(orar.reduction_type, '') as reduceType,
        ifnull(orar2.refund_type, '') as refundType
        from
        op_reduce_refund_approval_log orral
        left join op_refund_apply_record orar2 on
        orral.request_no = orar2.request_no
        left join op_reduce_apply_record orar on
        orar.id = (
        select
        id
        from
        op_reduce_apply_record tt
        where
        tt.request_no = orar2.request_no
        limit 1)
        join in_auth_crm_staff iacs on
        orral.staff_id = iacs.id
        where
        orral.approval_status = '0'
        and orral.gmt_create between #{dto.startTime} and #{dto.endTime}
        and orar2.approval_status = '2'
        <if test="dto.reduceReasonList != null and dto.reduceReasonList.size() > 0">
            and orar.reduction_reason in
            <foreach collection="dto.reduceReasonList" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.reduceTypeList != null and dto.reduceTypeList.size() > 0">
            and orar.reduction_type in
            <foreach collection="dto.reduceTypeList" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.applyStaffIds != null and dto.applyStaffIds.size() > 0">
            and orar.staff_id in
            <foreach collection="dto.applyStaffIds" index="index" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by
        orral.reduction_reason ,
        orar.reduction_type,
        orral.staff_id,
        orar2.refund_type
        )t2 on
        t1.reduceReason = t2.reduceReason
        and t1.applyStaff = t2.applyStaff
        and t1.reduceType = t2.reduceType
        and t1.refundType = t2.refundType
        

    </select>

</mapper>
