<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkWalletMapper">


    <select id="queryWalletTaskListPage" resultType="com.welab.crm.operate.vo.telemarketing.TmkTaskDetailVO">
        select t.*
        from(
            select
            t0.tmk_task_id,
            t0.username,
            t0.uuid,
            t0.mobile,
            t0.application_id,
            t0.gmt_create,
            t0.apply_origin,
            (case
            when t0.is_consu = '1' then '已消费'
            when t0.is_consu = '0' then '未消费'
            end
            ) as isConsu,
            c.cdr_start_time as lastCalledAt,
            tsd.summary_content as contact_result,
            tsd.result_code,
            ts.comment,
            t0.credit_line,
            t0.avl_credit,
            t0.register_at,
            t0.education,
            t0.reg_origin,
            t0.approved_at,
            t0.applied_at,
            t0.user_id,
            t0.assign_date as distributedAt,
            t0.product_code as productCode,
            t0.call_num
        from tmk_wallet t0
            left join tmk_summary ts on t0.summary_id = ts.id and t0.staff_id = ts.staff_id
            left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
            left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
            left join data_customer dc on t0.uuid = dc.uuid and dc.cust_type = 'wallet'
            where 1=1
            <if test="dto.username != null and dto.username != ''">
                and t0.username = #{dto.username}
            </if>
            <if test="dto.uuid != null and dto.uuid != ''">
                and t0.uuid = #{dto.uuid}
            </if>
            <if test="dto.mobile != null and dto.mobile != ''">
                and t0.mobile = #{dto.mobile}
            </if>
            <if test="dto.applicationId != null and dto.applicationId != ''">
                and t0.application_id = #{dto.applicationId}
            </if>
            <if test="dto.gmtCreateStart != null and dto.gmtCreateStart != ''">
                and t0.gmt_create &gt;= #{dto.gmtCreateStart}
            </if>
            <if test="dto.gmtCreateEnd != null and dto.gmtCreateEnd != ''">
                and t0.gmt_create &lt;= #{dto.gmtCreateEnd}
            </if>
            <if test="dto.source != null and dto.source != ''">
                and t0.apply_origin like concat('%',#{dto.source},'%')
            </if>
            <if test="dto.isConsu != null and dto.isConsu != ''">
                and t0.is_consu = #{dto.isConsu}
            </if>
            <if test="dto.avlCredit != null and dto.avlCredit != ''">
                and dc.avl_creditline &gt;= #{dto.avlCredit}
            </if>
            <if test="dto.avlCreditStart != null and dto.avlCreditStart != ''">
                and dc.avl_creditline &gt;= #{dto.avlCreditStart}
            </if>
            <if test="dto.avlCreditEnd != null and dto.avlCreditEnd != ''">
                and dc.avl_creditline &lt;= #{dto.avlCreditEnd}
            </if>
            <if test="dto.userId != null and dto.userId !=''">
                and t0.user_id = #{dto.userId}
            </if>
            <if test="dto.tmkTaskId != null and dto.tmkTaskId != ''">
                and t0.tmk_task_id = #{dto.tmkTaskId}
            </if>
            <if test='dto.callStatus != null and dto.callStatus == "1"'>
                and ts.id is not null and ts.gmt_create &gt; t0.assign_date
            </if>
            <if test='dto.callStatus != null and dto.callStatus == "0"'>
                and (ts.id is null or ts.gmt_create &lt; t0.assign_date)
            </if>
            <if test="dto.contactResultList != null and dto.contactResultList.size() >0">
                and tsd.result_code in
                <foreach collection="dto.contactResultList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.contactCode != null and dto.contactCode != ''">
                and tsd.summary_code = #{dto.contactCode}
            </if>
            <if test="dto.lastCalledAtStart != null and dto.lastCalledAtStart != ''">
                and c.cdr_start_time &gt;= #{dto.lastCalledAtStart}
            </if>
            <if test="dto.lastCalledAtEnd != null and dto.lastCalledAtEnd != ''">
                and c.cdr_start_time &lt;= #{dto.lastCalledAtEnd}
            </if>
            <if test="dto.distributedAtStart != null and dto.distributedAtStart != ''">
                and t0.assign_date &gt;= #{dto.distributedAtStart}
            </if>
            <if test="dto.distributedAtEnd != null and dto.distributedAtEnd != ''">
                and t0.assign_date &lt;= #{dto.distributedAtEnd}
            </if>
            <if test="dto.staffId != null and dto.staffId != ''">
                and t0.staff_id = #{dto.staffId}
            </if>
            <if test="dto.creditStatus != null and dto.creditStatus != ''">
                and dc.credit_state = #{dto.creditStatus}
            </if>
            <if test="dto.callNum != null and dto.callNum != ''">
                and t0.call_num = #{dto.callNum}
            </if>

            order by ts.gmt_create desc
            limit 100000
        ) t
        group by tmk_task_id


    </select>
    <select id="queryWalletTransformData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO">
        select
        iacs.staff_name,
        iacs.group_name as groupCode,
        count(case when tt.month_diff &gt;= 0 then 1 else null end) as callOutCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 then 1 else null end) as answerCount,
        count(case when tt.month_diff &gt;= 0 and tt.callestablished = 1 and tt.first_consu_date &gt;= #{dto.startTime} and
        tt.first_consu_date &lt; #{dto.endTime} and tt.conver_time > 0 and tt.is_consu = '1' then 1 else null end) as transformCount,
        count(case when tt.month_diff &lt; 0 then 1 else null end) as hisCallOutCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 then 1 else null end) as hisAnswerCount,
        count(case when tt.month_diff &lt; 0 and tt.callestablished = 1 and tt.first_consu_date &gt;= #{dto.startTime} and
        tt.first_consu_date &lt; #{dto.endTime} and tt.conver_time > 0 and tt.is_consu = '1' then 1 else null end) as hisTransformCount
        from (
        select
        t.*
        from
        (
        select
        ts.staff_id,
        cpci.cdr_start_time,
        ta.gmt_create,
        ta.is_consu,
        ta.first_consu_date,
        ta.application_id,
        timestampdiff(second,cpci.cdr_start_time,ta.first_consu_date) as conver_time,
        (DATE_FORMAT(ta.assign_date, '%Y%m')-DATE_FORMAT(#{dto.endTime},'%Y%m')) as month_diff,
        (case when cpci.cdr_bridge_time is not null then 1 else 0 end) as callestablished
        from tmk_wallet ta
        left join tmk_summary ts on ta.tmk_task_id = ts.tmk_task_id
        left join con_phone_call_info cpci on ts.cdr_main_unique_id = cpci.cdr_main_unique_id
        where 1 = 1
        and cpci.cdr_call_type = '4'
        and cpci.cdr_start_time between #{dto.startTime} and #{dto.endTime}
        and ts.gmt_create between #{dto.startTime} and #{dto.endTime}
        <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
            and cpci.staff_id in (
            <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
            and cpci.group_code in (
            <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">#{item}
            </foreach>
            )
        </if>
        order by ta.application_id, callestablished desc, cpci.cdr_start_time
        limit 100000
        ) t group by t.application_id
        ) tt
        left join in_auth_crm_staff iacs on tt.staff_id = iacs.id
        group by tt.staff_id
        order by tt.staff_id

    </select>
	
	<select id="queryTmkWalletData"
            resultType="com.welab.crm.operate.vo.telemarketing.TmkWalletReportVO">
        select
        tw.user_id,
        tw.uuid,
        '钱夹谷谷' as tmkType,
        tw.education,
        tw.reg_origin,
        tw.application_id,
        tw.applied_at,
        tw.apply_origin,
        tw.approved_at,
        tw.credit_line,
        tw.avl_credit,
        tw.first_consu_Date,
        tw.first_consu_succ_date,
        tw.consu_amount,
        iacs.staff_name as staff_id,
        iacs.group_name as group_code,
        tsd.result_code,
        tsd.summary_content,
        ts.comment,
        ts.gmt_create as summaryAt,
        tw.gmt_create,
        c.cdr_bridge_time as callStartTime,
        c.cdr_end_time as callEndTime,
        TIME_TO_SEC(c.cdr_end_bridge_time) as callTime
        from tmk_summary ts
        join tmk_wallet tw on tw.tmk_task_id = ts.tmk_task_id
        left join tmk_summary_dict tsd on ts.contact_code = tsd.summary_code
        left join in_auth_crm_staff iacs on ts.staff_id = iacs.id
        left join con_phone_call_info c on ts.cdr_main_unique_id = c.cdr_main_unique_id
        where ts.gmt_create between #{dto.startTime} and #{dto.endTime}
            <if test="dto.staffIdList != null and dto.staffIdList.size() > 0">
                and ts.staff_id in (
                <foreach collection="dto.staffIdList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
            <if test="dto.groupCodeList != null and dto.groupCodeList.size() > 0">
                and iacs.group_code in (
                <foreach collection="dto.groupCodeList" separator="," item="item" index="index" open="" close="">
                #{item}
                </foreach>
                )
            </if>
    </select>
</mapper>
