<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.WoAppointCallbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.WoAppointCallback">
        <id column="id" property="id" />
        <result column="appoint_time" property="appointTime" />
        <result column="appoint_title" property="appointTitle" />
        <result column="push_time" property="pushTime" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="staff_id" property="staffId" />
        <result column="gmt_create" property="gmtCreate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, appoint_time, appoint_title, push_time, order_no, status, staff_id, gmt_create
    </sql>

	<select id="queryAppointCallback" resultType="com.welab.crm.operate.domain.WoAppointCallback">
        SELECT
            *
        FROM wo_appoint_callback t
        WHERE 1 = 1 
        <if test="reqDTO.orderNo != null and reqDTO.orderNo != ''">
            and t.order_no = #{reqDTO.orderNo}
        </if>
        <if test="reqDTO.staffId != null and reqDTO.staffId != ''">
            and t.staff_id = #{reqDTO.staffId}
        </if>
        limit 1
    </select>
</mapper>
