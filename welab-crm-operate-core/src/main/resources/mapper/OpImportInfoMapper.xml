<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.OpImportInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.OpImportInfo">
        <id column="id" property="id" />
        <result column="file_name" property="fileName" />
        <result column="type" property="type" />
        <result column="create_user" property="createUser" />
        <result column="count" property="count" />
        <result column="gmt_create" property="gmtCreate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_name, type, create_user, count, gmt_create
    </sql>
    
	<select id="selectImportByPage" resultType="com.welab.crm.operate.vo.loan.LoanImportVO">
        select
            *
        from op_import_info oii
        where 1=1 
        <if test="filter.fileName != null and filter.fileName != ''">
            and oii.file_name like concat('%',#{filter.fileName},'%')
        </if>
        <if test="filter.type != null and filter.type != ''">
            and oii.type = #{filter.type}
        </if>
        <if test="filter.startCreateTime != null">
			and oii.gmt_create &gt;= #{filter.startCreateTime}
		</if>
		<if test="filter.endCreateTime != null">
			and oii.gmt_create &lt;= #{filter.endCreateTime}
		</if>
        order by oii.gmt_create desc
    </select>
    
    <select id="getImportLabelInfo" resultType="com.welab.crm.operate.vo.loan.LoanImportLabelVO">
       	select
            ooi.company_tel as companyTel,
            '0' as type,
            '' as companyName,
            null as deptDate
	        from op_outcase_info ooi
	        where ooi.application_id = #{applicationId}
	       limit 1
    </select>
</mapper>
