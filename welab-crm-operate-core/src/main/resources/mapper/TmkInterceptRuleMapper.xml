<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkInterceptRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.TmkInterceptRule">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="tmk_type" property="tmkType" />
        <result column="intercept_type" property="interceptType" />
        <result column="status" property="status" />
        <result column="product_code" property="productCode" />
        <result column="channel_code" property="channelCode" />
        <result column="valid_days" property="validDays" />
        <result column="remark" property="remark" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="create_staff_id" property="createStaffId" />
        <result column="modify_staff_id" property="modifyStaffId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, tmk_type, intercept_type, status, product_code, channel_code, valid_days, remark, gmt_create, gmt_modify, create_staff_id, modify_staff_id
    </sql>

    <select id="queryInterceptRuleByPage" resultType="com.welab.crm.operate.vo.tmkRule.TmkInterceptRuleVO">
        SELECT
        t.*,
        iacs1.staff_name as createStaffName,
        iacs2.staff_name as modifyStaffName
        FROM tmk_intercept_rule t
        left join in_auth_crm_staff iacs1 on iacs1.id = t.create_staff_id
        left join in_auth_crm_staff iacs2 on iacs2.id = t.modify_staff_id
        WHERE 1 = 1
        <if test="reqDTO.interceptType != null and reqDTO.interceptType != ''">
            and t.intercept_type = #{reqDTO.interceptType}
        </if>
        <if test="reqDTO.status != null and reqDTO.status != ''">
            and t.status = #{reqDTO.status}
        </if>
        <if test="reqDTO.productCode != null and reqDTO.productCode != ''">
            and t.product_code like concat('%',#{reqDTO.productCode},'%')
        </if>
        <if test="reqDTO.channelCode != null and reqDTO.channelCode != ''">
            and t.channel_code like concat('%',#{reqDTO.channelCode},'%')
        </if>
        <if test="reqDTO.remark != null and reqDTO.remark != ''">
            and t.remark = #{reqDTO.remark}
        </if>
        <if test="tmkTypes != null and tmkTypes.size() > 0">
            and t.tmk_type in
            <foreach item="item" index="index" collection="tmkTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t.gmt_modify desc
    </select>
    
    <select id="queryInterceptHistoryByPage" resultType="com.welab.crm.operate.vo.tmkRule.TmkInterceptHistoryVO">
        SELECT
            t.*
        FROM tmk_intercept_history t 
        WHERE t.rule_id = #{reqDTO.id}
        ORDER BY t.gmt_modify desc
    </select>
</mapper>
