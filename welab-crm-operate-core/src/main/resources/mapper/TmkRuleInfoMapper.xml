<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.TmkRuleInfoMapper">

    <select id="queryTmkInfoByPage" resultType="com.welab.crm.operate.vo.tmkRule.TmkInfoVO">
	    <if test='reqDTO.tmkType == "loan"'>
	        SELECT
	            t.uuid,
	            t.user_id as userId,
	            t.product_name as productName,
	            t.apply_origin as applyOrigin,
	            t.approved_at as approvedAt,
	            'loan' as tmkType,
	            t.tmk_task_id as tmkTaskId,
	            t.username,
	            t.mobile,
	            t.diversion_tag
	        FROM tmk_loan_invite t 
	        WHERE t.flag in('0', '7') and t.type = 'loan'
	        <if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
			<if test="reqDTO.productNameList != null and reqDTO.productNameList.size() > 0">
				and t.product_name in
				<foreach collection="reqDTO.productNameList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.originList != null and reqDTO.originList.size() > 0">
				and t.apply_origin in
				<foreach collection="reqDTO.originList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.approvedAtStart != null">
				and t.approved_at &gt;= #{reqDTO.approvedAtStart}
			</if>
			<if test="reqDTO.approvedAtEnd != null">
				and t.approved_at &lt;= #{reqDTO.approvedAtEnd}
			</if>
			<if test='reqDTO.isQueryTag == 0'>
				and (t.diversion_tag is null or t.diversion_tag = '')
			</if>
			<if test='reqDTO.isQueryTag == 1'>
				and t.diversion_tag is not null and t.diversion_tag != ''
			</if>
			<if test="reqDTO.diversionTag != null and reqDTO.diversionTag != ''">
				and t.diversion_tag = #{reqDTO.diversionTag}
			</if>
	        order by t.approved_at desc
		</if>

		<if test='reqDTO.tmkType == "credit"'>
	        SELECT
	            t.uuid,
	            t.user_id as userId,
	            t.product_name as productName,
	            t.apply_origin as applyOrigin,
	            t.approved_at as approvedAt,
	            'credit' as tmkType,
	            t.tmk_task_id as tmkTaskId,
				t.username as customerName,
				t.mobile,
				t.diversion_tag
	        FROM tmk_loan_invite t 
	        WHERE t.flag in('0', '7') and t.type = 'credit'
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
			<if test="reqDTO.productNameList != null and reqDTO.productNameList.size() > 0">
				and t.product_name in
				<foreach collection="reqDTO.productNameList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.originList != null and reqDTO.originList.size() > 0">
				and t.apply_origin in
				<foreach collection="reqDTO.originList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.approvedAtStart != null">
				and t.approved_at &gt;= #{reqDTO.approvedAtStart}
			</if>
			<if test="reqDTO.approvedAtEnd != null">
				and t.approved_at &lt;= #{reqDTO.approvedAtEnd}
			</if>
			<if test='reqDTO.isQueryTag == 0'>
				and (t.diversion_tag is null or t.diversion_tag = '')
			</if>
			<if test='reqDTO.isQueryTag == 1'>
				and t.diversion_tag is not null and t.diversion_tag != ''
			</if>
			<if test="reqDTO.diversionTag != null and reqDTO.diversionTag != ''">
				and t.diversion_tag = #{reqDTO.diversionTag}
			</if>
			order by t.approved_at desc
		</if>

		<if test='reqDTO.tmkType == "uuid"'>
			SELECT
			t.uuid,
			t.user_id as userId,
			'uuid' as tmkType,
			t.tmk_task_id as tmkTaskId,
			t.username as customerName,
			tuc.num_package_def as numPackageDef,
			tuc.call_instruction as callInstruction,
			t.gmt_create as gmtCreate,
			t.mobile
			FROM tmk_uuid t join tmk_uuid_config tuc on tuc.num_package_id=t.num_package_id
			and t.call_type = tuc.call_type and tuc.call_type='man' and tuc.delete_flag=0
			WHERE t.flag in('0', '7')
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
			<if test="reqDTO.beginCreateTime != null and reqDTO.beginCreateTime != ''">
				and t.gmt_create &gt;= #{reqDTO.beginCreateTime}
			</if>
			<if test="reqDTO.endCreateTime != null and reqDTO.endCreateTime != ''">
				and t.gmt_create &lt;= #{reqDTO.endCreateTime}
			</if>
			<if test="reqDTO.callInstruction != null and reqDTO.callInstruction != ''">
				and tuc.call_instruction like concat('%',#{reqDTO.callInstruction},'%')
			</if>
			<if test="reqDTO.numPackageDef != null and reqDTO.numPackageDef != ''">
				and tuc.num_package_def like concat('%', #{reqDTO.numPackageDef},'%')
			</if>
			order by t.gmt_create desc
		</if>

		<if test='reqDTO.tmkType == "cjhy"'>
	        SELECT
	            t.uuid,
	            t.user_id as userId,
	            '' as productName,
				t.source as applyOrigin,
	            NULL as approvedAt,
	            'cjhy' as tmkType,
	            t.tmk_task_id as tmkTaskId,
				t.username as customerName,
				t.mobile
	        FROM tmk_vip t
	        WHERE t.flag in('0', '7') and ((member_pay_mode='pay_later' and order_status!='4') or (member_pay_mode='pay_first' or member_pay_mode is null))
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
			<if test="reqDTO.originList != null and reqDTO.originList.size() > 0">
				and t.source in
				<foreach collection="reqDTO.originList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
	        order by t.gmt_create desc
		</if>

		<if test='reqDTO.tmkType == "wallet"'>
	        SELECT
	            t.uuid,
				t.user_id as userId,
	            t.product_code as productName,
				t.apply_origin as applyOrigin,
	            t.approved_at as approvedAt,
	            'wallet' as tmkType,
	            t.tmk_task_id as tmkTaskId ,
				t.username,
				t.mobile
	        FROM tmk_wallet t 
	        WHERE t.flag in('0', '7')
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
			<if test="reqDTO.originList != null and reqDTO.originList.size() > 0">
				and t.apply_origin in
				<foreach collection="reqDTO.originList" separator="," open="(" close=")" index="index" item="item">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.approvedAtStart != null">
				and t.approved_at &gt;= #{reqDTO.approvedAtStart}
			</if>
			<if test="reqDTO.approvedAtEnd != null">
				and t.approved_at &lt;= #{reqDTO.approvedAtEnd}
			</if>
	        order by t.gmt_create desc
		</if>
        
    </select>
    
    <select id="typeTotalTmkInfo" resultType="com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO">
        select temp.type,
            temp.count
        from 
	        (SELECT
	            'loan' as type,
	            count(1) as count
	        FROM tmk_loan_invite t 
	        WHERE t.flag in('0','7') and t.type = 'loan'

	        union all
	        SELECT
	            'credit' as type,
	            count(1) as count
	        FROM tmk_loan_invite t 
	        WHERE t.flag in('0','7') and t.type = 'credit'

	        union all
			SELECT
			'uuid' as type,
			count(1) as count
			FROM tmk_uuid t join tmk_uuid_config tuc on tuc.num_package_id=t.num_package_id
			and t.call_type = tuc.call_type and tuc.call_type='man' and tuc.delete_flag=0
			WHERE t.flag in('0','7')

	        union all 
	        SELECT
	            'cjhy' as type,
	            count(1) as count
	        FROM tmk_vip t 
	        WHERE t.flag in('0','7')

	        union all 
	        SELECT
	            'wallet' as type,
	            count(1) as count 
	        FROM tmk_wallet t 
	        WHERE t.flag in('0','7')
	        ) as temp
	        where 1 = 1
	        <if test="reqDTO.typeList != null  and reqDTO.typeList.size()>0">
	            AND temp.type in
	            <foreach item="item" index="index" collection="reqDTO.typeList" open="(" separator="," close=")">
	                #{item}
	            </foreach>
       		 </if>
       		 
       		 order by temp.type
    </select>
    
    <select id="queryTmkManagerByPage" resultType="com.welab.crm.operate.vo.tmkManager.TmkManagerVO">
	<trim prefix="" prefixOverrides="union all">
		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'loan' or reqDTO.tmkType == ''">
	        SELECT
	        	iacs.group_code as groupCode,
	        	iacs.group_name as groupName,
	        	t.staff_id as staffId,
	        	iacs.staff_name as staffName,
	        	'loan' as tmkType,
	        	t.mobile,
	        	t.application_id as applicationId,
	        	t.product_name as productName,
	        	t.assign_date as distributionTime,
	        	t.approved_at as approvedAt,
	        	t.applied_at as appliedAt,
	        	case when t.state in ('confirmed') then '1'
			     else '0' end as isChange,
	            t.apply_origin as applyOrigin,
	            t.username as customerName,
	            t.tmk_task_id as tmkTaskId,
	            t.uuid,
	            t.user_id,
	            t.diversion_tag
	        FROM tmk_loan_invite t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
	        WHERE t.flag in ('1','2') and t.type = 'loan'
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.productName != null and reqDTO.productName != ''">
				and t.product_name like concat('%',#{reqDTO.productName},'%')
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'credit' or reqDTO.tmkType == ''">
		union all
	        SELECT
	            iacs.group_code as groupCode,
	        	iacs.group_name as groupName,
	        	t.staff_id as staffId,
	        	iacs.staff_name as staffName,
	        	'credit' as tmkType,
	        	t.mobile,
	        	t.application_id as applicationId,
	        	t.product_name as productName,
	        	t.assign_date as distributionTime,
	        	t.approved_at as approvedAt,
	        	t.applied_at as appliedAt,
	        	case when t.state in ('confirmed') then '1'
			     else '0' end as isChange,
	            t.apply_origin as applyOrigin,
	            t.username as customerName,
	            t.tmk_task_id as tmkTaskId,
				t.uuid,
				t.user_id,
				t.diversion_tag
	        FROM tmk_loan_invite t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
	        WHERE t.flag in ('1','2') and t.type = 'credit'
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.productName != null and reqDTO.productName != ''">
				and t.product_name like concat('%',#{reqDTO.productName},'%')
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
		</if>


		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'uuid' or reqDTO.tmkType == ''">
		union all
	        SELECT
	            iacs.group_code as groupCode,
	        	iacs.group_name as groupName,
	        	t.staff_id as staffId,
	        	iacs.staff_name as staffName,
	        	'uuid' as tmkType,
	        	t.mobile,
	        	t.application_id as applicationId,
	        	'' as productName,
	        	t.assign_date as distributionTime,
	        	null as approvedAt,
	        	null as appliedAt,
	        	case when t.state in ('confirmed') then '1'
			     else '0' end as isChange,
	            '' as applyOrigin,
	            t.username as customerName,
	            t.tmk_task_id as tmkTaskId,
				t.uuid,
				t.user_id,
				'' as diversion_tag
	        FROM tmk_uuid t join tmk_uuid_config c on t.num_package_id=c.num_package_id
			and t.call_type=c.call_type and c.call_type='man' and c.delete_flag = 0
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
	        WHERE t.flag in ('1','2')
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>

		</if>
		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'cjhy' or reqDTO.tmkType == ''">
		union all
	        SELECT
	            iacs.group_code as groupCode,
	        	iacs.group_name as groupName,
	        	t.staff_id as staffId,
	        	iacs.staff_name as staffName,
	        	'cjhy' as tmkType,
	        	t.mobile,
	        	'' applicationId,
	        	'' as productName,
	        	t.assign_date as distributionTime,
	        	null as approvedAt,
	        	null as appliedAt,
	        	case when t.order_status = '4' then '1'
			     else '0' end as isChange,
	            t.source as applyOrigin,
	            t.username as customerName,
	            t.tmk_task_id as tmkTaskId,
				t.uuid,
				t.user_id,
				'' as diversion_tag
	        FROM tmk_vip t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
	        WHERE t.flag in ('1','2')
			and t.order_status != '4'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>

		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'wallet' or reqDTO.tmkType == ''">
		union all
	        SELECT
	            iacs.group_code as groupCode,
	        	iacs.group_name as groupName,
	        	t.staff_id as staffId,
	        	iacs.staff_name as staffName,
	        	'wallet' as tmkType,
	        	t.mobile,
	        	t.application_id as applicationId,
	        	t.product_code as productName,
	        	t.assign_date as distributionTime,
	        	null as approvedAt,
	        	null as appliedAt,
	        	case when t.is_consu = '1' then '1'
			     else '0' end as isChange,
	            t.apply_origin as applyOrigin,
	            t.username as customerName,
	            t.tmk_task_id as tmkTaskId,
				t.uuid,
				t.user_id,
				'' as diversion_tag
	        FROM tmk_wallet t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
	        WHERE t.flag in ('1','2')
			and t.is_consu = '0'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.productName != null and reqDTO.productName != ''">
				and t.product_code like concat('%',#{reqDTO.productName},'%')
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
		</if>
	</trim>
	        order by tmkTaskId
	    </select>
    	
    <select id="totalTmkManager" resultType="com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO">
        select temp.staff_id,
	            temp.group_code,
	            sum(temp.count) as count
        from 
	        (
		<trim prefix="" prefixOverrides="union all">
		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'loan' or reqDTO.tmkType == ''">
			SELECT
	             t.staff_id,
	            iacs.group_code,
	            count(1) as count
	        FROM tmk_loan_invite t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
		WHERE t.flag in ('1','2') and t.type = 'loan'
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.productName != null and reqDTO.productName != ''">
				and t.product_name like concat('%',#{reqDTO.productName},'%')
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        group by t.staff_id,iacs.group_code
		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'credit' or reqDTO.tmkType == ''">
		union all
	        SELECT
	            t.staff_id,
	            iacs.group_code,
	            count(1) as count
	        FROM tmk_loan_invite t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
		WHERE t.flag in ('1','2') and t.type = 'credit'
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.productName != null and reqDTO.productName != ''">
				and t.product_name like concat('%',#{reqDTO.productName},'%')
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        group by t.staff_id,iacs.group_code
		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'uuid' or reqDTO.tmkType == ''">
		union all
	        SELECT
	             t.staff_id,
	            iacs.group_code,
	            count(1) as count
	        FROM tmk_uuid t join tmk_uuid_config c on t.num_package_id=c.num_package_id
			and t.call_type=c.call_type and c.call_type='man' and c.delete_flag = 0
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
		WHERE t.flag in ('1','2')
			and t.state = 'aip'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        group by t.staff_id,iacs.group_code
		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'cjhy' or reqDTO.tmkType == ''">
		union all
	        SELECT
	             t.staff_id,
	            iacs.group_code,
	            count(1) as count
	        FROM tmk_vip t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
		WHERE t.flag in ('1','2')
			and t.order_status != '4'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        group by t.staff_id,iacs.group_code
		</if>

		<if test="reqDTO.tmkType == null or reqDTO.tmkType == 'wallet' or reqDTO.tmkType == ''">
		union all
	        SELECT
	             t.staff_id,
	            iacs.group_code,
	            count(1) as count
	        FROM tmk_wallet t 
	        join in_auth_crm_staff iacs on t.staff_id = iacs.id
		WHERE t.flag in ('1','2')
			and t.is_consu = '0'
			<if test="reqDTO.groupCode != null  and reqDTO.groupCode.size()>0">
				AND iacs.group_code in
				<foreach item="item" index="index" collection="reqDTO.groupCode" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.staffId != null  and reqDTO.staffId.size()>0">
				AND t.staff_id in
				<foreach item="item" index="index" collection="reqDTO.staffId" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="reqDTO.customerName != null and reqDTO.customerName != ''">
				and t.username = #{reqDTO.customerName}
			</if>
			<if test="reqDTO.mobile != null and reqDTO.mobile != ''">
				and t.mobile = #{reqDTO.mobile}
			</if>
			<if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
				and t.applicationId = #{reqDTO.applicationId}
			</if>
			<if test="reqDTO.distributeStartDate != null">
				and t.assign_date &gt;= #{reqDTO.distributeStartDate}
			</if>
			<if test="reqDTO.distributeEndDate != null">
				and t.assign_date &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.approvalStartDate != null">
				and t.approved_at &gt;= #{reqDTO.approvalStartDate}
			</if>
			<if test="reqDTO.approvalEndDate != null">
				and t.approved_at &lt; date_add(#{reqDTO.approvalEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.appliedStartDate != null">
				and t.applied_at &gt;= #{reqDTO.appliedStartDate}
			</if>
			<if test="reqDTO.appliedEndDate != null">
				and t.applied_at &lt; date_add(#{reqDTO.appliedEndDate}, interval 1 day)
			</if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        group by t.staff_id,iacs.group_code
		</if>
		</trim>
	        ) as temp
	        group by temp.staff_id,temp.group_code
	        order by temp.group_code
    </select>
    
    <select id="queryTmkAssignByPage" resultType="com.welab.crm.operate.vo.tmkManager.TmkAssignVO">
        select  t.*,
        		t1.diversion_tag
	        FROM tmk_assign_history t
	        left join tmk_loan_invite t1 on t.tmk_task_id = t1.tmk_task_id
	        where 1=1
	        <if test="reqDTO.username != null and reqDTO.username != ''">
	            and t.username = #{reqDTO.username}
	        </if>
	        <if test="reqDTO.mobile != null and reqDTO.mobile != ''">
	            and t.mobile = #{reqDTO.mobile}
	        </if>
	        <if test="reqDTO.applicationId != null and reqDTO.applicationId != ''">
	            and t.application_id = #{reqDTO.applicationId}
	        </if>
	        <if test="reqDTO.distributeStartDate != null">
	            and t.distribution_time &gt;= #{reqDTO.distributeStartDate}
	        </if>
	        <if test="reqDTO.distributeEndDate != null">
	            and t.distribution_time &lt; date_add(#{reqDTO.distributeEndDate}, interval 1 day)
	        </if>
			<if test="reqDTO.userId != null and reqDTO.userId != ''">
				and t.user_id = #{reqDTO.userId}
			</if>
			<if test="reqDTO.uuid != null and reqDTO.uuid != ''">
				and t.uuid = #{reqDTO.uuid}
			</if>
	        
	        order by t.distribution_time desc
	    </select>
</mapper>
