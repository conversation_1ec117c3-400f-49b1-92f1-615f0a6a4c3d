# MyBatis-Plus JSON字段配置说明

## 概述
本文档说明如何配置MyBatis-Plus来正确处理MiaodaComplaint表中的JSON字段。

## JSON字段说明

### 1. reply_info 字段
- **数据库类型**: TEXT
- **Java类型**: List<MiaodaReplyDetailVO>
- **用途**: 存储投诉单的回复记录
- **JSON结构示例**:
```json
[
  {
    "sender": 1,
    "senderDesc": "用户补充",
    "content": "回复内容",
    "contentHide": 0,
    "attachHide": 0,
    "replyedAt": "2025-01-24T10:30:00",
    "attaches": [
      {
        "type": "image",
        "src": "https://example.com/image.jpg",
        "name": "图片.jpg",
        "size": 1024000
      }
    ]
  }
]
```

### 2. complete_info 字段
- **数据库类型**: TEXT
- **Java类型**: List<MiaodaCompleteInfoVO>
- **用途**: 存储投诉单结案信息
- **JSON结构示例**:
```json
[
  {
    "coCompleteSolution": "解决方案",
    "coCompleteReason": "结案原因",
    "coCompleteBegin": 1706076600000,
    "coCompleteAttaches": [
      {
        "type": "image",
        "src": "https://example.com/complete.jpg",
        "name": "结案证明.jpg",
        "size": 2048000
      }
    ]
  }
]
```

### 3. attachment_info 字段
- **数据库类型**: TEXT
- **Java类型**: List<MiaodaAttachmentVO>
- **用途**: 存储投诉单相关附件
- **JSON结构示例**:
```json
[
  {
    "type": "image",
    "src": "https://example.com/attachment1.jpg",
    "name": "附件1.jpg",
    "size": 1536000
  },
  {
    "type": "video",
    "src": "https://example.com/video1.mp4",
    "name": "视频1.mp4",
    "size": 10485760
  }
]
```

## 配置要求

### 1. 实体类配置
```java
@TableField(value = "reply_info", typeHandler = JacksonTypeHandler.class)
private List<MiaodaReplyDetailVO> replyDetails;

@TableField(value = "complete_info", typeHandler = JacksonTypeHandler.class)
private List<MiaodaCompleteInfoVO> coCompleteInfo;

@TableField(value = "attachment_info", typeHandler = JacksonTypeHandler.class)
private List<MiaodaAttachmentVO> attaches;
```

### 2. 依赖要求
确保项目中包含以下依赖：
```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3+</version>
</dependency>
```

### 3. 数据库字段默认值处理
- 新字段默认值为NULL
- 查询时NULL值会被转换为空列表
- 空列表保存时会被转换为NULL（节省存储空间）

## 注意事项

1. **JSON序列化**: 使用Jackson进行JSON序列化和反序列化
2. **性能考虑**: JSON字段不适合作为查询条件，如需查询建议使用虚拟列或额外索引
3. **数据一致性**: 确保JSON结构与VO类定义保持一致
4. **错误处理**: JSON解析失败时会返回空列表，不会抛出异常
5. **字段映射**: 实体类字段名与数据库字段名通过@TableField的value属性映射

## 测试建议

1. 测试JSON字段的序列化和反序列化
2. 测试NULL值和空列表的处理
3. 测试大JSON数据的性能
4. 测试JSON格式错误时的异常处理
