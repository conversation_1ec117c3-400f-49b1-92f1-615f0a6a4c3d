<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration>
<configuration debug="true"><!-- 'milliseconds','seconds','minutes' and 'hours' -->
	<conversionRule conversionWord="msg" converterClass="com.welab.logback.convert.DesensitizationConverter"/>
	<Property name="log.name" value="${app.id:-default}" />
	<Property name="log.path" value="${log.path:-./data/logs}" />
	<Property name="log.level" value="${log.level:-INFO}" />
	<contextName>${log.name}</contextName>
	<Property name="patternTemplate" value="[%-5level] [%contextName] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{req.remoteHost}] [%X{req.requestURI}] [%X{traceId}] %logger - %msg%n" />
	<jmxConfigurator />
	<shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook" />
	<!-- 控制台输出日志 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${patternTemplate}</pattern>
		</encoder>
	</appender>

	<!-- 文件输出日志 (文件大小策略进行文件输出，超过指定大小对文件备份) -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${log.path}/${log.name}/${log.name}.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"><!-- rollover daily -->
			<FileNamePattern>${log.path}/${log.name}-log-%d{yyyy-MM-dd}.%i.zip</FileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize><!-- or whenever the file size reaches 5MB -->
			</timeBasedFileNamingAndTriggeringPolicy>
			<!-- <maxHistory></maxHistory> -->
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${patternTemplate}</pattern>
		</encoder>
	</appender>

	<!-- 异步输出 -->
	<appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>20</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>2048</queueSize>
		<includeCallerData>true</includeCallerData>
		<neverBlock>true</neverBlock>
		<!-- 停应用时最多等待多少毫秒处理queue中日志,暂定10秒 -->
		<maxFlushTime>10000</maxFlushTime>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="FILE" />
	</appender>

	<root level="${log.level}">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="ASYNC" />
	</root>

	<!--这里指定logger name 是为jmx设置日志级别做铺垫 -->
	<logger name="com.welab" additivity="true" level="info" />
	<logger name="org.springframework.core.env" additivity="true" level="info" />
	<logger name="org.springframework.beans.factory.support" additivity="true" level="info" />
	<logger name="org.springframework.amqp.rabbit" additivity="true" level="info" />
	<logger name="org.apache.ibatis.logging" additivity="true" level="info" />
	<logger name="org.apache.tomcat.util.net" additivity="true" level="error" />
	<logger name="com.ctrip.framework.apollo.util" additivity="true" level="error" />
	<!-- 关闭dubbo协议日志的输出 -->
	<logger name="com.alibaba.dubbo.rpc.protocol.dubbo" additivity="false" level="OFF" />
	
	
</configuration>