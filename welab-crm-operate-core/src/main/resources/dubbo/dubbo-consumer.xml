<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:jee="http://www.springframework.org/schema/jee" xmlns:aop="http://www.springframework.org/schema/aop"
  xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
  xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd  
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd 	
	http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.0.xsd 
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd 
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd
	">

  <!-- 当前服务作为消费者调用远程RPC服务时，因为都是基于接口的调用，所以interface只用写远程服务接口的路径，不要写实现 -->
  <!-- <dubbo:reference id="ssoService" interface="com.welab.sso.service.SsoService" /> -->

  <!-- welab-crm-interview-api -->
  <dubbo:reference id="loanApplicationService" interface="com.welab.crm.interview.service.LoanApplicationService" timeout="60000" retries="-1"/>
  <dubbo:reference id="bankCardService" interface="com.welab.crm.interview.service.BankCardService" timeout="60000" retries="-1"/>
  <dubbo:reference id="vipService" interface="com.welab.crm.interview.service.VipService" timeout="60000" retries="-1"/>
  <dubbo:reference id="labelService" interface="com.welab.crm.interview.service.LabelService" timeout="60000" retries="-1"/>
  <dubbo:reference id="userInfoService" interface="com.welab.crm.interview.service.UserInfoService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="loansApplicationService" interface="com.welab.crm.interview.service.LoansApplicationService"
    check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="couponService" interface="com.welab.crm.interview.service.CouponService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="walletService" interface="com.welab.crm.interview.service.WalletService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="uploadService" interface="com.welab.crm.interview.service.IUploadService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="messageService" interface="com.welab.crm.interview.service.MessageService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="weChatDubboService " interface="com.welab.crm.interview.service.WeChatDubboService " check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="conRepeatCallService " interface="com.welab.crm.interview.service.ConRepeatCallService " check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="aiPushJobService" interface="com.welab.crm.interview.service.AiPushJobService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="satisfactionService " interface="com.welab.crm.interview.service.SatisfactionService " check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="financeService" interface="com.welab.crm.interview.service.FinanceService" check="false" timeout="60000" retries="-1">
    <dubbo:method name="getSettlementUrl" retries="-1" timeout="60000"/>
  </dubbo:reference>
  <dubbo:reference id="complainOrderService" interface="com.welab.crm.interview.service.ComplainOrderService" timeout="60000" check="false" retries="-1">
    <dubbo:method name="pushComplainOrder" retries="-1" timeout="60000"/>
  </dubbo:reference>
  <dubbo:reference id="trTokenService" interface="com.welab.crm.interview.service.TrTokenService" check="false" retries="-1" timeout="60000"/>
  <dubbo:reference id="repayUploadService" interface="com.welab.crm.interview.service.RepayUploadService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="faSuService" interface="com.welab.crm.interview.service.FaSuService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="conPhoneSummaryService" interface="com.welab.crm.interview.service.ConPhoneSummaryService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="workOrderServiceInterview" interface="com.welab.crm.interview.service.WorkOrderService" check="false" timeout="60000" retries="-1"/>


  <!-- welab-crm-base-api -->
  <dubbo:reference id="staffService" interface="com.welab.crm.base.service.StaffService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="wFRunBusiServiceImpl" interface="com.welab.crm.base.workflow.busi.service.WFRunBusiService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="wFHisBusiService" interface="com.welab.crm.base.workflow.busi.service.WFHisBusiService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="woAssignLogService" interface="com.welab.crm.base.service.WoAssignLogService" check="false" timeout="60000" retries="-1"/>


  <dubbo:reference id="loanApplicationServiceFacade" interface="com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade" check="false" timeout="60000" retries="-1"/>

  <!--welab-collection-interview-api-->
  <dubbo:reference id="collectionFinanceService" interface="com.welab.collection.interview.service.FinanceService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="iRepaymentForWebService" interface="com.welab.collection.interview.service.IRepaymentForWebService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="productService" interface="com.welab.collection.interview.service.ProductService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="collectionBlackListService" interface="com.welab.collection.interview.service.BlackListService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="lenderService" interface="com.welab.collection.interview.service.ILenderService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="reduceSchemeService" interface="com.welab.collection.interview.service.ReduceSchemeService" check="false" timeout="60000" retries="-1"/>

  <!--操作日志-->
  <dubbo:reference id="notesServiceFacade" interface="com.welab.user.interfaces.facade.NotesServiceFacade" check="false" timeout="60000" retries="-1"/>
  <!--h5还款链接判断-->
  <dubbo:reference id="repaymentCalculationDubboService" interface="com.welab.finance.repayment.dubbo.RepaymentCalculationDubboService" check="false" timeout="60000" retries="-1"/>
  <dubbo:reference id="userService" interface="com.welab.usercenter.service.UserService" check="false" timeout="60000" retries="-1"/>

  <!--collection服务-->
  <dubbo:reference id="userCenterService" interface="com.welab.collection.interview.service.IUserCenterService" check="false"
                   retries="-1" timeout="60000"/>
  <!-- frs-account-api -->
  <dubbo:reference id="financialDubboService" interface="com.welab.frs.account.dubbo.FinancialDubboService" check="false"
                   retries="-1" timeout="60000"/>
  <dubbo:reference id="financeAccountsDubboService" interface="com.welab.finance.accounting.dubbo.FinanceAccountsDubboService"  check="false"/>
  <!--审批相关服务-->
  <dubbo:reference id="iApplicationService" interface="com.wolaidai.approval.service.IApplicationService" check="false" timeout="60000" retries="-1" />
  
  <!--welab-authority-->
  <dubbo:reference id="authorityUserService" interface="com.welab.authority.service.UserService" check="false" timeout="60000" retries="-1" />
  
  <dubbo:reference interface="com.welab.finance.loanprocedure.dubbo.LoanDubboService" id ="loanDubboService" timeout="60000" retries="-1"/>
</beans>
