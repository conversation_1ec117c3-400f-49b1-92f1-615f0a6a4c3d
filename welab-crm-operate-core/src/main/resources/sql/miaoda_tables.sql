-- 喵达投诉平台相关表结构
-- 创建时间：2025-01-16
-- 说明：用于对接喵达投诉平台的数据表

-- 1. 喵达投诉单表
CREATE TABLE `miaoda_complaint` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sn` varchar(50) NOT NULL COMMENT '喵达投诉单号',
  `work_order_no` varchar(50) DEFAULT NULL COMMENT '关联工单编号',
  `title` varchar(200) DEFAULT NULL COMMENT '投诉标题',
  `nickname` varchar(100) DEFAULT NULL COMMENT '投诉人昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `comp_phone` varchar(20) DEFAULT NULL COMMENT '投诉时预留手机号',
  `privacy` varchar(100) DEFAULT NULL COMMENT '涉诉单号',
  `content` text COMMENT '投诉内容',
  `issue` varchar(500) DEFAULT NULL COMMENT '投诉问题',
  `appeal` varchar(200) DEFAULT NULL COMMENT '投诉要求',
  `cost` varchar(20) DEFAULT NULL COMMENT '涉诉金额',
  `status` varchar(50) DEFAULT NULL COMMENT '投诉状态',
  `status_no` int(11) DEFAULT NULL COMMENT '状态版本号',
  `created_at` datetime DEFAULT NULL COMMENT '发起时间',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `uri` varchar(500) DEFAULT NULL COMMENT '投诉链接',
  `exposed` int(1) DEFAULT '0' COMMENT '是否已公开 0-未公开 1-已公开',
  `appeal_chance` int(11) DEFAULT NULL COMMENT '剩余申诉次数',
  `co_complete_chance` int(11) DEFAULT NULL COMMENT '剩余结案次数',
  `co_complete_status` varchar(50) DEFAULT NULL COMMENT '结案状态',
  `co_complete_at` datetime DEFAULT NULL COMMENT '结案完成时间',
  `auto_complete_at` datetime DEFAULT NULL COMMENT '自动完成时间',
  `user_complete_at` datetime DEFAULT NULL COMMENT '主动完成时间',
  `service` varchar(100) DEFAULT NULL COMMENT '服务名称',
  `attitude` int(11) DEFAULT NULL COMMENT '用户评价-服务态度',
  `process` int(11) DEFAULT NULL COMMENT '用户评价-处理速度',
  `satisfaction` int(11) DEFAULT NULL COMMENT '用户评价-满意度',
  `eval_content` varchar(500) DEFAULT NULL COMMENT '用户评价-评价内容',
  `eval_at` datetime DEFAULT NULL COMMENT '用户评价-评价时间',
  `sync_status` int(1) DEFAULT '0' COMMENT '同步状态 0-待同步 1-已同步 2-同步失败',
  `sync_fail_reason` varchar(500) DEFAULT NULL COMMENT '同步失败原因',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sn` (`sn`),
  KEY `idx_work_order_no` (`work_order_no`),
  KEY `idx_phone` (`phone`),
  KEY `idx_comp_phone` (`comp_phone`),
  KEY `idx_status` (`status`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_assigned_at` (`assigned_at`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='喵达投诉单表';

-- 2. 投诉类型映射表（简化版）
CREATE TABLE `complaint_type_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `miaoda_issue` varchar(200) NOT NULL COMMENT '喵达投诉问题类型',
  `op_dict_info_conf_id` bigint(20) NOT NULL COMMENT '工单组合配置ID，关联op_dict_info_conf表主键',
  `is_active` int(1) DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `priority` int(11) DEFAULT '100' COMMENT '优先级（数字越小优先级越高）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_miaoda_issue` (`miaoda_issue`),
  KEY `idx_op_dict_info_conf_id` (`op_dict_info_conf_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投诉类型映射表';

-- 3. 扩展现有工单表，添加喵达相关字段
ALTER TABLE `wo_task` 
ADD COLUMN `miaoda_sn` varchar(50) DEFAULT NULL COMMENT '喵达投诉单号' AFTER `order_no`,
ADD COLUMN `miaoda_status` varchar(50) DEFAULT NULL COMMENT '喵达投诉状态' AFTER `miaoda_sn`,
ADD COLUMN `miaoda_uri` varchar(500) DEFAULT NULL COMMENT '喵达投诉链接' AFTER `miaoda_status`;

-- 为新增字段添加索引
ALTER TABLE `wo_task` ADD INDEX `idx_miaoda_sn` (`miaoda_sn`);

-- 4. 初始化默认投诉类型映射数据
-- 注意：以下INSERT语句中的op_dict_info_conf_id需要根据实际的op_dict_info_conf表中的数据进行调整
-- 请先查询op_dict_info_conf表中实际存在的工单组合ID，然后替换下面的示例ID

-- 示例：查询现有工单组合的SQL
-- SELECT id, wo_type_detail, wo_type_fir_detail, wo_type_sec_detail, wo_type_thir_detail, wo_type_child_detail
-- FROM op_dict_info_conf WHERE is_status = 1 ORDER BY id;

-- 根据实际情况调整以下op_dict_info_conf_id值
INSERT INTO `complaint_type_mapping` (`miaoda_issue`, `op_dict_info_conf_id`, `priority`, `remark`, `create_by`) VALUES
('还款问题',
    (SELECT id FROM op_dict_info_conf WHERE wo_type_detail LIKE '%投诉%' AND wo_type_fir_detail LIKE '%还款%' AND is_status = 1 LIMIT 1),
    1, '还款相关问题默认映射', 'system'),
('逾期问题',
    (SELECT id FROM op_dict_info_conf WHERE wo_type_detail LIKE '%投诉%' AND wo_type_fir_detail LIKE '%逾期%' AND is_status = 1 LIMIT 1),
    2, '逾期相关问题默认映射', 'system'),
('服务态度问题',
    (SELECT id FROM op_dict_info_conf WHERE wo_type_detail LIKE '%投诉%' AND wo_type_fir_detail LIKE '%服务%' AND is_status = 1 LIMIT 1),
    3, '服务态度问题默认映射', 'system'),
('其他问题',
    (SELECT id FROM op_dict_info_conf WHERE wo_type_detail LIKE '%投诉%' AND wo_type_fir_detail LIKE '%其他%' AND is_status = 1 LIMIT 1),
    99, '其他问题默认映射', 'system');

-- 5. 创建喵达投诉回复详情表（可选，用于存储详细的回复信息）
CREATE TABLE `miaoda_complaint_reply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `complaint_sn` varchar(50) NOT NULL COMMENT '投诉单号',
  `sender` int(1) NOT NULL COMMENT '回复人 1-用户补充 2-商家回复',
  `content` text COMMENT '回复内容',
  `content_hide` int(1) DEFAULT '0' COMMENT '回复是否隐藏 0-公开 1-隐藏',
  `attach_hide` int(1) DEFAULT '0' COMMENT '附件是否隐藏 0-公开 1-隐藏',
  `replyed_at` datetime DEFAULT NULL COMMENT '回复时间',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_complaint_sn` (`complaint_sn`),
  KEY `idx_sender` (`sender`),
  KEY `idx_replyed_at` (`replyed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='喵达投诉回复详情表';

-- 6. 创建喵达投诉附件表（可选，用于存储附件信息）
CREATE TABLE `miaoda_complaint_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `complaint_sn` varchar(50) NOT NULL COMMENT '投诉单号',
  `reply_id` bigint(20) DEFAULT NULL COMMENT '关联回复ID',
  `type` varchar(20) NOT NULL COMMENT '附件类型 image-图片 video-视频',
  `src` varchar(500) NOT NULL COMMENT '附件URI',
  `name` varchar(200) DEFAULT NULL COMMENT '附件名称',
  `size` bigint(20) DEFAULT NULL COMMENT '附件大小',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_complaint_sn` (`complaint_sn`),
  KEY `idx_reply_id` (`reply_id`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='喵达投诉附件表';

-- 7. 创建喵达API调用日志表（可选，用于记录API调用情况）
CREATE TABLE `miaoda_api_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `api_path` varchar(200) NOT NULL COMMENT 'API路径',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_params` text COMMENT '请求参数',
  `response_body` text COMMENT '响应内容',
  `response_code` int(11) DEFAULT NULL COMMENT '响应状态码',
  `cost_time` bigint(20) DEFAULT NULL COMMENT '耗时（毫秒）',
  `success` int(1) DEFAULT '1' COMMENT '是否成功 0-失败 1-成功',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_path` (`api_path`),
  KEY `idx_success` (`success`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='喵达API调用日志表';

-- 注意事项：
-- 1. 投诉类型映射表使用op_dict_info表的ID，请根据实际数据调整
-- 2. 执行前请先查询op_dict_info表中的工单类型数据：
--    SELECT id, category, content FROM op_dict_info WHERE category = 'workOrderType' AND status = 1;
-- 3. 如果wo_task表已存在miaoda相关字段，请跳过ALTER TABLE语句
-- 4. 建议在生产环境执行前先在测试环境验证
-- 5. 可根据实际需求决定是否创建可选的表（reply、attachment、api_log）
-- 6. 如果上述INSERT语句中的子查询返回NULL，请手动替换为实际的ID值

-- 手动插入示例（如果子查询方式不适用）：
-- INSERT INTO `complaint_type_mapping` (`miaoda_issue`, `wo_type_id`, `wo_type_fir_id`, `wo_type_sec_id`, `wo_type_thir_id`, `priority`, `remark`, `create_by`) VALUES
-- ('还款问题', 实际的工单大类ID, 实际的工单一类ID, 实际的工单二类ID, 实际的工单三类ID, 1, '还款相关问题默认映射', 'system'),
-- ('逾期问题', 实际的工单大类ID, 实际的工单一类ID, 实际的工单二类ID, 实际的工单三类ID, 2, '逾期相关问题默认映射', 'system'),
-- ('服务态度问题', 实际的工单大类ID, 实际的工单一类ID, 实际的工单二类ID, 实际的工单三类ID, 3, '服务态度问题默认映射', 'system'),
-- ('其他问题', 实际的工单大类ID, 实际的工单一类ID, 实际的工单二类ID, 实际的工单三类ID, 99, '其他问题默认映射', 'system');
