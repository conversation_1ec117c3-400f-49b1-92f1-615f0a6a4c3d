jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=***********************************************************************************************************************************
#jdbc.url=*************************************************************************************************************************************
#jdbc.url=****************************************************************************
#jdbc.username=root
#jdbc.password=mysql321

# dev
#jdbc.url=*****************************************************************************
#jdbc.username=welab_crm_dev
#jdbc.password=qRlrkMOH^sg8

# fat
jdbc.url=******************************************************************************************************************************************************************
jdbc.username=db_public
jdbc.password=u#Lmmb&wgmr3U2S9

druid.filters=stat,wall
druid.connectionProperties=config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJv4CwvgYgsoHK+vsDlYfLyY9H6kUYB0UznYxrE4mF4eg8qwjMyG/N0PBhVbOFPlAD20Cg44hBegEeSjlf+DY7sCAwEAAQ==

#zookeeper.url=localhost:2181
#fat3
#zookeeper.url=**********:2181
zookeeper.url=zk-fat.welab-inc.com:32181
#dev
#zookeeper.url=**********:2181

#po package name 
table.package=com.welab.crm.operate.domain

#transaction aop execution
aop.services=execution(* com.welab.crm.operate.service.impl.*.*(..))

## Mybatis 配置
mybatis.typeAliasesPackage=com.welab.crm.operate.domain
mybatis.mapperLocations=classpath:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

#welabb-xdao async dao
#async.dao.core_pool_size=20
#async.dao.max_pool_size=200
#async.dao.queue_size=500

#mq相关参数
#welab.mq.uri=**********************************
#welab.mq.sender.queue=welab.message-wechat.activity.queue
#welab.mq.listener.queue=welab.message-wechat.activity.queue

#redis config
#redisType(required): single/sharded/cluster
redis.redisType=cluster
#address(required): host1:port1,host2:port2,host3:port3...
#single dev
#redis.address=**********:6379
#single fat
#redis.address=**********:6379
#cluster dev
#redis.address=**********:8001,**********:8002,**********:8003
#cluster fat
redis.address=redis-cluster-fat.welab-inc.com:32379
redis.password=devappwsx

# 工单报表配置
# 工单统计报表明细排序
work.order.report.statistic.details.sort=现金分期,钱夹谷谷,淘新机,电商业务

# 过河兵附件下载链接
elite.attachment.download.url = http://**********:8080/ngs/fs/get?file=
# 天润呼叫平台配置
# 天润接口基本url
tr.call.base.url = http://api-6.cticloud.cn/interface/v10
# 呼叫中心编号
tr.call.enterpriseId = 7600104
# 部门token值
tr.call.enterpriseToken = eb7662b3b773b5d8b8891a979aa36b2c

# 工单报表配置
# 外呼效能统计报表查询组别
report.outbound.efficiency.summary.group=zcz,tsz
# 工单中央监控默认查询组别
report.central.monitoring.group=zcz,tsz
report.valid.time = 3

lzbank.http.url=http://lzbank-investment.service:8080

# 电销团队呼叫中心编码
tr.tmk.enterpriseId = 7600080
# 电销团队呼叫中心token值
tr.tmk.token = 594f3978f453487d01d3297fb790490d

#welab.privacy.root.url = https://japi-fat.slb.cluster.coltd:8000/privacy/api/v2/config-info
welab.privacy.root.url = https://japi-fat.wolaidai.com/privacy/api/v2/config-info
welab.privacy.secret.key = N2qevU18ctVFJjJV


webChat.auth.customerId = 138825
webChat.auth.secretKey = aUfgkLh6
webChat.auth.url = https://xbot-dev.wld.net/oauth/authLoginToken

dubbo.provider.accepts = 300
dubbo.provider.thread = 600

lhbank.http.url = https://lender-fat.wolaidai.com/lhbank-investment/api/v1/user/settlement

lender.pre.url = https://lender-fat.wolaidai.com


job.registry.address=zk-fat.welab-inc.com:32181

job.ivr.shunt.cron = 0 0 12 * * ?

online.system.app.key = aS5u68Ke
online.system.user.token = df38a664-41a3-4631-8458-94321d5b67da
online.system.http.url.pre = https://cs-dev.wld.net/api
oss.prefix = system/documents/
exam.system.url.pre = http://exam-it-fat.slb.cluster.coltd:8000/exam-web-server

job.urge.order.cron = 0 0 1 * * ?
job.callback.order.cron = 0 0 1 * * ?
ai.summary.base.url = http://*********:8888


bairong.public.key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiDNGz1Qjg7R4jneq2IwK9k54xtwDFR65Kc1g9zgDHw5RNgdF+g3ZiarX0rCxkpCiM/mHtxqXweO2aWEa++vuJ/gqN9NqtkbwifEo5IEmn8bkYQV7ZDpVf0IgJHAl+tV/+Qim0Mn3wRi1llH7fhdk1/KTmlI8mccMQLgG2nl1MPCnCPrcnLMfFNfSmuyuGfEo897n1CW1mcgxfeys1VwelXp/RvdAWFQi+FI7qEJ5ZK0VcAB1p5A4v/2qioPzt4pLtBJ9JZKs9HRyESs4HRVd2p6IRR6f/31M7mjhakebQAD7PMhPC09j9cZaEKD13t2o2jIqpvZJaCQcipMoN5SicQIDAQAB
bairong.private.key = MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCIM0bPVCODtHiOd6rYjAr2TnjG3AMVHrkpzWD3OAMfDlE2B0X6DdmJqtfSsLGSkKIz+Ye3GpfB47ZpYRr76+4n+Co302q2RvCJ8SjkgSafxuRhBXtkOlV/QiAkcCX61X/5CKbQyffBGLWWUft+F2TX8pOaUjyZxwxAuAbaeXUw8KcI+tycsx8U19Ka7K4Z8Sjz3ufUJbWZyDF97KzVXB6Ven9G90BYVCL4UjuoQnlkrRVwAHWnkDi//aqKg/O3iku0En0lkqz0dHIRKzgdFV3anohFHp//fUzuaOFqR5tAAPs8yE8LT2P1xloQoPXe3ajaMiqm9kloJByKkyg3lKJxAgMBAAECggEAfcSDvY2fYc3ziPL9wwrGUJ0zr8WX3xfIuc4ZnQ+O6k2JNk/PEVFasGIe+0IIJyQBc+cWqEEZI4RHe9WcWkaJW0Uwju5d2sV09pvHPAH6jY3Wc7ThNTMcoe+IyLz6hT5C9s5DZAcsDZXokE9R4SRvDW3FWLpZlVKVgOmuAm4nhjFI/ZkpyfYYvE/OZMDAaHbdnm/Vv4CO/loQiSr4y6j2RHwauJyUHkT0DjQLEOGm8lc3ynk5EYnNz46l9UifAUVN4IBAq4B/HSGiBol/kmKRx17PWabWTZ2Z5yf/S/Ofhi+F+5uM3BunpA0fLjuKjYkHH9zFRJ2JXMY4vj2cjkrHtQKBgQDIpvkv85ft+peJ1oRCdzZ1S4dbLTv+YuDR3Gg7giEgQKUx/e2TbtvjyLOfdEh3j5or7eIJ+oKjx4MT/ciJYnrPsddEyssdDszgGFKtbRnDALaDdEMP6To1yw16IyS8T2XXu4V2DdAB+LimBNTySpZfV1nNw/WgQTYoxxCsMvjsVwKBgQCtxQobnUiC6tynytzSmA8+B+nRG7DlxcmeYpyk1oh8s/XzKFMC+N8jEkLuiTq1g9g1eLk0xvnysIVcOmAzxGTs+js/wAQw8Wg9LTjhVvUTtFop9rI3hoUMya1IFaBINajsm93vD4qgs+OCmPtIiArPJw7x3FHVn34PkA9wuluqdwKBgQC7G4E5JuUhJNHU1jYlKLeer1y8ODU+pYPSDr8f5tvvEHk7Yff9yJ3CDnD5m1zMKRvyJPToqzLReQWRSdGL00qEjOkZnCXFpfyEaAoI8LjaL7sCYBL9LlZ6vSuHHmhuNXfZsWLl0EGM5RuCrPTKL6WDqJf0mexdkRHo+qBgg3O+JwKBgGfZrWvJeO9863iAHa7iX6Gj2xI0qWj45sD4kENXT2SvrJqwi8Pn9nsgS84BR4v5irqSNqWbEQAAjQMMhDgL9IFIHya/XAHgFHsTTr4lWPx4ubYAkIvsohF9akj9W7gsEZOup1lbAW9/csW3Jd44J7Wb0yuqj2HLGuy0HPYy+duLAoGASd7Un+WggMN1oyWqQ861vDsFTI82AVi7+xlAkm1L4W0+nB+YSP64DCRKHn/brJyT4Ritv1Jm5EBUGyxttT4Re7aTJM1SZknS3DIa3prnn7/aphJxINyVywvljveGt7Iu71UPo1swrdhqD52EZ4T5gQTbfOBuwAjqi8WPjLDVBBk=

bairong.aes.key = bairong_
bairong.request.domain = https://dymapi.shurongdai.cn

featureidentify.url = https://featureidentify-fat.welab-inc.com
snap.check.url = http://10.90.0.10:13090
jwt.secret = welab_crm_operate_fat_20250611
