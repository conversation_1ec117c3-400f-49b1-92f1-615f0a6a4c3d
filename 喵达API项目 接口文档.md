好的，这是您提供的喵达API项目接口文档的Markdown格式。

***

# 喵达API项目 接口文档

## 1. 登陆接口

*   **接口地址:** `GET https://api-miaoda.sina.com.cn/auth/token`
*   **请求参数格式:** JSON
*   **请求参数样例 (调用示例):**
    ```http
    GET https://api-miaoda.sina.com.cn/auth/token
    Content-Type: application/json
    
    {
      "uid": 1120310613,      // 商家UID-int
      "key": "123456"         // 商家密钥-string
    }
    ```
*   **请求参数说明:**

| 字段名 | 字段名称 | 类型   | 是否必填 | 说明 |
| :----- | :------- | :----- | :------- | :--- |
| uid    | 商家UID  | int    | 是       |      |
| key    | 商家密钥 | string | 是       |      |

*   **返回结果格式:** JSON
*   **返回结果样例:**

    ```http
    HTTP/1.1 200 OK
    Content-Type: application/json; charset=utf-8
    Date: Thu, 10 Mar 2022 07:00:33 GMT
    Content-Length: 310
    
    {
      "result": {
        "status": {
          "code": 200,
          "msg": "success"
        },
        "timestamp": "Thursday, 10-Mar-22 15:00:33 CST",
        "data": {
          "expire": "2022-03-10T18:00:33+08:00",
          "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NDY5MDY0MzMsImlkIjoxMTIwMzEwNjEzLCJvcmlnX21hdCI6MTY0Njg5NTYzM30.vJ8Ae7taHckFDbLzotBybdclRq9JKog3zJ_Cpy8bHbg"
        }
      }
    }
    ```
*   **结果参数说明:**
    *   `data.expire`: token的过期时间，过期后token失效，需要重新登陆获取新token。token过期时间为3小时。
    *   `data.token`: 登陆之后获取的token，除登陆外的所有接口都需要携带此token。否则将拒绝服务。

## 2. 查询接口

*   **接口地址:** `GET https://api-miaoda.sina.com.cn/complaint/detail_v2`
*   **请求参数格式:** JSON
*   **请求参数样例 (调用示例):**

    ```http
    GET https://api-miaoda.sina.com.cn/complaint/detail_v2
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NDY5MDU3ODYsImlkIjoxMTIwMzEwNjEzLCJvcmlnX21hdCI6MTY0Njg5NDk4Nn0.J0Emh2fzi9OK-agAyYecabX0NZNuJpS1f4fWBGSKHD8
    
    {
      "status": 4,           // 投诉单状态4—待回复 6–已回复 7–已完成。选填，不填默认为所有有效投诉。
      "st": 1646693677,      // 起始时间（分配时间）选填，但必须和结束时间同时出现。若不填，则默认一周内。
      "et": 1646811678,      // 结束时间（分配时间）选填，但必须和起始时间同时出现。若不填，则默认一周内。
      "page": 1,             // 页码。选填，默认为第一页。
      "page_size": 10        // 每页数量。选填，默认每页10条，最多每页30条，超过30条按照30条查询。
    }
    ```
*   **请求参数说明:**

| 字段名     | 字段名称   | 类型  | 是否必填 | 说明                                             |
| :--------- | :--------- | :---- | :------- | :----------------------------------------------- |
| status     | 投诉单状态 | int   | 否       | 4—待回复 6—已回复 7—已完成                       |
| st         | 起始时间   | int64 | 否       | 必须和结束时间同时出现。若不填，则默认一周内     |
| et         | 结束时间   | int64 | 否       | 必须和起始时间同时出现。若不填，则默认一周内     |
| page       | 页码       | int   | 否       | 默认为第一页                                     |
| page\_size | 每页数量   | int   | 否       | 默认每页10条，最多每页30条，超过30条按照30条查询 |

*   **返回参数样例:** (为方便审阅,在json中备有注释,实际情况中没有注释。若某字段值为空,则不传输该字段。)

    ```json
    {
      "result": {
        "status": {
          "code": 200,                      // 状态码,详情见文档末尾
          "msg": "success"                  // 成功显示“success”,失败显示对应失败原因
        },
        "timestamp": "Thursday, 10-Mar-22 15:37:44 CST", // 时间戳
        "data": {
          "complaints": [                   // 投诉列表,按分配时间倒序显示
            {
              "sn": 20180315321,              // 投诉编号
              "uri": "https://tousu.sina.com.cn/complaint/view/20180315321", // 投诉链接
              "title": "315031",                // 投诉标题
              "nickname": "用户6823665889",      // 投诉人,若匿名则显示匿名
              "phone": "1590790823X",         // 联系方式
              "comp_phone": "1590790823X",     // 用户投诉时的预留手机号(新增)
              "privacy": "177293475",           // 涉诉单号
              "content": "投诉内容",             // 投诉内容
              "issue": "退货不退款,逾期不发货,商品破损,响应时间长", // 投诉问题
              "appeal": "退货退款",             // 投诉要求
              "cost": "1000",                   // 涉诉金额
              "attaches": [                   // 附件列表,如果没有附件则为空
                {
                  "type": "image",              // 附件类型
                  "src": "https://sinastorage.com/sandbox/products/202203/********************************.png?Expires=1646912264&KID=sina%2CSYS0000000000SANDBOX&ssig=aTfBiEdSdd" // 附件URI
                }
              ],
              "status": "投诉已完成",             // 投诉进度
              "created_at": 1646730257,        // 发起时间
              "assigned_at": 1646733275,       // 分配时间
              "reply_details": [              // 用户补充/商家回复详情,列表形式
                {
                  "sender": 1,                    // 回复人: 1.用户补充 2. 商家回复
                  "content": "补充投诉测试",          // 用户补充/商家回复内容
                  "content_hide": 0,              // 回复是否隐藏 0.代表公开 1.代表隐藏
                  "attaches": [
                    {
                      "type": "image",
                      "src": "https://sinastorage.com/sandbox/products/202203/********************************.png?Expires=1648480577&KID=sina%2CSYS0000000000SANDBOX&ssig=kZIdmm4N4C"
                    }
                  ],
                  "replyed_at": 1648466135,     // 回复时间
                  "attach_hide": 0                // 附件是否隐藏 0.代表公开 1.代表隐藏
                }
              ],
              "attitude": 5,                    // 用户评价-服务态度。此字段只有“投诉已完成”时才会出现
              "process": 4,                     // 用户评价-处理速度。此字段只有“投诉已完成”时才会出现
              "satisfaction": 3,                // 用户评价-满意度。此字段只有“投诉已完成”时才会出现
              "evalContent": "123",               // 用户评价-评价内容。此字段只有“投诉已完成”时才会出现
              "eval_at": 1646723895,            // 用户评价-评价时间。此字段只有“投诉已完成”时才会出现
              "completed_at": 1648450382,      // 投诉单完成时间。此字段只有“投诉已完成”时才会出现
              "exposed": 1,                     // 公开状态 0.未公开 1.已公开
              "status_no": 112,                 // 投诉流程号
              "service": "服务名",                // 所使用的服务名称(新增)
              "appeal_chance": 3,               // 剩余申诉次数(新增)
              "co_complete_chance": 2,          // 剩余结案次数(新增)
              "co_complete_info": [             // 结案信息列表(新增)
                {
                  "co_complete_solution": "用户说好", // 结案解决方案。此字段只有商家申请结案时才会出现
                  "co_complete_reason": "与用户达成一致", // 结案申请原因。此字段只有商家申请结案时才会出现
                  "co_complete_begin": 16859491817,   // 结案提交时间。此字段只有商家申请结案时才会出现
                  "co_complete_attaches": [       // 结案附件
                    {
                      "type": "image",
                      "src": "https://sinastorage.com/sandbox/products/202306/********************************.png?Expires=1686659529&KID=sina%2CSYS0000000000SANDBOX&ssig=e6WSVzgQnz"
                    }
                  ]
                },
                {
                  "co_complete_solution": "该方案需为慎重考虑用户利益且企业内部确认一致,能给予用户的最终方案",
                  "co_complete_reason": "最终解决方案",
                  "co_complete_begin": 1685951004
                }
              ],
              "co_complete_status": "用户同意结案",   // 结案中、用户拒绝结案、用户同意结案、平台自动结案
              "co_complete_at": 1648450382,     // 结案自动完成时间。此字段只有商家结案成功时才会出现
              "auto_complete_at": 1648450382,    // 自动完成时间
              "user_complete_at": 1648450382     // 主动完成时间
            }
          ],
          "pager": {                        // 页码信息
            "current": 1,                     // 当前页码
            "next": 2,                        // 下一页页码
            "page_amount": 7,                 // 页码总数
            "page_size": 10,                  // 每页投诉单数量
            "item_count": 64                  // 符合条件的总投诉单数量
          }
        }
      }
    }
    ```

## 3. 更新接口

*   **说明:** 考虑到投诉状态会持续更新,故新增更新接口。
*   **接口地址:** `GET https://api-miaoda.sina.com.cn/complaint/update`
*   **请求参数格式:** JSON
*   **请求参数样例 (调用示例):**

    ```http
    GET https://api-miaoda.sina.com.cn/complaint/update
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NDY4ODU0MzksImlkIjoxMTIwMzEwNjEzLCJvcmlnX21hdCI6MTY0Njg3NDYzOX0.fv5jke9wE4uSqXhNHioXH04LIMNve4pcxQ-Y1R8pDXU
    
    // 列表形式,每次请求最多接受30单,超过30单不予更新
    [
      {
        "sn": 20180315385,      // 待更新的投诉单号
        "status_no": 333      // 待更新的投诉单的状态版本号
      },
      {
        "sn": 20180315338,
        "status_no": 110
      }
    ]
    ```
*   **请求参数详情-表格 (参数说明):** (列表形式, 列表内元素详情如下):

| 字段名     | 字段名称                   | 类型  | 是否必填 | 说明 |
| :--------- | :------------------------- | :---- | :------- | :--- |
| sn         | 待更新的投诉单号           | int64 | 是       |      |
| status\_no | 待更新的投诉单的状态版本号 | int64 | 是       |      |

*   **请求参数说明:**
    *   `status_no`: 是由query接口返回的版本号。如果update接口请求中此值和query接口中返回的不一致,则不予更新,并返回错误。
    *   `sn`: 投诉单号。如果此投诉单不属于该商家,则不予更新,并返回错误。
    *   请求参数是列表形式的Json,同一次请求最多处理30条数据。超过30条的按照前30条处理。
*   **返回参数样例:** (为方便审阅,在json中备有注释,实际情况中没有注释。若某字段值为空,则不传输该字段。)

    ```json
    {
      "result": {
        "status": {
          "code": 200,
          "msg": "success"
        },
        "timestamp": "Thursday, 10-Mar-22 16:42:34 CST",
        "data": [
          {                               // 所更新的第一单
            "sn": 20180315385,
            "status": "投诉已移除",          // 如果重新分配此投诉于其他商家,视作此单已移除
            "status_no": 337
          },
          {                               // 所更新的第二单
            "sn": 20180315338,
            "status": "处理中",             // 投诉单当前最新状态
            "status_no": 112,             // 投诉单当前最新版本号
            "exposed": 1,                 // 投诉单是否已被曝光,0–未曝光 1-已曝光(新增)
            "complete_at": 1648450382,    // 完成时间。此字段只有“投诉已完成”时才会出现(新增)
            "attitude": 5,                // 用户评价-服务态度。此字段只有“投诉已完成”时才会出现(新增)
            "process": 4,                 // 用户评价-处理速度。此字段只有“投诉已完成”时才会出现(新增)
            "satisfaction": 3,            // 用户评价-满意度。此字段只有“投诉已完成”时才会出现(新增)
            "evalContent": "123",           // 用户评价-评价内容。此字段只有“投诉已完成”时才会出现(新增)
            "eval_at": 1646723895,         // 用户评价—评价时间。此字段只有“投诉已完成”时才会出现(新增)
            "reply_details": [            // 补充投诉、商家回复相关信息,列表形式
              {                           // 以下字段同query接口
                "sender": 1,
                "content": "回复11",
                "content_hide": 1,
                "attaches": [
                  {
                    "type": "image",
                    "src": "https://sinastorage.com/sandbox/products/202203/********************************.png?Expires=1646916154&KID=sina%2CSYS0000000000SANDBOX&ssig=CqMyY7R0nB"
                  }
                ],
                "replyed_at": 1646810682,
                "attach_hide": 0
              }
            ],
            "service": "服务名",              // 所使用的服务名称(新增)
            "appeal_chance": 3,             // 剩余申诉次数(新增)
            "co_complete_chance": 2,        // 剩余结案次数(新增)
            "co_complete_info": [           // 结案信息列表(新增)
              {
                "co_complete_solution": "用户说好", // 结案解决方案。此字段只有商家申请结案时才会出现
                "co_complete_reason": "与用户达成一致", // 结案申请原因。此字段只有商家申请结案时才会出现
                "co_complete_begin": 16859491817,   // 结案提交时间。此字段只有商家申请结案时才会出现
                "co_complete_attaches": [       // 结案附件
                  {
                    "type": "image",
                    "src": "https://sinastorage.com/sandbox/products/202306/********************************.png?Expires=1686659529&KID=sina%2CSYS0000000000SANDBOX&ssig=e6WSVzgQnz"
                  }
                ]
              },
              {
                "co_complete_solution": "该方案需为慎重考虑用户利益且企业内部确认一致,能给予用户的最终方案",
                "co_complete_reason": "最终解决方案",
                "co_complete_begin": 1685951004
              }
            ],
            "co_complete_status": "用户同意结案", // 结案中、用户拒绝结案、用户同意结案、平台自动结案
            "co_complete_at": 1648450382,   // 结案自动完成时间。此字段只有商家结案成功时才会出现
            "auto_complete_at": 1648450382,  // 自动完成时间
            "user_complete_at": 1648450382   // 主动完成时间
          }
        ]
      }
    }
    ```

## 4. 回复接口

*   **说明:** 可使用此接口直接对投诉单进行回复操作。需注意,只有通过detail接口查询过的投诉单才能回复,否则会返回错误。
*   **接口地址:** `POST https://api-miaoda.sina.com.cn/complaint/reply`
*   **请求参数格式:** application/json
*   **请求参数样例 (调用示例):**

    **HTTP格式:**
    ```http
    POST https://api-miaoda.sina.com.cn/complaint/reply
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTk1OTM5NDUsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY1OTU4MzE0NX0.YcrjDTsB2BmkUV4QNCOYLQ1lSyTzkMMyO9T2kLDrLlU
    
    {
      "sns": "20180316248",
      "content": "testing message",
      "hide_attach": 1,
      "hide_content": 1,
      "images": [
        "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
        "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
      ],
      "videos": [
        "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
      ]
    }
    ```
    **CURL格式:**
    ```shell
    curl --location --request POST 'https://api-miaoda.sina.com.cn/complaint/reply' \
    --header 'Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAyOTU0ODIsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDI4NDY4Mn0.84_De4qEWnKCpfiXZZT0gJUCuiJ91RS7t-Vhxnt-7vU' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "sns":"20180316248",
        "content":"testing message3",
        "hide_attach":1,
        "hide_content":1,
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ]
    }'
    ```
*   **请求参数详情 (参数说明):**

| 字段名        | 字段名称           | 类型    | 是否必填 | 说明                       |
| :------------ | :----------------- | :------ | :------- | :------------------------- |
| sns           | 投诉单号           | string  | 是       | 多个单号之间用英文逗号连接 |
| content       | 回复内容           | string  | 是       |                            |
| hide\_attach  | 是否隐藏附件       | int     | 否       |                            |
| hide\_content | 是否隐藏内容       | int     | 否       |                            |
| images        | 上传图片的网络地址 | uri列表 | 否       |                            |
| videos        | 上传视频的网络地址 | uri列表 | 否       |                            |

*   **参数解释:**
    *   **json参数:**
        *   `sns`: 投诉单号,支持多单批量回复。多个投诉单号中间使用英文逗号分隔。(必须)
        *   `content`: 回复内容。(必须)
        *   `hide_content`: 回复内容是否隐藏,若隐藏内容,则只有仅投诉平台、投诉人、被投诉人、帮帮团可见。0代表不隐藏,1代表隐藏。不上传此字段默认不隐藏。(非必须)
        *   `hide_attach`: 上传的附件是否隐藏,若隐藏附件,则只有仅投诉平台、投诉人、被投诉人、帮帮团可见。0代表不隐藏,1代表隐藏。不上传此字段默认不隐藏。(非必须)
        *   `images参数`: 欲上传的图片,路径为网络路径,支持格式“png, jpeg, jpg”;
        *   `videos参数`: 欲上传的音/视频文件,路径为网络路径,支持格式“mp3, mp4";
*   **限制规则:**
    *   同一次请求最多处理30条数据。超过30条则不予回复,并返回错误;
    *   图片最多上传30张,每张图片的大小不超过5M;
    *   音/视频最多上传3个,视频大小不超过100M,音频大小不超过10M;

## 5. 申诉接口

*   **说明:** 可使用此接口对不属于您的投诉单或重复的投诉单进行申诉操作。需注意,只有通过detail接口查询过的投诉单才能申诉,否则会返回错误。
*   **接口地址:** `POST https://api-miaoda.sina.com.cn/complaint/appeal`
*   **请求参数格式:** application/json
*   **请求参数样例 (调用示例):**

    **http格式**
    ```http
    ### 申诉接口
    POST https://api-miaoda.sina.com.cn/complaint/appeal
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjA3MzUxMTEsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDcyNDMxMX0.xE5lH5KqN8x6TJ7tdNRPxEXKWNaQuJt9DTrGpD-sZlk
    
    {
      "sns": "20180316259",
      "content": "申诉内容",
      "type": 1,
      "dup_sns": "20180316256",
      "images": [
        "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
        "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
      ],
      "videos": [
        "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
      ]
    }
    ```
    **curl格式**
    ```shell
    curl --location --request POST 'https://api-miaoda.sina.com.cn/complaint/appeal' \
    --header 'Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAyOTU0ODIsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDI4NDY4Mn0.84_De4qEWnKCpfiXZZT0gJUCuiJ91RS7t-Vhxnt-7vU' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "sns":"20180316248",
        "content":"testing message3",
        "type":1,
        "dup_sns":"20180316247",
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ]
    }'
    ```
*   **参数说明:**

| 字段名   | 字段名称           | 类型    | 是否必填 | 说明                                                        |
| :------- | :----------------- | :------ | :------- | :---------------------------------------------------------- |
| sns      | 投诉单号           | string  | 是       | 投诉单号,支持多单批量申诉。多个投诉单号中间使用英文逗号分隔 |
| content  | 申诉内容           | string  | 是       |                                                             |
| type     | 申诉类型           | int     | 是       | 1代表”重复投诉“ 2代表”非本商户投诉“                         |
| dup\_sns | 所重复的投诉单号   | string  | 否       | 当申诉类型为1是需要填写                                     |
| images   | 上传图片的网络地址 | uri列表 | 否       | 支持格式“png, jpeg, jpg"                                    |
| videos   | 上传视频的网络地址 | uri列表 | 否       | 支持格式“mp3, mp4"                                          |

*   **参数解释:**
    *   **json参数:**
        *   `sns`: 投诉单号,支持多单批量申诉。多个投诉单号中间使用英文逗号分隔。(必须)
        *   `content`: 申诉内容。(必须)
        *   `type`: 申诉类型 1代表”重复投诉“ 2代表”非本商户投诉“ (必须)
        *   `dup_sns`: 当申诉类型为1是需要填写,代表所重复的投诉单号 (非必须)
        *   `images参数`: 欲上传的图片,路径为网络路径,支持格式“png, jpeg, jpg”; (非必须)
        *   `videos参数`: 欲上传的音/视频文件,路径为网络路径,支持格式“mp3, mp4”; (非必须)
*   **限制规则:**
    *   同一次请求最多处理30条数据。超过30条则不予申诉,并返回错误;
    *   图片最多上传10张,每张图片的大小不超过5M;
    *   音/视频最多上传3个,视频大小不超过100M,音频大小不超过10M;

## 6. 申诉状态查询

*   **说明:** 可使用此接口对已经申诉的投诉单进行状态查询,以便得知申诉的结果。需注意,只有申诉过的投诉单才能进行申诉状态查询,否则会返回错误。
*   **接口地址:** `GET https://api-miaoda.sina.com.cn/complaint/appeal_status`
*   **请求参数格式:** application/json
*   **请求参数样例 (调用示例):**

    ```http
    ### 申诉查询接口
    GET https://api-miaoda.sina.com.cn/complaint/appeal_status
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjA2NTMxNTgsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDY0MjM1OHO.vqaPEas-W7OC2oVAnzvKPZrttRxUZUAumls37PL3aHg
    
    {
        "sns":"20180316167"
    }
    ```
    **curl格式**
    ```shell
    curl --location --request GET 'https://api-miaoda.sina.com.cn/complaint/appeal_status' \
    --header 'Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAyOTU0ODIsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDI4NDY4Mn0.84_De4qEWnKCpfiXZZT0gJUCuiJ91RS7t-Vhxnt-7vU' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "sns":"20180316167",
    }'
    ```
*   **参数说明:**

| 字段名 | 字段名称                             | 类型   | 是否必填 | 说明                              |
| :----- | :----------------------------------- | :----- | :------- | :-------------------------------- |
| sns    | 投诉单号,多个单号之间用英文逗号 连接 | string | 是       | 多个投诉单号中间使用英文逗号 分隔 |

*   **参数解释:**
    *   **json参数:**
        *   `sns`: 投诉单号,支持多单批量查询。多个投诉单号中间使用英文逗号分隔。(必须)
*   **限制规则:**
    *   同一次请求最多处理30条数据。超过30条则不予查询,并返回错误;
*   **返回结果样例:**

    ```http
    GET https://api-miaoda.sina.com.cn/complaint/appeal_status
    HTTP/1.1 200 OK
    Content-Type: application/json; charset=utf-8
    Date: Thu, 18 Aug 2022 02:07:36 GMT
    Content-Length: 211
    
    {
        "result": {
            "status": {
                "code": 200,
                "msg": "success"
            },
            "timestamp": "Thursday, 18-Aug-22 10:00:00 CST",
            "data": {
                "appeal_status": [
                    {
                        "sn": "20180316167",
                        "status": "申诉中"
                    },
                    {
                        "sn": "20180316118",
                        "status": "申诉驳回",
                        "reason": "驳回理由"
                    }
                ]
            }
        }
    }
    ```

## 7. 结案接口

*   **说明:** 可使用此接口对已经回复的投诉单进行结案操作。需注意,只有通过detail接口查询过、并通过reply接口回复过、且不在申诉中的投诉单才能发起结案,否则会返回错误。
*   **接口地址:** `POST https://api-miaoda.sina.com.cn/complaint/complete`
*   **请求参数格式:** application/json
*   **请求参数样例 (调用示例):**

    **http格式**
    ```http
    ### 申诉接口
    POST https://api-miaoda.sina.com.cn/complaint/complete
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjA3MzUxMTEsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDcyNDMxMX0.xE5lH5KqN8x6TJ7tdNRPXEXKWNaQuJt9DTrGpD-sZlk
    
    {
        "sn":"20180316259",
        "reason":3,
        "solution":"解决方案细节",
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ],
        "hide_attach":1
    }
    ```
    **curl格式**
    ```shell
    curl --location --request POST 'https://api-miaoda.sina.com.cn/complaint/complete' \
    --header 'Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAyOTU0ODIsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDI4NDY4Mn0.84_De4qEWnKCpfiXZZT0gJUCuiJ91RS7t-Vhxnt-7vU' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "sn":"20180316259",
        "reason":3,
        "solution":"解决方案细节",
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ],
        "hide_attach":1
    }'
    ```
*   **参数说明:**

| 字段名       | 字段名称             | 类型    | 是否必填 | 说明                                                         |
| :----------- | :------------------- | :------ | :------- | :----------------------------------------------------------- |
| sn           | 投诉单号             | string  | 是       | 结案接口的每一次调用仅支持一个投诉单                         |
| reason       | 结案类型             | int     | 是       | 1代表“已与用户沟通并达成一致” 2代表“联系不上用户” 3代表“最终解决方案” |
| solution     | 所提供的解决方案细节 | string  | 是       |                                                              |
| hide\_attach | 上传的附件是否隐藏   | int64   | 否       | 当申诉类型为1是需要填写                                      |
| images       | 上传图片的网络地址   | uri列表 | 否       | 支持格式“png, jpeg, jpg"                                     |
| videos       | 上传视频的网络地址   | uri列表 | 否       | 支持格式“mp3, mp4"                                           |

*   **参数解释:**
    *   **json参数:**
        *   `sn`: 投诉单号,结案接口的每一次调用仅支持一个投诉单。(必须)
        *   `reason`: 结案类型:1代表“已与用户沟通并达成一致”2代表“联系不上用户”3代表“最终解决方案”。(必须)
        *   `solution`: 所提供的解决方案细节。(必须)
        *   `hide_attach`: 上传的附件是否隐藏,若隐藏附件,则只有仅投诉平台、投诉人、被投诉人、帮帮团可见。0代表不隐藏,1代表隐藏。不上传此字段默认不隐藏。(非必须)
        *   `images参数`: 欲上传的图片,路径为网络路径,支持格式“png,jpeg,jpg”。(非必须)
        *   `videos参数`: 欲上传的音/视频文件,路径为网络路径,支持格式“mp3,mp4”。(非必须)
*   **限制规则:**
    *   同一次请求只处理一条投诉单的结案申请;
    *   图片最多上传30张,每张图片的大小不超过5M;
    *   音/视频最多上传3个,视频大小不超过100M,音频大小不超过10M;
    *   结案规则为1、2时,附件内容必填;

## 8. 快速解决商家查询

*   **接口地址:** `GET https://api-miaoda.sina.com.cn/complaint/rapid_solve`
*   **请求参数格式:** JSON
*   **请求参数样例 (调用示例):**

    ```http
    ###
    GET https://api-miaoda.sina.com.cn/complaint/rapid_solve
    Authorization:MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTI2NjM0NTksImlkIjo1ODg5MTc3NTc5LCJvcmlnX21hdCI6MTcxMjY1MjY1OX0.Mei9HlwmnVLjL5rT1pbVri5bPybpPqERb4xjsmStALk
    Content-Type: application/json
    
    {
        "month": "2024-1"
    }
    ```
*   **参数:**

| 参数  | 类型   | 样例              | 是否必须              | 说明 |
| :---- | :----- | :---------------- | :-------------------- | :--- |
| month | string | "month": "2024-1" | 否。若为空,则查询当月 |      |

*   **返回参数样例:**

    ```json
    {
        "result": {
            "status": {
                "code": 200,
                "msg": "ok"
            },
            "timestamp": "Tue Apr 09 17:10:06 +0800 2024",
            "data": {
                "can_noexp": 0,
                "valid_amt": 200,
                "reply_amt": 100,
                "reply_per": 50,
                "complete_amt": 40,
                "complete_per": 20,
                "avg_reply_time": "6.36",
                "total_complete_per": 33,
                "total_complete_amt_target": 30,
                "reply_amt_target": 70,
                "complete_amt_target": 60,
                "avg_reply_time_target": "1.36"
            }
        }
    }
    ```
*   **返回值说明:**

| 字段值                       | 说明                                                         |
| :--------------------------- | :----------------------------------------------------------- |
| can\_noexp                   | 是否为快速解决商家 0-否 1-是 注:查询当月时返回值仅作为预估判断,实际 是否达标需次月统计后查看 |
| valid\_amt                   | 当月投诉量                                                   |
| reply\_amt                   | 当月回复量                                                   |
| reply\_per                   | 当月回复率                                                   |
| complete\_amt                | 当月完成量                                                   |
| complete\_per                | 当月完成率                                                   |
| avg\_reply\_time             | 当月平均响应时间                                             |
| total\_complete\_per         | 总投诉完成率                                                 |
| total\_complete\_amt\_target | 总投诉完成率单量差,若达标则为0                               |
| reply\_amt\_target           | 当月回复率单量差,若达标则为0                                 |
| complete\_amt\_target        | 当月完成率单量差,若达标则为0                                 |
| avg\_reply\_time\_target     | 当月响应时间指标差,若达标则为""                              |

## 9. 查询套餐余量接口

*   **接口地址:** `GET https://api-miaoda.sina.com.cn/complaint/plan_info`
*   **请求参数格式:** JSON
*   **请求参数样例 (调用示例):**

    ```http
    ###
    GET https://api-miaoda.sina.com.cn/complaint/plan_info
    Content-Type: application/json
    Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NDg1NDkxNTksImlkIjolMzY0MDI4MzM0LCJvcmlnX21hdCI6MTY0ODUzODM1OX0.YTXVWM8EeAin_rEGS3fvjw5Hb3l2yPCHQd2fP8TJ5eI
    ```
*   **返回参数样例:**

    ```json
    {
        "result": {
            "status": {
                "code": 200,
                "msg": "success"
            },
            "timestamp": "Tuesday, 29-Mar-22 16:41:49 CST",
            "data": {
                "total_amount": 2000,                   // 查询接口总量
                "remaining_amount": 1999,               // 查询接口余量
                "appeal_total_amount": 1000,            // 申诉接口总量
                "appeal_remaining_amount": 1000,        // 申诉接口余量
                "complete_total_amount": 1000,          // 结案接口总量
                "complete_remaining_amount": 1000,      // 结案接口余量
                "packet_info": [
                    {
                        "packet_name": "基础包",
                        "packet_quantity": 1,
                        "packet_total_amount": 1000,
                        "packet_remaining_amount": 999,
                        "packet_st": 1646031960,
                        "packet_et": 1653376020
                    },
                    {
                        "packet_name": "加量包-A",
                        "packet_quantity": 1,
                        "packet_total_amount": 1000,
                        "packet_remaining_amount": 1000,
                        "packet_st": 1646064000,
                        "packet_et": 1648656000
                    }
                ]
            }
        }
    }
    ```

## 10. 投诉认领接口

*   **说明:** 可使用此接口对本属于贵司的投诉的进行认领申诉操作。
*   **接口地址:** `POST https://api-miaoda.sina.com.cn/complaint/claim`
*   **请求参数格式:** application/json
*   **请求参数样例 (调用示例):**

    **http格式**
    ```http
    ### 投诉认领接口
    POST https://api-miaoda.sina.com.cn/complaint/claim
    Content-Type: application/json
    Authorization:MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjA3MzUxMTEsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDcyNDMxMX0.xE5lH5KqN8x6TJ7tdNRPXEXKWNaQuJt9DTrGpD-sZlk
    
    {
        "sn":"20180316259",
        "content":"申诉内容",
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ]
    }
    ```
    **curl格式**
    ```shell
    curl --location --request POST 'https://api-miaoda.sina.com.cn/complaint/claim' \
    --header 'Authorization: MiaoDa eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAyOTU0ODIsImlkIjo3NDg3OTA0Mjg2LCJvcmlnX21hdCI6MTY2MDI4NDY4Mn0.84_De4qEWnKCpfiXZZT0gJUCuiJ91RS7t-Vhxnt-7vU' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "sn":"20180316248",
        "content":"testing message3",
        "images":[
            "https://wx1.sinaimg.cn/mw690/d8aeec87gy1h52n0a38h6j20rv0d276w.jpg",
            "https://wx3.sinaimg.cn/mw690/d8aeec87gy1h52n0ardkpj20wilycn4t.jpg"
        ],
        "videos":[
            "http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4"
        ]
    }'
    ```
*   **参数说明:**

| 字段名  | 字段名称           | 类型    | 是否必填 | 说明                     |
| :------ | :----------------- | :------ | :------- | :----------------------- |
| sn      | 投诉单号           | string  | 是       | 投诉单号                 |
| content | 投诉认领的说明     | string  | 是       |                          |
| images  | 上传图片的网络地址 | uri列表 | 否       | 支持格式“png, jpeg, jpg” |
| videos  | 上传视频的网络地址 | uri列表 | 否       | 支持格式“mp3, mp4”       |

*   **参数解释:**
    *   **json参数:**
        *   `sn`: 投诉单号 (必须)
        *   `content`: 投诉认领的说明 (必须)
        *   `images参数`: 欲上传的图片,路径为网络路径,支持格式“png,jpeg,jpg”; (非必须)
        *   `videos参数`: 欲上传的音/视频文件,路径为网络路径,支持格式“mp3,mp4”; (非必须)
*   **限制规则:**
    *   图片最多上传10张,每张图片的大小不超过5M;
    *   音/视频最多上传3个,视频大小不超过100M,音频大小不超过10M;

## 11. 错误码和状态码

### 11.1 错误码

| 错误码 | 错误详情          |
| :----- | :---------------- |
| 30001  | http请求参数错误  |
| 30002  | 身份校验错误      |
| 30003  | JSON解析失败      |
| 30004  | 数据库操作失败    |
| 30005  | 网络连接错误      |
| 30006  | 此次查询结果为空  |
| 30007  | 附件格式/参数错误 |

### 11.2 状态码

| 状态码 | 状态说明                   | 对应文字         |
| :----- | :------------------------- | :--------------- |
| -2     | 上传视频处理失败(异常状态) | 上传视频处理失败 |
| -1     | 上传视频处理中             | 上传视频处理中   |
| 0      | 刚提交待审核               | 待平台审核       |
| 1      | 审核通过                   | 审核已通过       |
| 2      | 审核不通过                 | 审核未通过       |
| 3      | 待分配                     | 待分配商家       |
| 4      | 分配成功 == 处理中         | 处理中           |
| 5      | 分配失败                   | 分配商家失败     |
| 6      | 投诉对象已经回复           | 已回复           |
| 7      | 投诉已完成                 | 投诉已完成       |
| 8      | 投诉已关闭                 | 投诉已关闭       |
| 9      | 投诉已移除                 | 投诉已移除       |

### 11.3 申诉状态码

| 申诉状态码 | 对应文字   |
| :--------- | :--------- |
| 0          | 未发起申诉 |
| 1          | 申诉中     |
| 2          | 申诉驳回   |
| 3          | 申诉通过   |