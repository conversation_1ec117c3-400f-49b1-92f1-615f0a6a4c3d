package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.vip.SVipQueryDTO;
import com.welab.crm.interview.dto.vip.VipLockDTO;
import com.welab.crm.interview.dto.vip.VipLockTipsDTO;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.vo.vip.VipBenefitOrderResVO;
import com.welab.crm.interview.vo.vip.VipInfoVO;
import com.welab.crm.interview.vo.vip.VipOperateLogVO;
import com.welab.crm.interview.vo.vip.VipOrderVO;
import com.welab.crm.operate.service.ExternalService;
import com.welab.crm.operate.web.constants.Constant;
import com.welab.crm.operate.web.constants.Urls.Vip;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/22
 */
@RestController
@Api(description = "vip客户服务")
@RequestMapping(Vip.ROOT)
public class VipController {

    @Resource
    private VipService vipService;
    @Autowired
    private ExternalService externalService;

    @GetMapping(value = Vip.V1_VIP_ORDER_LIST)
    @ApiOperation(value = Vip.V1_VIP_ORDER_LIST_DESC, notes = Vip.V1_VIP_ORDER_LIST_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "客户userId", paramType = "query"),
    })
    public Response<List<VipOrderVO>> getVipOrderList(@RequestHeader("X-Mobile") String mobile, @RequestParam @NotNull Integer userId) {
        List<VipOrderVO> vipOrderList = vipService.getVipOrderList(userId);
        if(externalService.isSpecificGroup(mobile, Constant.KEFU_GROUP)){
            vipOrderList = vipOrderList.stream().filter(vipOrderVO -> externalService.isClosedIn1Year(DateUtil.stringToDate(vipOrderVO.getTransactionTime()))).collect(Collectors.toList());
        }
        return Response.success(vipOrderList);
    }

    @GetMapping(value = Vip.V1_VIP_RIGHT_LIST)
    @ApiOperation(value = Vip.V1_VIP_RIGHT_LIST_DESC, notes = Vip.V1_VIP_RIGHT_LIST_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "客户userId", paramType = "query"),
    })
    public Response<List<VipInfoVO>> getVipRightList(@RequestHeader("X-Mobile") String mobile, @BeanParam SVipQueryDTO queryDTO) {
        if(queryDTO.getUuid() == null || queryDTO.getUserId() == null){
            return  Response.success(new ArrayList<>(0));
        }
        List<VipInfoVO> vipInfoList = vipService.getVipInfoList(queryDTO);
        if(externalService.isSpecificGroup(mobile, Constant.KEFU_GROUP)){
            vipInfoList = vipInfoList.stream().filter(vipInfoVO -> externalService.isClosedIn1Year(vipInfoVO.getPaymentAt())).collect(Collectors.toList());
        } else {
            vipInfoList.forEach(vipInfoVO -> {
                if (!externalService.isClosedIn1Year(vipInfoVO.getPaymentAt())) {
                    vipInfoVO.setBackgroundColor("grey");
                }
            });
        }
        
        return Response.success(vipInfoList);
    }

    @GetMapping(value = Vip.V1_VIP_OPERATE_LOG_LIST)
    @ApiOperation(value = Vip.V1_VIP_OPERATE_LOG_LIST_DESC, notes = Vip.V1_VIP_OPERATE_LOG_LIST_DESC)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "会员订单号", paramType = "query"),
    })
    public Response<List<VipOperateLogVO>> getVipLogList(@RequestParam @NotNull String orderNo) {
        return Response.success(vipService.getVipOperateLog(orderNo));
    }


    @GetMapping(value = Vip.V1_VIP_BENEFIT_LIST)
    @ApiOperation(value = Vip.V1_VIP_BENEFIT_LIST_DESC, notes = Vip.V1_VIP_BENEFIT_LIST_DESC)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "会员订单号", paramType = "query"),
    })
    public Response<List<VipBenefitOrderResVO>> getBenefitList(@RequestParam @NotNull String orderNo) {
        return Response.success(vipService.getVipBenefitOrders(orderNo));
    }

    @PostMapping(value = Vip.V1_VIP_LOCK)
    @ApiOperation(value = Vip.V1_VIP_LOCK_DESC, notes = Vip.V1_VIP_LOCK_DESC)
    public Response<String> vipOrderLock(@RequestBody VipLockDTO dto) {
        return vipService.lock(dto);
    }

    @PostMapping(value = Vip.V1_VIP_LOCK_TIPS)
    @ApiOperation(value = Vip.V1_VIP_LOCK_TIPS_DESC, notes = Vip.V1_VIP_LOCK_TIPS_DESC)
    public Response<String> vipOrderLockTips(@RequestBody VipLockTipsDTO dto) {
        return vipService.lockTips(dto);
    }

    @PostMapping(value = Vip.V1_VIP_UNLOCK)
    @ApiOperation(value = Vip.V1_VIP_UNLOCK_DESC, notes = Vip.V1_VIP_UNLOCK_DESC)
    public Response<String> vipOrderUnLock(@RequestBody VipLockDTO dto) {
        return vipService.unlock(dto);
    }
}
