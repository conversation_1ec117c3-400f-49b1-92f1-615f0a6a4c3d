package com.welab.crm.operate.web.response;

import com.welab.common.response.enums.ResponsCodeTypeEnum;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 客服返回对象
 * @date 2022/4/14 11:44
 */
@Data
@NoArgsConstructor
@Builder
public final class Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码。默认返回成功
     */
    private String code = ResponsCodeTypeEnum.SUCCESS.getCode();

    /**
     * 返回码信息
     */
    private String message = ResponsCodeTypeEnum.SUCCESS.getMessage();

    /**
     * 返回结果
     */
    private T result;



    /**
     * 返回成功
     *
     * @param <T>
     * @return
     */
    public static <T> Response<T> success() {
        return new Response<>();
    }

    /**
     * 返回成功
     *
     * @param result 返回结果
     * @param <T>
     * @return
     */
    public static <T> Response<T> success(T result) {
        Response<T> response = new Response<>();
        response.setResult(result);

        return response;
    }

    /**
     * 判断请求是否成功
     *
     * @return 请求是否成功的结果
     */
    public static boolean isSuccess(Response<?> response) {
        if (response == null) {
            return false;
        }
        return ResponsCodeTypeEnum.SUCCESS.getCode().equals(response.getCode());
    }

    /**
     * 返回码、返回信息映射
     */
    public static final Map<String, String> RESPONSE_MAP = new HashMap<String, String>();

    static {
        for (ResponsCodeTypeEnum responsCodeTypeEnum : ResponsCodeTypeEnum.values()) {
            if (Response.RESPONSE_MAP.containsKey(responsCodeTypeEnum.getCode())) {
                throw new RuntimeException("业务返回码重复，请确认！重复的业务码是：" + responsCodeTypeEnum.getCode());
            }
            Response.RESPONSE_MAP.put(responsCodeTypeEnum.getCode(), responsCodeTypeEnum.getMessage());
        }
    }

    /**
     * 根据错误码返回错误码和错误信息
     *
     * @param code 错误码
     */
    public Response(String code) {
        this.code = code;
        this.message = RESPONSE_MAP.get(code);
    }

    /**
     * 根据错误码和返回结果组装返回码、返回信息和返回结果
     *
     * @param code 错误码
     * @param result 返回结果
     */
    public Response(String code, T result) {
        this.code = code;
        this.message = RESPONSE_MAP.get(code);
        this.result = result;
    }

    /**
     * 根据错误码、返回信息和返回结果组装返回码、返回信息和返回结果
     *
     * @param code 错误码
     * @param message 错误信息
     * @param result 返回结果
     */
    public Response(String code, String message, T result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }

}
