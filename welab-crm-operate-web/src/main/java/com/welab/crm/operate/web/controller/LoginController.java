package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.dto.StaffQueryDTO;
import com.welab.crm.operate.dto.login.LoginTypeDTO;
import com.welab.crm.operate.service.LoginService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.loginReport.LoginReportDetailVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;


@RestController
@Api(description = "登录相关接口")
@RequestMapping(Urls.Login.ROOT)
public class LoginController extends BaseController {

	@Resource
	private LoginService loginService;

	@ApiOperation(value = Urls.Login.SUBMIT_LOGIN_RESULT_DESC, notes = Urls.Login.SUBMIT_LOGIN_RESULT_DESC)
	@PostMapping(Urls.Login.SUBMIT_LOGIN_RESULT)
	@NoRepeatSubmit(expireSeconds = 60, times = 10, key = "${mobile}" + Urls.Login.SUBMIT_LOGIN_RESULT)
	public Response<Void> submitLoginResult(@RequestBody @Validated LoginTypeDTO dto) {
		loginService.submitLoginRecord(dto);
		return Response.success();
	}

	@ApiOperation(value = Urls.Login.QUERY_DETAIL_REPORT_DESC, notes = Urls.Login.QUERY_DETAIL_REPORT_DESC)
	@PostMapping(Urls.Login.QUERY_DETAIL_REPORT)
	public Response<Page<LoginReportDetailVO>> queryLoginDetailReport(@RequestBody @Validated StaffQueryDTO dto) {
		return Response.success(loginService.queryLoginReportDetailPage(dto));
	}

	@ApiOperation(value = Urls.Login.EXPORT_DETAIL_REPORT_DESC, notes = Urls.Login.EXPORT_DETAIL_REPORT_DESC)
	@PostMapping(Urls.Login.EXPORT_DETAIL_REPORT)
	public Response<Void> exportLoginDetailReport(@RequestBody @Validated StaffQueryDTO dto) throws IOException {
		CommonUtils.commonExport(baseResponse, "登录明细报表", LoginReportDetailVO.class, loginService.queryLoginReportDetailList(dto));
		return Response.success();
	}


}

