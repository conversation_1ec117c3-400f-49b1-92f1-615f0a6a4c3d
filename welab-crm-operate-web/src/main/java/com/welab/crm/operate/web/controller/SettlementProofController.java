package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.operate.dto.settlement.SettleProofApplyRecordDTO;
import com.welab.crm.operate.service.ISettlementProofService;
import com.welab.crm.operate.vo.settlement.SettleProofApplyRecordVO;
import com.welab.crm.operate.vo.settlement.SettlementProofReqDTO;
import com.welab.crm.operate.vo.settlement.SettlementProofVO;
import com.welab.crm.operate.constant.ParamConstant;
import com.welab.crm.operate.web.constants.RequestURI;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.crm.operate.enums.LzCallbackEnum;
import com.welab.crm.operate.web.enums.ResultCodeEnum;
import com.welab.loanapplication.interfaces.dto.LoanApplicationDTO;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/18
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.SettlementProof.ROOT)
@Api(description = "获取结清证明")
@Slf4j
public class SettlementProofController {

    @Autowired
    private LoanApplicationServiceFacade loanApplicationServiceFacade;

    @Autowired
    private ISettlementProofService settlementProofService;

    @Value("${lzbank.http.url}")
    private String lzBankUrl;

    @Value("${lhbank.http.url}")
    private String lhBankUrl;

    /**
     * 获取结清证明文件
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.SettlementProof.V1_SETTLEMENTPROOF_APPLY)
    @ApiOperation(value = Urls.SettlementProof.V1_SETTLEMENTPROOF_APPLY_DESC, notes = Urls.SettlementProof.V1_SETTLEMENTPROOF_APPLY_DESC)
    public Response<SettlementProofVO> queryDetail(@Validated @RequestBody SettleProofApplyRecordDTO reqDTO){
        SettlementProofVO settlementProofVO = new SettlementProofVO();
        try {
            if (StringUtils.isEmpty(reqDTO.getApplicationId())) {
                return new Response(null, "贷款号不能为空!");
            }
            LoanApplicationDTO loanApplication = loanApplicationServiceFacade.getLoanApplicationByApplicationId(reqDTO.getApplicationId());
            if (Objects.nonNull(loanApplication)) {
                if (!loanApplication.getPartnerCode().contains(ParamConstant.LZBANK)) {
                } else {
                    settlementProofVO.setPartnerCode(ParamConstant.LZBANK);
                    String url = lzBankUrl + RequestURI.LZ_SETTLE_QUERY;
                    String result = HttpClientUtil.get(url + reqDTO.getApplicationId());
                    log.info("applySettlementProof lzBank result:{}", result);
                    JSONObject resultJson = JSON.parseObject(result);
                    if (Objects.nonNull(resultJson) && resultJson.containsKey("result")) {
                        JSONObject res = resultJson.getJSONObject("result");
                        if (Objects.nonNull(res)) {
                            //settlementProofVO.setStatus(LzCallbackEnum.getMsg(res.getString("submitFlag")));
                            settlementProofVO.setStatus(res.getString("submitFlag"));
                            settlementProofVO.setMsg(res.getString("submitMsg"));
                            if (res.containsKey("fileUrl")) {
                                settlementProofVO.setFileUrl(res.getString("fileUrl"));
                                settlementProofService.updateProofRecord(settlementProofVO, reqDTO.getApplicationId());
                            }
                        }
                    } else {
                        settlementProofVO.setMsg(result);
                        settlementProofVO.setStatus(LzCallbackEnum.FAIL.getMsg());
                    }
                }
                return Response.success(settlementProofVO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            if (e.getMessage().contains("请不要重复提交")) {
                return new Response(ResultCodeEnum.REPEAT_SUBMITTED.getCode(), ResultCodeEnum.REPEAT_SUBMITTED.getMsg(), "");
            } else {
                return new Response(ResponsCodeTypeEnum.FAILURE.getCode(), "获取结清证明文件异常", "");
            }
        }
        return new Response(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), "贷款号不存在!", "");
    }

    /**
     * 申请兰州银行结清证明
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.SettlementProof.V1_LZ_BANK_SETTLEMENTPROOF_APPLY)
    @ApiOperation(value = Urls.SettlementProof.V1_LZ_BANK_SETTLEMENTPROOF_APPLY_DESC, notes = Urls.SettlementProof.V1_LZ_BANK_SETTLEMENTPROOF_APPLY_DESC)
    public Response<SettlementProofVO> LzSettlementProofApply(@Validated @RequestBody SettleProofApplyRecordDTO reqDTO) {
        log.info("LzSettlementProofApply 申请结清文件,请求参数:{}", JSON.toJSONString(reqDTO));
        try {
            if (StringUtils.isEmpty(reqDTO.getApplicationId())) {
                return new Response(null, "贷款号不能为空!");
            }
            LoanApplicationDTO loanApplication = loanApplicationServiceFacade.getLoanApplicationByApplicationId(reqDTO.getApplicationId());
            if (Objects.isNull(loanApplication)){
                return new Response(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), "贷款号不存在!", "");
            }
            if (settlementProofService.queryDupApply(reqDTO.getApplicationId())){
                SettlementProofVO vo = new SettlementProofVO();
                vo.setStatus(LzCallbackEnum.SUCCESS.getMsg());
                return Response.success(vo);
            }
            if (loanApplication.getPartnerCode().contains(ParamConstant.LZBANK)) {
                SettlementProofVO vo = new SettlementProofVO();
                vo.setPartnerCode(ParamConstant.LZBANK);
                String url = lzBankUrl + RequestURI.LZ_SETTLE_APPLY;
                Map<String, Object> params = new HashMap<>();
                params.put("applicationId", reqDTO.getApplicationId());
                String result = HttpClientUtil.get(url, params);
                log.info("LzSettlementProofApply lzBank result:{}", result);
                JSONObject resultJson = JSON.parseObject(result);
                if (Objects.nonNull(resultJson) && resultJson.containsKey("result")) {
                    JSONObject res = resultJson.getJSONObject("result");
                    vo.setStatus(LzCallbackEnum.getMsg(res.getString("submitFlag")));
                    vo.setMsg(res.getString("submitMsg"));
                    if (LzCallbackEnum.PROCESSING.getMsg().equals(vo.getStatus()) || LzCallbackEnum.SUCCESS.getMsg().equals(vo.getStatus())) {
                        settlementProofService.addProofRecord(vo, loanApplication.getApplicationId());
                    }
                } else {
                    vo.setMsg(result);
                    vo.setStatus(LzCallbackEnum.FAIL.getMsg());
                }
                return getResponse(vo);
            } else if (loanApplication.getPartnerCode().contains(ParamConstant.LHBANK)){
                SettlementProofVO vo = new SettlementProofVO();
                vo.setPartnerCode(ParamConstant.LHBANK);
                Map<String, Object> params = new HashMap<>();
                params.put("applicationId", reqDTO.getApplicationId());
                String result = HttpClientUtil.post(lhBankUrl, params);
                log.info("申请蓝海银行结清证明返回结果:{}", JSON.toJSONString(result));
                if (StringUtils.isNotBlank(result)){
                    Response response = JSON.parseObject(result, Response.class);
                    if (Response.isSuccess(response)){
                        vo.setStatus(LzCallbackEnum.SUCCESS.getMsg());
                        vo.setMsg("申请蓝海结清证明成功");
                        settlementProofService.addProofRecord(vo, loanApplication.getApplicationId());
                    } else {
                        log.warn("申请蓝海结清证明失败,失败原因:{}", JSON.toJSONString(response));
                        vo.setMsg("申请蓝海结清证明失败");
                        vo.setStatus(LzCallbackEnum.FAIL.getMsg());
                    }
                }
                return getResponse(vo);
            } else if (loanApplication.getPartnerCode().contains(ParamConstant.OPPO)){
                SettlementProofVO vo = settlementProofService.getOppoSettlement(reqDTO.getApplicationId());
                return getResponse(vo);
            } else if (loanApplication.getPartnerCode().contains(ParamConstant.SDTRUST)){
                SettlementProofVO vo = settlementProofService.getSettlement(reqDTO.getApplicationId(),
                    loanApplication.getPartnerCode(), ParamConstant.SDTRUST);
                return getResponse(vo);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            if (e.getMessage().contains("请不要重复提交")) {
                return new Response(ResultCodeEnum.REPEAT_SUBMITTED.getCode(), ResultCodeEnum.REPEAT_SUBMITTED.getMsg(), "");
            } else {
                return new Response(ResponsCodeTypeEnum.FAILURE.getCode(), "申请结清证明文件异常", "");
            }
        }
        return new Response(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), "贷款号不存在!", "");
    }

    private Response<SettlementProofVO> getResponse(SettlementProofVO vo) {
        if (LzCallbackEnum.SUCCESS.getMsg().equals(vo.getStatus())){
            return Response.success(vo);
        } else {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(),vo.getMsg(),vo);
        }
    }

    /**
     * 获取申请记录
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.SettlementProof.V1_SETTLEMENTPROOF_DETAIL)
    @ApiOperation(value = Urls.SettlementProof.V1_SETTLEMENTPROOF_DETAIL_DESC, notes = Urls.SettlementProof.V1_SETTLEMENTPROOF_DETAIL_DESC)
    public Response<Page<SettleProofApplyRecordVO>> detail(@Validated @RequestBody SettlementProofReqDTO reqDTO) {
        Response<Page<SettleProofApplyRecordVO>> response = new Response<Page<SettleProofApplyRecordVO>>();
        Page<SettleProofApplyRecordVO> result = settlementProofService.queryProofRecord(reqDTO);
        response.setResult(result);
        return response;
    }


    @PostMapping(Urls.SettlementProof.V1_SETTLEMENTPROOF_DOWNLOAD_URL)
    @ApiOperation(value = Urls.SettlementProof.V1_SETTLEMENTPROOF_DOWNLOAD_URL_DESC, notes = Urls.SettlementProof.V1_SETTLEMENTPROOF_DOWNLOAD_URL_DESC)
    public Response<String> getDownloadUrl(@RequestParam Long id) {
        return Response.success(settlementProofService.queryDownloadUrl(id));
    }
}
