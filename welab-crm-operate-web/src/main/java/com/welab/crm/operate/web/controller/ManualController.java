package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.domain.CallInStatisticsReport;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.dto.telemarketing.TmkTransformReportReqDTO;
import com.welab.crm.operate.mapper.CallInStatisticsReportMapper;
import com.welab.crm.operate.service.IvrReportService;
import com.welab.crm.operate.service.TmkTaskService;
import com.welab.crm.operate.util.DateUtils;
import com.welab.crm.operate.vo.ivr.IvrStatisticsArtificialVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsReportVO;
import com.welab.crm.operate.vo.telemarketing.TmkTransformReportVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static com.welab.crm.operate.constant.TmkTaskTypeConstant.TMK_TYPE_LIST;

/**
 * <AUTHOR>
 */
@RestController
@Api(description = "手动触发接口")
@RequestMapping(Urls.Manual.ROOT)
@Slf4j
public class ManualController {

    @Resource
    private IvrReportService ivrReportService;

    @Resource
    private CallInStatisticsReportMapper callInStatisticsReportMapper;


    @Resource
    private TmkTaskService tmkTaskService;




    @ApiOperation(value = Urls.Manual.IVR_JOB_DESC, notes = Urls.Manual.IVR_JOB_DESC)
    @GetMapping(Urls.Manual.IVR_JOB)
    public Response<Void> ivrJobManual(@RequestParam String countDate) {

        log.info("开始执行 IvrRateCalJob");

        // 查询IVR分流率
        IvrReportReqDTO reqDTO = new IvrReportReqDTO();
        reqDTO.setHotline("10100518,4006000799,4006040888,4000666730");
        reqDTO.setPeriod("range");
        Date startDate = DateUtil.stringToDate(countDate, "yyyy-MM-dd");
        reqDTO.setStartTime(startDate);
        reqDTO.setEndTime(DateUtil.plusDays(startDate, 1));
        IvrStatisticsReportVO reportVo = ivrReportService.ivrShuntStatisticsReport(reqDTO);
        if (Objects.isNull(reportVo)) {
            return new Response<>();
        }
        // 分流率map
        Map<String, IvrStatisticsArtificialVO> shuntRateMap = reportVo.getShuntRateMap();
        // 总按键数map
        Map<String, IvrStatisticsArtificialVO> totalMap = reportVo.getTotalMap();
        // 转人工数map
        Map<String, IvrStatisticsArtificialVO> artificialMap = reportVo.getArtificialMap();

        if (MapUtils.isEmpty(shuntRateMap) || MapUtils.isEmpty(totalMap) || MapUtils.isEmpty(artificialMap)) {
            return new Response<>();
        }
        IvrStatisticsArtificialVO shuntRateVO = shuntRateMap.get("统计");
        IvrStatisticsArtificialVO totalVO = totalMap.get("统计");
        IvrStatisticsArtificialVO artificialVO = artificialMap.get("统计");

        if (Objects.isNull(shuntRateVO) || Objects.isNull(totalVO) || Objects.isNull(artificialVO)) {
            return new Response<>();
        }
        // 总的分流率
        String shuntRate = shuntRateVO.getTotal();
        String totalNum = totalVO.getTotal();
        String artificialNum = artificialVO.getTotal();

        // 查询电销转化量
        Integer tmkTransformCount = queryTmkTransformCount();

        CallInStatisticsReport callInStatisticsReport = callInStatisticsReportMapper.selectOne(Wrappers.lambdaQuery(CallInStatisticsReport.class)
                .eq(CallInStatisticsReport::getCountDay, startDate)
                .eq(CallInStatisticsReport::getHotline, "10100518"));

        if (Objects.isNull(callInStatisticsReport)){
            return new Response<>();
        }




        callInStatisticsReport.setIvrShuntRate(shuntRate);
        callInStatisticsReport.setIvrTotalNum(Integer.parseInt(totalNum));
        callInStatisticsReport.setIvrArtNum(Integer.parseInt(artificialNum));
        callInStatisticsReport.setTmkTransformNum(tmkTransformCount);
        callInStatisticsReportMapper.updateById(callInStatisticsReport);

        log.info("结束执行 IvrRateCalJob");
        return Response.success();
    }


    private Integer queryTmkTransformCount() {
        Integer tmkTransformCount = 0;
        TmkTransformReportReqDTO dto = new TmkTransformReportReqDTO();
        dto.setStartTime(DateUtil.dateToString(DateUtils.getStartOfYesterday()));
        dto.setEndTime(DateUtil.dateToString(DateUtils.getStartOfToday()));
        for (String tmkType : TMK_TYPE_LIST) {
            dto.setTmkType(tmkType);
            List<TmkTransformReportVO> transformList = tmkTaskService.queryTmkTransformData(dto);
            if (CollectionUtils.isNotEmpty(transformList)){
                for (TmkTransformReportVO transformReportVO : transformList) {
                    tmkTransformCount += Optional.ofNullable(transformReportVO.getTransformCount()).orElse(0);
                    tmkTransformCount += Optional.ofNullable(transformReportVO.getHisTransformCount()).orElse(0);
                }
            }
        }
        return tmkTransformCount;
    }
    
   
}

