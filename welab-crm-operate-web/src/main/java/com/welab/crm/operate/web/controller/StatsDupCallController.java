package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.report.ReportDupCallDTO;
import com.welab.crm.operate.service.StatsDupCallService;
import com.welab.crm.operate.vo.woReport.ReportDupCallDetailVO;
import com.welab.crm.operate.vo.woReport.ReportDupCallTotalVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.util.List;

/**
 * 客户重复来电报表controller
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/3/11
 */
@RestController
@Api(description = "客户重复来电报表")
@RequestMapping(Urls.Duplication.ROOT)
public class StatsDupCallController extends BaseController {

    @Resource
    private StatsDupCallService callService;

    /**
     * 查询客户重复来电报表的总量统计数据
     */
    @GetMapping(value = Urls.Duplication.V1_TOTAL_QUERY)
    @ApiOperation(value = Urls.Duplication.V1_TOTAL_QUERY_DESC, notes = Urls.Duplication.V1_TOTAL_QUERY_DESC)
    public Response<Page<ReportDupCallTotalVO>> getDupTotalList(@BeanParam @Validated ReportDupCallDTO callDTO) {
        Page<ReportDupCallTotalVO> callPage = callService.listTotalByPage(callDTO);
        return Response.success(callPage);
    }

    /**
     * 导出客户重复来电报表的总量统计数据
     */
    @PostMapping(value = Urls.Duplication.V1_TOTAL_EXPORT)
    @ApiOperation(value = Urls.Duplication.V1_TOTAL_EXPORT_DESC, notes = Urls.Duplication.V1_TOTAL_EXPORT_DESC)
    public void exportDupTotalList(@RequestBody @Validated ReportDupCallDTO callDTO) {
        List<ReportDupCallTotalVO> callList = callService.listTotal(callDTO);
        ExcelUtil.listToExcelAndExport(callList, ReportDupCallTotalVO.class, baseResponse);
    }

    /**
     * 查询客户重复来电报表的明细数据
     */
    @GetMapping(value = Urls.Duplication.V1_DETAIL_QUERY)
    @ApiOperation(value = Urls.Duplication.V1_DETAIL_QUERY_DESC, notes = Urls.Duplication.V1_DETAIL_QUERY_DESC)
    public Response<Page<ReportDupCallDetailVO>> getDupDetailList(@BeanParam @Validated ReportDupCallDTO callDTO) {
        Page<ReportDupCallDetailVO> callPage = callService.listDetailByPage(callDTO);
        return Response.success(callPage);
    }

    /**
     * 导出客户重复来电报表的明细数据
     */
    @PostMapping(value = Urls.Duplication.V1_DETAIL_EXPORT)
    @ApiOperation(value = Urls.Duplication.V1_DETAIL_EXPORT_DESC, notes = Urls.Duplication.V1_DETAIL_EXPORT_DESC)
    public void exportDupDetailList(@RequestBody @Validated ReportDupCallDTO callDTO) {
        List<ReportDupCallDetailVO> callList = callService.listDetail(callDTO);
        ExcelUtil.listToExcelAndExport(callList, ReportDupCallDetailVO.class, baseResponse);
    }
}
