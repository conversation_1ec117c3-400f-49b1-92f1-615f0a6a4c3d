package com.welab.crm.operate.web.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.blacklist.BatchDictInfoReqDTO;
import com.welab.collection.interview.dto.blacklist.BlackRecordReqDTO;
import com.welab.collection.interview.dto.blacklist.StopReasonDictDTO;
import com.welab.collection.interview.service.IUserCenterService;
import com.welab.collection.interview.vo.CaseContactReq;
import com.welab.collection.interview.vo.CaseContactRes;
import com.welab.collection.interview.vo.blacklist.BlackRecordVO;
import com.welab.common.response.Response;
import com.welab.crm.interview.vo.LiaisonVo;
import com.welab.crm.operate.dto.blacklist.BlackDetailReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListAddDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryApprovalReqDTO;
import com.welab.crm.operate.dto.blacklist.BlackListQueryReqDTO;
import com.welab.crm.operate.model.BlackListImportModel;
import com.welab.crm.operate.service.BlackListService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.blacklist.BlackDetailReportVO;
import com.welab.crm.operate.vo.blacklist.BlackListSummaryReportStrVO;
import com.welab.crm.operate.vo.blacklist.KfBlackListApprovalVO;
import com.welab.crm.operate.vo.blacklist.KfBlackListVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

;

/**
 * 客服黑名单接口
 * @module 客服项目
 */
@RestController
@Api(value = "黑名单服务")
@RequestMapping(Urls.BlackList.ROOT)
@Slf4j
public class BlackListController extends BaseController{

    @Resource
    private BlackListService blackListService;

    @Resource
    private IUserCenterService userCenterService;

    @PostMapping(value = Urls.BlackList.QUERY)
    @ApiOperation(value = Urls.BlackList.QUERY_DESC, notes = Urls.BlackList.QUERY_DESC)
    public Response<Page<KfBlackListVO>> queryBlackList(@RequestBody BlackListQueryReqDTO dto) {
        return Response.success(blackListService.queryBlackListPage(dto));
    }

    @PostMapping(value = Urls.BlackList.QUERY_APPROVAL)
    @ApiOperation(value = Urls.BlackList.QUERY_APPROVAL_DESC, notes = Urls.BlackList.QUERY_APPROVAL_DESC)
    public Response<Page<KfBlackListApprovalVO>> queryBlackApprovalList(@RequestBody BlackListQueryApprovalReqDTO dto) {
        return Response.success(blackListService.queryBlackListApprovalPage(dto));
    }

    @PostMapping(value = Urls.BlackList.ADD)
    @ApiOperation(value = Urls.BlackList.ADD_DESC, notes = Urls.BlackList.ADD_DESC)
    public Response<Void> addBlackList(@RequestBody BlackListAddDTO dto) {
        blackListService.addBlackList(dto);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.UPDATE)
    @ApiOperation(value = Urls.BlackList.UPDATE_DESC, notes = Urls.BlackList.UPDATE_DESC)
    public Response<Void> updateBlackList(@RequestBody BlackListAddDTO dto) {
        dto.setMobile(AesUtils.getRealMobile(dto.getMobile()));
        dto.setIdNo(AesUtils.getRealIdNo(dto.getIdNo()));
        blackListService.updateBlackList(dto);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.DELETE)
    @ApiOperation(value = Urls.BlackList.DELETE_DESC, notes = Urls.BlackList.DELETE_DESC)
    public Response<Void> deleteBlackList(@RequestBody BatchDictInfoReqDTO dto) {
        blackListService.deleteBlackList(dto);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.TEMPLATE)
    @ApiOperation(value = Urls.BlackList.TEMPLATE_DESC, notes = Urls.BlackList.TEMPLATE_DESC)
    public Response<Void> exportTemplate() throws IOException {
        EasyExcelUtils.export(baseResponse,null, BlackListImportModel.class,"停催名单导入模板","停催名单导入模板");
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.IMPORT)
    @ApiOperation(value = Urls.BlackList.IMPORT_DESC, notes = Urls.BlackList.IMPORT_DESC)
    public Response<Void> importBlackList(@RequestPart MultipartFile file) throws IOException {
        List<List<String>> cellList = EasyExcelUtils.syncRead(file.getInputStream(), 0, 1);
        blackListService.importBlackList(cellList);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.EXPORT)
    @ApiOperation(value = Urls.BlackList.EXPORT_DESC, notes = Urls.BlackList.EXPORT_DESC)
    public Response<Void> exportBlackList(@RequestBody BlackListQueryReqDTO dto) throws IOException {
        dto.setCurPage(1);
        dto.setPageSize(500000);
        EasyExcelUtils.export(baseResponse,blackListService.queryBlackListPage(dto).getRecords(), KfBlackListVO.class,"停催名单","停催名单");
        return Response.success();
    }

    @GetMapping(value = Urls.BlackList.CONTACT)
    @ApiOperation(value = Urls.BlackList.CONTACT_DESC, notes = Urls.BlackList.CONTACT_DESC)
    public Response<List<LiaisonVo>> queryContactList(@RequestParam Integer userId){
        CaseContactReq dto = new CaseContactReq();
        dto.setUserId(String.valueOf(userId));
        List<CaseContactRes> contact = userCenterService.queryContactInfo(dto);
        if (CollectionUtils.isEmpty(contact)) {
            return Response.success(blackListService.getLiaisonByUserId(userId));
        }
        List<LiaisonVo> result = contact.stream().map(t -> {
            LiaisonVo vo = new LiaisonVo();
            vo.setName(t.getContactName());
            vo.setMobile(t.getTelNo());
            vo.setRelation(t.getRelationshipName());
            return vo;
        }).collect(Collectors.toList());
        
        return Response.success(result);
    }


    @PostMapping(value = Urls.BlackList.OPERATE_RECORD)
    @ApiOperation(value = Urls.BlackList.OPERATE_RECORD_DESC, notes = Urls.BlackList.OPERATE_RECORD_DESC)
    public Response<Page<BlackRecordVO>> queryRecord(@RequestBody BlackRecordReqDTO reqDTO){
        return Response.success(blackListService.getRecord(reqDTO));
    }

    @PostMapping(value = Urls.BlackList.SYNC_DICT)
    @ApiOperation(value = Urls.BlackList.SYNC_DICT_DESC, notes = Urls.BlackList.SYNC_DICT_DESC)
    public Response<Void> syncRecord(@RequestBody List<StopReasonDictDTO> list){
        blackListService.syncDict(list);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.DETAIL_REPORT)
    @ApiOperation(value = Urls.BlackList.DETAIL_REPORT_DESC, notes = Urls.BlackList.DETAIL_REPORT_DESC)
    public Response<Page<BlackDetailReportVO>> queryDetailReport(@RequestBody BlackDetailReqDTO dto){
        return Response.success(blackListService.queryBlackDetailReport(dto));
    }

    @PostMapping(value = Urls.BlackList.DETAIL_REPORT_EXPORT)
    @ApiOperation(value = Urls.BlackList.DETAIL_REPORT_EXPORT_DESC, notes = Urls.BlackList.DETAIL_REPORT_EXPORT_DESC)
    public Response<Void> exportDetailReport(@RequestBody BlackDetailReqDTO dto) throws IOException {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(500000);
        EasyExcelUtils.export(baseResponse,blackListService.queryBlackDetailReport(dto).getRecords(),BlackDetailReportVO.class,"黑名单明细报表","黑名单明细报表");
        return Response.success();
    }

    @PostMapping(value = Urls.BlackList.SUMMARY_REPORT)
    @ApiOperation(value = Urls.BlackList.SUMMARY_REPORT_DESC, notes = Urls.BlackList.SUMMARY_REPORT_DESC)
    public Response<List<BlackListSummaryReportStrVO>> querySummaryReport(@RequestBody BlackDetailReqDTO dto){
        return Response.success(blackListService.queryBlackListSummary(dto));
    }

    @GetMapping(value = Urls.BlackList.DETAIL)
    @ApiOperation(value = Urls.BlackList.DETAIL_DESC, notes = Urls.BlackList.DETAIL_DESC)
    public Response<KfBlackListVO> queryDetail(@RequestParam Long id){
        return Response.success(blackListService.queryBlackDetail(id));
    }

}
