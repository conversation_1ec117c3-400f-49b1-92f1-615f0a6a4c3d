package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.domain.InAuthCrmStaff;
import com.welab.crm.operate.domain.InPhoneLoginInfo;
import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.mapper.InAuthCrmStaffMapper;
import com.welab.crm.operate.mapper.InPhoneLoginInfoMapper;
import com.welab.crm.operate.service.ICrmOrgService;
import com.welab.crm.operate.service.ICrmOrgStaffService;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @description 员工配置管理
 * @date 2021-11-09 17:35:43
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.Staff.V1_STAFF)
@Api(value = "ColStaffController", description = "员工配置Controller")
public class CrmStaffController {

    @Autowired
    private ICrmOrgStaffService crmOrgStaffService;
    
    @Resource
    private ICrmOrgService crmOrgService;

    @Autowired
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Resource
    private InPhoneLoginInfoMapper inPhoneLoginInfoMapper;

    /**
     * 查询员工配置
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_QUERY)
    @ApiOperation(value = Urls.Staff.V1_STAFF_QUERY, notes = Urls.Staff.V1_STAFF_QUERY_DESC)
    public Response<List<ColStaffResVO>> query(@Validated @RequestBody ColStaffReqDTO reqDTO) {
        List<ColStaffResVO> colStaffList = crmOrgStaffService.getColStaffList(reqDTO);
        return Response.success(colStaffList);
    }

    /**
     * 添加员工配置
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_ADD)
    @ApiOperation(value = Urls.Staff.V1_STAFF_ADD, notes = Urls.Staff.V1_STAFF_ADD_DESC)
    public Response<?> addOrg(@Validated @RequestBody ColStaffReqDTO reqDTO) {
        crmOrgStaffService.addColStaff(reqDTO);
        return Response.success();
    }

    /**
     * 根据id删除员工
     * @param id
     * @return
     */
    @GetMapping(Urls.Staff.V1_STAFF_DEL_BY_ID)
    @ApiOperation(value = Urls.Staff.V1_STAFF_DEL_BY_ID, notes = Urls.Staff.V1_STAFF_DEL_BY_ID_DESC)
    public Response<?> deleteStaffById(@PathVariable Long id) {
        InAuthCrmStaff staff = inAuthCrmStaffMapper.selectById(id);
        if (Objects.nonNull(staff)) {
            inAuthCrmStaffMapper.deleteById(id);
            LambdaQueryWrapper<InPhoneLoginInfo> wrapper = Wrappers.<InPhoneLoginInfo>lambdaQuery().eq(InPhoneLoginInfo::getUserTel, staff.getLoginName());
            inPhoneLoginInfoMapper.delete(wrapper);
        }
        return Response.success();
    }

    /**
     * 删除员工配置
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_DEL)
    @ApiOperation(value = Urls.Staff.V1_STAFF_DEL, notes = Urls.Staff.V1_STAFF_DEL_DESC)
    public Response<?> deleteOrg(@Validated @RequestBody BatchDictInfoReqDTO reqDTO) {
        crmOrgStaffService.deleteColStaff(reqDTO);
        return Response.success();
    }

    /**
     * 更新员工配置
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_UPDATE)
    @ApiOperation(value = Urls.Staff.V1_STAFF_UPDATE, notes = Urls.Staff.V1_STAFF_UPDATE_DESC)
    public Response<?> updateOrgStaff(@Validated @RequestBody ColStaffReqDTO reqDTO) {
        crmOrgStaffService.updateColOrgStaff(reqDTO);
        return Response.success();
    }

    /**
     * 批量更新员工配置
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_UPDATES)
    @ApiOperation(value = Urls.Staff.V1_STAFF_UPDATES, notes = Urls.Staff.V1_STAFF_UPDATES_DESC)
    public Response<?> updateOrgStaffs(@Validated @RequestBody ColStaffReqDTO reqDTO) {
        crmOrgStaffService.updateColOrgStaff(reqDTO.getLists());
        return Response.success();
    }

    /**
     * 分页获取员工数据
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.Staff.V1_STAFF_QUERY_PAGE)
    @ApiOperation(value = Urls.Staff.V1_STAFF_QUERY_PAGE, notes = Urls.Staff.V1_STAFF_QUERY_PAGE_DESC)
    public Response<?> getStaffByPage(@Validated @RequestBody ColStaffReqDTO reqDTO) {
        Page<ColStaffResVO> staffsByPage = crmOrgStaffService.getStaffsByPage(reqDTO);
        return Response.success(staffsByPage);
    }

    @GetMapping(Urls.Staff.V1_SYNC_STAFF_TO_EXAM_SYSTEM)
    @ApiOperation(value = Urls.Staff.V1_SYNC_STAFF_TO_EXAM_SYSTEM_DESC, notes = Urls.Staff.V1_SYNC_STAFF_TO_EXAM_SYSTEM_DESC)
    public Response<Void> syncStaff() {
        crmOrgService.syncAllOrgToExamSystem();
        crmOrgStaffService.syncAllStaffToExamSystem();
        return Response.success();
    }

}
