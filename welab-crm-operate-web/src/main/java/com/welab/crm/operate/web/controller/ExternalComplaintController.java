package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.dto.dict.DictInfoReqDTO;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintDetailDTO;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintQueryDTO;
import com.welab.crm.operate.dto.externalCompalaint.ReminderDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.service.*;
import com.welab.crm.operate.service.impl.CustomUserDetailsService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.JwtUtil;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;
import com.welab.crm.operate.vo.dict.DictInfoResVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.workorder.*;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.domain.vo.ResponseVo;
import com.welab.exception.FastRuntimeException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;


@RestController
@Api(tags = "外部投诉推送管理")
@RequestMapping(Urls.EXTERNAL_COMPLAINT.ROOT)
@Slf4j
public class ExternalComplaintController extends BaseController {


	@Resource
	private ExternalComplaintService externalComplaintService;

	@Resource
	private ICrmOrgStaffService crmOrgStaffService;


	@Resource
	private AuthenticationManager authenticationManager;
	@Resource
	private JwtUtil jwtUtil;
	@Resource
	private CustomUserDetailsService userDetailsService;

	@Resource
	private ICrmDictInfoService crmDictInfoService;
	
	@Resource
	private CaptchaService captchaService;
	
	@Resource
	private WorkOrderService workOrderService;
	
	@Resource
	private IUploadService uploadService;

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_ADD_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_ADD_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_ADD)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Void> addExternalComplaint(@RequestBody @Validated ExternalComplaintDetailDTO externalComplaintDetailDTO) {
		externalComplaintDetailDTO.setLoginName(SecurityContextHolder.getContext().getAuthentication().getName());
		if (StringUtils.isNotBlank(externalComplaintDetailDTO.getAesName())) {
			externalComplaintDetailDTO.setName(AesUtils.decrypt(externalComplaintDetailDTO.getAesName()));
		}
		externalComplaintService.addExternalComplaint(externalComplaintDetailDTO);
		return Response.success();
	}


	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LIST_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LIST_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LIST)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Page<ExternalComplaintListVO>> list(@RequestBody @Validated ExternalComplaintQueryDTO queryDTO) {
		queryDTO.setLoginName(SecurityContextHolder.getContext().getAuthentication().getName());
		return Response.success(externalComplaintService.queryPage(queryDTO));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_EXPORT_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_EXPORT_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_EXPORT)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Void> export(@RequestBody @Validated ExternalComplaintQueryDTO queryDTO) throws IOException {
		queryDTO.setLoginName(SecurityContextHolder.getContext().getAuthentication().getName());
		CommonUtils.commonExport(baseResponse, "投诉列表", ExternalComplaintListVO.class, externalComplaintService.queryList(queryDTO));
		return Response.success();
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_DETAIL_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_DETAIL_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_DETAIL)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<ExternalComplaintDetailDTO> detail(@PathVariable Long id) {
		return Response.success(externalComplaintService.queryDetail(id, SecurityContextHolder.getContext().getAuthentication().getName()));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_CALL_LIST_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_CALL_LIST_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_CALL_LIST)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<List<OrderContactVO>> callList(@PathVariable Long id) {
		return Response.success(externalComplaintService.queryOrderContactList(id, SecurityContextHolder.getContext().getAuthentication().getName()));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LOG_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LOG_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_COMPLAINT_LOG)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<List<WorkOrderLogVO>> orderLog(@PathVariable Long id) {
		return Response.success(externalComplaintService.queryOrderLogList(id, SecurityContextHolder.getContext().getAuthentication().getName()));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_GET_NAME_BY_ORDER_NO_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_GET_NAME_BY_ORDER_NO_DESC)
	@GetMapping(Urls.EXTERNAL_COMPLAINT.V1_GET_NAME_BY_ORDER_NO)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<ExternalComplaintDetailDTO> getName(@RequestParam(required = false) String channelOrderNo, @RequestParam(required = false) String debtOrderNo) {
		return Response.success(externalComplaintService.queryNameByOrderNo(channelOrderNo, debtOrderNo));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_QUICK_ORDER_QUERY_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_QUICK_ORDER_QUERY_DESC)
	@GetMapping(Urls.EXTERNAL_COMPLAINT.V1_QUICK_ORDER_QUERY)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<List<DictInfoConfResVO>> queryQuickOrder(@RequestParam String userType) {
		return Response.success(externalComplaintService.getQuickOrderOption(userType));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_REMINDER_ORDER_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_REMINDER_ORDER_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_REMINDER_ORDER)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Void> reminderOrder(@RequestBody ReminderDTO reminderDTO) {
		externalComplaintService.reminderOrder(reminderDTO, SecurityContextHolder.getContext().getAuthentication().getName());
		return Response.success();
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_ORDER_UPDATE_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_ORDER_UPDATE_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_ORDER_UPDATE)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Void> updateOrder(@RequestBody ExternalComplaintDetailDTO dto) {
		externalComplaintService.updateOrder(dto, SecurityContextHolder.getContext().getAuthentication().getName());
		return Response.success();
	}


	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_LOGIN_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_LOGIN_DESC)
	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_EXTERNAL_LOGIN)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<AuthResponse> externalLogin(@RequestBody AuthRequest authenticationRequest) throws IOException {
		boolean validateCaptcha = captchaService.validateCaptcha(authenticationRequest.getCaptchaUuid(), authenticationRequest.getCaptchaCode());
		if (!validateCaptcha) {
			throw new BadCredentialsException("验证码错误或已过期");
		}
		
		try {
			authenticationManager.authenticate(
					new UsernamePasswordAuthenticationToken(authenticationRequest.getUsername(), authenticationRequest.getPassword())
			);
		} catch (BadCredentialsException e) {
			throw new FastRuntimeException("Incorrect username or password", e);
		}
		final UserDetails userDetails = userDetailsService.loadUserByUsername(authenticationRequest.getUsername());
		final String jwt = jwtUtil.generateToken(userDetails);
		ColStaffReqDTO reqDTO = new ColStaffReqDTO();
		List<ColStaffResVO> colStaffList = crmOrgStaffService.getColStaffList(reqDTO.setLoginName(userDetails.getUsername()));
		return Response.success(new AuthResponse(jwt, colStaffList));
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_DICTINFO_QUERY_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_DICTINFO_QUERY_DESC)
	@PostMapping(value = Urls.EXTERNAL_COMPLAINT.V1_DICTINFO_QUERY)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<Page<DictInfoResVO>> list(@Validated @RequestBody DictInfoReqDTO req) {
		Response<Page<DictInfoResVO>> response = new Response<Page<DictInfoResVO>>();
		Page<DictInfoResVO> result = crmDictInfoService.getDictInfos(req);
		response.setResult(result);
		return response;
	}

	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_CAPTCHA_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_CAPTCHA_DESC)
	@PostMapping(value = Urls.EXTERNAL_COMPLAINT.V1_CAPTCHA)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 30, times = 10)
	public Response<JSONObject> getCaptcha() {
		// 生成唯一ID用于关联验证码
		String uuid = UUID.randomUUID().toString();

		// 生成验证码图片
		byte[] captchaImage = captchaService.generateCaptchaImage(uuid);

		// 返回结果
		JSONObject result = new JSONObject();
		result.put("uuid", uuid);
		result.put("imageBase64", Base64.getEncoder().encodeToString(captchaImage));

		return Response.success(result);
	}

	@PostMapping(Urls.EXTERNAL_COMPLAINT.V1_UPLOAD)
	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_UPLOAD_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_UPLOAD_DESC)
	@NoRepeatSubmit(key = "externalComplaint", expireSeconds = 60, times = 10)
	public ResponseVo<List<WoAttachmentVO>> saveAttachment(MultipartFile[] file, Long id) {

		String orderNo = externalComplaintService.checkOrderNo(SecurityContextHolder.getContext().getAuthentication().getName(), id);
		try {
			List<MultipartFile> fileList = Arrays.asList(file);
			List<String> fileNameList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(fileList)) {
				for (MultipartFile multipartFile : fileList) {
					String fileName = workOrderService.uploadFile(multipartFile);
					if (StringUtils.isNotBlank(fileName)) {
						fileNameList.add(fileName);
					}
				}
			}
			ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(SecurityContextHolder.getContext().getAuthentication().getName());
			List<WoAttachmentVO> woAttachmentVOList = workOrderService.getDownLoadUrl(fileNameList, queryWoIdByOrderNo(orderNo), staff);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(),
					woAttachmentVOList);
		} catch (Exception e) {
			log.warn("externalComplaint saveAttachment", e);
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), e.getMessage(), null);
		}
	}


	@GetMapping(value = Urls.EXTERNAL_COMPLAINT.V1_GET_FILE_URL)
	@ApiOperation(value = Urls.EXTERNAL_COMPLAINT.V1_GET_FILE_URL_DESC, notes = Urls.EXTERNAL_COMPLAINT.V1_GET_FILE_URL_DESC)
	public Response<String> getFileUrl(@RequestParam String fileName, @RequestParam Long id) {
		String loginName = SecurityContextHolder.getContext().getAuthentication().getName();
		String orderNo = externalComplaintService.checkOrderNo(loginName, id);
		ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(loginName);
		workOrderService.checkExternalOrderFile(orderNo, fileName, staff.getId());
		
		Map<String, Object> uploadFile = uploadService.getUploadFile(Collections.singletonList(fileName));
		return Response.success((String)uploadFile.get(fileName));
	}

	private Long queryWoIdByOrderNo(String orderNo) {
		if (StringUtils.isEmpty(orderNo)) {
			return null;
		}
		WorkOrderInfoVO vo = workOrderService.queryOrderInfoByOrderNo(orderNo);
		if (Objects.nonNull(vo)) {
			return vo.getId();
		}
		return null;
	}


	@Data
	public static class AuthRequest {
		private String username;
		private String password;
		private String captchaCode;
		private String captchaUuid;

	}

	@Data
	static class AuthResponse {
		private String jwt;
		private List<ColStaffResVO> staffList;

		public AuthResponse(String jwt, List<ColStaffResVO> staffList) {
			this.jwt = jwt;
			this.staffList = staffList;
		}
	}


}

