
package com.welab.crm.operate.web.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import javax.servlet.DispatcherType;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ClassName: ParamsFilter <br/> Function: 转义XSS <br/>
 *
 * <AUTHOR>
 */
@Component
@WebFilter(urlPatterns = "/**", filterName = "ParamsFilter", dispatcherTypes = DispatcherType.REQUEST)
@Slf4j
public class ParamsFilter implements Filter {


    private final List<String> EXCLUDE_PATH =
            Arrays.asList("/welab-crm-operate/v1/workOrder/fileUrl");

    FilterConfig filterConfig = null;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterConfig)
            throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest)request;
        String requestUri = httpServletRequest.getRequestURI();
        if (requestUri.contains("external-complaint")){
            filterConfig.doFilter(new XssHttpServletRequestBodyWrapper(
                    (HttpServletRequest) request), response);
        } else if (!EXCLUDE_PATH.contains(requestUri)) {
            filterConfig.doFilter(new XssHttpServletRequestWrapper(
                    (HttpServletRequest) request), response);
        } else {
            filterConfig.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        this.filterConfig = null;

    }


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("初始化过滤器");

        this.filterConfig = filterConfig;


    }


}