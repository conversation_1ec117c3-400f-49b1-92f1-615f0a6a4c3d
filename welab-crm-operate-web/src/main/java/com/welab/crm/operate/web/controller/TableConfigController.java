package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.operate.dto.CustTabConfigDTO;
import com.welab.crm.operate.service.TabConfigService;
import com.welab.crm.operate.web.constants.Urls.TableConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 */
@RestController
@Api(description = "选项卡配置接口")
@RequestMapping(TableConfig.ROOT)
public class TableConfigController {

    @Resource
    private TabConfigService tabConfigService;

    @GetMapping(value = TableConfig.V1_TABLE_TITLE_QUERY)
    @ApiOperation(value = TableConfig.V1_TABLE_TITLE_QUERY_DESC, notes = TableConfig.V1_TABLE_TITLE_QUERY_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "staffId", value="员工编码", paramType = "query"),
        @ApiImplicitParam(name = "tabName", value="表名", paramType = "query")
    })
    public Response<List<String>> query(@RequestParam @NotBlank String staffId, @RequestParam @NotBlank String tabName) {
        return Response.success(tabConfigService.getNotDisplayedColNames(staffId, tabName));
    }

    @PostMapping(value = TableConfig.V1_TABLE_TITLE_UPDATE)
    @ApiOperation(value = TableConfig.V1_TABLE_TITLE_UPDATE_DESC, notes = TableConfig.V1_TABLE_TITLE_UPDATE_DESC)
    public Response<Void> update(@RequestBody @Validated CustTabConfigDTO dto) {
        tabConfigService.updateNotDisplayedColNames(dto.getStaffId(), dto.getTabName(), dto.getColNames());
        return Response.success();
    }
}
