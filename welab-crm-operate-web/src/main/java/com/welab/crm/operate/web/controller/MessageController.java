package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSON;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.interview.dto.message.MessageTemplateAddDTO;
import com.welab.crm.interview.dto.message.MessageTemplateQueryDTO;
import com.welab.crm.interview.dto.message.MessageTemplateUpdateDTO;
import com.welab.crm.interview.service.MessageService;
import com.welab.crm.interview.vo.message.MessageHistoryVO;
import com.welab.crm.interview.vo.message.MessageTemplateVO;
import com.welab.crm.interview.vo.webot.ResponseData;
import com.welab.crm.interview.vo.webot.ResponseVO;
import com.welab.crm.operate.annotation.AesDecrypt;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.message.SmsSendDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.CustomerService;
import com.welab.crm.operate.service.MessageValidateService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.Message;
import com.welab.exception.FastRuntimeException;
import com.welab.validator.Mobile;
import com.welab.xdao.context.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 * @module 客服项目
 */
@RestController
@Api(description = "短信服务")
@RequestMapping(Message.ROOT)
@Slf4j
public class MessageController {

    @Resource
    private MessageService messageService;
    @Resource
    private CustOperateService custOperateService;
    @Resource
    private CustomerService customerService;
    @Resource
    private MessageValidateService validateService;
    @Resource
    private JedisCommands jedisCommands;

    /**
     * redis key前缀
     */
    private static final String REDIS_KEY_PRE = "crm_sms_sendTimes_";

    @PostMapping(value = Message.V1_MESSAGE_SEND)
    @ApiOperation(value = Message.V1_MESSAGE_SEND_DESC, notes = Message.V1_MESSAGE_SEND_DESC)
    @Mobile
    @AesDecrypt
    public Response<String> send(@Validated @RequestBody SmsSendDTO dto,
                                 @GetStaff StaffVO staffVO) {
        String errMsg = validateService.sendMsg(dto, staffVO);
        if (errMsg != null) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), errMsg, null);
        } else {
            return Response.success();
        }
    }

    @GetMapping(value = Message.V1_MESSAGE_HISTORY_QUERY)
    @ApiOperation(value = Message.V1_MESSAGE_HISTORY_QUERY_DESC, notes = Message.V1_MESSAGE_HISTORY_QUERY_DESC)
    public Response<List<MessageHistoryVO>> getMessageHistory(String uuid, String mobile) {
        if (StringUtils.isBlank(uuid) && StringUtils.isBlank(mobile)) {
            return Response.success(Collections.emptyList());
        }
        List<CashCustInfoVO> voList = customerService.queryMobileByUuid(uuid, mobile);
        List<MessageHistoryVO> results = new ArrayList<>();
        if (voList.isEmpty()) {
            return Response.success(results);
        } else {
            for (CashCustInfoVO vo : voList) {
                results.addAll(messageService.getMessageHistory(vo.getMobile(), vo.getId()));
            }
        }
        results = results.stream().distinct().sorted(Comparator.comparing(MessageHistoryVO::getSendTime).reversed()).collect(Collectors.toList());
        maskMobile(results);
        return Response.success(results);
    }

    private void maskMobile(List<MessageHistoryVO> results) {
        if (CollectionUtils.isNotEmpty(results)){
            for (MessageHistoryVO messageHistoryVO : results) {
                messageHistoryVO.setMobile(SecurityUtil.maskMobilePro(messageHistoryVO.getMobile()));
            }
        }
    }

    @PostMapping(value = Message.V1_MESSAGE_TEMPLATE_ADD)
    @ApiOperation(value = Message.V1_MESSAGE_TEMPLATE_ADD_DESC, notes = Message.V1_MESSAGE_TEMPLATE_ADD_DESC)
    public Response<Void> addMessageTemplate(@Validated @RequestBody MessageTemplateAddDTO dto) {
        messageService.addMessageTemplate(dto);
        return Response.success();
    }

    @PostMapping(value = Message.V1_MESSAGE_TEMPLATE_UPDATE)
    @ApiOperation(value = Message.V1_MESSAGE_TEMPLATE_UPDATE_DESC, notes = Message.V1_MESSAGE_TEMPLATE_UPDATE_DESC)
    public Response<Void> updateMessageTemplate(@Validated @RequestBody MessageTemplateUpdateDTO dto) {
        messageService.updateMessageTemplate(dto);
        return Response.success();
    }

    @PostMapping(value = Message.V1_MESSAGE_TEMPLATE_DELETE)
    @ApiOperation(value = Message.V1_MESSAGE_TEMPLATE_DELETE_DESC, notes = Message.V1_MESSAGE_TEMPLATE_DELETE_DESC)
    public Response<Void> deleteMessageTemplate(@PathVariable Long id) {
        messageService.deleteMessageTemplate(id);
        return Response.success();
    }

    @GetMapping(value = Message.V1_MESSAGE_TEMPLATE_QUERY)
    @ApiOperation(value = Message.V1_MESSAGE_TEMPLATE_QUERY_DESC, notes = Message.V1_MESSAGE_TEMPLATE_QUERY_DESC)
    public Response<Page<MessageTemplateVO>> getMessageTemplate(@ModelAttribute MessageTemplateQueryDTO dto) {
        return Response.success(messageService.getMessageTemplateList(dto));
    }

    @GetMapping(value = Message.V1_MESSAGE_TEMPLATE_TOP)
    @ApiOperation(value = Message.V1_MESSAGE_TEMPLATE_TOP_DESC, notes = Message.V1_MESSAGE_TEMPLATE_TOP_DESC)
    public Response<Void> topMessageTemplate(@RequestParam Long id) {
        messageService.topMessageTemplate(id);
        return new Response<>();
    }

    @GetMapping(value = Message.V1_MESSAGE_VALIDATE_QUERY)
    @ApiOperation(value = Message.V1_MESSAGE_VALIDATE_QUERY_DESC, notes = Message.V1_MESSAGE_VALIDATE_QUERY_DESC)
    public Response<ResponseVO<List<ResponseData>>> getValidateRecords(@RequestParam("mobile") String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return Response.success();
        }
        try {
            if (!CommonUtil.isMobile(mobile)) {
                mobile = AesUtils.decrypt(mobile);
            }
            ResponseVO<List<ResponseData>> response = messageService.listFaceValidateRecords(mobile);
            validateService.addGroupToResponse(response);
            return Response.success(response);
        } catch (Exception e) {
            log.error("getValidateRecords error: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "接口调用发生异常", null);
        }
    }

    /**
     * 限制短信发送频率，每个客服每分钟发送限制
     */
    private void limitSendFrequency(String mobile) {
        String redisKey = REDIS_KEY_PRE + mobile;
        String count = jedisCommands.get(redisKey);
        List<OpDictInfo> dict = CommonUtils.getDict("interface_limit_times", "sms");
        int maxCount = Integer.parseInt(dict.get(0).getContent());
        if (StringUtils.isBlank(count)) {
            jedisCommands.set(redisKey, "1");
            jedisCommands.expire(redisKey, 60);
        } else {
            int intCount = Integer.parseInt(count);
            Long ttl = jedisCommands.ttl(redisKey);
            if (-1 == ttl) {
                jedisCommands.del(redisKey);
            } else if (intCount < maxCount) {
                jedisCommands.incr(redisKey);
            } else {
                throw new FastRuntimeException("发送短信频率过高，请稍后再试");
            }
        }

    }
}
