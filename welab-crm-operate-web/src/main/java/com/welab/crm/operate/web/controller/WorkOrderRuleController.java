package com.welab.crm.operate.web.controller;

import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.workorder.OpOrderConfigDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleAdjustReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleReqDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.WorkOrderRuleService;
import com.welab.crm.operate.vo.workorder.OpOrderConfigVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleVO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO;
import com.welab.crm.operate.web.constants.Urls.WoRule;
import com.welab.domain.vo.ResponseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 */
@RestController
@Api(description = "分单规则服务")
@RequestMapping(WoRule.ROOT)
public class WorkOrderRuleController {

	@Resource
	private WorkOrderRuleService workOrderRuleService;

	@PostMapping(WoRule.V1_WO_RULE_QUERY)
	@ApiOperation(value = WoRule.V1_WO_RULE_QUERY, notes = WoRule.V1_WO_RULE_QUERY_DESC)
	public ResponseVo<?> queryWorkOrderRuleList(@Validated @RequestBody WorkOrderRuleReqDTO reqDTO){
		Page<WorkOrderRuleVO> rootList=null;
		try {
			rootList = workOrderRuleService.queryWorkOrderRuleList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}

	@PostMapping(WoRule.V1_WO_RULE_ADD)
	@ApiOperation(value = WoRule.V1_WO_RULE_ADD, notes = WoRule.V1_WO_RULE_ADD_DESC)
	public ResponseVo<?> addWorkOrderRule(@Validated @RequestBody WorkOrderRuleReqDTO reqDTO) {
		try {
			workOrderRuleService.addWorkOrderRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(WoRule.V1_WO_RULE_UPDATE)
	@ApiOperation(value = WoRule.V1_WO_RULE_UPDATE, notes = WoRule.V1_WO_RULE_UPDATE_DESC)
	public ResponseVo<?> updateWorkOrderRule(@RequestBody WorkOrderRuleReqDTO reqDTO) {
		try{
			workOrderRuleService.updateWorkOrderRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(WoRule.V1_WO_RULE_UPDATE_ALL)
	@ApiOperation(value = WoRule.V1_WO_RULE_UPDATE_ALL, notes = WoRule.V1_WO_RULE_UPDATE_ALL_DESC)
	public ResponseVo<?> updateWorkOrderRuleAll(@RequestBody WorkOrderRuleReqDTO reqDTO) {
		try{
			if (Objects.isNull(reqDTO) || StringUtils.isBlank(reqDTO.getRuleType())) {
	            throw new CrmOperateException("规则类型参数不能为空");
	        }
			workOrderRuleService.updateWorkOrderRuleAll(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	/**  
	 * 删除分单规则
	 * @param ids
	 * @return
	 */
	@PostMapping(WoRule.V1_WO_RULE_DELETE)
	@ApiOperation(value = WoRule.V1_WO_RULE_DELETE, notes = WoRule.V1_WO_RULE_DELETE_DESC)
	public ResponseVo<?> deleteWorkOrderRuleList(@Validated @RequestBody BatchInfoReqDTO ids) {
		try {
			workOrderRuleService.deleteWorkOrderRule(ids);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}
	
	@PostMapping(WoRule.V1_WO_RULE_ADJUST_QUERY)
	@ApiOperation(value = WoRule.V1_WO_RULE_ADJUST_QUERY, notes = WoRule.V1_WO_RULE_ADJUST_QUERY_DESC)
	public ResponseVo<?> queryWorkOrderRuleAdjustList(@Validated @RequestBody WorkOrderRuleAdjustReqDTO reqDTO){
		Page<WorkOrderRuleAdjustVO> rootList=null;
		try {
			rootList = workOrderRuleService.queryWorkOrderRuleAdjustList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}
	
	@PostMapping(WoRule.V1_WO_RULE_ADJUST)
	@ApiOperation(value = WoRule.V1_WO_RULE_ADJUST, notes = WoRule.V1_WO_RULE_ADJUST_DESC)
	public ResponseVo<?> adjustWorkOrderRule(@Validated @RequestBody WorkOrderRuleAdjustReqDTO reqDTO,
		@GetStaff StaffVO staffVO){
		try {
			reqDTO.setOperatorId(String.valueOf(staffVO.getId()));
			workOrderRuleService.adjustWorkOrderRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(WoRule.V1_WO_RULE_MANUAL_QUERY)
	@ApiOperation(value = WoRule.V1_WO_RULE_MANUAL_QUERY, notes = WoRule.V1_WO_RULE_MANUAL_QUERY_DESC)
	public ResponseVo<?> queryOrderConfig(){
		OpOrderConfigVO vo=null;
		try {
			vo = workOrderRuleService.queryOrderRuleConfig();
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(vo);
	}
	
	@PostMapping(WoRule.V1_WO_RULE_MANUAL_CONFIG)
	@ApiOperation(value = WoRule.V1_WO_RULE_MANUAL_CONFIG, notes = WoRule.V1_WO_RULE_MANUAL_CONFIG_DESC)
	public ResponseVo<?> updateOrderConfig(@Validated @RequestBody OpOrderConfigDTO reqDTO){
		try {
			workOrderRuleService.updateOrderRuleConfig(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	/**
     * 统计工单数量
     *
     * @return
     */
    @PostMapping(value = WoRule.V1_WO_RULE_TYPETOTAL)
    @ApiOperation(value = WoRule.V1_WO_RULE_TYPETOTAL, notes = WoRule.V1_WO_RULE_TYPETOTAL_DESC)
    public ResponseVo<?> typeTotalWorkOrder() {
    	List<WorkOrderTypeTotalVO> list = null;
		try {
			list = workOrderRuleService.typeTotalWorkOrder();
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<List<WorkOrderTypeTotalVO>>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(list);
    }
}
