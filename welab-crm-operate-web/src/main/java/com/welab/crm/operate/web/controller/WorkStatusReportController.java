package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.report.ReportWorkStatusDTO;
import com.welab.crm.operate.service.WorkStatusReportService;
import com.welab.crm.operate.vo.woReport.ReportCallInWorkStatusVO;
import com.welab.crm.operate.vo.woReport.ReportStaffEfficiencyVO;
import com.welab.crm.operate.vo.woReport.ReportWorkStatusSummaryVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/05/07
 * @module 客服项目
 */
@RestController
@Api(description = "员工状态效能统计服务")
@RequestMapping(Urls.WorkStatusReport.ROOT)
@Slf4j
public class WorkStatusReportController extends BaseController {

    @Resource
    private WorkStatusReportService reportService;

    /**
     * 工作状态统计报表
     */
    @GetMapping(Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY_DESC)
    public Response<Page<ReportWorkStatusSummaryVO>> getWorkStatusSummary(
            @Validated @ModelAttribute ReportWorkStatusDTO dto) {
        return Response.success(reportService.getWorkStatusSummaryPage(dto));
    }

    /**
     * 工作状态统计报表导出
     */
    @PostMapping(Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY_EXPORT)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY_EXPORT_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_SUMMARY_EXPORT_DESC)
    public Response<Void> exportWorkStatusSummary(@Validated @RequestBody ReportWorkStatusDTO dto) {
        long startTime = System.currentTimeMillis();
        List<ReportWorkStatusSummaryVO> list = reportService.listWorkStatusSummary(dto);
        ExcelUtil.listToExcelAndExport(list, ReportWorkStatusSummaryVO.class, baseResponse);
        log.info("exportWorkStatusSummary spend time: {}ms, s:{}, e:{}", System.currentTimeMillis() - startTime,
                dto.getStartTime(), dto.getEndTime());
        return Response.success();
    }

    /**
     * 客服呼入工作状态清单
     */
    @GetMapping(Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN_DESC)
    public Response<Page<ReportCallInWorkStatusVO>> getCallInWorkStatus(
            @Validated @ModelAttribute ReportWorkStatusDTO dto) {
        return Response.success(reportService.getCallInWorkStatus(dto));
    }

    /**
     * 客服呼入工作状态清单导出
     */
    @PostMapping(Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN_EXPORT)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN_EXPORT_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_WORK_STATUS_CALL_IN_EXPORT_DESC)
    public Response<Void> exportCallInWorkStatus(@Validated @RequestBody ReportWorkStatusDTO dto) {
        List<ReportCallInWorkStatusVO> list = reportService.    getCallInWorkStatusList(dto);
        ExcelUtil.listToExcelAndExport(list, ReportCallInWorkStatusVO.class, baseResponse);
        return Response.success();
    }

    /**
     * 员工效能报表
     */
    @GetMapping(Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF_DESC)
    public Response<Page<ReportStaffEfficiencyVO>> getStaffEfficiency(
            @Validated @ModelAttribute ReportWorkStatusDTO dto) {
        return Response.success(reportService.getStaffEfficiencyPage(dto));
    }

    /**
     * 员工效能报表导出
     */
    @PostMapping(Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF_EXPORT)
    @ApiOperation(value = Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF_EXPORT_DESC, notes = Urls.WorkStatusReport.V1_WO_REPORT_EFFICIENCY_STAFF_EXPORT_DESC)
    public Response<Void> exportStaffEfficiency(@Validated @RequestBody ReportWorkStatusDTO dto) {
        long startTime = System.currentTimeMillis();
        List<ReportStaffEfficiencyVO> list = reportService.listStaffEfficiency(dto);
        ExcelUtil.listToExcelAndExport(list, ReportStaffEfficiencyVO.class, baseResponse);
        log.info("exportStaffEfficiency spend time: {}ms, s:{}, e:{}", System.currentTimeMillis() - startTime,
                dto.getStartTime(), dto.getEndTime());
        return Response.success();
    }
}
