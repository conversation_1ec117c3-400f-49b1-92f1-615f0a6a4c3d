package com.welab.crm.operate.web.controller;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.welab.common.response.Response;
import com.welab.crm.operate.service.ScreenService;
import com.welab.crm.operate.vo.personalPanel.PersonalPanelVO;
import com.welab.crm.operate.vo.screen.AgentEfficientVO;
import com.welab.crm.operate.vo.screen.AgentOverview;
import com.welab.crm.operate.vo.screen.CountVO;
import org.springframework.web.bind.annotation.*;

import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(description = "大屏监控接口")
@RequestMapping(Urls.Screen.ROOT)
public class ScreenController {

    @Resource
    private ScreenService screenService;

    @ApiOperation(value = Urls.Screen.V1_AGENT_OVERVIEW_DESC, notes = Urls.Screen.V1_AGENT_OVERVIEW_DESC)
    @GetMapping(Urls.Screen.V1_AGENT_OVERVIEW)
    public Response<AgentOverview> getAgentOverview() {
        return Response.success(screenService.queryAgentWorkOverview());
    }

    @ApiOperation(value = Urls.Screen.V1_TOP_10_QUESTION_DESC, notes = Urls.Screen.V1_TOP_10_QUESTION_DESC)
    @GetMapping(Urls.Screen.V1_TOP_10_QUESTION)
    public Response<List<CountVO>> getTop10Question() {
        return Response.success(screenService.queryTop10Question());
    }

    @ApiOperation(value = Urls.Screen.V1_ZK_OVERVIEW_DESC, notes = Urls.Screen.V1_ZK_OVERVIEW_DESC)
    @GetMapping(Urls.Screen.V1_ZK_OVERVIEW)
    public Response<JSONObject> getZkOverview() {
        return Response.success(screenService.queryZkOverview());
    }

    @ApiOperation(value = Urls.Screen.V1_HOURS_CALL_INFO_DESC, notes = Urls.Screen.V1_HOURS_CALL_INFO_DESC)
    @GetMapping(Urls.Screen.V1_HOURS_CALL_INFO)
    public Response<JSONObject> getHoursCallInfo() {
        return Response.success(screenService.queryHoursCallInfo());
    }

    @ApiOperation(value = Urls.Screen.V1_SERVICE_LEVEL_DESC, notes = Urls.Screen.V1_SERVICE_LEVEL_DESC)
    @GetMapping(Urls.Screen.V1_SERVICE_LEVEL)
    public Response<JSONObject> getServiceLevel() {
        return Response.success(screenService.queryServiceLevel());
    }

    @ApiOperation(value = Urls.Screen.V1_CALL_MAP_DESC, notes = Urls.Screen.V1_CALL_MAP_DESC)
    @GetMapping(Urls.Screen.V1_CALL_MAP)
    public Response<List<CountVO>> getCallMap() {
        return Response.success(screenService.queryCallMap());
    }

    @ApiOperation(value = Urls.Screen.V1_AGENT_EFFICIENT_DESC, notes = Urls.Screen.V1_AGENT_EFFICIENT_DESC)
    @GetMapping(Urls.Screen.V1_AGENT_EFFICIENT)
    public Response<List<AgentEfficientVO>> getAgentEfficientList() {
        return Response.success(screenService.queryAgentEfficient());
    }

    @ApiOperation(value = Urls.Screen.V1_HOURS_REPEAT_DATA_DESC, notes = Urls.Screen.V1_HOURS_REPEAT_DATA_DESC)
    @GetMapping(Urls.Screen.V1_HOURS_REPEAT_DATA)
    public Response<JSONObject> getHoursRepeatData() {
        return Response.success(screenService.queryRepeatData());
    }

    @ApiOperation(value = Urls.Screen.V1_PERSONAL_PANEL_QUERY_DESC, notes = Urls.Screen.V1_PERSONAL_PANEL_QUERY_DESC)
    @GetMapping(Urls.Screen.V1_PERSONAL_PANEL_QUERY)
    public Response<PersonalPanelVO> getPersonalPanel() {
        return Response.success(screenService.queryPersonalPanelVO());
    }
}
