package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO.Common;
import com.welab.crm.operate.dto.ivr.IvrReportReqDTO.Period;
import com.welab.crm.operate.service.impl.IvrReportServiceImpl;
import com.welab.crm.operate.vo.ivr.IvrKeyDetailVO;
import com.welab.crm.operate.vo.ivr.IvrStatisticsReportVO;
import com.welab.crm.operate.web.constants.Urls.Ivr;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0
 * @module 客服项目
 */
@RestController
@Api(description = "ivr按键报表接口")
@RequestMapping(Ivr.ROOT)
@Slf4j
public class IvrReportController extends BaseController{

    @Resource
    private IvrReportServiceImpl ivrReportService;


    @ApiOperation(value = Ivr.V1_KEY_DETAIL_REPORT_DESC, notes = Ivr.V1_KEY_DETAIL_REPORT_DESC)
    @GetMapping(Ivr.V1_KEY_DETAIL_REPORT)
    public Response<Page<IvrKeyDetailVO>> ivrKeyDetailReport(
            @BeanParam @Validated(Common.class) IvrReportReqDTO reqDTO) {
        try {
            reqDTO.setExport(false);
            return Response.success(ivrReportService.ivrKeyDetailReport(reqDTO));
        } catch (Exception e){
            log.error("ivrKeyDetailReport 查询ivr按键明细报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @ApiOperation(value = Ivr.V1_KEY_STATISTICS_REPORT_DESC, notes = Ivr.V1_KEY_STATISTICS_REPORT_DESC)
    @GetMapping(Ivr.V1_KEY_STATISTICS_REPORT)
    public Response<IvrStatisticsReportVO> ivrKeyStatisticsReport(
            @BeanParam @Validated({Common.class, Period.class}) IvrReportReqDTO reqDTO) {
        try {
            return Response.success(ivrReportService.ivrShuntStatisticsReport(reqDTO));
        } catch (Exception e){
            log.error("ivrKeyDetailReport 查询ivr按键统计报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Ivr.V1_KEY_DETAIL_REPORT_EXPORT)
    @ApiOperation(value = Ivr.V1_KEY_DETAIL_REPORT_EXPORT_DESC, notes = Ivr.V1_KEY_DETAIL_REPORT_EXPORT_DESC)
    public Response<Void> ivrKeyDetailReportExport(@BeanParam @Validated(Common.class) IvrReportReqDTO reqDTO) {
        try {
            ivrReportService.exportIvrKeyDetailReport(baseResponse,reqDTO,"IVR按键明细报表");
            return Response.success();
        } catch (Exception e){
            log.error("ivrKeyDetailReportExport 导出ivr按键明细报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Ivr.V1_KEY_STATISTICS_REPORT_EXPORT)
    @ApiOperation(value = Ivr.V1_KEY_STATISTICS_REPORT_EXPORT_DESC, notes = Ivr.V1_KEY_STATISTICS_REPORT_EXPORT_DESC)
    public Response<Void> ivrKeyStatisticsReportExport(@ModelAttribute @Validated({Common.class, Period.class}) IvrReportReqDTO reqDTO) {
        try {
            ivrReportService.exportIvrStatisticsReport(baseResponse,reqDTO,"IVR按键统计报表");
            return Response.success();
        } catch (Exception e){
            log.error("ivrKeyStatisticsReportExport 导出ivr按键统计报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }




}

