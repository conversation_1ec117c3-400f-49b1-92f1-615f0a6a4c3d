package com.welab.crm.operate.web.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.tmkManager.TmkAssignReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerAdjustReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkManager.TotalTmkManagerReqDTO;
import com.welab.crm.operate.service.TmkManagerService;
import com.welab.crm.operate.vo.tmkManager.TmkAssignVO;
import com.welab.crm.operate.vo.tmkManager.TmkManagerVO;
import com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO;
import com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.TmkManager;
import com.welab.domain.vo.ResponseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2022-02-24
 */
@RestController
@Api(description = "电销数据管理服务") 
@RequestMapping(TmkManager.ROOT)
public class TmkManagerController {

	@Resource
	private TmkManagerService tmkManagerService;

	@PostMapping(TmkManager.V1_TMK_MANAGER_QUERY)
	@ApiOperation(value = TmkManager.V1_TMK_MANAGER_QUERY, notes = TmkManager.V1_TMK_MANAGER_QUERY_DESC)
	public ResponseVo<?> queryTmkManagerList(@RequestBody TmkManagerReqDTO reqDTO){
		Page<TmkManagerVO> rootList=null;
		try {
			rootList = tmkManagerService.queryTmkManagerList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}
	
	/**
     * 统计电销管理数量
     *
     * @return
     */
    @PostMapping(value = TmkManager.V1_TMK_MANAGER_TOTAL)
    @ApiOperation(value = TmkManager.V1_TMK_MANAGER_TOTAL, notes = TmkManager.V1_TMK_MANAGER_TOTAL_DESC)
    public ResponseVo<?> totalTmkManager(@RequestBody TmkManagerReqDTO reqDTO) {
    	Page<TotalTmkManagerVO> list = null;
		try {
			list = tmkManagerService.totalTmkManager(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(list);
    }
    
    @PostMapping(TmkManager.V1_TMK_MANAGER_ADJUST)
	@ApiOperation(value = TmkManager.V1_TMK_MANAGER_ADJUST, notes = TmkManager.V1_TMK_MANAGER_ADJUST_DESC)
	public ResponseVo<?> adjustTmkManager(@Validated @RequestBody TmkManagerAdjustReqDTO reqDTO,
		@GetStaff StaffVO staffVO){
		try {
			reqDTO.setOperatorId(String.valueOf(staffVO.getId()));
			reqDTO.setOperatorGroupCode(staffVO.getGroupCode());
			tmkManagerService.adjustTmkManager(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
    
    @PostMapping(TmkManager.V1_TMK_MANAGER_ASSIGN_QUERY)
	@ApiOperation(value = TmkManager.V1_TMK_MANAGER_ASSIGN_QUERY, notes = TmkManager.V1_TMK_MANAGER_ASSIGN_QUERY_DESC)
	public ResponseVo<?> queryTmkAssignList(@RequestBody TmkAssignReqDTO reqDTO){
		Page<TmkAssignVO> rootList=null;
		try {
			rootList = tmkManagerService.queryTmkAssignList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}
}
