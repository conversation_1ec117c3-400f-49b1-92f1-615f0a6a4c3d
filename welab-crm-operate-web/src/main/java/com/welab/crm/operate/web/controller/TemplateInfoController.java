package com.welab.crm.operate.web.controller;

import javax.annotation.Resource;

import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.workorder.TemplateInfoReqDTO;
import com.welab.crm.operate.service.TemplateInfoService;
import com.welab.crm.operate.vo.workorder.TemplateInfoVO;
import com.welab.crm.operate.web.constants.Urls.Template;
import com.welab.domain.vo.ResponseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 */
@RestController
@Api(description = "工单审批模板服务")
@RequestMapping(Template.ROOT)
public class TemplateInfoController {

	@Resource
	private TemplateInfoService templateService;

	@PostMapping(Template.V1_TEMPLATE_QUERY)
	@ApiOperation(value = Template.V1_TEMPLATE_QUERY, notes = Template.V1_TEMPLATE_QUERY_DESC)
	public ResponseVo<?> queryTemplateList(@Validated @RequestBody TemplateInfoReqDTO reqDTO){
		Page<TemplateInfoVO> rootList=null;
		try {
			rootList = templateService.queryTemplateInfoList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}

	@PostMapping(Template.V1_TEMPLATE_ADD)
	@ApiOperation(value = Template.V1_TEMPLATE_ADD, notes = Template.V1_TEMPLATE_ADD_DESC)
	public ResponseVo<?> addTemplate(@Validated @RequestBody TemplateInfoReqDTO reqDTO) {
		try {
			templateService.addTemplateInfo(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(Template.V1_TEMPLATE_UPDATE)
	@ApiOperation(value = Template.V1_TEMPLATE_UPDATE, notes = Template.V1_TEMPLATE_UPDATE_DESC)
	public ResponseVo<?> updateTemplate(@RequestBody TemplateInfoReqDTO reqDTO) {
		try{
			templateService.updateTemplateInfo(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	/**  
	 * 删除工单审批模板
	 * @param reqDTO
	 * @return  
	 */
	@PostMapping(Template.V1_TEMPLATE_DELETE)
	@ApiOperation(value = Template.V1_TEMPLATE_DELETE, notes = Template.V1_TEMPLATE_DELETE_DESC)
	public ResponseVo<?> deleteTemplateList(@Validated @RequestBody BatchInfoReqDTO ids) {
		try {
			templateService.deleteTemplateInfo(ids);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}
}
