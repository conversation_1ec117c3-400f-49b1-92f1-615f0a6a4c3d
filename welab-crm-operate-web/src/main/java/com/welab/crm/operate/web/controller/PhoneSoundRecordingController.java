package com.welab.crm.operate.web.controller;

import com.welab.crm.operate.domain.ConPhoneCallInfo;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.operate.service.IPhoneSoundRecordingService;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.exception.FastRuntimeException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @description 获取录音文件
 * @date 2022/2/22
 * @module 客服项目
 */
@Controller
@RequestMapping(Urls.PhoneSoundRecording.ROOT)
@Api(description = "获取录音文件")
@Slf4j
public class PhoneSoundRecordingController extends BaseController{

    @Autowired
    private IPhoneSoundRecordingService phoneSoundRecordingService;
    
    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    /**
     * 获取录音文件
     * @param reqDTO
     * @return
     */
    @GetMapping(Urls.PhoneSoundRecording.V1_QUERY_FILE)
    @ApiOperation(value = Urls.PhoneSoundRecording.V1_QUERY_FILE, notes = Urls.PhoneSoundRecording.V1_QUERY_FILE_DESC)
    public void query(@Validated @ModelAttribute PhoneSundRecordingDTO reqDTO){
        ServletOutputStream out = null;
        try {
            // 与服务器建立连接
            URL url = new URL(phoneSoundRecordingService.queryRecordFile(reqDTO));
            URLConnection conn = url.openConnection();
            InputStream inputStream = conn.getInputStream();
            try {
                //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
                baseResponse.setContentType("multipart/form-data");
                baseResponse.setHeader("Content-Disposition", "attachment;fileName=" + reqDTO.getRecordFile() + ".mp3");
            } catch (Exception e) {
                e.printStackTrace();
            }
            out = baseResponse.getOutputStream();
            // 读取文件流
            int len = 0;
            byte[] buffer = new byte[1024 * 10];
            while ((len = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //return "redirect:" + phoneSoundRecordingService.queryRecordFile(reqDTO);
    }


    @GetMapping(Urls.PhoneSoundRecording.V1_QUERY_FILE_BY_ID)
    @ApiOperation(value = Urls.PhoneSoundRecording.V1_QUERY_FILE_BY_ID_DESC, notes = Urls.PhoneSoundRecording.V1_QUERY_FILE_BY_ID_DESC)
    @LogSaveToDb
    public void queryFileById(@RequestParam Long id){
        ServletOutputStream out = null;
        ConPhoneCallInfo callInfo = conPhoneCallInfoMapper.selectById(id);
        if (Objects.isNull(callInfo)){
            throw new FastRuntimeException("通话记录不存在");
        }
        try {
            PhoneSundRecordingDTO reqDTO = new PhoneSundRecordingDTO();
            reqDTO.setRecordFile(callInfo.getCdrRecordFile());
            // 与服务器建立连接
            URL url = new URL(phoneSoundRecordingService.queryRecordFile(reqDTO));
            URLConnection conn = url.openConnection();
            InputStream inputStream = conn.getInputStream();
            try {
                //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
                baseResponse.setContentType("multipart/form-data");
                baseResponse.setHeader("Content-Disposition", "attachment;fileName=" + reqDTO.getRecordFile() + ".mp3");
            } catch (Exception e) {
                e.printStackTrace();
            }
            out = baseResponse.getOutputStream();
            // 读取文件流
            int len = 0;
            byte[] buffer = new byte[1024 * 10];
            while ((len = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
        //return "redirect:" + phoneSoundRecordingService.queryRecordFile(reqDTO);
    }
}
