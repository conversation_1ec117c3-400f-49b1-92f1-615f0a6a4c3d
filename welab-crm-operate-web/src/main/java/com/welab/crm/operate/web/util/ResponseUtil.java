package com.welab.crm.operate.web.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
/**
 * @Description: 返回响应工具类
 * <AUTHOR>
 * @date 2020/07/30
 */
public class ResponseUtil {
    public static Response buildExportFileResponse(final String fileName, byte[] content)
	{
		return Response.ok(content, MediaType.APPLICATION_OCTET_STREAM_TYPE)
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName)
				.header(HttpHeaders.CACHE_CONTROL, "no-cache").header(HttpHeaders.CONTENT_LENGTH, content.length)
				.header("X-Filename", fileName).header("Pragma", "no-cache").build();
	}
    public static ResponseEntity<byte[]> buildExportResponse(HSSFWorkbook wb,final String fileName,HttpServletRequest request, HttpServletResponse response)
  	{
    	ByteArrayOutputStream outByteStream = new ByteArrayOutputStream();
    	try {
			wb.write(outByteStream);
    	} catch (IOException e) {
    		e.printStackTrace();
    	}
    	return buildExportResponse(outByteStream.toByteArray(), fileName, request, response);
  	}
    public static ResponseEntity<byte[]> buildExportResponse(byte[] bs,final String fileName,HttpServletRequest request, HttpServletResponse response)
  	{
    	HttpHeaders headers = new HttpHeaders();
    	try {
			headers.setContentDispositionFormData("attachment", new String(fileName.getBytes(), "ISO8859-1"));
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		}
    	headers.add("fileName", fileName);
    	List<String> exposedHeaders = new ArrayList<String>();
    	exposedHeaders.add("Content-Disposition");
    	exposedHeaders.add("fileName");
		headers.setAccessControlExposeHeaders(exposedHeaders);
    	return new ResponseEntity<byte[]>(bs, headers, HttpStatus.OK);
  	}
}
