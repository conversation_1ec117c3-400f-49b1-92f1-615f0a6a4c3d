/**
 * @Title: CrmDictInfoController.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.dict.DictInfoConfReqDTO;
import com.welab.crm.operate.service.ICrmDictInfoConfService;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;
import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021-10-14 14:47:19
 * @version v1.0
 */
@RestController
@Validated
@Api(description = "工单组合配置接口")
@RequestMapping(Urls.DictInfoConf.V1_DICTINFOCONF)
public class CrmDictInfoConfController {
    
    @Autowired
    private ICrmDictInfoConfService crmDictInfoConfService;
    
    @ApiOperation(value = Urls.DictInfoConf.V1_DICTINFOCONF_QUERY, notes = Urls.DictInfoConf.V1_DICTINFOCONF_QUERY_DESC)
    @PostMapping(value = Urls.DictInfoConf.V1_DICTINFOCONF_QUERY)
    public Response<Page<DictInfoConfResVO>> list(@Validated @RequestBody DictInfoConfReqDTO req) {
        Response<Page<DictInfoConfResVO>> response = new Response<Page<DictInfoConfResVO>>();
        Page<DictInfoConfResVO> result = crmDictInfoConfService.getDictInfosConf(req);
        response.setResult(result);
        return response;
    }
    
    @ApiOperation(value = Urls.DictInfoConf.V1_DICTINFOCONF_ADD, notes = Urls.DictInfoConf.V1_DICTINFOCONF_ADD_DESC)
    @PostMapping(value = Urls.DictInfoConf.V1_DICTINFOCONF_ADD)
    public Response<?> add(@Validated @RequestBody DictInfoConfReqDTO req) {
        return Response.success(crmDictInfoConfService.addDictInfoConf(req));
    }
    
    @ApiOperation(value = Urls.DictInfoConf.V1_DICTINFOCONF_UPDATE, notes = Urls.DictInfoConf.V1_DICTINFOCONF_UPDATE_DESC)
    @PostMapping(value = Urls.DictInfoConf.V1_DICTINFOCONF_UPDATE)
    public Response<?> update(@Validated @RequestBody DictInfoConfReqDTO req) {
        return Response.success(crmDictInfoConfService.updateDictInfoConf(req));
    }
    
    @ApiOperation(value = Urls.DictInfoConf.V1_DICTINFOCONF_DEL, notes = Urls.DictInfoConf.V1_DICTINFOCONF_DEL_DESC)
    @PostMapping(value = Urls.DictInfoConf.V1_DICTINFOCONF_DEL)
    public Response<?> delete(@Validated @RequestBody DictInfoConfReqDTO req) {
        return Response.success(crmDictInfoConfService.deleteDictInfoConf(req));
    }
}
