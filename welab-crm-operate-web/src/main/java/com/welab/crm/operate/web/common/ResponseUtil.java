package com.welab.crm.operate.web.common;

import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;

/**
 * 通用响应工具类
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
public class ResponseUtil {

    /**
     * 返回成功响应（无数据）
     *
     * @param <T> 泛型类型
     * @return 成功响应
     */
    public static <T> Response<T> success() {
        return Response.success();
    }

    /**
     * 返回成功响应（带数据）
     *
     * @param data 响应数据
     * @param <T> 泛型类型
     * @return 成功响应
     */
    public static <T> Response<T> success(T data) {
        return Response.success(data);
    }

    /**
     * 返回错误响应
     *
     * @param message 错误信息
     * @param <T> 泛型类型
     * @return 错误响应
     */
    public static <T> Response<T> error(String message) {
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), message, null);
    }

    /**
     * 返回错误响应（指定错误码）
     *
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 泛型类型
     * @return 错误响应
     */
    public static <T> Response<T> error(String code, String message) {
        return new Response<>(code, message, null);
    }

    /**
     * 返回错误响应（使用枚举）
     *
     * @param codeEnum 错误码枚举
     * @param <T> 泛型类型
     * @return 错误响应
     */
    public static <T> Response<T> error(ResponsCodeTypeEnum codeEnum) {
        return new Response<>(codeEnum.getCode());
    }

    /**
     * 返回错误响应（使用枚举和自定义消息）
     *
     * @param codeEnum 错误码枚举
     * @param message 自定义错误信息
     * @param <T> 泛型类型
     * @return 错误响应
     */
    public static <T> Response<T> error(ResponsCodeTypeEnum codeEnum, String message) {
        return new Response<>(codeEnum.getCode(), message, null);
    }

    /**
     * 返回参数错误响应
     *
     * @param message 错误信息
     * @param <T> 泛型类型
     * @return 参数错误响应
     */
    public static <T> Response<T> paramError(String message) {
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), message, null);
    }

    /**
     * 返回业务错误响应
     *
     * @param message 错误信息
     * @param <T> 泛型类型
     * @return 业务错误响应
     */
    public static <T> Response<T> businessError(String message) {
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), message, null);
    }

    /**
     * 返回系统错误响应
     *
     * @param message 错误信息
     * @param <T> 泛型类型
     * @return 系统错误响应
     */
    public static <T> Response<T> systemError(String message) {
        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), message, null);
    }

    /**
     * 根据条件返回响应
     *
     * @param condition 条件
     * @param data 成功时的数据
     * @param errorMessage 失败时的错误信息
     * @param <T> 泛型类型
     * @return 响应结果
     */
    public static <T> Response<T> result(boolean condition, T data, String errorMessage) {
        if (condition) {
            return success(data);
        } else {
            return error(errorMessage);
        }
    }

    /**
     * 根据条件返回响应（无数据版本）
     *
     * @param condition 条件
     * @param errorMessage 失败时的错误信息
     * @param <T> 泛型类型
     * @return 响应结果
     */
    public static <T> Response<T> result(boolean condition, String errorMessage) {
        if (condition) {
            return success();
        } else {
            return error(errorMessage);
        }
    }

    /**
     * 返回分页成功响应
     *
     * @param page 分页数据
     * @param <T> 泛型类型
     * @return 分页响应结果
     */
    public static <T> Response<com.baomidou.mybatisplus.extension.plugins.pagination.Page<T>> successPage(
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return success(page);
    }

}
