package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.dto.report.ReportPhoneSummaryDTO;
import com.welab.crm.operate.service.IReportPhoneService;
import com.welab.crm.operate.vo.phone.ReportPhoneSummaryItem;
import com.welab.crm.operate.vo.phone.ReportPhoneSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/21
 */
@RestController
@RequestMapping(Urls.ReportPhone.ROOT)
@Api(description = "来电报表服务")
public class ReportPhoneController extends BaseController{

    @Autowired
    private IReportPhoneService reportPhoneService;

    /**
     * 查询来电明细
     * @return
     */
    @GetMapping(Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL)
    @ApiOperation(value = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL_DESC, notes = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL_DESC)
    public Response<Page<ReportPhoneVO>> queryDetail(@Validated @ModelAttribute ReportPhoneResultDTO dto){
        Response<Page<ReportPhoneVO>> response = new Response<Page<ReportPhoneVO>>();
        Page<ReportPhoneVO> result = reportPhoneService.queryDetai(dto);
        response.setResult(result);
        return response;
    }

    /**
     * 导出查询来电明细
     * @return
     */
    @GetMapping(Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL_EXCEL)
    @ApiOperation(value = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL_EXCEL_DESC, notes = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DETAIL_EXCEL_DESC)
    public Response<Void> queryDetailExcel(@Validated @ModelAttribute ReportPhoneResultDTO dto) {
        List<ReportPhoneVO> list = reportPhoneService.queryDetailExcel(dto);
        ExcelUtil.listToExcelAndExport(list, ReportPhoneVO.class, baseResponse);
        return Response.success();
    }

    /**
     * 查询来电统计
     * @return
     */
    @GetMapping(Urls.ReportPhone.V1_REPORTPHONE_QUERY)
    @ApiOperation(value = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DESC, notes = Urls.ReportPhone.V1_REPORTPHONE_QUERY_DESC)
    public Response<ReportPhoneSummaryVO> query(@Validated @ModelAttribute ReportPhoneSummaryDTO dto) {
        Response<ReportPhoneSummaryVO> response = new Response<ReportPhoneSummaryVO>();
        response.setResult(reportPhoneService.querySummary(dto));
        return response;
    }

    /**
     * 导出查询来电统计
     * @return
     */
    @GetMapping(Urls.ReportPhone.V1_REPORTPHONE_QUERY_EXCEL)
    @ApiOperation(value = Urls.ReportPhone.V1_REPORTPHONE_QUERY_EXCEL_DESC, notes = Urls.ReportPhone.V1_REPORTPHONE_QUERY_EXCEL_DESC)
    public Response<Void> queryExcel(@Validated @ModelAttribute ReportPhoneSummaryDTO dto) {
        ReportPhoneSummaryVO reportPhoneSummaryVO = reportPhoneService.querySummary(dto);
        ExcelUtil.listToExcelAndExport(reportPhoneSummaryVO.getList(), ReportPhoneSummaryItem.class, baseResponse);
        return Response.success();
    }

}
