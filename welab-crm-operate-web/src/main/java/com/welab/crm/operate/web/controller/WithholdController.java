package com.welab.crm.operate.web.controller;

import com.alibaba.druid.wall.WallProvider;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.interview.vo.repay.RepaymentVO;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.withhold.RepaymentExtDTO;
import com.welab.crm.operate.dto.withhold.WithholdReportVO;
import com.welab.crm.operate.dto.withhold.WithholdReqDTO;
import com.welab.crm.operate.mapper.InLenderWithholdRecordMapper;
import com.welab.crm.operate.service.impl.WithholdServiceImpl;
import com.welab.crm.operate.vo.withhold.WithholdVO;
import com.welab.crm.operate.web.constants.Urls.withhold;
import com.welab.finance.repayment.dto.RepayChannelReq;
import com.welab.finance.repayment.vo.RepayChannelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 代扣接口
 * @date 2022/4/24 14:16
 */
@RestController
@RequestMapping(withhold.ROOT)
@Api(value = "WithholdController",description = "代扣接口服务")
public class WithholdController extends BaseController{

    @Resource
    private WithholdServiceImpl withholdService;

    @Resource
    InLenderWithholdRecordMapper inLenderWithholdRecordMapper;

    @PostMapping(withhold.V1_WITHHOLD_QUERY)
    @ApiOperation(value = withhold.V1_WITHHOLD_QUERY_DESC, notes = withhold.V1_WITHHOLD_QUERY_DESC)
    public Response<Page<WithholdVO>> queryWithholdList(@RequestBody WithholdReqDTO dto){
        Page<WithholdVO> withholdVOS = withholdService.queryWithholdRecord(dto);
        return Response.success(withholdVOS);
    }

    @GetMapping(withhold.V1_WITHHOLD_GROUPS_QUERY)
    @ApiOperation(value = withhold.V1_WITHHOLD_GROUPS_QUERY_DESC, notes = withhold.V1_WITHHOLD_GROUPS_QUERY_DESC)
    public Response<List<String>> queryWithholdListByGroups(){
        return Response.success(inLenderWithholdRecordMapper.queryWithholdRecordByConditionGroups());
    }

    @GetMapping(withhold.V1_WITHHOLD_QUERY_EXPORT)
    @ApiOperation(value = withhold.V1_WITHHOLD_QUERY_EXPORT_DESC, notes = withhold.V1_WITHHOLD_QUERY_EXPORT_DESC)
    public Response<Void> exportWithholdList(@BeanParam WithholdReqDTO dto){
        dto.setCurPage(1);
        dto.setPageSize(100000);
        Page<WithholdVO> withholdVOS = withholdService.queryWithholdRecord(dto);
        ExcelUtil.listToExcelAndExport(withholdVOS.getRecords(), WithholdVO.class, baseResponse);
        return Response.success();
    }

    @PostMapping(withhold.V1_WITHHOLD_ADD)
    @ApiOperation(value = withhold.V1_WITHHOLD_ADD_DESC, notes = withhold.V1_WITHHOLD_ADD_DESC)
    public Response<RepaymentVO> addWithholdRecord(@RequestBody @Validated RepaymentExtDTO dto){
        return withholdService.repayment(dto);
    }

    @PostMapping(withhold.V1_WITHHOLD_AMOUNT)
    @ApiOperation(value = withhold.V1_WITHHOLD_AMOUNT_DESC, notes = withhold.V1_WITHHOLD_AMOUNT_DESC)
    public Response<BigDecimal> getWithholdAmount(@RequestBody WithholdReqDTO dto){
        return Response.success(withholdService.getRepayAmountByMode(dto));
    }

    @GetMapping(withhold.V1_WITHHOLD_REPORT)
    @ApiOperation(value = withhold.V1_WITHHOLD_REPORT_DESC, notes = withhold.V1_WITHHOLD_REPORT_DESC)
    public Response<List<WithholdReportVO>> withholdReport(@BeanParam ReportBaseDTO dto){
        return Response.success(withholdService.queryWithholdReport(dto));
    }

    @GetMapping(withhold.V1_WITHHOLD_REPORT_EXPORT)
    @ApiOperation(value = withhold.V1_WITHHOLD_REPORT_EXPORT_DESC, notes = withhold.V1_WITHHOLD_REPORT_EXPORT_DESC)
    public Response<Void> withholdReportExport(@BeanParam ReportBaseDTO dto){
        ExcelUtil.listToExcelAndExport(withholdService.queryWithholdReport(dto), WithholdReportVO.class, baseResponse);
        return Response.success();
    }

    @GetMapping(withhold.V1_GET_REPAY_MODE)
    @ApiOperation(value = withhold.V1_GET_REPAY_MODE_DESC, notes = withhold.V1_GET_REPAY_MODE_DESC)
    public Response<List<String>> queryRepayModel(@RequestParam String appNo){
        return Response.success(withholdService.queryRepaymentModeByAppNo(appNo));
    }

    @PostMapping(withhold.V1_REPAY_CHANNEL_QUERY)
    @ApiOperation(value = withhold.V1_REPAY_CHANNEL_QUERY_DESC, notes = withhold.V1_REPAY_CHANNEL_QUERY_DESC)
    public Response<RepayChannelVO> queryRepayChannel(@RequestBody RepayChannelReq repayChannelReq) {
        return Response.success(withholdService.getRepayChannel(repayChannelReq));
    }
}
