/**
 * @Title: CrmDictInfoController.java
 * @Copyright: © 2021 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */

package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.dict.CallSummaryReqDTO;
import com.welab.crm.operate.dto.dict.CallSummaryRespDTO;
import com.welab.crm.operate.dto.dict.DictInfoReqDTO;
import com.welab.crm.operate.service.ICrmDictInfoService;
import com.welab.crm.operate.vo.dict.DictInfoResVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-10-14 14:47:19
 * @version v1.0
 */
@RestController
@Validated
@Api(description = "字典配置接口")
@RequestMapping(Urls.DictInfo.V1_DICTINFO)
public class CrmDictInfoController {
    
    @Autowired
    private ICrmDictInfoService crmDictInfoService;
    
    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_QUERY, notes = Urls.DictInfo.V1_DICTINFO_QUERY_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_QUERY)
    public Response<Page<DictInfoResVO>> list(@Validated @RequestBody DictInfoReqDTO req) {
        Response<Page<DictInfoResVO>> response = new Response<Page<DictInfoResVO>>();
        Page<DictInfoResVO> result = crmDictInfoService.getDictInfos(req);
        response.setResult(result);
        return response;
    }
    
    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_ADD, notes = Urls.DictInfo.V1_DICTINFO_ADD_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_ADD)
    public Response<?> add(@Validated @RequestBody DictInfoReqDTO req) {
        return Response.success(crmDictInfoService.addDictInfo(req));
    }
    
    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_UPDATE, notes = Urls.DictInfo.V1_DICTINFO_UPDATE_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_UPDATE)
    public Response<?> update(@Validated @RequestBody DictInfoReqDTO req) {
        return Response.success(crmDictInfoService.updateDictInfo(req));
    }
    
    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_DEL, notes = Urls.DictInfo.V1_DICTINFO_DEL_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_DEL)
    public Response<?> delete(@Validated @RequestBody DictInfoReqDTO req) {
        return Response.success(crmDictInfoService.deleteDictInfo(req));
    }

    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_QUERY, notes = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_QUERY_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_QUERY)
    public Response<Page<CallSummaryRespDTO>> list(@Validated @RequestBody CallSummaryReqDTO req) {
        Response<Page<CallSummaryRespDTO>> response = new Response<Page<CallSummaryRespDTO>>();
        Page<CallSummaryRespDTO> result = crmDictInfoService.getCallSummarys(req);
        response.setResult(result);
        return response;
    }

    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_ADD, notes = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_ADD_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_ADD)
    public Response<?> add(@Validated @RequestBody CallSummaryReqDTO req) {
        return Response.success(crmDictInfoService.addCallSummary(req));
    }

    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_DEL, notes = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_DEL_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_DEL)
    public Response<?> delete(@Validated @RequestBody CallSummaryReqDTO req) {
        return Response.success(crmDictInfoService.deleteCallSummary(req));
    }

    @ApiOperation(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_TOP, notes = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_TOP_DESC)
    @PostMapping(value = Urls.DictInfo.V1_DICTINFO_CALL_SUMMARY_TOP)
    public Response<?> top(@Validated @RequestBody CallSummaryReqDTO req) {
        return Response.success(crmDictInfoService.topCallSummary(req));
    }
}
