package com.welab.crm.operate.web.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.welab.common.response.Response;
import com.welab.crm.operate.dto.staff.BatchDictInfoReqDTO;
import com.welab.crm.operate.dto.staff.ColOrgReqDTO;
import com.welab.crm.operate.service.ICrmOrgService;
import com.welab.crm.operate.vo.staff.ColOrgInfoResVO;
import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @description 机构配置管理
 * <AUTHOR>
 * @date 2021-11-10 11:22:22
 * @version v1.0
 */
@RestController
@RequestMapping(Urls.OrgInfo.V1_ORG)
@Api(value = "CrmOrgInfoController", description = "机构配置Controller")
public class CrmOrgInfoController {

	@Autowired
	private ICrmOrgService crmOrgService;

	/**
	 * 查询机构配置 
	 * @param reqDTO
	 * @return
	 */
    @PostMapping(Urls.OrgInfo.V1_ORG_QUERY)
    @ApiOperation(value = Urls.OrgInfo.V1_ORG_QUERY, notes = Urls.OrgInfo.V1_ORG_QUERY_DESC)
    public Response<?> queryOrgInfos(@Validated @RequestBody ColOrgReqDTO reqDTO) {
        Response<List<ColOrgInfoResVO>> response = new Response<List<ColOrgInfoResVO>>();
        List<ColOrgInfoResVO> result = crmOrgService.updateColOrgInfoList(reqDTO);
        response.setResult(result);
        return response;
    }
    
	/**  
	 * 组织下拉选项
	 * @param reqDTO
	 * @return  
	 */
    @GetMapping(Urls.OrgInfo.V1_ORG_SELECT_QUERY)
    @ApiOperation(value = Urls.OrgInfo.V1_ORG_SELECT_QUERY, notes = Urls.OrgInfo.V1_ORG_SELECT_QUERY_DESC)
    public Response<?> queryOrgList(@NotNull @PathVariable("code") String code) {
        Response<List<ColOrgInfoResVO>> response = new Response<List<ColOrgInfoResVO>>();
        // null=CommonUtils.getCurrentlogged()
        List<ColOrgInfoResVO> result = crmOrgService.getOrgSelectList(code);
        response.setResult(result);
        return response;
    }

    /**
     * 添加机构配置
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.OrgInfo.V1_ORG_ADD)
    @ApiOperation(value = Urls.OrgInfo.V1_ORG_ADD, notes = Urls.OrgInfo.V1_ORG_ADD_DESC)
    public Response<?> addOrg(@Validated @RequestBody ColOrgReqDTO reqDTO) {
        crmOrgService.addColOrgInfo(reqDTO);
        return Response.success();
    }


    /**
     * 删除机构配置
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.OrgInfo.V1_ORG_DELETE)
    @ApiOperation(value = Urls.OrgInfo.V1_ORG_DELETE, notes = Urls.OrgInfo.V1_ORG_DELETE_DESC)
    public Response<?> deleteOrg(@Validated @RequestBody BatchDictInfoReqDTO reqDTO) {
        crmOrgService.deleteColOrgInfo(reqDTO);
        return Response.success();
    }
    
    /**
     * 更新机构配置
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.OrgInfo.V1_ORG_UPDATE)
    @ApiOperation(value = Urls.OrgInfo.V1_ORG_UPDATE, notes = Urls.OrgInfo.V1_ORG_UPDATE_DESC)
    public Response<?> updateOrg(@RequestBody ColOrgReqDTO reqDTO) {
        crmOrgService.updateColOrg(reqDTO);
        return Response.success();
    }
}
