package com.welab.crm.operate.web.config;

import com.welab.crm.operate.web.annotation.ExcelToListHandlerMethodArgumentResolver;
import com.welab.crm.operate.web.annotation.GetStaffHandlerMethodArgumentResolver;
import java.util.List;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        super.addArgumentResolvers(argumentResolvers);
        argumentResolvers.add(new GetStaffHandlerMethodArgumentResolver());
        argumentResolvers.add(new ExcelToListHandlerMethodArgumentResolver());
    }
}
