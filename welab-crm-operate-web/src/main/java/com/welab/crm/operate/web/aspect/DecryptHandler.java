package com.welab.crm.operate.web.aspect;


import com.welab.crm.operate.annotation.AesDecryptParam;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.util.AesUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version v1.0
 * @description 符合条件的入参解密aop
 * @date 2023/6/30
 */
@Aspect
@Component
public class DecryptHandler {

    @Pointcut("@annotation(com.welab.crm.operate.annotation.AesDecrypt)")
    public void annotationPointCut() {

    }

    @Around("annotationPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object requestObj = joinPoint.getArgs()[0];
        handleDecrypt(requestObj);
        return joinPoint.proceed();
    }

    /**
     * 处理解密
     *
     * @param responseObj
     */
    private void handleDecrypt(Object responseObj) throws IllegalAccessException {
        if (Objects.isNull(responseObj)) {
            return;
        }
        List<Field> fieldList = new ArrayList<>();
        Class<?> tempClass = responseObj.getClass();
        while (null != tempClass) {
            fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
            tempClass = tempClass.getSuperclass();
        }
        for (Field field : fieldList) {
            boolean hasSecureField = field.isAnnotationPresent(AesDecryptParam.class);
            if (hasSecureField) {
                field.setAccessible(true);
                String encryptValue = (String) field.get(responseObj);
                if (StringUtils.isNotBlank(encryptValue)) {
                    boolean matches = Pattern.matches(Constant.MOBILE, encryptValue);
                    if (!matches) {
                        String plaintextValue = AesUtils.decrypt(encryptValue);
                        field.set(responseObj, plaintextValue);
                    }
                }
            }
        }
    }
}