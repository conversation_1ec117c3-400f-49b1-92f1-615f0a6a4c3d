package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.operate.domain.TmkSummaryDict;
import com.welab.crm.operate.dto.dict.tmk.TmkSumDictQueryDTO;
import com.welab.crm.operate.dto.dict.tmk.TmkSummaryDictDTO;
import com.welab.crm.operate.service.TmkSummaryDictService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/18
 */
@RestController
@Api(description = "电销话务小结服务")
@RequestMapping(Urls.SuDict.ROOT)
public class TmkSummaryDictController {

    @Resource
    private TmkSummaryDictService summaryDictService;

    /**
     * 查询营销小结数据列表
     */
    @ApiOperation(value = Urls.SuDict.V1_SALE_SUMMARY_QUERY_DESC, notes = Urls.SuDict.V1_SALE_SUMMARY_QUERY_DESC)
    @GetMapping(Urls.SuDict.V1_SALE_SUMMARY_QUERY)
    public Response<Page<TmkSummaryDict>> querySummary(@BeanParam @Validated TmkSumDictQueryDTO queryDTO) {
        Page<TmkSummaryDict> summaryPage = summaryDictService.listTmkSummaryDict(queryDTO);
        return Response.success(summaryPage);
    }

    /**
     * 新增营销小结单条数据
     */
    @ApiOperation(value = Urls.SuDict.V1_SALE_SUMMARY_ADD_DESC, notes = Urls.SuDict.V1_SALE_SUMMARY_ADD_DESC)
    @PostMapping(Urls.SuDict.V1_SALE_SUMMARY_ADD)
    public Response<Void> addSummary(@RequestBody @Validated TmkSummaryDictDTO tmkSummaryDictDTO) {
        summaryDictService.addTmkSummaryDict(CommonUtils.getCurrentlogged(), tmkSummaryDictDTO);
        return Response.success();
    }

    /**
     * 更新营销小结单条数据
     */
    @ApiOperation(value = Urls.SuDict.V1_SALE_SUMMARY_UPDATE_DESC, notes = Urls.SuDict.V1_SALE_SUMMARY_UPDATE_DESC)
    @PostMapping(Urls.SuDict.V1_SALE_SUMMARY_UPDATE)
    public Response<Void> updateSummary(@RequestBody @Validated TmkSummaryDictDTO tmkSummaryDictDTO) {
        if (tmkSummaryDictDTO.getSummaryCode() == null) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), "更新小结编号不能为空", null);
        }
        summaryDictService.updateTmkSummaryDict(CommonUtils.getCurrentlogged(), tmkSummaryDictDTO);
        return Response.success();
    }
}
