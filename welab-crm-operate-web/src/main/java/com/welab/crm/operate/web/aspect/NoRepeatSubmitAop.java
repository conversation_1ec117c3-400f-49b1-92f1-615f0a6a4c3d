package com.welab.crm.operate.web.aspect;

import com.dangdang.ddframe.job.util.env.IpUtils;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.util.IpUtil;
import com.welab.crm.operate.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import redis.clients.jedis.JedisCommands;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Aspect
@Component
@Slf4j
public class NoRepeatSubmitAop {

    @Autowired
    private JedisCommands jedisCommands;

    @Around("execution(* com.welab.crm.operate.web.controller.*.*(..)) && @annotation(nrs)")
    public Object around(ProceedingJoinPoint pjp, NoRepeatSubmit nrs) throws Throwable {
        // 获取执行方法
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        NoRepeatSubmit annotation = method.getAnnotation(NoRepeatSubmit.class);
        int expireSeconds = annotation.expireSeconds();
        int maxTimes = annotation.times();
        String redisKey = annotation.key();
        String key;

        // 如果注解中未配置redis key，则从请求头中获取 x-mobile 与请求路径拼接作为 key
        if (StringUtils.isBlank(redisKey)) {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes == null) {
                throw new CrmOperateException("无法获取请求上下文");
            }
            HttpServletRequest request = attributes.getRequest();
            String mobile = request.getHeader("x-mobile");
            if (StringUtils.isBlank(mobile)) {
                throw new CrmOperateException("x-mobile 不能为空");
            }
            key = mobile + request.getServletPath();
        } else if ("externalComplaint".equals(redisKey)){
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            key = request.getServletPath() + IpUtil.getIpAddr(request);
            log.info("NoRepeatSubmitAop - externalComplaint key: {}", key);
        } else {
            // 如果配置了 key，则根据传入参数动态生成 key
            String[] paramNames = ((MethodSignature) pjp.getSignature()).getParameterNames();
            Object[] paramValues = pjp.getArgs();
            key = getLockKey(paramNames, paramValues, redisKey);
        }

        String value = jedisCommands.get(key);
        if (StringUtils.isBlank(value)) {
            setRedisCacheKey(key, "1", expireSeconds);
        } else {
            int count = Integer.parseInt(value);
            Long ttl = jedisCommands.ttl(key);
            if (ttl != null && ttl == -1) {
                // 如果 key 没有设置过期时间，则删除该 key，防止误判
                jedisCommands.del(key);
            } else if (count < maxTimes) {
                jedisCommands.incr(key);
            } else {
                throw new CrmOperateException("请勿重复提交或者操作过于频繁");
            }
        }

        return pjp.proceed();
    }

    private void setRedisCacheKey(String key, String value, int expireSeconds) {
        // 通过 Redis 分布式锁设置缓存
        RedisUtil.tryGetDistributedLock(jedisCommands, key, value, expireSeconds);
    }

    public String getLockKey(String[] paramNames, Object[] paramValues, String lockKey) {
        Map<String, Object> paramMap = objectToMap(paramNames, paramValues);
        // 利用正则表达式解析 ${field} 占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(lockKey);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String fieldName = matcher.group(1);
            Object fieldValue = paramMap.get(fieldName);
            String replacement = fieldValue == null ? "" : String.valueOf(fieldValue);
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        String finalKey = sb.toString();
        log.info("NoRepeatSubmitAop - lockKey: {}", finalKey);
        return finalKey;
    }

    public Map<String, Object> objectToMap(String[] paramNames, Object[] paramValues) {
        Map<String, Object> map = new HashMap<>();
        try {
            for (int i = 0; i < paramValues.length; i++) {
                Object paramValue = paramValues[i];
                if (paramValue instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramMap = (Map<String, Object>) paramValue;
                    map.putAll(paramMap);
                } else if (paramValue != null && paramValue.getClass().getClassLoader() != null) {
                    Field[] fields = paramValue.getClass().getDeclaredFields();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        map.put(field.getName(), field.get(paramValue));
                    }
                } else {
                    map.put(paramNames[i], paramValue);
                }
            }
        } catch (Exception ex) {
            log.error("NoRepeatSubmitAop - 解析参数字段错误", ex);
        }
        return map;
    }
}
