package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.RepaymentCalculateDTO;
import com.welab.collection.interview.service.FinanceService;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.annotation.AesDecrypt;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkQueryDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayLinkSendDTO;
import com.welab.crm.operate.dto.repaylink.H5RepayMentDTO;
import com.welab.crm.operate.mapper.CsRepayLinkRecordMapper;
import com.welab.crm.operate.service.H5RepayLinkService;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.repaylink.H5RepayLinkVO;
import com.welab.crm.operate.vo.repaylink.H5RepaySummaryReportVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.finance.account.enums.UserTypeEnum;
import com.welab.finance.accounting.dto.QuerySingleAccountDTO;
import com.welab.finance.accounting.dubbo.FinanceAccountsDubboService;
import com.welab.finance.accounting.vo.AccountVO;
import com.welab.thirdparty.partner.wld.ProxyInteractionRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/28 18:08
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.H5RepayLink.ROOT)
@Api(value = "h5还款链接接口", tags = "客服h5还款链接相关接口")
@Slf4j
public class H5RepayLinkController extends BaseController{

    @Resource
    private H5RepayLinkService h5RepayLinkService;

    @Resource
    private CsRepayLinkRecordMapper csRepayLinkRecordMapper;

    @Resource
    private FinanceService collectionFinanceService;

    @Resource
    private FinanceAccountsDubboService financeAccountsDubboService;

    @GetMapping(Urls.H5RepayLink.V1_IS_ALLOW_DIY)
    @ApiOperation(value = Urls.H5RepayLink.V1_IS_ALLOW_DIY_DESC, notes = Urls.H5RepayLink.V1_IS_ALLOW_DIY_DESC)
    public Response<Map<String, Boolean>> isAllowDiyRepay(@Validated @NotBlank(message = "合同号不能为空") @RequestParam String appNo) {
        return h5RepayLinkService.isAllowOfflineRepayment(appNo);
    }

    @PostMapping(Urls.H5RepayLink.V1_AMOUNT)
    @ApiOperation(value = Urls.H5RepayLink.V1_AMOUNT_DESC, notes = Urls.H5RepayLink.V1_AMOUNT_DESC)
    public Response<String> getAmount(@RequestBody RepaymentCalculateDTO dto) {
        return Response.success(h5RepayLinkService.calRepayAmount(dto));
    }

    @PostMapping(Urls.H5RepayLink.V1_GENERATE_URL)
    @ApiOperation(value = Urls.H5RepayLink.V1_GENERATE_URL_DESC, notes = Urls.H5RepayLink.V1_GENERATE_URL_DESC)
    public Boolean generateUrl(@RequestBody ProxyInteractionRequest reqDTO) {
        try {
            Response<String> res = h5RepayLinkService.getRepayLink(reqDTO);
            if (Response.isSuccess(res) && StringUtils.isNotBlank(res.getResult())) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.info("generateUrl is error");
        }
        return Boolean.FALSE;
    }

    @PostMapping(Urls.H5RepayLink.V1_RECORD)
    @ApiOperation(value = Urls.H5RepayLink.V1_RECORD_DESC, notes = Urls.H5RepayLink.V1_RECORD_DESC)
    public Response<Page<H5RepayLinkVO>> queryRecord(@RequestBody H5RepayLinkQueryDTO dto) {
        return Response.success(h5RepayLinkService.queryRecord(dto));
    }

    @GetMapping(Urls.H5RepayLink.V1_RECORD_GROUPS)
    @ApiOperation(value = Urls.H5RepayLink.V1_RECORD_GROUPS_DESC, notes = Urls.H5RepayLink.V1_RECORD_GROUPS_DESC)
    public Response<List<String>> queryRecordByGroups() {
        return Response.success(csRepayLinkRecordMapper.queryRecordByGroups());
    }

    @PostMapping(Urls.H5RepayLink.V1_SEND)
    @ApiOperation(value = Urls.H5RepayLink.V1_SEND_DESC, notes = Urls.H5RepayLink.V1_SEND_DESC)
    @AesDecrypt
    public Response<String> sendH5RepayLink(@RequestBody H5RepayLinkSendDTO dto, @GetStaff StaffVO staffVO) {
        return Response.success(h5RepayLinkService.sendH5RepayLink(dto, staffVO));
    }

    @GetMapping(Urls.H5RepayLink.V1_COUNT)
    @ApiOperation(value = Urls.H5RepayLink.V1_COUNT_DESC, notes = Urls.H5RepayLink.V1_COUNT_DESC)
    public Response<Integer> queryCount(@Validated @NotBlank(message = "合同号不能为空") @RequestParam String appNo) {
        return Response.success(h5RepayLinkService.queryRepayLinkRecordSendCount(appNo));
    }


    @PostMapping(Urls.H5RepayLink.V1_RECORD_EXPORT)
    @ApiOperation(value = Urls.H5RepayLink.V1_RECORD_EXPORT_DESC, notes = Urls.H5RepayLink.V1_RECORD_EXPORT_DESC)
    public Response<Void> exportRecord(@RequestBody @Validated H5RepayLinkQueryDTO dto) throws IOException {
        dto.setCurPage(1);
        dto.setPageSize(500000);
        EasyExcelUtils.export(baseResponse, h5RepayLinkService.queryRecord(dto).getRecords(), H5RepayLinkVO.class,
            "h5还款链接发送记录", "发送记录");
        return Response.success();
    }

    @GetMapping(Urls.H5RepayLink.V1_REPAY_REPORT)
    @ApiOperation(value = Urls.H5RepayLink.V1_REPAY_REPORT_DESC, notes = Urls.H5RepayLink.V1_REPAY_REPORT_DESC)
    public Response<List<H5RepaySummaryReportVO>> repayReport(@BeanParam ReportBaseDTO dto) {
        return Response.success(h5RepayLinkService.queryReportSummaryReport(dto));
    }

    @PostMapping(Urls.H5RepayLink.V1_REPAY_REPORT_EXPORT)
    @ApiOperation(value = Urls.H5RepayLink.V1_REPAY_REPORT_EXPORT_DESC,
        notes = Urls.H5RepayLink.V1_REPAY_REPORT_EXPORT_DESC)
    public Response<Void> exportRepayReport(@RequestBody ReportBaseDTO dto) throws IOException {
        EasyExcelUtils.export(baseResponse, h5RepayLinkService.queryReportSummaryReport(dto),
            H5RepaySummaryReportVO.class, "h5还款统计", "h5还款统计");
        return Response.success();
    }

    /**
     * 查余额
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_BALANCE, notes = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_BALANCE_DESC)
    @PostMapping(value = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_BALANCE)
    public Response<String> repaymentBalance(@RequestBody H5RepayMentDTO dto) {
        log.info("repaymentBalance start,{}", dto.getApplicationId());
        QuerySingleAccountDTO querySingleAccountDTO = new QuerySingleAccountDTO();
        querySingleAccountDTO.setOwnerId(dto.getUserId());
        querySingleAccountDTO.setOwnerType(UserTypeEnum.USER.type);
        querySingleAccountDTO.setAccountType(com.welab.finance.accounting.enums.AccountTypeEnum.CASH_ACCT.getCode());
        querySingleAccountDTO.setChannelCode("allinpay");
        querySingleAccountDTO.setOrgId(0L);
        Response<AccountVO> result = financeAccountsDubboService.queryAccount(querySingleAccountDTO);
        log.info("repaymentBalance end,{}", JSONObject.toJSONString(result));
        Response<String> response = new Response<>();
        response.setCode(result.getCode());
        response.setMessage(result.getMessage());
        if (ResponsCodeTypeEnum.SUCCESS.getCode().equals(result.getCode())) {
            response.setResult(result.getResult().getCashBalance().toString());
            return response;
        } else {
            log.warn("repaymentBalance 接口请求错误：{}，{}", result.getCode(), result.getMessage());
        }
        return response;
    }

    /**
     * 查通道
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_CHANNEL, notes = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_CHANNEL_DESC)
    @PostMapping(value = Urls.H5RepayLink.V1_FINANCE_REPAYMENT_CHANNEL)
    public Response<String> repaymentChannelCode(@RequestBody RepaymentCalculateDTO dto) {
        return collectionFinanceService.calculateWithholdchannelCode(dto);
    }
}
