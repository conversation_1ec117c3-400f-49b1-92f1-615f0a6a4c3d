package com.welab.crm.operate.web.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.interview.dto.repeatcall.RepeatCallDTO;
import com.welab.crm.interview.service.ConRepeatCallService;
import com.welab.crm.interview.service.SatisfactionService;
import com.welab.crm.interview.vo.repeatcall.ReportResolvedRateVO;
import com.welab.crm.interview.vo.satisfation.ReportSatisfactionVO;
import com.welab.crm.operate.dto.ReportBaseDTO;
import com.welab.crm.operate.dto.report.*;
import com.welab.crm.operate.dto.transferReport.TransferReportDetailDTO;
import com.welab.crm.operate.dto.transferReport.TransferReportRangeDTO;
import com.welab.crm.operate.dto.workorder.FundAndRegulatoryComplaintDTO;
import com.welab.crm.operate.dto.workorder.FundNameResDTO;
import com.welab.crm.operate.dto.workorder.RegulatoryComplaintResDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderComplainDTO;
import com.welab.crm.operate.excel.ExcelMergeStrategy;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.WorkOrderReportService;
import com.welab.crm.operate.service.WorkOrderService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.woReport.ComplaintEscalationStatisticsReportVO;
import com.welab.crm.operate.vo.transferReport.RangeTransferReportVO;
import com.welab.crm.operate.vo.transferReport.TransferReportDetailVO;
import com.welab.crm.operate.vo.woReport.*;
import com.welab.crm.operate.vo.workorder.WorkOrderComplainVO;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.crm.operate.web.constants.Urls.WorkOrderReport;
import com.welab.domain.vo.ResponseVo;
import com.welab.xdao.context.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2021/12/23
 */
@RestController
@Api(description = "工单报表统计服务")
@RequestMapping(WorkOrderReport.ROOT)
@Slf4j
public class WorkOrderReportController extends BaseController {

    @Resource
    private WorkOrderReportService workOrderReportService;

    @Resource
    private ConRepeatCallService conRepeatCallService;

    @Resource
    private SatisfactionService satisfactionService;

    @Resource
    private WorkOrderService workOrderService;
	@Autowired
	private CommonUtils commonUtils;

    @GetMapping(WorkOrderReport.V1_WO_REPORT_SUMMARY)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SUMMARY_DESC, notes = WorkOrderReport.V1_WO_REPORT_SUMMARY_DESC)
    public Response<List<ReportSummaryVO>> getSummary(@Validated @ModelAttribute WoReportDTO dto,
                                                      @PathVariable String reportType) {
        List<ReportSummaryVO> list = null;
        if ("complaint".equals(reportType)) {
            list = Arrays.asList(workOrderReportService.getSummaryComplaint(dto));
        } else if ("product".equals(reportType)) {
            list = Arrays.asList(workOrderReportService.getSummaryProduct(dto));
        } else if ("all".equals(reportType)) {
            list = workOrderReportService.getSummary(dto);
        }
        return Response.success(list);
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_SUMMARY_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SUMMARY_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_SUMMARY_EXPORT_DESC)
    public Response<Void> getSummaryExport(@Validated @ModelAttribute WoReportDTO dto,
                                           @PathVariable String reportType) {
        setDtoPageParam(dto);
        List<ReportSummaryVO> resultList = getSummary(dto, reportType).getResult();
        ExcelUtil.listToExcelAndExport(resultList, ReportSummaryVO.class, baseResponse);
        return Response.success();
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE_DESC, notes = WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE_DESC)
    public Response<Page<ReportSummaryTypeVO>> getSummaryType(@Validated @ModelAttribute ReportSummaryTypeDTO dto) {
        return Response.success(workOrderReportService.getSummaryType(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_SUMMARY_TYPE_EXPORT_DESC)
    public Response<Void> getSummaryTypeExport(@Validated @ModelAttribute ReportSummaryTypeDTO dto) {
        setDtoPageParam(dto);
        ExcelUtil.listToExcelAndExport(workOrderReportService.getSummaryType(dto).getList(), ReportSummaryTypeVO.class,
                baseResponse);
        return Response.success();
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_DETAILS)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_DETAILS_DESC, notes = WorkOrderReport.V1_WO_REPORT_DETAILS_DESC)
    public Response<Page<ReportDetailsVO>> getDetails(@Validated @ModelAttribute WoReportDTO dto) {
        return Response.success(workOrderReportService.getDetails(dto, false));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_DETAILS_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_DETAILS_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_DETAILS_EXPORT_DESC)
    public Response<Void> getDetailsExport(@Validated @ModelAttribute WoReportDTO dto) {
        setDtoPageParam(dto);
        workOrderReportService.exportReportDetail(baseResponse,dto,"工单明细报表");
        return Response.success();
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY_DESC, notes = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY_DESC)
    public Response<Page<ReportAssignmentSummaryVO>> getAssignmentSummary(
            @Validated @ModelAttribute ReportAssignmentSummaryDTO dto) {
        return Response.success(workOrderReportService.getAssignmentSummary(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_SUMMARY_EXPORT_DESC)
    public Response<Void> exportAssignmentSummary(
            @Validated @ModelAttribute ReportAssignmentSummaryDTO dto) {
        try {
            ExcelUtil.listToExcelAndExport(workOrderReportService.getAssignmentSummaryList(dto), ReportAssignmentSummaryVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportAssignmentSummary exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS_DESC, notes = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS_DESC)
    public Response<Page<ReportAssignmentDetailsVO>> getAssignmentDetails(@Validated @ModelAttribute WoReportDTO dto) {
        return Response.success(workOrderReportService.getAssignmentDetails(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_ASSIGNMENT_DETAILS_EXPORT_DESC)
    public Response<Void> exportAssignmentDetails(@Validated @ModelAttribute WoReportDTO dto) {
        try {
            ExcelUtil.listToExcelAndExport(workOrderReportService.getAssignmentDetailsList(dto), ReportAssignmentDetailsVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportAssignmentDetails exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY_DESC, notes = WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY_DESC)
    public Response<Page<ReportEfficiencySummaryVO>> getEfficiencySummary(
            @Validated @ModelAttribute ReportEfficiencySummaryDTO dto) {
        return Response.success(workOrderReportService.getEfficiencySummary(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_EFFICIENCY_SUMMARY_EXPORT_DESC)
    public Response<Void> exportEfficiencySummary(@Validated @ModelAttribute ReportEfficiencySummaryDTO dto) {
        try {
            ExcelUtil.listToExcelAndExport(workOrderReportService.getEfficiencySummaryList(dto), ReportEfficiencySummaryVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportEfficiencySummary exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_DESC, notes = WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_DESC)
    public Response<Page<OutboundEfficiencySummaryVO>> getOutboundEfficiencySummary(
            @Validated @ModelAttribute OutboundEfficiencySummaryDTO dto) {
        return Response.success(workOrderReportService.getOutboundEfficiencySummary(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_EXPORT_DESC)
    public Response<Void> exportOutboundEfficiencySummary(@Validated @ModelAttribute OutboundEfficiencySummaryDTO dto) {
        try {
            ExcelUtil.listToExcelAndExport(workOrderReportService.getOutboundEfficiencySummaryList(dto), OutboundEfficiencySummaryVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportOutboundEfficiencySummary exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_CENTRAL_MONITORING)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_CENTRAL_MONITORING_DESC, notes = WorkOrderReport.V1_WO_REPORT_CENTRAL_MONITORING_DESC)
    public Response<Page<CentralMonitoringVO>> getCentralMonitoring(
            @Validated @ModelAttribute CentralMonitoringDTO dto) {
        return Response.success(workOrderReportService.getCentralMonitoring(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE_DESC, notes = WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE_DESC)
    public Response<Page<ReportResolvedRateVO>> getStaffEfficiency(@Validated @ModelAttribute RepeatCallDTO dto) {
        return Response.success(conRepeatCallService.queryRecord(dto));
    }

    @GetMapping(WorkOrderReport.V1_WO_REPORT_SATISFACTION)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SATISFACTION_DESC, notes = WorkOrderReport.V1_WO_REPORT_SATISFACTION_DESC)
    public Response<Page<ReportSatisfactionVO>> getSatisfactionReport(@Validated @ModelAttribute RepeatCallDTO dto) {
        checkTime(dto.getStartTime(),dto.getEndTime());
        return Response.success(satisfactionService.getSatisfactionReport(dto));
    }


    @PostMapping(WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_RESOLUTION_RATE_EXPORT_DESC)
    public Response<Void> exportResolutionRateReport(@Validated @RequestBody RepeatCallDTO dto) {
        List<ReportResolvedRateVO> list = conRepeatCallService.queryRecordList(dto);
        ExcelUtil.listToExcelAndExport(list, ReportResolvedRateVO.class, baseResponse);
        return Response.success();
    }

    @PostMapping(WorkOrderReport.V1_WO_REPORT_SATISFACTION_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_WO_REPORT_SATISFACTION_EXPORT_DESC, notes = WorkOrderReport.V1_WO_REPORT_SATISFACTION_EXPORT_DESC)
    public Response<Void> exportSatisfactionReport(@Validated @RequestBody RepeatCallDTO dto) {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(100000);
        List<ReportSatisfactionVO> list = satisfactionService.getSatisfactionReportList(dto);
        ExcelUtil.listToExcelAndExport(list, ReportSatisfactionVO.class, baseResponse);
        return Response.success();
    }

    /**
     * 工单客户投诉历史列表数据
     */
    @GetMapping(Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_LIST)
    @ApiOperation(value = Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_LIST_DESC, notes = Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_LIST_DESC)
    public Response<Object> queryComplainList(@BeanParam WorkOrderComplainDTO reqDTO) {
        Page<WorkOrderComplainVO> complains = workOrderService.listComplains(reqDTO);
        return new Response<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), complains);
    }

    /**
     * 导出工单客户投诉历史列表数据
     */
    @GetMapping(Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_EXPORT)
    @ApiOperation(value = Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_EXPORT_DESC, notes = Urls.WorkOrderReport.V1_WORKORDER_COMPLAIN_EXPORT_DESC)
    public Response<Void> exportComplainList(@BeanParam WorkOrderComplainDTO reqDTO) {
        List<WorkOrderComplainVO> complains = workOrderService.exportComplains(reqDTO);
        ExcelUtil.listToExcelAndExport(complains, WorkOrderComplainVO.class, baseResponse);
        return Response.success();
    }

    private void setDtoPageParam(WoReportDTO reportDTO){
        reportDTO.setCurrentPage(1);
        reportDTO.setRowsPerPage(1000000);
    }



    private void checkTime(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            throw new CrmOperateException("查询时间不能为空");
        }
        if (DateUtil.getDaysBetween(startTime, endTime) >= 93) {
            throw new CrmOperateException("查询范围不能超过3个月");
        }
    }

    @GetMapping(WorkOrderReport.V1_FUND_COMPLAINT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_FUND_COMPLAINT_EXPORT_DESC, notes = WorkOrderReport.V1_FUND_COMPLAINT_EXPORT_DESC)
    public Response<Void> getFundNameExport(@Validated @ModelAttribute FundAndRegulatoryComplaintDTO dto) {
        setPageParam(dto);
        List<FundNameResDTO> records = workOrderService.queryFundName(dto);
        ExcelUtil.listToExcelAndExport(records, FundNameResDTO.class, baseResponse);
        return Response.success();
    }

    @GetMapping(WorkOrderReport.V1_REGULATORY_COMPLAINT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_REGULATORY_COMPLAINT_EXPORT_DESC, notes = WorkOrderReport.V1_REGULATORY_COMPLAINT_EXPORT_DESC)
    public Response<Void> getComplaintExport(@Validated @ModelAttribute FundAndRegulatoryComplaintDTO dto) {
        setPageParam(dto);
        List<RegulatoryComplaintResDTO> records = workOrderService.queryComplaint(dto);
        ExcelUtil.listToExcelAndExport(records, RegulatoryComplaintResDTO.class, baseResponse);
        return Response.success();
    }

    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT_DESC)
    public Response<com.baomidou.mybatisplus.extension.plugins.pagination.Page<RangeTransferReportVO>> queryTransferRangeReport(
            @Validated @RequestBody TransferReportRangeDTO dto) {
        return Response.success(workOrderReportService.queryTransferRangeReport(dto));
    }

    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_REPORT_EXPORT_DESC)
    public Response<Void> exportTransferRangeReport(
            @Validated @RequestBody TransferReportRangeDTO dto) throws IOException {
        List<RangeTransferReportVO> list = workOrderReportService.queryTransferRangeReportList(dto);

        String fileName = "债转投诉周期统计报表";
        CommonUtil.setResponse(baseResponse, fileName);
        EasyExcelFactory.write(new BufferedOutputStream(baseResponse.getOutputStream()))
                .head(rangeTransferHead(dto))
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.TRUE)
                .registerWriteHandler(new ExcelMergeStrategy(buildTransferRangeMergeList(list)))
                .sheet(fileName)
                .doWrite(list);
                
        return new Response<>();
    }

    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT_DESC)
    public Response<List<List<String>>> queryTransferMonthReport(
            @Validated @RequestBody TransferReportRangeDTO dto) {
        List<List<String>> dataList = workOrderReportService.queryTransferMonthReport(dto);
        List<String> headList = getMonthHeadList(dto.getStartTime(), dto.getEndTime());
        dataList.add(0, headList);
        return Response.success(dataList);
    }

    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_MONTH_REPORT_EXPORT_DESC)
    public Response<Void> exportTransferMonthReport(
            @Validated @RequestBody TransferReportRangeDTO dto) throws IOException {
        List<List<String>> dataList = workOrderReportService.queryTransferMonthReport(dto);
        String fileName = "债转投诉月份统计报表";
        CommonUtil.setResponse(baseResponse, fileName);
        EasyExcelFactory.write(new BufferedOutputStream(baseResponse.getOutputStream()))
                .head(getMonthHead(getMonthHeadList(dto.getStartTime(), dto.getEndTime())))
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.TRUE)
                .sheet(fileName)
                .doWrite(dataList);

        return new Response<>();
    }


    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT_DESC)
    public Response<com.baomidou.mybatisplus.extension.plugins.pagination.Page<TransferReportDetailVO>> queryTransferDetailReport(
            @Validated @RequestBody TransferReportDetailDTO dto) {
        return Response.success(workOrderReportService.queryTransferDetailReport(dto));
    }

    @PostMapping(WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_TRANSFER_COMPLAINT_DETAIL_REPORT_EXPORT_DESC)
    public Response<Void> exportTransferDetailReport(
            @Validated @RequestBody TransferReportDetailDTO dto) throws IOException {
        List<TransferReportDetailVO> dataList = workOrderReportService.queryTransferDetailReportList(dto);
        CommonUtils.mergeExport(baseResponse, "债转投诉明细报表", TransferReportDetailVO.class, dataList, getTransferDetailMergeList(dataList));
        return Response.success();
    }

    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT_DESC)
    public Response<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReportDetailsVO>> queryComplaintEscalationReport(
            @Validated @RequestBody WoReportDTO dto) {
        return Response.success(workOrderReportService.queryComplaintEscalationReport(dto));
    }
    
    
    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_REPORT_EXPORT_DESC)
    public Response<Void> exportComplaintEscalationReport(
            @Validated @RequestBody WoReportDTO dto) throws IOException {
        setDtoPageParam(dto);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReportDetailsVO> page = workOrderReportService.queryComplaintEscalationReport(dto);
        if (Objects.isNull(page)) {
            return Response.success();
        }
        CommonUtils.commonExport(baseResponse, "投诉升级明细报表", ReportDetailsVO.class, page.getRecords());
        return Response.success();
    }


    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_DESC)
    public Response<List<ComplaintEscalationStatisticsReportVO>> queryComplaintEscalationStatisticsReport(
            @Validated @RequestBody ReportBaseDTO dto) {
        return Response.success(workOrderReportService.queryComplaintEscalationStatisticsReport(dto));
    }
    
    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_EXPORT_DESC)
    public Response<Void> exportComplaintEscalationStatisticsReport(
            @Validated @RequestBody ReportBaseDTO dto) throws IOException {
        List<ComplaintEscalationStatisticsReportVO> dataList = workOrderReportService.queryComplaintEscalationStatisticsReport(dto);
        String fileName = "投诉升级统计报表";
        CommonUtil.setResponse(baseResponse, fileName);
        EasyExcelFactory.write(new BufferedOutputStream(baseResponse.getOutputStream()))
                .head(ComplaintEscalationStatisticsReportVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.TRUE)
                .sheet(fileName)
                .doWrite(dataList);
        return Response.success();
    }
    
    // 升级投诉原因报表
    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT_DESC)
    public Response<List<ComplaintEscalationReasonReportVO>> queryComplaintEscalationReasonReport(
            @Validated @RequestBody WoReportDTO dto) {
        return Response.success(workOrderReportService.queryComplaintEscalationReasonReport(dto));
    }
    
    @PostMapping(WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT_EXPORT)
    @ApiOperation(value = WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT_EXPORT_DESC, notes = WorkOrderReport.V1_COMPLAINT_ESCALATION_REASON_REPORT_EXPORT_DESC)
    public Response<Void> exportComplaintEscalationReasonReport(
            @Validated @RequestBody WoReportDTO dto) throws IOException {
        setDtoPageParam(dto);
        List<ComplaintEscalationReasonReportVO> list = workOrderReportService.queryComplaintEscalationReasonReport(dto);
        CommonUtils.commonExport(baseResponse, "投诉升级原因报表", ComplaintEscalationReasonReportVO.class, list);
        return Response.success();
    }
    
    

    private List<CellRangeAddress> getTransferDetailMergeList(List<TransferReportDetailVO> dataList) {
        List<CellRangeAddress> mergeList = new ArrayList<>();
        List<String> orderNoList = dataList.stream().map(TransferReportDetailVO::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, List<TransferReportDetailVO>> orderNoMap = dataList.stream().collect(Collectors.groupingBy(TransferReportDetailVO::getOrderNo));
        
        int row = 1;
        int mergeColCount = 14;
        for (String orderNo : orderNoList) {
            int size = orderNoMap.get(orderNo).size();
            if (size > 1) {
                for (int i = 0; i <= mergeColCount; i++) {
                    mergeList.add(new CellRangeAddress(row, row + size - 1, i, i));
                }
            } 
            row += size;
        }
        return mergeList;
    }

    private List<List<String>> getMonthHead(List<String> monthHeadList) {
        List<List<String>> list = new ArrayList<List<String>>();
        for (String s : monthHeadList) {
            List<String> list1 = new ArrayList<>();
            list1.add(s);
            list.add(list1);
        }
        return list;
    }

    private List<String> getMonthHeadList(String startTime, String endTime) {
        String startMonth = startTime.substring(0, 7);
        String endMonth = endTime.substring(0, 7);
        List<String> head = new ArrayList<>();
        head.add("债转公司名称");
        while (startMonth.compareTo(endMonth) <= 0) {
            head.add(startMonth);
            startMonth = DateUtil.dateToString(DateUtil.plusMonths(DateUtil.stringToDate(startMonth), 1)).substring(0, 7);
        }
        head.add("总计");
        return head;
    }

    private List<CellRangeAddress> buildTransferRangeMergeList(List<RangeTransferReportVO> dataList) {
        List<CellRangeAddress> mergeList = new ArrayList<>();
        Map<String, List<RangeTransferReportVO>> companyMap =
                dataList.stream().collect(Collectors.groupingBy(RangeTransferReportVO::getTransferCompany));
        List<String> companyList = dataList.stream().map(RangeTransferReportVO::getTransferCompany).distinct().collect(Collectors.toList());
        int beginIndex = 1;
        for (String company : companyList) {
            int rowSize = companyMap.get(company).size();
            if (rowSize > 1) {
                mergeList.add(new CellRangeAddress(beginIndex, beginIndex + rowSize - 1, 0, 0));
                mergeList.add(new CellRangeAddress(beginIndex, beginIndex + rowSize - 1, 14, 14));
                mergeList.add(new CellRangeAddress(beginIndex, beginIndex + rowSize - 1, 15, 15));
                mergeList.add(new CellRangeAddress(beginIndex, beginIndex + rowSize - 1, 16, 16));
            }
            beginIndex += rowSize;
        }
        
        return mergeList;
    }
    

    private List<List<String>> rangeTransferHead(TransferReportRangeDTO dto) {
        List<List<String>> list = new ArrayList<List<String>>();

        List<String> titleList = new ArrayList<>(Arrays.asList("债转公司", "投诉来源", "暴力催收", "使用我司名义催收", "不接受债转", "核实还款问题", "结清核实", "联系不到债转", "联系单位", "联系第三方", "其他还款问题", "协商还款",
                "投诉诉讼冻结", "延期还款", "合计"));
        if (StringUtils.isNotBlank(dto.getRoundStartTime()) && StringUtils.isNotBlank(dto.getRoundEndTime())) {
            titleList.add(dto.getRoundStartTime().substring(0, 10) + "~" + dto.getRoundEndTime().substring(0, 10));
        } else {
            titleList.add("环比时间合计");
        }
        titleList.add("环比");
        for (String s : titleList) {
            List<String> list1 = new ArrayList<>();
            list1.add(s);
            list.add(list1);
        }
        
        return list;
    }

    private void setPageParam(FundAndRegulatoryComplaintDTO dto) {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(1000000);
    }
}
