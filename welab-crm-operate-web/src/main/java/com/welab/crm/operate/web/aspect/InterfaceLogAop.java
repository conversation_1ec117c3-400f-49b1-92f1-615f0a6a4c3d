package com.welab.crm.operate.web.aspect;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.TypeReference;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.OpHttpLogRecord;
import com.welab.crm.operate.mapper.OpHttpLogRecordMapper;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.crm.operate.web.filter.XssHttpServletRequestWrapper;
import com.welab.crm.operate.web.response.Response;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@Slf4j
public class InterfaceLogAop {

    @Pointcut("execution(* com.welab.crm.operate.web.controller.*.*(..)) && (@annotation(org.springframework.web.bind.annotation.GetMapping) || @annotation(org.springframework.web.bind.annotation.PostMapping))")
    public void pointCut() {

    }

    private static final Integer MAX_RES_LENGTH = 2000;
    @Resource
    private OpHttpLogRecordMapper opHttpLogRecordMapper;

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        StringBuilder logStr = new StringBuilder();
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        OpHttpLogRecord logRecord = null;
        String requestURI = request.getRequestURI();
        try {
            String mobile = request.getHeader("x-mobile");
            String staffId = CommonUtils.getStaffIdByMobile(mobile);
            logStr.append("staffId:").append(staffId);
            logStr.append(",请求路径:").append(requestURI);
            logStr.append(",URL参数:").append(replaceMobileAndCnid(request.getQueryString()));
            logStr.append(",body参数:");
            StringBuilder requestBody = new StringBuilder();
            for (Object arg : pjp.getArgs()) {
                if (arg instanceof XssHttpServletRequestWrapper){
                    continue;
                }
                if (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse)) {
                    logStr.append(replaceMobileAndCnid(JSON.toJSONString(arg))).append(",");
                    requestBody.append(replaceMobileAndCnid(JSON.toJSONString(arg))).append(",");
                }
            }
            Method method = ((MethodSignature)pjp.getSignature()).getMethod();
            if (method.isAnnotationPresent(LogSaveToDb.class)) {
                logRecord = new OpHttpLogRecord();
                logRecord.setLoginName(staffId);
                logRecord.setRequestParam(replaceMobileAndCnid(request.getQueryString()));
                logRecord.setRequestBody(requestBody.toString());
                logRecord.setRequestPath(requestURI);
                logRecord.setCreateTime(new Date());
            }
        } catch (Exception e) {
            log.warn("接口日志切面异常,path:" + requestURI, e);
        }

        Object result = pjp.proceed();

        try {
            logStr.append(",返回结果:");
            if (Objects.nonNull(result)) {
                String jsonString = JSON.toJSONString(result);
                if (jsonString.length() >= MAX_RES_LENGTH) {
                    logStr.append(replaceMobileAndCnid(jsonString.substring(0, MAX_RES_LENGTH)));
                } else {
                    logStr.append(replaceMobileAndCnid(jsonString));
                }
            }
            if (Objects.nonNull(logRecord)) {
                if (requestURI.equals("/welab-crm-operate" + Urls.User.ROOT + Urls.User.USER_DETAIL_QUERY)) {
                    Response<PersonalDetailsVoExpand> userDetailResultRes = JSON.parseObject(JSON.toJSONString(result),
                        new TypeReference<Response<PersonalDetailsVoExpand>>() {});
                    if (Response.isSuccess(userDetailResultRes)) {
                        logRecord.setUuid(Optional.ofNullable(userDetailResultRes.getResult())
                            .map(PersonalDetailsVoExpand::getUuid).orElse(""));
                    }

                } else if (requestURI
                    .equals("/welab-crm-operate" + Urls.User.ROOT + Urls.User.WALLET_USER_DETAIL_QUERY)) {
                    Response<WalletUserDTO> walletDetail =
                        JSON.parseObject(JSON.toJSONString(result), new TypeReference<Response<WalletUserDTO>>() {});
                    if (Response.isSuccess(walletDetail)) {
                        logRecord.setUuid(
                            Optional.ofNullable(walletDetail.getResult()).map(WalletUserDTO::getUuid).orElse(""));

                    }
                }
                opHttpLogRecordMapper.insert(logRecord);
            }
        } catch (Exception e) {
            log.warn("解析响应异常,path:" + requestURI, e);
        }

        log.info(logStr.toString());

        return result;

    }

    private String replaceMobileAndCnid(String queryString) {
        if (StringUtils.isBlank(queryString)) {
            return "";
        }

        return queryString.replaceAll(Constant.MOBILE_GROUP2, "***$2").replaceAll(Constant.ID_NO_REX, "***");
    }

}
