package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.SensitiveWorkOrderDTO;
import com.welab.crm.operate.service.SensitiveWorkOrderService;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@Api(tags = "敏感工单配置接口")
@RequestMapping(Urls.SensitiveWorkorderConfig.ROOT)
public class SensitiveWorkorderController extends BaseController {

	@Resource
	private SensitiveWorkOrderService sensitiveWorkOrderService;

	@ApiOperation(value = Urls.SensitiveWorkorderConfig.ADD_DESC, notes = Urls.SensitiveWorkorderConfig.ADD_DESC)
	@PostMapping(Urls.SensitiveWorkorderConfig.ADD)
	public Response<Void> add(@RequestBody @Validated SensitiveWorkOrderDTO dto) {
		sensitiveWorkOrderService.add(dto);
		return Response.success();
	}

	@ApiOperation(value = Urls.SensitiveWorkorderConfig.QUERY_DESC, notes = Urls.SensitiveWorkorderConfig.QUERY_DESC)
	@PostMapping(Urls.SensitiveWorkorderConfig.QUERY)
	public Response<Page<SensitiveWorkOrderDTO>> query(@RequestBody SensitiveWorkOrderDTO dto) {
		return Response.success(sensitiveWorkOrderService.query(dto));
	}


	@ApiOperation(value = Urls.SensitiveWorkorderConfig.DELETE_DESC, notes = Urls.SensitiveWorkorderConfig.DELETE_DESC)
	@PostMapping(Urls.SensitiveWorkorderConfig.DELETE)
	public Response<Void> delete(@RequestBody BatchInfoReqDTO dto) {
		sensitiveWorkOrderService.delete(dto.getIds());
		return Response.success();
	}


	@ApiOperation(value = Urls.SensitiveWorkorderConfig.UPDATE_DESC, notes = Urls.SensitiveWorkorderConfig.UPDATE_DESC)
	@PostMapping(Urls.SensitiveWorkorderConfig.UPDATE)
	public Response<Void> update(@RequestBody @Validated SensitiveWorkOrderDTO dto) {
		sensitiveWorkOrderService.update(dto);
		return Response.success();
	}


}

