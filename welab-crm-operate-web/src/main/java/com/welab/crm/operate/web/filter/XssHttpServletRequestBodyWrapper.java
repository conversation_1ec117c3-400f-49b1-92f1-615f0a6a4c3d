
package com.welab.crm.operate.web.filter;

import com.alibaba.fastjson.JSON;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;

public class XssHttpServletRequestBodyWrapper extends HttpServletRequestWrapper {  
    public XssHttpServletRequestBodyWrapper(HttpServletRequest servletRequest) {
        super(servletRequest);
    }
    @Override
    public ServletInputStream getInputStream() throws IOException {
        //为空，直接返回
        String json = IOUtils.toString(super.getInputStream(), "utf-8");
        if (StringUtils.isEmpty(json)) {
            return super.getInputStream();
        }
        Map<String,Object> map = JSON.parseObject(json);
        Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
        while(iterator.hasNext()) {
            Map.Entry<String, Object> entry =  iterator.next();
            if(null==entry.getValue()||"[]".equals(""+entry.getValue())||"".equals(""+entry.getValue())||(""+entry.getValue()).startsWith("[")) {
            }else {
         	   entry.setValue(cleanXSS(""+entry.getValue()));
            }
          
        }
        ByteArrayInputStream bis = new ByteArrayInputStream(JSON.toJSONString(map).getBytes("utf-8"));
        return new MyServletInputStream(bis);
    }
    public String getHeader(String name) {
        String value = super.getHeader(name);
        if (value == null)
            return null;
        return cleanXSS(value);
    }
    private String cleanXSS(String value) {
    	value=value.replaceAll("<","&lt;").replaceAll(">","&gt;");
    	value=value.replaceAll("\\(","&#40;").replaceAll("\\)","&#41;");
    	value=value.replaceAll("'","&#39;");
    	value=value.replaceAll("eval\\((.*)\\)","");
    	value=value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']","\"\"");
    	value=value.replaceAll("script","");
    	return value;
    }
    class MyServletInputStream extends  ServletInputStream{
        private ByteArrayInputStream bis;
        public MyServletInputStream(ByteArrayInputStream bis){
            this.bis=bis;
        }
        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {
            //
        }
        @Override
        public int read() {
            return bis.read();
        }
    }
}
