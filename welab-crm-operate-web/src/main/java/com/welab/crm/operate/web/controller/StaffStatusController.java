package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.operate.dto.staff.ReportStaffStatusDTO;
import com.welab.crm.operate.service.StaffStatusService;
import com.welab.crm.operate.web.constants.Urls.StaffStatus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/11/20 11:46
 */

@RestController
@Api(description = "员工工作状态时长相关接口")
@RequestMapping(StaffStatus.ROOT)
public class StaffStatusController {

    @Autowired
    private StaffStatusService staffStatusService;


    @PostMapping(value = StaffStatus.V1_STAFF_STATUS_REPORT)
    @ApiOperation(value = StaffStatus.V1_STAFF_STATUS_REPORT, notes = StaffStatus.V1_STAFF_STATUS_REPORT_DESC)
    public Response<Void> reportStaffStatus(@RequestBody ReportStaffStatusDTO dto) {
        staffStatusService.reportStatusMsg(dto);
        return Response.success();
    }

}
