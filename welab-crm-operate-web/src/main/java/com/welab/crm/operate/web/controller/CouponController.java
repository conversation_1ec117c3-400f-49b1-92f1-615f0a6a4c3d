package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.coupon.CouponCardDTO;
import com.welab.crm.interview.dto.coupon.CouponCardSendDTO;
import com.welab.crm.interview.dto.coupon.CouponSendLogDTO;
import com.welab.crm.interview.service.CouponService;
import com.welab.crm.interview.vo.coupon.CouponCardVO;
import com.welab.crm.interview.vo.coupon.CouponSendLogVO;
import com.welab.crm.interview.vo.coupon.CouponVO;
import com.welab.crm.interview.vo.coupon.CouponWithdrawVO;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.Coupon;
import com.welab.exception.FastRuntimeException;
import com.welab.marketing.vo.PagedResponse;
import com.welab.xdao.context.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 */
@RestController
@Api(description = "卡券服务")
@RequestMapping(Coupon.V1_ROOT)
public class CouponController extends BaseController {

    @Resource
    private CouponService couponService;
    @Resource
    private CustOperateService custOperateService;

    @GetMapping(value = Coupon.V1_COUPON_GIVEN_LIST)
    @ApiOperation(value = Coupon.V1_COUPON_GIVEN_LIST_DESC, notes = Coupon.V1_COUPON_GIVEN_LIST_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uuid", value = "客户uuid", paramType = "query"),
    })
    public Response<List<CouponVO>> getGivenList(@RequestParam @NotNull Long uuid) {
        return Response.success(couponService.getUserCouponList(uuid));
    }

    @GetMapping(value = Coupon.V1_COUPON_CARD_LIST)
    @ApiOperation(value = Coupon.V1_COUPON_CARD_LIST_DESC, notes = Coupon.V1_COUPON_CARD_LIST_DESC)
    public Response<PagedResponse<CouponCardVO>> getCardList(@ModelAttribute CouponCardDTO dto) {
        return Response.success(couponService.getUserCardPage(dto));
    }

    @PostMapping(value = Coupon.V1_COUPON_SEND)
    @ApiOperation(value = Coupon.V1_COUPON_SEND_DESC, notes = Coupon.V1_COUPON_SEND_DESC)
    public Response<List<Long>> sendCoupon(@Validated @RequestBody CouponCardSendDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        List<Long> avlCouponIdList = queryUserAvlCoupon();
        if (!avlCouponIdList.contains(dto.getCouponId())){
            throw new FastRuntimeException("该卡券不允许发送" + dto.getCouponId());
        }
        dto.setCreateStaffId(staffVO.getId().toString());
        dto.setCreateStaffGroup(staffVO.getGroupCode());
        List<Long> users = couponService.sendCoupon(dto);
        saveOperation(dto, staffVO);
        return Response.success(users);
    }

    private List<Long> queryUserAvlCoupon() {
        CouponCardDTO couponCardDTO = new CouponCardDTO();
        couponCardDTO.setDept("kefu");
        couponCardDTO.setBusinessType("loan");
        couponCardDTO.setCurrentPage(1);
        couponCardDTO.setRowsPerPage(100);
        PagedResponse<CouponCardVO> page = couponService.getUserCardPage(couponCardDTO);
        return page.getList().stream().map(CouponCardVO::getId).collect(Collectors.toList());
    }

    @GetMapping(value = Coupon.V1_COUPON_SENDING_RECORD)
    @ApiOperation(value = Coupon.V1_COUPON_SENDING_RECORD_DESC, notes = Coupon.V1_COUPON_SENDING_RECORD_DESC)
    public Response<Page<CouponSendLogVO>> getSendLog(@ModelAttribute CouponSendLogDTO dto) {
        return Response.success(couponService.getSendLog(dto));
    }

    @GetMapping(value = Coupon.V1_COUPON_WITHDRAW)
    @ApiOperation(value = Coupon.V1_COUPON_WITHDRAW_DESC, notes = Coupon.V1_COUPON_WITHDRAW_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uuid", value = "客户uuid", paramType = "query"),
    })
    public Response<CouponWithdrawVO> getWithdraw(@RequestParam @NotNull Long uuid) {
        return Response.success(couponService.getWithdrawCoupons(uuid));
    }

    @GetMapping(value = Coupon.V1_COUPON_MONITOR)
    @ApiOperation(value = Coupon.V1_COUPON_MONITOR_DESC, notes = Coupon.V1_COUPON_MONITOR_DESC)
    public Response<PagedResponse<CouponCardVO>> couponGlobalMonitoring(@ModelAttribute CouponCardDTO dto) {
        return Response.success(couponService.couponGlobalMonitoring(dto));
    }

    @PostMapping(value = Coupon.V1_COUPON_MONITOR_EXPORT)
    @ApiOperation(value = Coupon.V1_COUPON_MONITOR_EXPORT_DESC, notes = Coupon.V1_COUPON_MONITOR_EXPORT_DESC)
    public void couponGlobalMonitoringExport(@RequestBody CouponCardDTO dto) {
        List<CouponCardVO> list = couponService.getCouponGlobalMonitoringList(dto);
        ExcelUtil.listToExcelAndExport(list, CouponCardVO.class, baseResponse);
    }

    @PostMapping(value = Coupon.V1_COUPON_SENDING_RECORD_EXPORT)
    @ApiOperation(value = Coupon.V1_COUPON_SENDING_RECORD_EXPORT_DESC, notes = Coupon.V1_COUPON_SENDING_RECORD_EXPORT_DESC)
    public void getSendLogList(@RequestBody CouponSendLogDTO dto) {
        List<CouponSendLogVO> list = couponService.getSendLogList(dto);
        ExcelUtil.listToExcelAndExport(list, CouponSendLogVO.class, baseResponse);
    }

    private void saveOperation(CouponCardSendDTO dto, StaffVO staffVO) {
        CustHisOperate custHisOperate = new CustHisOperate();
        custHisOperate.setComment(dto.getComment());
        custHisOperate.setUserId(dto.getUserId());
        custHisOperate.setStaffId(staffVO.getLoginName());
        custHisOperate.setGroupCode(staffVO.getGroupCode());
        custHisOperate.setOperateType(OperateTypeEnum.COUPON_CARD_SEND.getCode());
        custHisOperate.setOperateTime(new Date());
        custOperateService.saveOperateHistory(custHisOperate);
    }
}
