package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.operate.web.constants.Urls.WebSocket;
import com.welab.crm.operate.websocket.util.WebSocketUtil;
import com.welab.crm.operate.ws.SendMsgDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.websocket.Session;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.socket.WebSocketSession;

/**
 * <AUTHOR>
 * @Description: websocket接口
 * @date 2022/3/1 11:52
 */
@RestController
@RequestMapping(value = WebSocket.ROOT)
@Api(description = "webSocket相关接口")
public class WebSocketController {

    @PostMapping(value = WebSocket.V1_SEND_MSG)
    @ApiOperation(value = WebSocket.V1_SEND_MSG_DESC, notes = WebSocket.V1_SEND_MSG_DESC)
    public Response<Void> sendMessage(@RequestBody SendMsgDTO dto) {
        WebSocketSession session = WebSocketUtil.getSessionByMobile(dto.getMobile());
        WebSocketUtil.send(session, dto.getMessageJson().toJSONString());
        return Response.success();
    }

}
