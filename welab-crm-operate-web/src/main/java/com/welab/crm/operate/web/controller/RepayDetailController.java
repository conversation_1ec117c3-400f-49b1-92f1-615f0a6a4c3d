package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.repay.RepayImportDTO;
import com.welab.crm.operate.dto.repay.RepayQueryDTO;
import com.welab.crm.operate.service.RepayQueryService;
import com.welab.crm.operate.vo.repay.RepayImportVO;
import com.welab.crm.operate.vo.repay.RepayQueryVO;
import com.welab.crm.operate.web.annotation.ExcelToList;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 客户还款明细controller
 *
 * <AUTHOR>
 * @date 2022-11-05
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.Repay.ROOT)
@Api(description = "客户还款明细")
public class RepayDetailController extends BaseController {

    @Autowired
    private RepayQueryService repayQueryService;

    /**
     * 获取客户还款导入模板
     */
    @ApiOperation(value = Urls.Repay.V1_IMPORT_TEMPLATE_DESC, notes = Urls.Repay.V1_IMPORT_TEMPLATE_DESC)
    @GetMapping(Urls.Repay.V1_IMPORT_TEMPLATE)
    public Response<Void> getTemplate() {
        ExcelUtil.getTemplate(RepayImportVO.class, baseResponse);
        return Response.success();
    }

    @PostMapping(Urls.Repay.V1_QUERY)
    @ApiOperation(value = Urls.Repay.V1_QUERY_DESC, notes = Urls.Repay.V1_QUERY_DESC)
    public Response<Page<RepayQueryVO>> queryRepayList(@RequestBody RepayQueryDTO dto) {
        Page<RepayQueryVO> repayPage = repayQueryService.getRepayQuery(dto);
        return Response.success(repayPage);
    }

    @PostMapping(Urls.Repay.V1_IMPORT)
    @ApiOperation(value = Urls.Repay.V1_IMPORT_DESC, notes = Urls.Repay.V1_IMPORT_DESC)
    public Response<String> repayDetailImport(@ApiIgnore @ExcelToList(value = RepayImportDTO.class, param = "file")
                                                      ExcelList<RepayImportDTO> uuidList,
                                              @ApiIgnore @GetStaff StaffVO staffVO) {
        repayQueryService.importQueryUuidList(uuidList, staffVO);
        return Response.success();
    }

    @GetMapping(Urls.Repay.V1_FILE_URL)
    @ApiOperation(value = Urls.Repay.V1_FILE_URL_DESC, notes = Urls.Repay.V1_FILE_URL_DESC)
    public Response<String> getFileUrl(@RequestParam Long id) {
        return Response.success(repayQueryService.getFileUrl(id));
    }

}
