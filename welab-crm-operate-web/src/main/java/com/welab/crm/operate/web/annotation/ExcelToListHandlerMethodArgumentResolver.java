package com.welab.crm.operate.web.annotation;

import com.welab.common.spring.SpringContextHolder;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.exception.CrmOperateException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.util.WebUtils;

/**
 * 通过注解实现上传excel转List传入controller层
 *
 * <AUTHOR>
 */
public class ExcelToListHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    private final Logger logger = LoggerFactory.getLogger(ExcelToListHandlerMethodArgumentResolver.class);

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        if (parameter.hasParameterAnnotation(ExcelToList.class)) {
            return true;
        }
        return false;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer modelAndViewContainer,
        NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        MultipartHttpServletRequest multipartRequest = getMultipartRequestRequest();
        if (multipartRequest == null) {
            logger.error("没有上传文件");
            throw new CrmOperateException("找不到上传文件");
        }
        Class<?> pClass = parameter.getParameterType();
        if (!pClass.isAssignableFrom(ExcelList.class)) {
            logger.error("ExcelToList参数映射类型必须是ExcelList或父类");
            throw new CrmOperateException("找不到上传文件");
        }
        ExcelToList excelToList = parameter.getParameterAnnotation(ExcelToList.class);
        // 要转化的类
        Class<?> clazz = excelToList.value();
        // 上传文件的变量名
        String paramName = excelToList.param();
        List<MultipartFile> files = multipartRequest.getFiles(paramName);
        if (files.isEmpty()) {
            logger.error("通过文件名找不到文件");
            throw new CrmOperateException("找不到上传文件");
        }
        if (files.size() > 1) {
            logger.error("通过文件名找到多个映射文件");
        }
        MultipartFile file = files.get(0);
        long fileSize = file.getSize();
        if (fileSize > 50 * 1024 * 1024) {
            throw new CrmOperateException("文件大小超过50M");
        }
        Workbook wb = WorkbookFactory.create(file.getInputStream());
        return ExcelUtil.excelToList(wb, clazz, file.getOriginalFilename());
    }

    private MultipartHttpServletRequest getMultipartRequestRequest() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
            .getRequest();
        MultipartResolver resolver = SpringContextHolder.getBean("multipartResolver", MultipartResolver.class);
        String enctype = servletRequest.getContentType();
        if (StringUtils.isNotBlank(enctype) && enctype.contains("multipart/form-data")) {
            servletRequest = resolver.resolveMultipart(servletRequest);
        }
        return WebUtils.getNativeRequest(servletRequest, MultipartHttpServletRequest.class);
    }
}
