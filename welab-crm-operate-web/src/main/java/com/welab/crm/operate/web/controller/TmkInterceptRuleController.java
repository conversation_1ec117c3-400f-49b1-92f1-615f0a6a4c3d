package com.welab.crm.operate.web.controller;

import javax.annotation.Resource;

import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkInterceptRuleReqDTO;
import com.welab.crm.operate.service.TmkInterceptRuleService;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptHistoryVO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptRuleVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.TmkIntercept;
import com.welab.domain.vo.ResponseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2022/3/21
 */
@RestController
@Api(description = "电销拦截规则服务")
@RequestMapping(TmkIntercept.ROOT)
public class TmkInterceptRuleController {

	@Resource
	private TmkInterceptRuleService tmkInterceptRuleService;

	@PostMapping(TmkIntercept.V1_INTERCEPT_QUERY)
	@ApiOperation(value = TmkIntercept.V1_INTERCEPT_QUERY, notes = TmkIntercept.V1_INTERCEPT_QUERY_DESC)
	public ResponseVo<?> queryInterceptRuleList(@Validated @RequestBody TmkInterceptRuleReqDTO reqDTO){
		Page<TmkInterceptRuleVO> rootList;
		try {
			rootList = tmkInterceptRuleService.queryInterceptRuleList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}

	@PostMapping(TmkIntercept.V1_INTERCEPT_ADD)
	@ApiOperation(value = TmkIntercept.V1_INTERCEPT_ADD, notes = TmkIntercept.V1_INTERCEPT_ADD_DESC)
	public ResponseVo<?> addInterceptRule(@Validated @RequestBody TmkInterceptRuleReqDTO reqDTO, @GetStaff StaffVO staffVO) {
		try {
			reqDTO.setCreateStaffId(String.valueOf(staffVO.getId()));
			reqDTO.setModifyStaffId(String.valueOf(staffVO.getId()));
			tmkInterceptRuleService.addInterceptRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(TmkIntercept.V1_INTERCEPT_UPDATE)
	@ApiOperation(value = TmkIntercept.V1_INTERCEPT_UPDATE, notes = TmkIntercept.V1_INTERCEPT_UPDATE_DESC)
	public ResponseVo<?> updateInterceptRule(@RequestBody TmkInterceptRuleReqDTO reqDTO, @GetStaff StaffVO staffVO) {
		try{
			reqDTO.setModifyStaffId(String.valueOf(staffVO.getId()));
			tmkInterceptRuleService.updateInterceptRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	/**  
	 * 删除分单规则
	 * @param ids
	 * @return
	 */
	@PostMapping(TmkIntercept.V1_INTERCEPT_DELETE)
	@ApiOperation(value = TmkIntercept.V1_INTERCEPT_DELETE, notes = TmkIntercept.V1_INTERCEPT_DELETE_DESC)
	public ResponseVo<?> deleteInterceptRuleList(@Validated @RequestBody BatchInfoReqDTO ids) {
		try {
			tmkInterceptRuleService.deleteInterceptRule(ids);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}
	
	@PostMapping(TmkIntercept.V1_INTERCEPT_HISTORY)
	@ApiOperation(value = TmkIntercept.V1_INTERCEPT_HISTORY, notes = TmkIntercept.V1_INTERCEPT_HISTORY_DESC)
	public ResponseVo<?> queryInterceptHistoryList(@Validated @RequestBody TmkInterceptRuleReqDTO reqDTO){
		Page<TmkInterceptHistoryVO> rootList=null;
		try {
			rootList = tmkInterceptRuleService.queryInterceptHistoryList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}
}
