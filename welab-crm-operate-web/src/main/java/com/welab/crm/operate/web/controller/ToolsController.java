package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.operate.dto.notice.ToolsNoticeDTO;
import com.welab.crm.operate.service.ToolsService;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/12/27
 */
@RestController
@Api(value = "菜单提示相关接口", tags = "菜单提示tips")
@RequestMapping(Urls.Tools.ROOT)
@Slf4j
public class ToolsController {

    @Resource
    private ToolsService toolsService;

    @GetMapping(value = Urls.Tools.TIPS)
    @ApiOperation(value = Urls.Tools.TIPS_DESC, notes = Urls.Tools.TIPS_DESC)
    public Response<List<ToolsNoticeDTO>> query() {
        try {
            return Response.success(toolsService.menuTips());
        } catch (Exception e) {
            log.error("toolsService query 发生异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }
}
