package com.welab.crm.operate.web.controller;

import java.io.IOException;
import java.util.List;

import com.welab.crm.operate.dto.partner.PartnerInfoUpdateDTO;
import com.welab.crm.operate.service.PartnerInfoService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.welab.common.response.Response;
import com.welab.crm.operate.dto.partner.PartnerInfoImportDTO;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

/**
 * 资金方信息维护
 * 
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(value = "资金方信息维护", tags = "资金方信息")
@RequestMapping(Urls.PartnerInfo.ROOT)
public class PartnerInfoController extends BaseController {

    @Resource
    private PartnerInfoService partnerInfoService;

    @ApiOperation(value = Urls.PartnerInfo.IMPORT_DESC, notes = Urls.PartnerInfo.IMPORT_DESC)
    @PostMapping(Urls.PartnerInfo.IMPORT)
    public Response<Void> importPartnerInfo(MultipartFile file) throws IOException {
        List<PartnerInfoImportDTO> partnerInfoImportDTOList =
            EasyExcelUtils.syncReadObject(file.getInputStream(), PartnerInfoImportDTO.class, 1);
        partnerInfoService.importPartnerInfo(partnerInfoImportDTOList);
        return Response.success();
    }
    @ApiOperation(value = Urls.PartnerInfo.ADD_DESC, notes = Urls.PartnerInfo.ADD_DESC)
    @PostMapping(Urls.PartnerInfo.ADD)
    public Response<Void> addPartnerInfo(@RequestBody PartnerInfoImportDTO dto) {
        partnerInfoService.addPartnerInfo(dto);
        return Response.success();
    }

    @ApiOperation(value = Urls.PartnerInfo.UPDATE_DESC, notes = Urls.PartnerInfo.UPDATE_DESC)
    @PostMapping(Urls.PartnerInfo.UPDATE)
    public Response<Void> updatePartnerInfo(@RequestBody PartnerInfoUpdateDTO dto) {
        partnerInfoService.updatePartnerInfo(dto.getUpdateList());
        return Response.success();
    }

    @ApiOperation(value = Urls.PartnerInfo.QUERY_DESC, notes = Urls.PartnerInfo.QUERY_DESC)
    @PostMapping(Urls.PartnerInfo.QUERY)
    public Response<List<PartnerInfoImportDTO>> queryPartnerInfo() {
        return Response.success(partnerInfoService.queryPartnerInfo());
    }

    @ApiOperation(value = Urls.PartnerInfo.EXPORT_DESC, notes = Urls.PartnerInfo.EXPORT_DESC)
    @PostMapping(Urls.PartnerInfo.EXPORT)
    public Response<Void> exportPartnerInfo() throws IOException {
        EasyExcelUtils.export(baseResponse, partnerInfoService.queryPartnerInfo(), PartnerInfoImportDTO.class, "资金方信息",
            "资金方信息");
        return Response.success();
    }

    @ApiOperation(value = Urls.PartnerInfo.QUERY_MSG_DESC, notes = Urls.PartnerInfo.QUERY_MSG_DESC)
    @GetMapping(Urls.PartnerInfo.QUERY_MSG)
    public Response<PartnerInfoImportDTO> queryPartnerInfoMsg(@RequestParam String partnerName) {
        return Response.success(partnerInfoService.queryPartnerInfoByNameAndCode(partnerName));
    }
}
