package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.ApplicationListDTO;
import com.welab.crm.operate.dto.AppNoBatchDTO;
import com.welab.crm.operate.dto.reduce.*;
import com.welab.crm.operate.mapper.OpReduceApplyRecordMapper;
import com.welab.crm.operate.service.ReduceAndRefundService;
import com.welab.crm.operate.service.UploadAndNoticeService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.IRRCalculatorUtil;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundSubmitVO;
import com.welab.crm.operate.vo.reduce.ReduceAndRefundVO;
import com.welab.crm.operate.vo.reduce.ReduceDetailReportVO;
import com.welab.crm.operate.vo.reduce.ReduceStatisticsReportVO;
import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.exception.FastRuntimeException;
import com.welab.frs.account.vo.AllowanceCalculateVO;
import com.welab.frs.account.vo.AllowanceRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 减免、退款接口
 *
 * @module 客服项目
 */
@RestController
@Api(value = "减免、退款相关接口", tags = "减免、退款")
@RequestMapping(Urls.Reduce.ROOT)
@Slf4j
public class ReduceAndRefundController extends BaseController {

	@Resource
	private ReduceAndRefundService reduceAndRefundService;

	@Resource
	private OpReduceApplyRecordMapper opReduceApplyRecordMapper;
	
	@Resource
	private IUploadService uploadService;

	@Resource
	private UploadAndNoticeService uploadAndNoticeService;

	@ApiOperation(value = Urls.Reduce.SUBMIT_DATA_QUERY_DESC, notes = Urls.Reduce.SUBMIT_DATA_QUERY_DESC)
	@PostMapping(Urls.Reduce.SUBMIT_DATA_QUERY)
	public Response<List<ReduceAndRefundSubmitVO>> submitDataQuery(@RequestBody ApplicationListDTO dto) {
		return Response.success(reduceAndRefundService.querySubmitData(dto.getApplicationList()));
	}


	@ApiOperation(value = Urls.Reduce.REDUCIBLE_AMOUNT_QUERY_DESC, notes = Urls.Reduce.REDUCIBLE_AMOUNT_QUERY_DESC)
	@GetMapping(Urls.Reduce.REDUCIBLE_AMOUNT_QUERY)
	public Response<BigDecimal> reducibleAmountQuery(@RequestParam @ApiParam(value = "本金") BigDecimal principal,
	                                                 @RequestParam @ApiParam(value = "期数") Integer tenor,
	                                                 @RequestParam @ApiParam(value = "除本金外的其他金额之和") BigDecimal otherFee) {
		return Response.success(reduceAndRefundService.queryReducibleAmount(principal, tenor, otherFee));
	}

	@ApiOperation(value = Urls.Reduce.IRR_CALCULATE_DESC, notes = Urls.Reduce.IRR_CALCULATE_DESC)
	@GetMapping(Urls.Reduce.IRR_CALCULATE)
	public Response<BigDecimal> reducibleAmountQuery(@RequestParam @ApiParam(value = "本金") BigDecimal principal,
	                                                 @RequestParam @ApiParam(value = "期数") Integer tenor,
	                                                 @RequestParam @ApiParam(value = "除本金外的其他金额之和") BigDecimal otherFee,
	                                                 @RequestParam @ApiParam(value = "申请减免金额") BigDecimal reduceAmount) {
		BigDecimal rate = IRRCalculatorUtil.calculateAnnualInterestRate(principal.add(otherFee).subtract(reduceAmount), tenor, principal);
		return Response.success(rate.multiply(new BigDecimal("100")));
	}


	@ApiOperation(value = Urls.Reduce.REDUCE_REFUND_SUBMIT_DESC, notes = Urls.Reduce.REDUCE_REFUND_SUBMIT_DESC)
	@PostMapping(Urls.Reduce.REDUCE_REFUND_SUBMIT)
	public Response<Void> submit(@RequestBody ReduceAndRefundApplySubmitDTO dto) {
		reduceAndRefundService.submitReduceAndRefundApply(dto);
		return Response.success();
	}

	@ApiOperation(value = Urls.Reduce.RECORD_APPROVAL_DESC, notes = Urls.Reduce.RECORD_APPROVAL_DESC)
	@PostMapping(Urls.Reduce.RECORD_APPROVAL)
	public Response<Void> approval(@RequestBody ReduceApprovalDTO dto) {
		reduceAndRefundService.approvalRecord(dto);
		return Response.success();
	}

	@ApiOperation(value = Urls.Reduce.RECORD_QUERY_DESC, notes = Urls.Reduce.RECORD_QUERY_DESC)
	@GetMapping(Urls.Reduce.RECORD_QUERY)
	public Response<Page<ReduceAndRefundVO>> queryRecord(@BeanParam RecordQueryDTO dto) {
		return Response.success(reduceAndRefundService.queryApplyRecord(dto));
	}

	@ApiOperation(value = Urls.Reduce.RECORD_DETAIL_QUERY_DESC, notes = Urls.Reduce.RECORD_DETAIL_QUERY_DESC)
	@GetMapping(Urls.Reduce.RECORD_DETAIL_QUERY)
	public Response<ReduceAndRefundApplySubmitDTO> queryDetail(@RequestParam String requestNo) {
		return Response.success(reduceAndRefundService.queryApprovalRecord(requestNo));
	}

	@ApiOperation(value = Urls.Reduce.DETAIL_REPORT_QUERY_DESC, notes = Urls.Reduce.DETAIL_REPORT_QUERY_DESC)
	@GetMapping(Urls.Reduce.DETAIL_REPORT_QUERY)
	public Response<Page<ReduceDetailReportVO>> queryDetailReport(@BeanParam ReduceReportDTO dto) {
		return Response.success(reduceAndRefundService.reduceAndRefundDetailReport(dto));
	}

	@ApiOperation(value = Urls.Reduce.DETAIL_REPORT_EXPORT_DESC, notes = Urls.Reduce.DETAIL_REPORT_EXPORT_DESC)
	@GetMapping(Urls.Reduce.DETAIL_REPORT_EXPORT)
	public Response<Void> exportDetailReport(@BeanParam ReduceReportDTO dto) throws IOException {
		dto.setCurrentPage(1);
		dto.setRowsPerPage(500000);
		Page<ReduceDetailReportVO> page = reduceAndRefundService.reduceAndRefundDetailReport(dto);
		List<ReduceDetailReportVO> records = page.getRecords();
		List<CellRangeAddress> cellRangeAddressList = getCellRangeAddressList(records);
		CommonUtils.mergeExport(baseResponse, "减免退款明细报表", ReduceDetailReportVO.class, records, cellRangeAddressList);
		return Response.success();
	}

	@ApiOperation(value = Urls.Reduce.STATISTIC_REPORT_QUERY_DESC, notes = Urls.Reduce.STATISTIC_REPORT_QUERY_DESC)
	@GetMapping(Urls.Reduce.STATISTIC_REPORT_QUERY)
	public Response<Page<ReduceStatisticsReportVO>> queryStatisticReport(@BeanParam ReduceReportDTO dto) {
		return Response.success(reduceAndRefundService.reduceStatisticReport(dto));
	}

	@ApiOperation(value = Urls.Reduce.STATISTIC_REPORT_EXPORT_DESC, notes = Urls.Reduce.STATISTIC_REPORT_EXPORT_DESC)
	@GetMapping(Urls.Reduce.STATISTIC_REPORT_EXPORT)
	public Response<Page<ReduceStatisticsReportVO>> exportStatisticReport(@BeanParam ReduceReportDTO dto) throws IOException {
		dto.setRowsPerPage(500000);
		dto.setCurrentPage(1);
		Page<ReduceStatisticsReportVO> page = reduceAndRefundService.reduceStatisticReport(dto);
		CommonUtils.commonExport(baseResponse, "减免退款统计报表", ReduceStatisticsReportVO.class, page.getRecords());
		return Response.success();
	}

	@ApiOperation(value = Urls.Reduce.HISTORY_REDUCED_RECORD_DESC, notes = Urls.Reduce.HISTORY_REDUCED_RECORD_DESC)
	@GetMapping(Urls.Reduce.HISTORY_REDUCED_RECORD)
	public Response<List<AllowanceRecordVO>> historyRecordQuery(@RequestParam String appNo) {
		return Response.success(reduceAndRefundService.queryReducedRecord(appNo));
	}

	@ApiOperation(value = Urls.Reduce.REDUCED_APPLY_NAME_DESC, notes = Urls.Reduce.REDUCED_APPLY_NAME_DESC)
	@GetMapping(Urls.Reduce.REDUCED_APPLY_NAME)
	public Response<List<String>> applyName() {
		return Response.success(opReduceApplyRecordMapper.selectApplyRecordApplyName());
	}

    @ApiOperation(value = Urls.Reduce.UPLOAD_ATTACHMENT_DESC, notes = Urls.Reduce.UPLOAD_ATTACHMENT_DESC)
    @PostMapping(Urls.Reduce.UPLOAD_ATTACHMENT)
    public Response<WoAttachmentVO> uploadAttachment(MultipartFile file, @RequestParam(required = false) String requestNo) throws IOException {
	    byte[] bytes = file.getBytes();
	    String fileName = CommonUtils.replaceBlank(file.getOriginalFilename());
	    if (!SecurityUtil.checkFileTypeByFileName(fileName)){
		    throw new FastRuntimeException("文件不合法");
	    }
	    Response<String> res = uploadService.uploadFile(bytes, fileName);
		if (!Response.isSuccess(res)){
			log.warn("减免文件上传失败,res:{}", JSON.toJSONString(res));
			throw new FastRuntimeException("文件上传失败");
		}
	    Long staffId = CommonUtils.getCurrentloggedStaffId();
	    String staffName = CommonUtils.getCurrentloggedName();
	    String uniqueFileName = res.getResult();
	    WoAttachmentVO vo = new WoAttachmentVO();
	    Date currentDate = new Date();
	    if (StringUtils.isNotBlank(requestNo)){
			ReduceAttachmentDTO dto = new ReduceAttachmentDTO();
			dto.setFileName(fileName);
			dto.setStaffId(staffId);
			dto.setRequestNo(requestNo);
			dto.setUniqueFileName(uniqueFileName);
			dto.setGmtCreate(currentDate);
			dto.setStaffName(staffName);
			vo.setId(reduceAndRefundService.saveAttachment(dto));
		}
		vo.setFilename(fileName);
		vo.setUniqueFilename(uniqueFileName);
        vo.setPath(uploadAndNoticeService.getFileUrlFromCache(uniqueFileName));
        vo.setStaffName(staffName);
        vo.setStaffId(staffId);
        vo.setUploadTime(currentDate);
	    return Response.success(vo);
    }


	@ApiOperation(value = Urls.Reduce.GET_ATTACHMENT_DESC, notes = Urls.Reduce.GET_ATTACHMENT_DESC)
	@GetMapping(Urls.Reduce.GET_ATTACHMENT)
	public Response<String> getAttachmentUrl(@RequestParam Long id) {
		return Response.success(reduceAndRefundService.getAttachmentUrl(id));
	}

	@ApiOperation(value = Urls.Reduce.DEL_ATTACHMENT_DESC, notes = Urls.Reduce.DEL_ATTACHMENT_DESC)
	@PostMapping(Urls.Reduce.DEL_ATTACHMENT)
	public Response<Void> deleteAttachment(@RequestParam Long id) {
		reduceAndRefundService.deleteAttachment(id);
		return Response.success();
	}

	@ApiOperation(value = Urls.Reduce.CHECK_DESC, notes = Urls.Reduce.CHECK_DESC)
	@PostMapping(Urls.Reduce.CHECK)
	public Response<List<AllowanceCalculateVO>> reduceCheck(@RequestBody @Validated AppNoBatchDTO dto) {
		return Response.success(reduceAndRefundService.checkReduce(dto.getAppNoList()));
	}

	/**
	 * 计算合并单元格配置
	 */
	private List<CellRangeAddress> getCellRangeAddressList(List<ReduceDetailReportVO> records) {
		if (records.size() > 1) {
			// 先计算每多少条数据需要合并在一起
			// 相同 requestNo 的数据需要合并
			// map 记录每个 requestNo 有多少条记录
			Map<String, Integer> requestNoCountMap = new HashMap<>(records.size(), 1);
			for (ReduceDetailReportVO reduceRecord : records) {
				String requestNo = reduceRecord.getRequestNo();
				Integer count = requestNoCountMap.getOrDefault(requestNo, 0);
				requestNoCountMap.put(requestNo, ++count);
			}

			// 再计算哪几列需要合并
			// 当前报表有9列需要合并
			List<CellRangeAddress> addressList = new ArrayList<>(requestNoCountMap.size() * 9);
			for (int row = 0; row < records.size(); row++) {
				String requestNo = records.get(row).getRequestNo();
				Integer count = requestNoCountMap.get(requestNo);
				if (Objects.nonNull(count) && count > 1) {
					requestNoCountMap.remove(requestNo);
					for (int column = 0; column < 9; column++) {
						addressList.add(new CellRangeAddress(row + 1, row + count, column, column));
					}
				}
			}

			return addressList;
		}
		
		return Collections.emptyList();
	}

}

