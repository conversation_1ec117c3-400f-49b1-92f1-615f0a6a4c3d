package com.welab.crm.operate.web.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.InPhoneLoginInfoMapper;
import com.welab.crm.operate.service.impl.CrmOrgStaffServiceImpl;
import com.welab.crm.operate.service.impl.SoftPhoneCallServiceImpl;
import com.welab.crm.operate.util.Md5Util;
import com.welab.crm.operate.vo.phone.PhoneLoginInfoResVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.webchat.AuthDTO;
import com.welab.crm.operate.web.constants.Urls.webchat;
import com.welab.privacy.util.http.HttpClients;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对接在线网聊服务
 */
@RestController
@Api(description = "对接在线网聊服务")
@RequestMapping(webchat.ROOT)
@Slf4j
public class WebChatController {

    @Value("${webChat.auth.customerId}")
    private String customerId;
    @Value("${webChat.auth.secretKey}")
    private String secretKey;
    @Value("${webChat.auth.url}")
    private String url;

    @Autowired
    private CrmOrgStaffServiceImpl crmOrgStaffService;
    @Autowired
    private SoftPhoneCallServiceImpl softPhoneCallService;


    @PostMapping(value = webchat.V1_AUTH_PARAMS)
    @ApiOperation(value = webchat.V1_AUTH_PARAMS_DESC, notes = webchat.V1_AUTH_PARAMS_DESC)
    public Response<String> getAuthParams(@RequestBody AuthDTO authDTO) {
        if (Objects.isNull(authDTO) || StringUtils.isBlank(authDTO.getMobile())) {
            throw new CrmOperateException("参数异常,手机号不能为空");
        }
        String mobile = authDTO.getMobile();
        // 获取员工信息
        ColStaffResVO colStaff = crmOrgStaffService.getColStaff(mobile);
        String username = colStaff.getEmail();
        String fullName = colStaff.getStaffName();
        String nickName = colStaff.getLoginName();
        // 软电话登陆信息
        PhoneLoginInfoResVO phoneLoginInfo = softPhoneCallService.getPhoneLoginInfoByLoginName(nickName);
        String workNumber = phoneLoginInfo.getCno();

        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = Md5Util.md5(mobile + username + customerId + timestamp + secretKey);
        Map<String, String> map = new HashMap<>();
        map.put("mobile", mobile);
        map.put("username", username);
        map.put("extraField[nickName]", nickName);
        map.put("extraField[workNumber]", workNumber);
        map.put("customerId", customerId);
        map.put("timestamp", timestamp);
        map.put("sign", sign);
        map.put("fullName", fullName);
        try {
            String result = HttpClients.create().setUrl(url).addURLParams(map).doGet();
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if (Objects.nonNull(jsonObject)) {
                    if (jsonObject.containsKey("ret") && jsonObject.getInteger("ret") == 0) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (Objects.nonNull(data)) {
                            return Response.success(data.getString("token"));
                        }
                    } else {
                        return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), jsonObject.getString("message"));
                    }
                }
            }
        } catch (Exception e){
            log.error("getAuthParams 调用创研接口获取在线网聊鉴权参数异常,参数:" + JSON.toJSONString(map), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage());
        }
        return new Response<>();
    }

}
