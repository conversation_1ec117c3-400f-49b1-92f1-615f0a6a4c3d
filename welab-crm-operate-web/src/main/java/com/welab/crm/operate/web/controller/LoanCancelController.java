package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.utils.CollectionUtil;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.operate.dto.loan.LoanCancelApproveDTO;
import com.welab.crm.operate.dto.loan.LoanCancelDTO;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.loan.LoanCancelExcelDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.LoanCancelService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.loan.LoanCancelVO;
import com.welab.crm.operate.web.annotation.ExcelToList;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.BeanParam;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/16
 */
@RestController
@Api(description = "贷款取消服务")
@RequestMapping(Urls.Cancel.ROOT)
public class LoanCancelController {

    @Resource
    private LoanCancelService cancelService;

    /**
     * 查询符合条件的贷款取消列表
     */
    @GetMapping(value = Urls.Cancel.V1_CANCEL_QUERY)
    @ApiOperation(value = Urls.Cancel.V1_CANCEL_QUERY_DESC, notes = Urls.Cancel.V1_CANCEL_QUERY_DESC)
    public Response<Page<LoanCancelVO>> getCancelList(@BeanParam LoanCancelDTO cancelDTO) {
        Page<LoanCancelVO> cancelPage = cancelService.listCancel(cancelDTO);
        return Response.success(cancelPage);
    }

    /**
     * 获取贷款取消导入模板
     */
    @ApiOperation(value = Urls.Cancel.V1_CANCEL_TEMPLATE_DESC, notes = Urls.Cancel.V1_CANCEL_TEMPLATE_DESC)
    @GetMapping(Urls.Cancel.V1_CANCEL_TEMPLATE)
    public Response<Void> getTemplate(HttpServletResponse response) {
        ExcelUtil.getTemplate(LoanCancelExcelDTO.class, response);
        return Response.success();
    }

    /**
     * 导入贷款取消合同号列表
     */
    @ApiOperation(value = Urls.Cancel.V1_CANCEL_IMPORT_DESC, notes = Urls.Cancel.V1_CANCEL_IMPORT_DESC)
    @PostMapping(Urls.Cancel.V1_CANCEL_IMPORT)
    public Response<Void> importCancelList(@ApiIgnore @ExcelToList(value = LoanCancelExcelDTO.class, param = "file")
        ExcelList<LoanCancelExcelDTO> list) {
        if (CollectionUtil.isNull(list)) {
            throw new CrmOperateException("导入贷款号列表数据不能为空");
        }
        cancelService.saveCancelList(CommonUtils.getCurrentlogged(), list);
        return Response.success();
    }

    /**
     * 审批贷款取消申请
     */
    @ApiOperation(value = Urls.Cancel.V1_CANCEL_APPROVE_DESC, notes = Urls.Cancel.V1_CANCEL_APPROVE_DESC)
    @PostMapping(Urls.Cancel.V1_CANCEL_APPROVE)
    public Response<Void> approveLoanCancel(@RequestBody @Validated LoanCancelApproveDTO approveDTO) {
        cancelService.updateApproveState(CommonUtils.getCurrentlogged(), approveDTO);
        return Response.success();
    }

}
