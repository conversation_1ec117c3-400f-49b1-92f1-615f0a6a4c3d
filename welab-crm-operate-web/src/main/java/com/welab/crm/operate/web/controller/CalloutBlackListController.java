package com.welab.crm.operate.web.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.BlackListCheckDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListApprovalDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutBlackListReqDTO;
import com.welab.crm.operate.dto.blacklist.callout.CalloutInterceptionRulesReqDTO;
import com.welab.crm.operate.service.CallOutBlackListService;
import com.welab.crm.operate.service.impl.CallOutBlackListServiceImpl;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.blacklist.callout.CalloutBlackListVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 外呼黑名单接口
 * 
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(value = "外呼黑名单接口")
@RequestMapping(Urls.CalloutBlackList.ROOT)
public class CalloutBlackListController extends BaseController {

    @Resource
    private CallOutBlackListService callOutBlackListService;

    @ApiOperation(value = Urls.CalloutBlackList.ADD_DESC, notes = Urls.CalloutBlackList.ADD_DESC)
    @PostMapping(Urls.CalloutBlackList.ADD)
    public Response<Void>
        addCalloutBlackList(@RequestBody @Validated({BaseReqDTO.GroupInsert.class}) CalloutBlackListReqDTO dto) {
        callOutBlackListService.addCallOutBlackList(dto);
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.QUERY_DESC, notes = Urls.CalloutBlackList.QUERY_DESC)
    @GetMapping(Urls.CalloutBlackList.QUERY)
    public Response<Page<CalloutBlackListVO>> queryCalloutBlackList(@BeanParam CalloutBlackListReqDTO dto) {
        return Response.success(callOutBlackListService.queryCallOutBlackList(dto));
    }

    @ApiOperation(value = Urls.CalloutBlackList.APPROVAL_DESC, notes = Urls.CalloutBlackList.APPROVAL_DESC)
    @PostMapping(Urls.CalloutBlackList.APPROVAL)
    public Response<Void> approvalCalloutBlackList(@RequestBody CalloutBlackListApprovalDTO dto) {
        callOutBlackListService.approvalCalloutBlackList(dto);
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.CANCEL_DESC, notes = Urls.CalloutBlackList.CANCEL_DESC)
    @PostMapping(Urls.CalloutBlackList.CANCEL)
    public Response<Void> cancelBlackList(@RequestBody CalloutBlackListApprovalDTO dto) {
        callOutBlackListService.cancelCalloutBlackList(dto.getIds());
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.EXPORT_ALL_DESC, notes = Urls.CalloutBlackList.EXPORT_ALL_DESC)
    @PostMapping(Urls.CalloutBlackList.EXPORT_ALL)
    public Response<Void> exportAll() throws IOException {
        CalloutBlackListReqDTO dto = new CalloutBlackListReqDTO();
        dto.setCurPage(1);
        dto.setPageSize(500000);
        dto.setApprovalStatus(Arrays.asList(CallOutBlackListServiceImpl.ApprovalStatusEnum.APPROVED.getCode(),
            CallOutBlackListServiceImpl.ApprovalStatusEnum.REFUSE.getCode()));
        CommonUtils.commonExport(baseResponse, "外呼黑名单", CalloutBlackListVO.class,
            callOutBlackListService.queryCallOutBlackList(dto).getRecords());
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.INTERCEPTION_RULE_ADD_DESC,
        notes = Urls.CalloutBlackList.INTERCEPTION_RULE_ADD_DESC)
    @PostMapping(Urls.CalloutBlackList.INTERCEPTION_RULE_ADD)
    public Response<Void> addInterceptionRules(@RequestBody CalloutInterceptionRulesReqDTO dto) {
        callOutBlackListService.addCalloutInterceptionRules(dto);
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.INTERCEPTION_RULE_UPDATE_DESC,
        notes = Urls.CalloutBlackList.INTERCEPTION_RULE_UPDATE_DESC)
    @PostMapping(Urls.CalloutBlackList.INTERCEPTION_RULE_UPDATE)
    public Response<Void> updateInterceptionRules(@RequestBody CalloutInterceptionRulesReqDTO dto) {
        callOutBlackListService.updateCalloutInterceptionRules(dto);
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.INTERCEPTION_RULE_DELETE_DESC,
        notes = Urls.CalloutBlackList.INTERCEPTION_RULE_DELETE_DESC)
    @PostMapping(Urls.CalloutBlackList.INTERCEPTION_RULE_DELETE)
    public Response<Void> deleteInterceptionRules(@RequestBody BatchInfoReqDTO dto) {
        callOutBlackListService.deleteInterceptionRules(dto.getIds());
        return Response.success();
    }

    @ApiOperation(value = Urls.CalloutBlackList.INTERCEPTION_RULE_QUERY_DESC,
        notes = Urls.CalloutBlackList.INTERCEPTION_RULE_QUERY_DESC)
    @PostMapping(Urls.CalloutBlackList.INTERCEPTION_RULE_QUERY)
    public Response<Page<CalloutInterceptionRulesReqDTO>> queryInterceptionRules(@RequestBody BaseReqDTO dto) {
        return Response.success(callOutBlackListService.queryCalloutInterceptionRules(dto));
    }


    @ApiOperation(value = Urls.CalloutBlackList.CHECK_BLACK_LIST_DESC,
            notes = Urls.CalloutBlackList.CHECK_BLACK_LIST_DESC)
    @PostMapping(Urls.CalloutBlackList.CHECK_BLACK_LIST)
    public Response<Boolean> checkIsCallout(@RequestBody BlackListCheckDTO dto) {
        return Response.success(callOutBlackListService.checkBlackList(dto));
    }
    
    
    
}
