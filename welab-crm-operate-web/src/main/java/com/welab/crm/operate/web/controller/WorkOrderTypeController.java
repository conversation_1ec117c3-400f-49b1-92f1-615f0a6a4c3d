package com.welab.crm.operate.web.controller;

import javax.annotation.Resource;

import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.workorder.WorkOrderTypeReqDTO;
import com.welab.crm.operate.service.WorkOrderTypeService;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeVO;
import com.welab.crm.operate.web.constants.Urls.WorkOrderType;
import com.welab.domain.vo.ResponseVo;
import com.welab.label.dto.BaseReqDTO.GroupInsert;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 */
@RestController
@Api(description = "工单分类服务")
@RequestMapping(WorkOrderType.ROOT)
public class WorkOrderTypeController {

	@Resource
	private WorkOrderTypeService workOrderTypeService;

	@PostMapping(WorkOrderType.V1_WORKORDER_TYPE_QUERY)
	@ApiOperation(value = WorkOrderType.V1_WORKORDER_TYPE_QUERY, notes = WorkOrderType.V1_WORKORDER_TYPE_QUERY_DESC)
	public ResponseVo<?> queryWorkOrderTypeList(@Validated @RequestBody WorkOrderTypeReqDTO reqDTO){
		Page<WorkOrderTypeVO> rootList=null;
		try {
			rootList = workOrderTypeService.queryWorkOrderTypeList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}

	@PostMapping(WorkOrderType.V1_WORKORDER_TYPE_ADD)
	@ApiOperation(value = WorkOrderType.V1_WORKORDER_TYPE_ADD, notes = WorkOrderType.V1_WORKORDER_TYPE_ADD_DESC)
	public ResponseVo<?> addWorkOrderType(@Validated({ GroupInsert.class }) @RequestBody WorkOrderTypeReqDTO reqDTO) {
		try {
			workOrderTypeService.addWorkOrderType(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
	
	@PostMapping(WorkOrderType.V1_WORKORDER_TYPE_UPDATE)
	@ApiOperation(value = WorkOrderType.V1_WORKORDER_TYPE_UPDATE, notes = WorkOrderType.V1_WORKORDER_TYPE_UPDATE_DESC)
	public ResponseVo<?> updateWorkOrderType(@RequestBody WorkOrderTypeReqDTO reqDTO) {
		try{
			workOrderTypeService.updateWorkOrderType(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
}
