package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.report.ReportPhoneResultDTO;
import com.welab.crm.operate.service.IReportPhoneResultService;
import com.welab.crm.operate.vo.phone.ReportPhoneResultSummaryVO;
import com.welab.crm.operate.vo.phone.ReportPhoneResultVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/18
 */
@RestController
@RequestMapping(Urls.ReportPhoneResult.ROOT)
@Api(description = "电话小结报表服务")
public class ReportPhoneResultController extends BaseController{

    @Autowired
    private IReportPhoneResultService reportPhoneResultService;

    /**
     * 查询电话小结明细
     * @return
     */
    @GetMapping(Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_DETAIL)
    @ApiOperation(value = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_DETAIL_DESC, notes = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_DETAIL_DESC)
    public Response<Page<ReportPhoneResultVO>> queryDetail(@Validated @ModelAttribute ReportPhoneResultDTO dto){
        Page<ReportPhoneResultVO> result = reportPhoneResultService.queryDetail(dto);
        Response<Page<ReportPhoneResultVO>> response = new Response<Page<ReportPhoneResultVO>>();
        response.setResult(result);
        return response;
    }

    /**
     * 导出查询电话小结明细
     * @return
     */
    @GetMapping(Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_EXCEL_DETAIL)
    @ApiOperation(value = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_EXCEL_DETAIL_DESC, notes = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_EXCEL_DETAIL_DESC)
    public Response<Void> queryDetailExcel(@Validated @ModelAttribute ReportPhoneResultDTO dto) {
        List<ReportPhoneResultVO> list = reportPhoneResultService.queryDetailExcel(dto);
        ExcelUtil.listToExcelAndExport(list, ReportPhoneResultVO.class, baseResponse);
        return Response.success();
    }

    /**
     * 查询电话小结统计
     * @return
     */
    @GetMapping(Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY)
    @ApiOperation(value = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_DESC, notes = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_QUERY_DESC)
    public Response<Page<ReportPhoneResultSummaryVO>> query(@Validated @ModelAttribute ReportPhoneResultDTO dto){
        Page<ReportPhoneResultSummaryVO> result = reportPhoneResultService.query(dto);
        Response<Page<ReportPhoneResultSummaryVO>> response = new Response<Page<ReportPhoneResultSummaryVO>>();
        response.setResult(result);
        return response;
    }

    /**
     * 导出查询电话小结统计
     * @return
     */
    @GetMapping(Urls.ReportPhoneResult.V1_REPORTPHONERESULT_EXCEL_QUERY)
    @ApiOperation(value = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_EXCEL_QUERY_DESC, notes = Urls.ReportPhoneResult.V1_REPORTPHONERESULT_EXCEL_QUERY_DESC)
    public Response<Void> queryExcel(@Validated @ModelAttribute ReportPhoneResultDTO dto) {
        List<ReportPhoneResultSummaryVO> list = reportPhoneResultService.queryExcel(dto);
        ExcelUtil.listToExcelAndExport(list, ReportPhoneResultSummaryVO.class, baseResponse);
        return Response.success();
    }
}
