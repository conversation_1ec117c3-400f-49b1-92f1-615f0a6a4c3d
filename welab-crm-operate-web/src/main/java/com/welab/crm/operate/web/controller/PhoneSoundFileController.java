package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.phone.PhoneSundRecordingDTO;
import com.welab.crm.operate.service.IPhoneSoundRecordingService;
import com.welab.crm.operate.vo.phone.CallInPhoneDetailVO;
import com.welab.crm.operate.vo.screen.CountVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description 获取录音文件
 * @date 2022/2/22
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.PhoneSoundRecording.ROOT)
@Api(description = "获取通话明细")
@Slf4j
public class PhoneSoundFileController {

    @Autowired
    private IPhoneSoundRecordingService phoneSoundRecordingService;

    /**
     * 通话明细
     * @param reqDTO
     * @return
     */
    @PostMapping(Urls.PhoneSoundRecording.V1_QUERY_DETAIL)
    @ApiOperation(value = Urls.PhoneSoundRecording.V1_QUERY_DETAIL_DESC, notes = Urls.PhoneSoundRecording.V1_QUERY_DETAIL_DESC)
    public Response<Page<CallInPhoneDetailVO>> detail(@Validated @RequestBody PhoneSundRecordingDTO reqDTO){
        Response<Page<CallInPhoneDetailVO>> response = new Response<Page<CallInPhoneDetailVO>>();
        response.setResult(phoneSoundRecordingService.queryDetail(reqDTO));
        return response;
    }

    @PostMapping(Urls.PhoneSoundRecording.V1_CONNECTED_COUNT)
    @ApiOperation(value = Urls.PhoneSoundRecording.V1_CONNECTED_COUNT_DESC, notes = Urls.PhoneSoundRecording.V1_CONNECTED_COUNT_DESC)
    public Response<List<CountVO>> count(@Validated @RequestBody PhoneSundRecordingDTO reqDTO){
        return Response.success(phoneSoundRecordingService.queryCount(reqDTO));
    }
}
