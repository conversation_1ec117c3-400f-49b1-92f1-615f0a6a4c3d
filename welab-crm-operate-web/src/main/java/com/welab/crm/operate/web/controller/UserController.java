package com.welab.crm.operate.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.dto.collection.CollectionResModel;
import com.welab.collection.interview.service.IUserCenterService;
import com.welab.collection.interview.vo.complain.CustomerBlackListVO;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.DateUtil;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.UserInfoDTO;
import com.welab.crm.interview.dto.WalletUserDTO;
import com.welab.crm.interview.service.ComplainOrderService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.service.VipService;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.interview.vo.vip.PrivilegeCardVO;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.domain.CsCallOutBlackList;
import com.welab.crm.operate.dto.customer.AddCustReqDTO;
import com.welab.crm.operate.dto.users.UserQueryDTO;
import com.welab.crm.operate.mapper.CsCallOutBlackListMapper;
import com.welab.crm.operate.model.LogOutModel;
import com.welab.crm.operate.service.CallOutBlackListService;
import com.welab.crm.operate.service.CustomerService;
import com.welab.crm.operate.service.ExternalService;
import com.welab.crm.operate.service.impl.CallOutBlackListServiceImpl;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls.User;
import com.wolaidai.approval.model.ApplicationDTO;
import com.wolaidai.approval.service.IApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/12 9:54
 * @module 客服项目
 */


@RestController
@RequestMapping(User.ROOT)
@Api(description = "用户查询服务")
@Slf4j
public class UserController {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private CustomerService customerService;

    @Resource
    private VipService vipService;

    @Resource
    private ComplainOrderService complainOrderService;
    
    @Resource
    private IUserCenterService userCenterService;

    @Autowired
    private IApplicationService applicationService;
    @Autowired
    private ExternalService externalService;

    /*@Resource
    private BlackListService blackListService;*/
    
    @Resource
    private CallOutBlackListServiceImpl callOutBlackListService;

    private static final String PARAMETER_ERROR = "参数不合法";
    private static final String USER_NOT_EXISTS = "用户不存在";
    private static final String USER_BLOCKED = "用户已注销";

    @PostMapping(User.USER_DETAIL_QUERY)
    @ApiOperation(value = User.USER_DETAIL_QUERY, notes = User.USER_DETAIL_QUERY_DESC)
    @LogSaveToDb
    public Response<PersonalDetailsVoExpand> userDetailQuery(@RequestHeader("X-Mobile") String mobile, @RequestBody UserQueryDTO userQueryDTO) {
        log.info("{} 查询客服信息，参数:{}", CommonUtils.getCurrentlogged(), userQueryDTO.toString());
        UserDetailQueryDTO userDetailQueryDTO = new UserDetailQueryDTO();
        try {
            convertToDetailDTO(userDetailQueryDTO, userQueryDTO);
        } catch (Exception e) {
            log.warn("userDetailQuery 参数转换异常", e);
            return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode(), PARAMETER_ERROR, null);
        }
        try {
            PersonalDetailsVoExpand result = userInfoService.queryUserInfo(userDetailQueryDTO);
            if (Objects.nonNull(result) && StringUtils.isNotBlank(result.getMobile())) {
                Long id = customerService.saveUserInfoCash(result);
                result.setId(id);
                result.setMobileMask(SecurityUtil.maskMobile2(result.getMobile()));
                result.setMobile(AesUtils.encrypt(result.getMobile()));
            } else if (Objects.nonNull(result) && result.getBlock()) {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_BLOCKED, null);
            } else {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
            }
            List<CsCallOutBlackList> blackList = callOutBlackListService.queryValidBlackListByUuid(result.getUuid());
            if (CollectionUtils.isNotEmpty(blackList)) {
                CsCallOutBlackList vo = blackList.get(0);
                if (Objects.nonNull(vo) && Objects.nonNull(vo.getValidStartTime()) && Objects.nonNull(vo.getValidEndTime())) {
                    String start = DateUtil.dateToString(vo.getValidStartTime(), DateUtil.TimeFormatter.YYYY_MM_DD);
                    String end = DateUtil.dateToString(vo.getValidEndTime(), DateUtil.TimeFormatter.YYYY_MM_DD);
                    result.setBlackListExpiry(start + "~" + end);
                }
            }
            if(externalService.isSpecificGroup(mobile, com.welab.crm.operate.web.constants.Constant.KEFU_GROUP)){
                result.setSucLoanCount(null);
            }
            return Response.success(result);
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), e.getMessage(), null);
        }
    }

    private void convertToDetailDTO(UserDetailQueryDTO userDetailQueryDTO, UserQueryDTO userQueryDTO) {
        if (Objects.nonNull(userQueryDTO)){
            BeanUtils.copyProperties(userQueryDTO, userDetailQueryDTO, "uuid", "userId");
            if (StringUtils.isNotBlank(userQueryDTO.getUserId())){
                userDetailQueryDTO.setUserId(Integer.valueOf(userQueryDTO.getUserId()));
            }
            if (StringUtils.isNotBlank(userQueryDTO.getUuid())){
                userDetailQueryDTO.setUuid(Long.valueOf(userQueryDTO.getUuid()));
            }
        }
    }

    @PostMapping(User.LOGOUT_USER_QUERY)
    @ApiOperation(value = User.LOGOUT_USER_QUERY, notes = User.LOGOUT_USER_QUERY_DESC)
    public Response<?> logoutUserQuery(@RequestBody UserDetailQueryDTO userDetailQueryDTO) {
        try {
            if (StringUtils.isNotBlank(userDetailQueryDTO.getMobile())) {
                boolean matches = Pattern.matches(Constant.MOBILE, userDetailQueryDTO.getMobile());
                if (!matches) {
                    String mobile = AesUtils.decrypt(userDetailQueryDTO.getMobile());
                    userDetailQueryDTO.setMobile(mobile);
                }
            }
            JSONObject result = userInfoService.queryBlockUserInfo(userDetailQueryDTO.getMobile());
            log.info("logoutUserQuery json result:{}", result.toJSONString());
            LogOutModel model = result.getJSONObject("data").toJavaObject(LogOutModel.class);
            model.setName(SecurityUtil.maskName(model.getName()));
            model.setCnid(SecurityUtil.maskCnid(model.getCnid()));
            model.setMobile(SecurityUtil.maskMobilePro(model.getMobile()));
            model.setId(Long.valueOf(model.getUserId()));
            return Response.success(model);
        } catch (Exception e) {
            log.warn("logoutUserQuery error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

    }

    @PostMapping(User.WALLET_USER_DETAIL_QUERY)
    @ApiOperation(value = User.WALLET_USER_DETAIL_QUERY, notes = User.WALLET_USER_DETAIL_QUERY_DESC)
    @LogSaveToDb
    public Response<WalletUserDTO> walletDetailQuery(@RequestBody UserQueryDTO userQueryDTO) {
        log.info("{} 查询客服信息-钱包，参数:{}", CommonUtils.getCurrentlogged(), userQueryDTO.toString());
        UserDetailQueryDTO userDetailQueryDTO = new UserDetailQueryDTO();
        try {
            convertToDetailDTO(userDetailQueryDTO, userQueryDTO);
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode(), PARAMETER_ERROR, null);
        }
        try {
            WalletUserDTO result = userInfoService.queryWalletUser(userDetailQueryDTO);
            if (Objects.nonNull(result) && Objects.nonNull(result.getBlock())) {
                result.setId(customerService.saveUserInfoWallet(result));
                result.setMobileMask(SecurityUtil.maskMobile2(result.getMobile()));
                result.setMobile(AesUtils.encrypt(result.getMobile()));
            } else if (Objects.nonNull(result) && Objects.nonNull(result.getBlock()) && result.getBlock()) {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_BLOCKED, null);
            } else {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
            }
            List<CsCallOutBlackList> blackList = callOutBlackListService.queryValidBlackListByUuid(result.getUuid());
            if (CollectionUtils.isNotEmpty(blackList)) {
                CsCallOutBlackList vo = blackList.get(0);
                if (Objects.nonNull(vo) && Objects.nonNull(vo.getValidStartTime()) && Objects.nonNull(vo.getValidEndTime())) {
                    String start = DateUtil.dateToString(vo.getValidStartTime(), DateUtil.TimeFormatter.YYYY_MM_DD);
                    String end = DateUtil.dateToString(vo.getValidEndTime(), DateUtil.TimeFormatter.YYYY_MM_DD);
                    result.setBlackListExpiry(start + "~" + end);
                }
            }
            return Response.success(result);
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(User.SAVE_USER_INFO)
    @ApiOperation(value = User.SAVE_USER_INFO, notes = User.SAVE_USER_INFO_DESC)
    public Response<Boolean> saveUserInfoCash(@RequestBody @Validated AddCustReqDTO addCustReqDTO) {
        try {
            return Response.success(customerService.saveUserInfo(addCustReqDTO));
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(User.QUERY_USER_INFO_CASH)
    @ApiOperation(value = User.QUERY_USER_INFO_CASH, notes = User.QUERY_USER_INFO_CASH_DESC)
    public Response<CashCustInfoVO> queryUserCash(@RequestParam("mobile") String mobile) {
        try {
            return Response.success(customerService.queryUserInfoCash(mobile));
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(User.MOBILE_QUERY)
    @ApiOperation(value = User.MOBILE_QUERY, notes = User.MOBILE_QUERY_DESC)
    @LogSaveToDb
    public Response<List<UserInfoDTO>> mobileQuery(@RequestBody UserDetailQueryDTO dto) {
        try {
            List<UserInfoDTO> userInfoDTOS = userInfoService.mobileQuery(dto);
            removeBlockedUser(userInfoDTOS);
            maskProfile(userInfoDTOS);
            if (CollectionUtils.isNotEmpty(userInfoDTOS)) {
                return Response.success(userInfoDTOS);
            } else {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
            }
        } catch (Exception e){
            log.warn("mobileQuery 根据手机号查询客户信息异常", e);
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
        }
    }

    @PostMapping(User.MOBILE_QUERY_MANUAL)
    @ApiOperation(value = User.MOBILE_QUERY_MANUAL_DESC, notes = User.MOBILE_QUERY_MANUAL_DESC)
    @LogSaveToDb
    public Response<List<UserInfoDTO>> mobileQueryManual(@RequestBody UserDetailQueryDTO dto) {
        try {
            List<UserInfoDTO> userInfoDTOS = userInfoService.mobileQuery(dto);
            removeBlockedUser(userInfoDTOS);
            maskProfile(userInfoDTOS);
            if (CollectionUtils.isNotEmpty(userInfoDTOS)) {
                return Response.success(userInfoDTOS);
            } else {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
            }
        } catch (Exception e){
            log.warn("mobileQueryManual 根据手机号查询客户信息异常", e);
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), USER_NOT_EXISTS, null);
        }
    }

    /**
     * 移除已经注销的用户
     */
    private void removeBlockedUser(List<UserInfoDTO> userInfoDTOS) {
        if (CollectionUtils.isEmpty(userInfoDTOS)){
            return;
        }
        userInfoDTOS.removeIf(user -> Objects.nonNull(user) && Boolean.TRUE.equals(user.getBlocked()));
    }



    private void maskProfile(List<UserInfoDTO> userInfoDTOS) {
        if (CollectionUtils.isEmpty(userInfoDTOS)){
            return;
        }
        for (UserInfoDTO userInfoDTO : userInfoDTOS) {
            if (Objects.nonNull(userInfoDTO)){
                if (StringUtils.isNotBlank(userInfoDTO.getCnid())){
                    userInfoDTO.setCnid(SecurityUtil.maskCnid(userInfoDTO.getCnid()));
                }
                if (StringUtils.isNotBlank(userInfoDTO.getMobile())){
                    userInfoDTO.setMobile(SecurityUtil.maskMobile(userInfoDTO.getMobile()));
                }
            }
        }
    }

    @GetMapping(User.CARD_QUERY)
    @ApiOperation(value = User.CARD_QUERY_DESC, notes = User.CARD_QUERY_DESC)
    public Response<PrivilegeCardVO> privilegeCardQuery(@Validated @NotNull Long uuid, @Validated @NotNull Integer userId) {
        try {
            PrivilegeCardVO cardVO = vipService.getUserPrivilegeCardListPlus(uuid, userId);
            return new Response<>(ResponsCodeTypeEnum.SUCCESS.getCode(), ResponsCodeTypeEnum.SUCCESS.getMessage(), cardVO);
        } catch (Exception e) {
            log.warn("getUserPrivilegeCardList exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), ResponsCodeTypeEnum.FAILURE.getMessage(), null);
        }
    }

    @GetMapping(User.BLACK_LIST_QUERY)
    @ApiOperation(value = User.BLACK_LIST_QUERY_DESC, notes = User.BLACK_LIST_QUERY_DESC)
    public Response<List<CustomerBlackListVO>> blackListQuery(@Validated @NotNull String mobile, Integer userId) {
        try {
            boolean matches = Pattern.matches(Constant.MOBILE, mobile);
            if (StringUtils.isNotBlank(mobile) && !matches) {
                String plaintextValue = AesUtils.decrypt(mobile);
                mobile = plaintextValue;
            }
            List<CustomerBlackListVO> blackList = complainOrderService.getUserBlackList(userId, mobile);
            return new Response<>(ResponsCodeTypeEnum.SUCCESS.getCode(), ResponsCodeTypeEnum.SUCCESS.getMessage(), blackList);
        } catch (Exception e) {
            log.warn("blackListQuery exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), ResponsCodeTypeEnum.FAILURE.getMessage(), null);
        }
    }

    /**
     * 解密
     * @param mobile
     * @return
     */
    @GetMapping(User.DECODE)
    @ApiOperation(value = User.DECODE_DESC, notes = User.DECODE_DESC)
    @LogSaveToDb
    public Response<String> decode(@Validated @NotNull String mobile) {
        try {
            String decrypt = AesUtils.decrypt(mobile);
            return Response.success(decrypt);
        } catch (Exception e) {
            log.warn("decode exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), ResponsCodeTypeEnum.FAILURE.getMessage(), null);
        }
    }

    @GetMapping(User.COLLECTIONS)
    @ApiOperation(value = User.COLLECTIONS_DESC, notes = User.COLLECTIONS_DESC)
    public Response<List<CollectionResModel>> collection(@RequestParam(value = "userId")Integer userId, @RequestParam(value ="appNo", required = false)String appNo, @RequestParam(value ="collector", required = false)String collector) {
        try {
            List<CollectionResModel> result = userCenterService.queryAllCollectionsList(userId);
            if (StringUtils.isNotBlank(appNo)) {
                result = result.stream().filter(t -> appNo.equals(t.getAppNo())).collect(Collectors.toList());
            }
            if (StringUtils.isNotBlank(collector)) {
                result = result.stream().filter(i -> collector.equalsIgnoreCase(i.getCollector())).collect(Collectors.toList());
            }
            return Response.success(result);
        } catch (Exception e) {
            log.warn("collection exception: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), ResponsCodeTypeEnum.FAILURE.getMessage(), null);
        }
    }

    @GetMapping(User.APPLICATION_HISTORY)
    @ApiOperation(value = User.APPLICATION_HISTORY, notes = User.APPLICATION_HISTORY_DESC)
    public Response<List<ApplicationDTO>> queryUserApplications(@RequestParam(name = "mobile", required = false) String mobile, @RequestParam(name = "cnid", required = false) String cnid) {
        try {
            if (StringUtils.isBlank(mobile) && StringUtils.isBlank(cnid)) {
                return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode(), PARAMETER_ERROR, null);
            }
            return Response.success(applicationService.findApplicationHistory(mobile, cnid));
        } catch (Exception e) {
            log.info("queryUserApplications error:{}", e.getMessage());
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), e.getMessage(), null);
        }
    }
}
