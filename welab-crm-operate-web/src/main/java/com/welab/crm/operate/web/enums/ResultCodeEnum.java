package com.welab.crm.operate.web.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/22
 */
public enum ResultCodeEnum {
    SUCCESS("SUCCESS", "操作成功"),
    ERROR("ERROR", "操作失败"),
    PARAM_ERROR("PARAM_ERROR", "参数错误"),
    SIGN_ERROR("SIGN_ERROR", "签名错误"),
    TIMED_OUT("TIMED_OUT", "请求超时"),
    REPEAT_SUBMITTED("6666", "请不要重复提交");

    private String code;
    private String msg;

    private ResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public String toString() {
        return this.getCode();
    }
}
