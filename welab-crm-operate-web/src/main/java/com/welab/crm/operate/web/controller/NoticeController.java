package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.dto.notice.NoticeMsgReqDTO;
import com.welab.crm.operate.dto.notice.NoticeReplyReqDTO;
import com.welab.crm.operate.service.NoticeMsgService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.notice.NoticeMsgDetailVO;
import com.welab.crm.operate.vo.notice.NoticeMsgVO;
import com.welab.crm.operate.web.constants.Urls.Notice;
import com.welab.crm.operate.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/10/28 18:08
 */
@RestController
@RequestMapping(Notice.ROOT)
@Api(description = "通知公告服务")
@Slf4j
public class NoticeController {

    @Resource
    private NoticeMsgService noticeMsgService;

    @Resource
    private IUploadService uploadService;

    @PostMapping(Notice.NOTICE_PUBLISH)
    @ApiOperation(value = Notice.NOTICE_PUBLISH, notes = Notice.NOTICE_PUBLISH_DESC)
    public Response<Boolean> noticePublish(@RequestBody NoticeMsgReqDTO dto) {

        boolean result = false;
        try {
            result = noticeMsgService.publishNotice(dto);
        } catch (Exception e) {
            log.error("noticePublish,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
        return Response.success(result);
    }

    @GetMapping(Notice.NOTICE_DELETE)
    @ApiOperation(value = Notice.NOTICE_DELETE, notes = Notice.NOTICE_DELETE_DESC)
    @ApiImplicitParams(
            @ApiImplicitParam(name = "msgId", value = "消息Id", paramType = "query")
    )
    public Response<String> noticeDelete(@RequestParam(value = "msgId") @NotBlank String msgId) {
        try {
            noticeMsgService.deleteNotice(msgId);
        } catch (Exception e) {
            log.error("noticeDelete,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
        return Response.success("删除成功");
    }

    @PostMapping(Notice.NOTICE_QUERY_RECEIVE)
    @ApiOperation(value = Notice.NOTICE_QUERY_RECEIVE, notes = Notice.NOTICE_QUERY_RECEIVE_DESC)
    public Response<Page<NoticeMsgVO>> queryReceive(@RequestBody @Validated NoticeMsgReqDTO dto) {
        try {
            return Response.success(noticeMsgService.queryReceive(dto));
        } catch (Exception e) {
            log.error("queryReceive,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Notice.NOTICE_QUERY_SEND)
    @ApiOperation(value = Notice.NOTICE_QUERY_SEND, notes = Notice.NOTICE_QUERY_SEND_DESC)
    public Response<Page<NoticeMsgVO>> querySend(@RequestBody @Validated NoticeMsgReqDTO dto) {
        try {
            return Response.success(noticeMsgService.querySend(dto));
        } catch (Exception e) {
            log.error("querySend,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(Notice.NOTICE_QUERY_DETAIL)
    @ApiOperation(value = Notice.NOTICE_QUERY_DETAIL, notes = Notice.NOTICE_QUERY_DETAIL_DESC)
    @ApiImplicitParams(
            @ApiImplicitParam(name = "msgId", value = "消息Id", paramType = "query")
    )
    public Response<NoticeMsgDetailVO> queryDetail(@RequestParam(value = "msgId") @NotBlank String msgId) {
        try {
            return Response.success(noticeMsgService.queryMsgDetail(msgId));
        } catch (Exception e) {
            log.error("queryDetail,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Notice.NOTICE_REPLY)
    @ApiOperation(value = Notice.NOTICE_REPLY, notes = Notice.NOTICE_REPLY_DESC)
    public Response<Void> noticeReply(@RequestBody NoticeReplyReqDTO dto) {
        try {
            noticeMsgService.replyMsg(dto);
            return Response.success();
        } catch (Exception e) {
            log.error("noticeReply,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Notice.NOTICE_ATTACHMENT_UPLOAD)
    @ApiOperation(value = Notice.NOTICE_ATTACHMENT_UPLOAD, notes = Notice.NOTICE_ATTACHMENT_UPLOAD_DESC)
    public Response<List<String>> noticeAttachUpload(MultipartFile[] file) {
        try {
            List<MultipartFile> fileList = Arrays.asList(file);
            List<String> fileNameList = new ArrayList<String>();
            if (CollectionUtils.isNotEmpty(fileList)) {
                for (MultipartFile multipartFile : fileList) {
                    byte[] bytes = multipartFile.getBytes();
                    if (!SecurityUtil.checkFileTypeByFileName(multipartFile.getOriginalFilename())){
                        continue;
                    }
                    Response<String> result = uploadService
                            .uploadFile(bytes, CommonUtils.replaceBlank(multipartFile.getOriginalFilename()));
                    fileNameList.add(result.getResult());
                }
            }
            return Response.success(fileNameList);
        } catch (Exception e) {
            log.error("noticeAttachUpload,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD)
    @ApiOperation(value = Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD, notes = Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD_DESC)
    public Response<Map<String, Object>> noticeAttachDownload(@RequestBody List<String> fileNameList) {
        try {
            Map<String, Object> fileMap = uploadService.getUploadFile(fileNameList);
            return Response.success(fileMap);
        } catch (Exception e) {
            log.error("noticeAttachDownload,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD_DIRECT)
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD_DIRECT, notes = Notice.V1_NOTICE_ATTACHMENT_DOWNLOAD_DIRECT_DESC)
    public ResponseEntity<byte[]> noticeAttachDownloadDirect(@RequestParam("fileName") String fileName,
            HttpServletRequest request,
            HttpServletResponse response) {
        return ResponseUtil
                .buildExportResponse(uploadService.getFile(fileName).getResult(), fileName, request, response);
    }

    @GetMapping(Notice.NOTICE_READ)
    @ApiOperation(value = Notice.NOTICE_READ, notes = Notice.NOTICE_READ_DESC)
    @ApiImplicitParams(
            @ApiImplicitParam(name = "msgId", value = "消息Id", paramType = "query")
    )
    public Response<Map<String, Object>> noticeRead(@RequestParam("msgId") String msgId) {
        try {
            noticeMsgService.readMsg(msgId, CommonUtils.getCurrentlogged());
            return Response.success();
        } catch (Exception e) {
            log.error("noticeRead,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


    @PostMapping(Notice.NOTICE_READ_QUERY)
    @ApiOperation(value = Notice.NOTICE_READ_QUERY, notes = Notice.NOTICE_READ_QUERY_DESC)
    public Response<Page<NoticeMsgVO>> queryRead(@RequestBody @Validated NoticeMsgReqDTO dto) {
        try {
            return Response.success(noticeMsgService.queryReadMsg(dto));
        } catch (Exception e) {
            log.error("queryRead,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Notice.NOTICE_UNREAD_QUERY)
    @ApiOperation(value = Notice.NOTICE_UNREAD_QUERY, notes = Notice.NOTICE_UNREAD_QUERY_DESC)
    public Response<Page<NoticeMsgVO>> queryUnRead(@RequestBody @Validated NoticeMsgReqDTO dto) {
        try {
            return Response.success(noticeMsgService.queryUnreadMsg(dto));
        } catch (Exception e) {
            log.warn("queryUnRead,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(Notice.V1_NOTICE_ALL_TYPE_COUNT)
    @ApiOperation(value = Notice.V1_NOTICE_ALL_TYPE_COUNT, notes = Notice.V1_NOTICE_ALL_TYPE_COUNT_DESC)
    public Response<Map<String,Integer>> queryAllCount() {
        try {
            return Response.success(noticeMsgService.queryAllTypeCount());
        } catch (Exception e) {
            log.error("queryAllCount,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(Notice.V1_NOTICE_ALL_READ)
    @ApiOperation(value = Notice.V1_NOTICE_ALL_READ, notes = Notice.V1_NOTICE_ALL_READ_DESC)
    public Response<Void> allRead(@RequestParam("type") String type) {
        try {
            noticeMsgService.allRead(type);
            return Response.success();
        } catch (Exception e) {
            log.error("allRead,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(Notice.V1_NOTICE_BANNER)
    @ApiOperation(value = Notice.V1_NOTICE_BANNER, notes = Notice.V1_NOTICE_BANNER_DESC)
    public Response<List<NoticeMsgVO>> queryBanner() {
        try {
            return Response.success(noticeMsgService.queryAllBanner());
        } catch (Exception e) {
            log.warn("queryAllBanner,error", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }



}
