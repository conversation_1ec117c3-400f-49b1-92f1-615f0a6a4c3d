package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.utils.CollectionUtil;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.SingleApprovalDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionAddReqDTO;
import com.welab.crm.operate.dto.blackProduction.BlackProductionQueryReqDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.BlackProductionService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportFailVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionImportTempVO;
import com.welab.crm.operate.vo.blackProduction.BlackProductionVO;
import com.welab.crm.operate.web.annotation.ExcelToList;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(value = "黑产相关接口", tags = "黑产")
@RequestMapping(Urls.BlackProduction.ROOT)
@Slf4j
public class BlackProductionController extends BaseController {

    @Resource
    private BlackProductionService blackProductionService;

    @PostMapping(value = Urls.BlackProduction.ADD)
    @ApiOperation(value = Urls.BlackProduction.ADD_DESC, notes = Urls.BlackProduction.ADD_DESC)
    public Response<Void> addBlackProductionInfo(
        @RequestBody @Validated({BaseReqDTO.GroupInsert.class}) BlackProductionAddReqDTO reqDTO) {
        blackProductionService.addBlackProduction(reqDTO);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackProduction.QUERY)
    @ApiOperation(value = Urls.BlackProduction.QUERY_DESC, notes = Urls.BlackProduction.QUERY_DESC)
    public Response<Page<BlackProductionVO>> queryBlackProductionInfo(@RequestBody BlackProductionQueryReqDTO reqDTO) {
        return Response.success(blackProductionService.queryBlackProductionUserInfoPage(reqDTO));
    }

    @PostMapping(value = Urls.BlackProduction.EXPORT)
    @ApiOperation(value = Urls.BlackProduction.EXPORT_DESC, notes = Urls.BlackProduction.EXPORT_DESC)
    public Response<Void> exportBlackProductionInfo(@RequestBody BlackProductionQueryReqDTO reqDTO) throws IOException {
        reqDTO.setCurPage(1);
        reqDTO.setPageSize(500000);
        EasyExcelUtils.export(baseResponse,
            blackProductionService.queryBlackProductionUserInfoPage(reqDTO).getRecords(), BlackProductionVO.class,
            "黑产用户数据", "黑产用户数据");
        return Response.success();
    }

    @PostMapping(value = Urls.BlackProduction.TEMPLATE_EXPORT)
    @ApiOperation(value = Urls.BlackProduction.TEMPLATE_EXPORT_DESC, notes = Urls.BlackProduction.TEMPLATE_EXPORT_DESC)
    public Response<Void> exportTemplate() throws IOException {
        //EasyExcelUtils.export(baseResponse, null, BlackProductionImportTempVO.class, "黑产导入模板", "黑产导入模板");
        ExcelUtil.getTemplate(BlackProductionImportTempVO.class, baseResponse);
        return Response.success();
    }

    @PostMapping(value = Urls.BlackProduction.IMPORT)
    @ApiOperation(value = Urls.BlackProduction.IMPORT_DESC, notes = Urls.BlackProduction.IMPORT_DESC)
    public void importBlackProductionUserInfo(@ApiIgnore @ExcelToList(value = BlackProductionImportTempVO.class, param = "file") ExcelList<BlackProductionImportTempVO> list) throws IOException {
        Long staffId = CommonUtils.getCurrentloggedStaffId();
        if (CollectionUtil.isNull(list)) {
            throw new CrmOperateException("导入贷款号列表数据不能为空");
        }
        List<BlackProductionImportFailVO> failVOList = blackProductionService.importBlackProductionVo(list, staffId);
        if (failVOList.size() > 0) {
            ExcelUtil.listToExcelAndExport(failVOList, BlackProductionImportFailVO.class, baseResponse);
        }
        /*EasyExcelFactory.read(file.getInputStream(), BlackProductionImportTempVO.class,
            new PageReadListener<BlackProductionImportTempVO>(
                list -> blackProductionService.importBlackProductionVo(list, staffId)))
            .sheet().doRead();*/
    }

    @PostMapping(value = Urls.BlackProduction.APPROVE)
    @ApiOperation(value = Urls.BlackProduction.APPROVE_DESC, notes = Urls.BlackProduction.APPROVE_DESC)
    public Response<Void> approve(@RequestBody @Validated SingleApprovalDTO dto) {
        blackProductionService.approvalBlackProductionRecord(dto);
        return Response.success();
    }

}
