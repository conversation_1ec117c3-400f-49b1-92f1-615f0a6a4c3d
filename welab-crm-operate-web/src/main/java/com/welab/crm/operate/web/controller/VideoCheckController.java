package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.video.VideoTaskReqDTO;
import com.welab.crm.operate.service.VideoCheckService;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.video.VideoTaskVO;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.crm.operate.web.response.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 视频验证服务
 * 
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(value = "视频验证控制器", tags = "视频验证相关")
@RequestMapping(Urls.Video.ROOT)
public class VideoCheckController extends BaseController{

    @Resource
    private VideoCheckService videoCheckService;

    @PostMapping(Urls.Video.V1_TASK)
    @ApiOperation(value = Urls.Video.V1_TASK_DESC, notes = Urls.Video.V1_TASK_DESC)
    public Response<Page<VideoTaskVO>> queryVideoTask(@RequestBody VideoTaskReqDTO dto) {
        return Response.success(videoCheckService.queryVideoTask(dto));
    }

    @PostMapping(Urls.Video.V1_EXPORT)
    @ApiOperation(value = Urls.Video.V1_EXPORT_DESC, notes = Urls.Video.V1_EXPORT_DESC)
    public Response<Void> exportRecord(@RequestBody @Validated VideoTaskReqDTO dto) throws IOException {
        EasyExcelUtils.export(baseResponse, videoCheckService.queryVideoTasks(dto), VideoTaskVO.class, "录制任务记录", "录制任务详情记录");
        return Response.success();
    }

    @GetMapping(Urls.Video.V1_URL)
    @ApiOperation(value = Urls.Video.V1_URL_DESC, notes = Urls.Video.V1_URL_DESC)
    @ApiImplicitParam(name = "id", value = "视频任务的主键ID", required = true, dataType = "Long", paramType = "path")
    public Response<String> queryVideoTask(@PathVariable("id") Long id) {
        return Response.success(videoCheckService.queryVideoUrl(id));
    }


    @PostMapping(Urls.Video.V1_COLLECT)
    @ApiOperation(value = Urls.Video.V1_COLLECT_DESC, notes = Urls.Video.V1_COLLECT_DESC)
    public Response<Void> collectVideo(@RequestBody VideoTaskReqDTO dto) {
        videoCheckService.collectVideo(dto);
        return Response.success();
    }
}
