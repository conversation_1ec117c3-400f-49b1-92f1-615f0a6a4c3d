package com.welab.crm.operate.web.controller;

import javax.annotation.Resource;

import com.welab.crm.operate.util.CommonUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.monitor.StaffMonitorDTO;
import com.welab.crm.operate.service.StaffMonitorReportService;
import com.welab.crm.operate.vo.monitor.StaffMonitorReportVO;
import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;

/**
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@Api(value = "员工监控相关接口")
@RequestMapping(Urls.StaffMonitor.ROOT)
public class StaffMonitorController extends BaseController {

    @Resource
    private StaffMonitorReportService reportService;

    @ApiOperation(value = Urls.StaffMonitor.REPORT_QUERY_DESC, notes = Urls.StaffMonitor.REPORT_QUERY_DESC)
    @PostMapping(Urls.StaffMonitor.REPORT_QUERY)
    public Response<Page<StaffMonitorReportVO>> queryReport(@RequestBody @Validated StaffMonitorDTO dto) {
        return Response.success(reportService.queryMonitorReportPage(dto));
    }

    @ApiOperation(value = Urls.StaffMonitor.REPORT_EXPORT_DESC, notes = Urls.StaffMonitor.REPORT_EXPORT_DESC)
    @PostMapping(Urls.StaffMonitor.REPORT_EXPORT)
    public Response<Void> exportReport(@RequestBody @Validated StaffMonitorDTO dto) throws IOException {

        CommonUtils.commonExport(baseResponse, "监控平台用户报表", StaffMonitorReportVO.class,
            reportService.queryMonitorReportList(dto));
        return new Response<>();
    }

}
