package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.bankcard.BankCardMatchDTO;
import com.welab.crm.interview.service.BankCardService;
import com.welab.crm.interview.vo.bankcard.BankCardUserVO;
import com.welab.crm.interview.vo.bankcard.BankChannelVO;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.dto.bankcard.BankCardChangeDTO;
import com.welab.crm.operate.dto.bankcard.BankCardReleaseAuthDTO;
import com.welab.crm.operate.dto.bankcard.BankCardUnbindReqDTO;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.BankCard;
import com.welab.exception.FastRuntimeException;
import com.welab.finance.bankcard.dto.BankCardUnbindDTO;
import com.welab.finance.bankcard.dto.BankCardVerifyDTO;
import com.welab.finance.bankcard.vo.BankCardVerifyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/22
 * @module 客服项目
 */
@RestController
@Api(description = "银行卡服务")
@RequestMapping(BankCard.ROOT)
@Slf4j
public class BankCardController {

    @Resource
    private BankCardService bankCardService;

    @Resource
    private CustOperateService custOperateService;

    @GetMapping(value = BankCard.V1_BANK_CARD_LIST)
    @ApiOperation(value = BankCard.V1_BANK_CARD_LIST_DESC, notes = BankCard.V1_BANK_CARD_LIST_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "客户userId", paramType = "query")
    })
    public Response<BankCardUserVO> getUserBankCardInfo(@RequestParam @NotNull Integer userId) {
        return Response.success(bankCardService.getUserBankCardInfo(userId));
    }

    @GetMapping(value = BankCard.V1_BANK_CARD_MATCH)
    @ApiOperation(value = BankCard.V1_BANK_CARD_MATCH_DESC, notes = BankCard.V1_BANK_CARD_MATCH_DESC)
    public Response<List<BankChannelVO>> getUserBankCardInfo(@ModelAttribute @Validated BankCardMatchDTO dto) {
        try {
            List<BankChannelVO> channelVOS = bankCardService.bankCardMatch(dto);
            return Response.success(channelVOS);
        } catch (Exception e) {
            log.warn("getUserBankCardInfo exception: {}", e.getMessage(), e);
            // 避免调用资金接口capitalMatchDubboService.capitalMatchForecast频繁超时使得用户体检太差
            return Response.success();
        }
    }

    @PostMapping(value = BankCard.V1_BANK_CARD_CHANGE)
    @ApiOperation(value = BankCard.V1_BANK_CARD_CHANGE_DESC, notes = BankCard.V1_BANK_CARD_CHANGE_DESC)
    public Response<Void> change(@RequestBody @Validated BankCardChangeDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        bankCardService.changeBankCard(staffVO.getStaffMobile(), Long.valueOf(dto.getBankCardId()));
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.BANK_CARD_CHANGE.getCode());
        return Response.success();
    }

    @PostMapping(value = BankCard.V1_BANK_CARD_RELEASE)
    @ApiOperation(value = BankCard.V1_BANK_CARD_RELEASE_DESC, notes = BankCard.V1_BANK_CARD_RELEASE_DESC)
    public String release(@RequestBody @Validated BankCardReleaseAuthDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        String bankName = checkBankName(dto.getBankName());
        dto.setComment(bankName);
        String result = bankCardService.releaseBankCardAuth(dto.getUserId(), dto.getBankName());
        log.info("release result:{}", result);
        custOperateService.saveOperationHistory(dto, staffVO,
            OperateTypeEnum.QUOTA_WRITE_OFF.getCode());
        return result;
    }

    @PostMapping(value = BankCard.V1_BANK_OPEN_UNPIN)
    @ApiOperation(value = BankCard.V1_BANK_OPEN_UNPIN_DESC, notes = BankCard.V1_BANK_OPEN_UNPIN_DESC)
    public Response<Object> openUnpin(@RequestBody @Validated BankCardUnbindReqDTO dto, @ApiIgnore @GetStaff StaffVO staffVO) {
        dto.setAccountNo(AesUtils.decrypt(dto.getAccountNo()));
        BankCardUnbindDTO unbindDTO = new BankCardUnbindDTO();
        BeanUtils.copyProperties(dto, unbindDTO);
        Response<Object> response = bankCardService.openUnbind(unbindDTO);
        saveBankCardOpenUnpinHistory(dto, staffVO);
        if (Response.isSuccess(response)){
            return new Response<>();
        } else {
            throw new FastRuntimeException(response.getMessage());
        }
    }

    @PostMapping(value = BankCard.V1_BANK_CHANNEL_VERIFY)
    @ApiOperation(value = BankCard.V1_BANK_CHANNEL_VERIFY_DESC, notes = BankCard.V1_BANK_CHANNEL_VERIFY_DESC)
    public Response<List<BankCardVerifyVO>> channelVerify(@RequestBody BankCardVerifyDTO dto) {
        dto.setAccountNo(AesUtils.decrypt(dto.getAccountNo()));
        return bankCardService.getChannelVerify(dto);
    }

    private void saveBankCardOpenUnpinHistory(BankCardUnbindReqDTO dto, StaffVO staffVO) {
        HistoryOperationDTO historyOperationDTO = new HistoryOperationDTO();
        historyOperationDTO.setUserId(Math.toIntExact(dto.getUserId()));
        historyOperationDTO.setApplicationId(dto.getAccountNo());
        historyOperationDTO.setCustomerId(dto.getCustomerId());
        custOperateService.saveOperationHistory(historyOperationDTO, staffVO, OperateTypeEnum.BANK_CARD_OPEN_UNPIN.getCode());
    }

    /**
     * 检查银行名称是否合法
     * 通过查询字典信息，判断给定的银行名称是否存在于指定的字典集合中
     * 如果银行名称不合法，则抛出异常
     *
     * @param bankName 需要检查的银行名称
     * @throws IllegalArgumentException 如果银行名称不合法，则抛出此异常
     */
    private String checkBankName(String bankName) {
        // 确保bankName不为空
        if (bankName == null || bankName.trim().isEmpty()) {
            throw new IllegalArgumentException("银行名称不能为空");
        }

        // 从字典中获取银行名称列表并转换为Set以提高查找效率
        Set<String> dictSet = Objects.requireNonNull(CommonUtils.getDict("quote_block_bank", null)).stream()
                .map(OpDictInfo::getType)
                .collect(Collectors.toSet());

        // 检查给定的银行名称是否存在于字典列表中
        if (!dictSet.contains(bankName)) {
            throw new IllegalArgumentException("银行名称 '" + bankName + "' 不合法");
        }
        return Objects.requireNonNull(CommonUtils.getDict("quote_block_bank", bankName)).get(0).getContent();
    }

}
