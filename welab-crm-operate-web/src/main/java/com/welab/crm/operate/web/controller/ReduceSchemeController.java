package com.welab.crm.operate.web.controller;

import com.welab.collection.interview.service.ReduceSchemeService;
import com.welab.collection.interview.vo.reduceScheme.ReduceSchemeVO;
import com.welab.common.utils.DateUtil;
import com.welab.crm.operate.vo.reduce.KfReduceSchemeVO;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.domain.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@RestController
@Validated
@Slf4j
@Api(value = "ReduceSchemeController", tags = "减免方案")
@RequestMapping(Urls.ReduceScheme.ROOT)
public class ReduceSchemeController extends BaseController {

	@Resource
	private ReduceSchemeService reduceSchemeService;
	

	@ApiOperation(value = Urls.ReduceScheme.QUERY_DESC, notes = Urls.ReduceScheme.QUERY_DESC)
	@GetMapping(value = Urls.ReduceScheme.QUERY)
	public ResponseVo<List<KfReduceSchemeVO>> queryScheme(@RequestParam Integer userId) {
		List<ReduceSchemeVO> reduceSchemeVOS = reduceSchemeService.queryCollectionReduceScheme(userId);
		List<KfReduceSchemeVO> resultList = new ArrayList<>();
		reduceSchemeVOS.forEach(item -> {
			KfReduceSchemeVO schemeVO = new KfReduceSchemeVO();
			BeanUtils.copyProperties(item, schemeVO);
			if (StringUtils.isNotBlank(item.getValidTime()) && item.getValidTime().compareTo(DateUtil.dateToString(new Date(), DateUtil.TimeFormatter.YYYY_MM_DD)) < 0){
				schemeVO.setBackgroundColor("grey");
			}
			resultList.add(schemeVO);
		});
		return new ResponseVo<>(resultList);
	}


}
