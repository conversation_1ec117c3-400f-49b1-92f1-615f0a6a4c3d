package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.interview.dto.PageQueryDTO;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.dto.telemarketing.*;
import com.welab.crm.operate.mapper.DataCustomerMapper;
import com.welab.crm.operate.service.impl.TmkTaskServiceImpl;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.telemarketing.*;
import com.welab.crm.operate.vo.tmkReport.StaffEfficiencyVO;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls.Telemarketing;
import com.welab.crm.operate.web.response.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 电销任务服务接口
 * @date 2022/2/22 11:14
 * @module 客服项目
 */
@RestController
@Api(description = "电销任务服务接口")
@RequestMapping(Telemarketing.ROOT)
@Slf4j
public class TmkTaskController extends BaseController {

    @Resource
    private TmkTaskServiceImpl tmkTaskService;
    @Resource
    private DataCustomerMapper customerMapper;


    @PostMapping(value = Telemarketing.V1_TASK_LIST)
    @ApiOperation(value = Telemarketing.V1_TASK_LIST_DESC, notes = Telemarketing.V1_TASK_LIST_DESC)
    @NoRepeatSubmit
    public Response<Page<TmkTaskDetailVO>> queryTmkTaskList(@RequestBody @Validated TmkReqDTO tmkReqDTO) {
        try {
            tmkReqDTO.setQueryType("L");
            return Response.success(tmkTaskService.queryTmkTask(tmkReqDTO));
        } catch (Exception e) {
            log.error("queryTmkTaskList 查询电销列表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


    @PostMapping(value = Telemarketing.V1_TASK_DETAIL)
    @ApiOperation(value = Telemarketing.V1_TASK_DETAIL_DESC, notes = Telemarketing.V1_TASK_DETAIL_DESC)
    public Response<TmkTaskDetailVO> queryTmkTaskDetail(@RequestBody TmkReqDTO tmkReqDTO) {
        try {
            setPageParams(tmkReqDTO);
            Page<TmkTaskDetailVO> tmkTaskDetailVOS = tmkTaskService.queryTmkTask(tmkReqDTO);
            if (Objects.nonNull(tmkTaskDetailVOS) && CollectionUtils.isNotEmpty(tmkTaskDetailVOS.getRecords())) {
                TmkTaskDetailVO vo = tmkTaskDetailVOS.getRecords().get(0);
                vo.setMobileMask(SecurityUtil.maskMobile2(vo.getMobile()));
                vo.setMobile(AesUtils.encrypt(vo.getMobile()));
                return Response.success(vo);
            } else {
                return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), "未查询出可用数据,请检查请求参数是否正常", null);
            }
        } catch (Exception e) {
            log.warn("queryTmkTaskDetail 查询电销详情异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    private void setPageParams(TmkReqDTO tmkReqDTO) {
        if (Objects.nonNull(tmkReqDTO)) {
            tmkReqDTO.setPageSize(10);
            tmkReqDTO.setCurPage(1);
            tmkReqDTO.setQueryType("D");
        }
    }

    @PostMapping(value = Telemarketing.V1_TASK_COUNT)
    @ApiOperation(value = Telemarketing.V1_TASK_COUNT_DESC, notes = Telemarketing.V1_TASK_COUNT_DESC)
    @NoRepeatSubmit
    public Response<Map<String, Long>> queryTmkTaskCount(@RequestBody TmkReqDTO tmkReqDTO) {
        try {
            return Response.success(tmkTaskService.queryCountByType(tmkReqDTO));
        } catch (Exception e) {
            log.error("queryTmkTaskCount 查询新单过期单数量异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(value = Telemarketing.V1_TASK_APPOINT)
    @ApiOperation(value = Telemarketing.V1_TASK_APPOINT_DESC, notes = Telemarketing.V1_TASK_APPOINT_DESC)
    public Response<Void> appointTmkTask(@RequestBody @Validated TmkAppointReqDTO tmkAppointReqDTO) {
        try {
            tmkTaskService.appoint(tmkAppointReqDTO);
            return Response.success();
        } catch (Exception e) {
            log.error("appointTmkTask 电销预约回电异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(value = Telemarketing.V1_SUMMARY_SAVE)
    @ApiOperation(value = Telemarketing.V1_SUMMARY_SAVE_DESC, notes = Telemarketing.V1_SUMMARY_SAVE_DESC)
    @LogSaveToDb
    public Response<Void> saveSummary(@RequestBody TmkSummaryReqDTO dto) {
        try {
            tmkTaskService.saveTmkSummary(dto);
            return Response.success();
        } catch (Exception e) {
            log.error("saveSummary 保存电销小结异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_CALL_OUT_REPORT)
    @ApiOperation(value = Telemarketing.V1_CALL_OUT_REPORT_DESC, notes = Telemarketing.V1_CALL_OUT_REPORT_DESC)
    public Response<List<TmkStaffWorkInfo>> tmkCallOutReport(@BeanParam @Validated TmkReportBaseReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkCallInfo(dto));
        } catch (Exception e) {
            log.error("tmkCallOutReport 查询电销外呼报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_TRANSFORM_REPORT)
    @ApiOperation(value = Telemarketing.V1_TRANSFORM_REPORT_DESC, notes = Telemarketing.V1_TRANSFORM_REPORT_DESC)
    public Response<List<TmkTransformReportVO>> tmkTransformReport(@BeanParam @Validated TmkTransformReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkTransformData(dto));
        } catch (Exception e) {
            log.error("tmkTransformReport 查询电销转化报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_CALL_OUT_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_CALL_OUT_REPORT_EXPORT_DESC, notes = Telemarketing.V1_CALL_OUT_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkCalloutReport(@BeanParam @Validated TmkReportBaseReqDTO reqDTO) {
        try {
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkCallInfo(reqDTO), TmkStaffWorkInfo.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkCalloutReport 导出电销外呼统计报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_TRANSFORM_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_TRANSFORM_REPORT_EXPORT_DESC, notes = Telemarketing.V1_TRANSFORM_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkTransformExport(@BeanParam @Validated TmkTransformReportReqDTO reqDTO) {
        try {
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkTransformData(reqDTO), TmkTransformReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkTransformExport 导出电销转化报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_AI_PUSH_REPORT)
    @ApiOperation(value = Telemarketing.V1_AI_PUSH_REPORT_DESC, notes = Telemarketing.V1_AI_PUSH_REPORT_DESC)
    public Response<Page<AiPushReportVO>> aiPushReport(@BeanParam @Validated TmkReportBaseReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryAiPushDetailReport(dto));
        } catch (Exception e) {
            log.error("aiPushReport 查询ai外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_AI_PUSH_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_AI_PUSH_REPORT_EXPORT_DESC, notes = Telemarketing.V1_AI_PUSH_REPORT_EXPORT_DESC)
    public Response<Void> aiPushReportExport(@BeanParam @Validated TmkReportBaseReqDTO dto) {
        try {
            setPages(dto);
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryAiPushDetailReport(dto).getRecords(), AiPushReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("aiPushReport 查询ai外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_LOAN_REPORT)
    @ApiOperation(value = Telemarketing.V1_LOAN_REPORT, notes = Telemarketing.V1_LOAN_REPORT_DESC)
    public Response<Page<TmkLoanReportVO>> tmkLoanReport(@BeanParam @Validated TmkTransformReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkLoanData(dto));
        } catch (Exception e) {
            log.error("tmkLoanReport 查询放款邀约外呼营销结果小结表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_LOAN_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_LOAN_REPORT_EXPORT, notes = Telemarketing.V1_LOAN_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkLoanExport(@BeanParam @Validated TmkTransformReportReqDTO reqDTO) {
        try {
            setPages(reqDTO);
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkLoanData(reqDTO).getRecords(), TmkLoanReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkLoanExport 导出放款邀约外呼营销结果小结表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_CJHY_REPORT)
    @ApiOperation(value = Telemarketing.V1_CJHY_REPORT, notes = Telemarketing.V1_CJHY_REPORT_DESC)
    public Response<Page<TmkCjhyReportVO>> tmkCjhyReport(@BeanParam @Validated TmkTransformReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkCjhyData(dto));
        } catch (Exception e) {
            log.error("tmkCjhyReport 查询电销超级会员外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_CJHY_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_CJHY_REPORT_EXPORT, notes = Telemarketing.V1_CJHY_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkCjhyExport(@BeanParam @Validated TmkTransformReportReqDTO reqDTO) {
        try {
            setPages(reqDTO);
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkCjhyData(reqDTO).getRecords(), TmkCjhyReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkCjhyExport 导出电销超级会员外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_WALLET_REPORT)
    @ApiOperation(value = Telemarketing.V1_WALLET_REPORT, notes = Telemarketing.V1_WALLET_REPORT_DESC)
    public Response<Page<TmkWalletReportVO>> tmkWalletReport(@BeanParam @Validated TmkTransformReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkWalletData(dto));
        } catch (Exception e) {
            log.error("tmkWalletReport 查询电销钱夹谷谷营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_WALLET_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_WALLET_REPORT_EXPORT, notes = Telemarketing.V1_WALLET_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkWalletExport(@BeanParam @Validated TmkTransformReportReqDTO reqDTO) {
        try {
            setPages(reqDTO);
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkWalletData(reqDTO).getRecords(), TmkWalletReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkWalletExport 导出电销钱夹谷谷营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_UUID_REPORT)
    @ApiOperation(value = Telemarketing.V1_UUID_REPORT, notes = Telemarketing.V1_UUID_REPORT_DESC)
    public Response<Page<TmkUuidReportVO>> tmkUuidReport(@BeanParam @Validated TmkTransformReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkUuidData(dto));
        } catch (Exception e) {
            log.error("tmkUuidReport 查询电销UUID外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_UUID_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_UUID_REPORT_EXPORT, notes = Telemarketing.V1_UUID_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkUuidExport(@BeanParam @Validated TmkTransformReportReqDTO reqDTO) {
        try {
            setPages(reqDTO);
            List<TmkUuidReportVO> records = tmkTaskService.queryTmkUuidData(reqDTO).getRecords();
            CommonUtils.commonExport(baseResponse, "电销UUID报表", TmkUuidReportVO.class, records);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkUuidExport 导出电销UUID外呼营销小结报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_ASSIGN_DETAIL_REPORT)
    @ApiOperation(value = Telemarketing.V1_ASSIGN_DETAIL_REPORT, notes = Telemarketing.V1_ASSIGN_DETAIL_REPORT_DESC)
    public Response<Page<TmkAssignDetailReportVO>> tmkAssignDetailReport(@BeanParam @Validated TmkAssignDetailReportReqDTO dto) {
        try {
            return Response.success(tmkTaskService.queryTmkAssignDetailData(dto));
        } catch (Exception e) {
            log.error("tmkTransformReport 查询电销名单分配明细报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_ASSIGN_DETAIL_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_ASSIGN_DETAIL_REPORT_EXPORT, notes = Telemarketing.V1_ASSIGN_DETAIL_REPORT_EXPORT_DESC)
    public Response<Void> exportTmkAssignDetailExport(@BeanParam @Validated TmkAssignDetailReportReqDTO reqDTO) {
        try {
            setPages(reqDTO);
            ExcelUtil.listToExcelAndExport(tmkTaskService.queryTmkAssignDetailData(reqDTO).getRecords(), TmkAssignDetailReportVO.class, baseResponse);
            return Response.success();
        } catch (Exception e) {
            log.error("exportTmkTransformExport 导出电销名单分配明细报表异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @GetMapping(value = Telemarketing.V1_UUID_PACKAGE_QUERY)
    @ApiOperation(value = Telemarketing.V1_UUID_PACKAGE_QUERY_DESC, notes = Telemarketing.V1_UUID_PACKAGE_QUERY_DESC)
    public Response<List<String>> queryPackage() {
        return Response.success(tmkTaskService.queryUuidPackageName());
    }


    @GetMapping(value = Telemarketing.V1_STAFF_EFFICIENCY_REPORT)
    @ApiOperation(value = Telemarketing.V1_STAFF_EFFICIENCY_REPORT_DESC, notes = Telemarketing.V1_STAFF_EFFICIENCY_REPORT_DESC)
    public Response<List<StaffEfficiencyVO>> queryStaffEfficiency(@BeanParam @Validated TmkReportBaseReqDTO reqDTO) {
        return Response.success(tmkTaskService.queryStaffEfficiencyReportRealTime(reqDTO));
    }


    @GetMapping(value = Telemarketing.V1_STAFF_EFFICIENCY_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_STAFF_EFFICIENCY_REPORT_EXPORT_DESC, notes = Telemarketing.V1_STAFF_EFFICIENCY_REPORT_EXPORT_DESC)
    public Response<Void> exportStaffEfficiency(@BeanParam @Validated TmkReportBaseReqDTO reqDTO) {
        ExcelUtil.listToExcelAndExport(tmkTaskService.queryStaffEfficiencyReportRealTime(reqDTO),
                StaffEfficiencyVO.class, baseResponse);
        return Response.success();
    }


    @GetMapping(value = Telemarketing.V1_AI_TRANSFORM_REPORT)
    @ApiOperation(value = Telemarketing.V1_AI_TRANSFORM_REPORT_DESC, notes = Telemarketing.V1_AI_TRANSFORM_REPORT_DESC)
    public Response<List<AiTransformReportVO>> aiTransformReport(@BeanParam @Validated TmkReportBaseReqDTO reqDTO) {
        return Response.success(tmkTaskService.queryAiTransformReport(reqDTO));
    }

    @GetMapping(value = Telemarketing.V1_AI_TRANSFORM_REPORT_EXPORT)
    @ApiOperation(value = Telemarketing.V1_AI_TRANSFORM_REPORT_EXPORT_DESC, notes = Telemarketing.V1_AI_TRANSFORM_REPORT_EXPORT_DESC)
    public Response<Void> exportAiTransformReport(@BeanParam @Validated TmkReportBaseReqDTO reqDTO) {
        ExcelUtil.listToExcelAndExport(tmkTaskService.queryAiTransformReport(reqDTO), AiTransformReportVO.class,
                baseResponse);
        return Response.<Void>builder().build();
    }

    @GetMapping(value = Telemarketing.V1_AI_CONFIG_ALL)
    @ApiOperation(value = Telemarketing.V1_AI_CONFIG_ALL_DESC, notes = Telemarketing.V1_AI_CONFIG_ALL_DESC)
    public Response<List<AiTmkConfigVO>> queryAllAiConfig() {
        return Response.success(tmkTaskService.queryAllAiConfig());
    }

    private void setPages(PageQueryDTO reqDTO){
        reqDTO.setCurrentPage(1);
        reqDTO.setRowsPerPage(500000);
    }
}
