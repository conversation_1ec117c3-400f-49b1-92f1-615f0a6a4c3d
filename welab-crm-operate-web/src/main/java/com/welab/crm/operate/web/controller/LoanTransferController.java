package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.common.utils.CollectionUtil;
import com.welab.crm.base.excel.ExcelList;
import com.welab.crm.base.excel.ExcelUtil;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.domain.LoanTransferAttachment;
import com.welab.crm.operate.dto.dict.DictInfoReqDTO;
import com.welab.crm.operate.dto.loan.*;
import com.welab.crm.operate.enums.ApproveStateEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.ICrmDictInfoService;
import com.welab.crm.operate.service.LoanTransferService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.dict.DictInfoResVO;
import com.welab.crm.operate.vo.loan.*;
import com.welab.crm.operate.web.annotation.ExcelToList;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.BeanParam;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/9
 * @module 客服项目
 */
@RestController
@Api(description = "债转结清服务")
@RequestMapping(Urls.Transfer.ROOT)
public class LoanTransferController extends BaseController{

    @Resource
    private LoanTransferService transferService;

    @Resource
    private IUploadService uploadService;

    @Resource
    private ICrmDictInfoService dictInfoService;


    /**
     * 查询符合条件的债转结清列表
     */
    @GetMapping(value = Urls.Transfer.V1_TRANSFER_QUERY)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_QUERY_DESC, notes = Urls.Transfer.V1_TRANSFER_QUERY_DESC)
    public Response<Page<LoanTransferVO>> getTransferList(@BeanParam LoanTransferDTO transferDTO) {
        Page<LoanTransferVO> transferPage = transferService.listTransfer(transferDTO);
        return Response.success(transferPage);
    }

    /**
     * 获取债转结清导入模板
     */
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_TEMPLATE_DESC, notes = Urls.Transfer.V1_TRANSFER_TEMPLATE_DESC)
    @GetMapping(Urls.Transfer.V1_TRANSFER_TEMPLATE)
    public Response<Void> getTemplate(HttpServletResponse response) {
        ExcelUtil.getTemplate(LoanTransferExcelDTO.class, response);
        return Response.success();
    }

    /**
     * 导入债转结清合同号列表
     */
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_IMPORT_DESC, notes = Urls.Transfer.V1_TRANSFER_IMPORT_DESC)
    @PostMapping(Urls.Transfer.V1_TRANSFER_IMPORT)
    public Response<Void> importContractNoList(@ApiIgnore @ExcelToList(value = LoanTransferExcelDTO.class, param = "file")
                                                       ExcelList<LoanTransferExcelDTO> list) {
        if (CollectionUtil.isNull(list)) {
            throw new CrmOperateException("导入债转结清合同列表数据不能为空");
        }
        Long id = transferService.saveContractNoList(CommonUtils.getCurrentlogged(), list);
        // 直接审批通过并推送数据
        LoanTransferIdDTO stateDTO = new LoanTransferIdDTO();
        stateDTO.setId(id);
        stateDTO.setApproveState(ApproveStateEnum.AGREE.getValue());
        transferService.updateApproveState(CommonUtils.getCurrentlogged(), stateDTO);
        transferService.pushLoanDataAsync(stateDTO);
        return Response.success();
    }

    /**
     * 查询贷款详情
     */
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_DETAIL_DESC, notes = Urls.Transfer.V1_TRANSFER_DETAIL_DESC)
    @GetMapping(Urls.Transfer.V1_TRANSFER_DETAIL)
    public Response<Page<LoanContactVO>> getTransferDetail(@BeanParam LoanTransferIdDTO contractDTO) {
        Page<LoanContactVO> contactPage = transferService.listDetails(contractDTO);
        return Response.success(contactPage);
    }

    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_DETAIL_EXPORT_DESC, notes = Urls.Transfer.V1_TRANSFER_DETAIL_EXPORT_DESC)
    @GetMapping(Urls.Transfer.V1_TRANSFER_DETAIL_EXPORT)
    public Response<Void> exportTransferDetail(@BeanParam LoanTransferIdDTO contractDTO) {
        contractDTO.setCurPage(1);
        contractDTO.setPageSize(500000);
        Page<LoanContactVO> contactPage = transferService.listDetails(contractDTO);
        ExcelUtil.listToExcelAndExport(contactPage.getRecords(), LoanContactVO.class, baseResponse);
        return Response.success();
    }

    /**
     * 上传债转结清相关的附件
     */
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_UPLOAD_DESC, notes = Urls.Transfer.V1_TRANSFER_UPLOAD_DESC)
    @PostMapping(Urls.Transfer.V1_TRANSFER_UPLOAD)
    public Response<Void> uploadAttachment(MultipartFile[] file, Long id) throws IOException {
        if (id == null || file == null || file.length == 0) {
            return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode());
        }
        List<MultipartFile> fileList = Arrays.asList(file);
        // 收集已上传附件路径的实体对象列表
        List<LoanTransferAttachment> attachments = new ArrayList<>();
        String operator = CommonUtils.getCurrentlogged();
        if (CollectionUtils.isNotEmpty(fileList)) {
            for (MultipartFile multipartFile : fileList) {
                byte[] bytes = multipartFile.getBytes();
                // 检查上传的文件类型
                checkFileType(multipartFile.getOriginalFilename());
                String originFileName = CommonUtils.replaceBlank(multipartFile.getOriginalFilename());
                // 附件上传到阿里云oss服务
                Response<String> result = uploadService.uploadFile(bytes, originFileName);
                if (!Objects.equals(Response.success().getCode(), result.getCode())) {
                    // 如果附件上传失败直接返回错误
                    return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), result.getResult(), null);
                }
                LoanTransferAttachment attachment = new LoanTransferAttachment();
                attachment.setAttachmentName(originFileName);
                attachment.setFilePath(result.getResult());
                attachment.setTransferId(id);
                attachment.setCreateUser(operator);
                attachments.add(attachment);
            }
        }
        if (!attachments.isEmpty()) {
            transferService.saveAttachments(operator, attachments);
        }
        return Response.success();
    }

    /**
     * 下载附件,附件列表数量有限不需要分页
     */
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_DOWNLOAD_DESC, notes = Urls.Transfer.V1_TRANSFER_DOWNLOAD_DESC)
    @GetMapping(Urls.Transfer.V1_TRANSFER_DOWNLOAD)
    public Response<List<LoanTranAttachmentVO>> downloadAttachments(@BeanParam @Validated LoanTransferIdDTO idDTO) {
        List<LoanTranAttachmentVO> attachments = transferService.listAttachments(idDTO.getId());
        return Response.success(attachments);
    }

    private void checkFileType(String fileName) {
        List<String> names = Arrays.asList(fileName.split("\\."));
        if (names.size() == 1) {
            throw new CrmOperateException("上传文件缺少文件后缀名");
        }
        DictInfoReqDTO reqDTO = new DictInfoReqDTO();
        reqDTO.setCurPage(1);
        reqDTO.setPageSize(10);
        reqDTO.setCategory("fileType");
        reqDTO.setStatus(1);
        List<DictInfoResVO> records = dictInfoService.getDictInfos(reqDTO).getRecords();
        if (records.isEmpty()) {
            throw new CrmOperateException("上传文件类型限定的字典数据未配置");
        }
        String detail = records.get(0).getDetail();
        if (StringUtils.isBlank(detail)) {
            throw new CrmOperateException("上传文件类型限定的字典数据配置不正确");
        }
        List<String> fileTypeList = Arrays.asList(detail.split(","));
        String suffix = names.get(names.size() - 1);
        if (!fileTypeList.contains(suffix)) {
            throw new CrmOperateException("上传的文件类型不支持");
        }
    }
    
    /**
     * 查询导入文件信息列表
     */
    @GetMapping(value = Urls.Transfer.V1_TRANSFER_IMPORT_QUERY)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_IMPORT_QUERY, notes = Urls.Transfer.V1_TRANSFER_IMPORT_QUERY)
    public Response<Page<LoanImportVO>> getImportInfoList(@BeanParam LoanImportDTO importDTO) {
        Page<LoanImportVO> importPage = transferService.listImport(importDTO);
        return Response.success(importPage);
    }

    /**
     * 获取委外导入模板
     */
    @GetMapping(Urls.Transfer.V1_TRANSFER_OUTCASE_TEMPLATE)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_OUTCASE_TEMPLATE, notes = Urls.Transfer.V1_TRANSFER_OUTCASE_TEMPLATE_DESC)
    public Response<Void> getOutcaseTemplate(HttpServletResponse response) {
        ExcelUtil.getTemplate(LoanOutcaseExcelDTO.class, response);
        return Response.success();
    }

    /**
     * 导入委外列表
     */
    @PostMapping(Urls.Transfer.V1_TRANSFER_OUTCASE_IMPORT)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_OUTCASE_IMPORT, notes = Urls.Transfer.V1_TRANSFER_OUTCASE_IMPORT_DESC)
    public Response<Void> importOutcaseList(@ApiIgnore @ExcelToList(value = LoanOutcaseExcelDTO.class, param = "file")
                                                       ExcelList<LoanOutcaseExcelDTO> list) {
        if (CollectionUtil.isNull(list)) {
            throw new CrmOperateException("导入债转结清合同列表数据不能为空");
        }
        transferService.saveOutcaseList(CommonUtils.getCurrentlogged(), list);
        return Response.success();
    }

    /**
     * 获取债转导入模板
     */
    @GetMapping(Urls.Transfer.V1_TRANSFER_DEPT_TEMPLATE)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_DEPT_TEMPLATE, notes = Urls.Transfer.V1_TRANSFER_DEPT_TEMPLATE_DESC)
    public Response<Void> getDeptTemplate(HttpServletResponse response) {
        ExcelUtil.getTemplate(LoanDeptExcelDTO.class, response);
        return Response.success();
    }

    /**
     * 导入债转列表
     */
    @PostMapping(Urls.Transfer.V1_TRANSFER_DEPT_IMPORT)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_DEPT_IMPORT, notes = Urls.Transfer.V1_TRANSFER_DEPT_IMPORT_DESC)
    public Response<Void> importDeptList(@ApiIgnore @ExcelToList(value = LoanDeptExcelDTO.class, param = "file")
                                                       ExcelList<LoanDeptExcelDTO> list) {
        if (CollectionUtil.isNull(list)) {
            throw new CrmOperateException("导入债转结清合同列表数据不能为空");
        }
        transferService.saveDeptList(CommonUtils.getCurrentlogged(), list);
        return Response.success();
    }

    @GetMapping(Urls.Transfer.V1_TRANSFER_ORDER_DETAIL)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_ORDER_DETAIL_DESC, notes = Urls.Transfer.V1_TRANSFER_ORDER_DETAIL)
    public Response<Page<OrderLoanTransferVO>> listOrderLoanTransfer(@BeanParam OrderLoanTransferDTO dto) {
        return Response.success(transferService.listOrderLoanTransfer(dto));
    }

    @GetMapping(Urls.Transfer.V1_TRANSFER_ORDER_DETAIL_EXPORT)
    @ApiOperation(value = Urls.Transfer.V1_TRANSFER_ORDER_DETAIL_EXPORT_DESC, notes = Urls.Transfer.V1_TRANSFER_ORDER_DETAIL_EXPORT_DESC)
    public Response<Void> exportOrderLoanTransfer(@BeanParam OrderLoanTransferDTO dto) throws IOException {
        dto.setCurPage(1);
        dto.setPageSize(100000);
        List<OrderLoanTransferVO> records = transferService.listOrderLoanTransfer(dto).getRecords();
        EasyExcelUtils.export(baseResponse, records, OrderLoanTransferVO.class, "工单债转明细", "工单债转明细");
        return Response.success();
    }
    @PostMapping(Urls.Transfer.V1_SEND_SMS)
    @ApiOperation(value = Urls.Transfer.V1_SEND_SMS_DESC, notes = Urls.Transfer.V1_SEND_SMS_DESC)
    public Response<Void> listOrderLoanTransfer(@RequestParam Long id) {
        transferService.sendTransferSms(id);
        return Response.success();
    }
}
