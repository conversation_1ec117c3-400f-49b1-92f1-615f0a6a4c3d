package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.service.FaceService;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/22
 */
@RestController
@Api(description = "人脸识别服务")
@RequestMapping(Urls.FaceIdentify.ROOT)
public class FaceIdentifyController {

	@Resource
	private FaceService faceService;

	@ApiOperation(value = Urls.FaceIdentify.UPLOAD_BASE_PHOTO_DESC, notes = Urls.FaceIdentify.UPLOAD_BASE_PHOTO_DESC)
	@PostMapping(Urls.FaceIdentify.UPLOAD_BASE_PHOTO)
	@NoRepeatSubmit
	public Response<Void> uploadBasePhoto(MultipartFile file, Long staffId, @RequestHeader("x-user-token") String token) throws IOException {
		faceService.uploadBasePhoto(file.getBytes(), staffId, token);
		return Response.success();
	}


	@ApiOperation(value = Urls.FaceIdentify.QUERY_BASE_PHOTO_DESC, notes = Urls.FaceIdentify.QUERY_BASE_PHOTO_DESC)
	@GetMapping(Urls.FaceIdentify.QUERY_BASE_PHOTO)
	public Response<String> queryBasePhoto(@RequestParam Long staffId) throws IOException {
		return Response.success(faceService.queryBasePhoto(staffId));
	}


}

