package com.welab.crm.operate.web.controller;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.welab.crm.operate.dto.earlySettle.EarlySettledReqDTO;
import com.welab.crm.operate.service.EarlySettledService;
import com.welab.crm.operate.vo.earlySettle.EarlySettledRecordUserSummaryVO;
import org.springframework.web.bind.annotation.*;

import com.welab.common.response.Response;
import com.welab.crm.operate.web.constants.Urls;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 提前结清报表controller
 * @module 客服项目
 */
@RestController
@Api(tags = "提前结清数据查询服务")
@RequestMapping(Urls.EarlySettled.ROOT)
public class EarlySettledController {

    @Resource
    private EarlySettledService earlySettledService;

    @ApiOperation(value = Urls.EarlySettled.USER_SUMMARY_DESC, notes = Urls.EarlySettled.USER_SUMMARY_DESC)
    @PostMapping(Urls.EarlySettled.USER_SUMMARY)
    public Response<List<EarlySettledRecordUserSummaryVO>> getUserSummary(@RequestBody EarlySettledReqDTO dto) {
        return Response.success(earlySettledService.querySettleUserSummary(dto));
    }

    @ApiOperation(value = Urls.EarlySettled.REASON_TENOR_SUMMARY_DESC, notes = Urls.EarlySettled.REASON_TENOR_SUMMARY_DESC)
    @GetMapping(Urls.EarlySettled.REASON_TENOR_SUMMARY)
    public Response<JSONObject> getReasonTenorSummary(@RequestParam String startTime, @RequestParam String endTime) {
        return Response.success(earlySettledService.queryReasonTenorSummary(startTime, endTime));
    }

    @ApiOperation(value = Urls.EarlySettled.ORIGIN_PARTNER_SUMMARY_DESC, notes = Urls.EarlySettled.ORIGIN_PARTNER_SUMMARY_DESC)
    @GetMapping(Urls.EarlySettled.ORIGIN_PARTNER_SUMMARY)
    public Response<JSONObject> getOriginPartnerSummary(@RequestParam String startTime, @RequestParam String endTime) {
        return Response.success(earlySettledService.queryOriginPartnerCodeSummary(startTime, endTime));
    }


}

