package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.operate.dto.phone.AgentSkillUpdateDTO;
import com.welab.crm.operate.dto.phone.PhoneLoginInfoReqDTO;
import com.welab.crm.operate.dto.phone.PhoneSummaryReqDTO;
import com.welab.crm.operate.dto.phone.SoftPhoneReqDTO;
import com.welab.crm.operate.service.SoftPhoneCallService;
import com.welab.crm.operate.vo.agent.AgentVO;
import com.welab.crm.operate.vo.phone.IsSummaryVO;
import com.welab.crm.operate.vo.phone.PhoneLoginInfoResVO;
import com.welab.crm.operate.vo.phone.PhoneSummaryVO;
import com.welab.crm.operate.vo.phone.SoftPhoneInfoVO;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls.Phone;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 通话相关controller
 *
 * <AUTHOR>
 * @date 2021/10/21 14:51
 * @module 客服项目
 */

@RestController
@Api(description = "通话相关服务")
@RequestMapping(Phone.ROOT)
@Slf4j
public class PhoneController {

    @Resource
    private SoftPhoneCallService phoneCallService;

    @PostMapping(value = Phone.ADD_SOFT_PHONE_RECORD)
    @ApiOperation(value = Phone.ADD_SOFT_PHONE_RECORD, notes = Phone.ADD_SOFT_PHONE_RECORD_DESC)
    public Response<Void> addSoftPhoneRecord(@RequestBody SoftPhoneReqDTO softPhoneReqDTO) {
        try {
            phoneCallService.addSoftPhoneRecord(softPhoneReqDTO);
        } catch (Exception e) {
            log.error("addSoftPhoneRecord 发生异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
        return Response.success();
    }

    @PostMapping(value = Phone.QUERY_SOFT_PHONE_RECORD)
    @ApiOperation(value = Phone.QUERY_SOFT_PHONE_RECORD, notes = Phone.QUERY_SOFT_PHONE_RECORD_DESC)
    public Response<Page<SoftPhoneInfoVO>> querySoftPhoneRecord(@RequestBody SoftPhoneReqDTO softPhoneReqDTO) {
        try {
            return Response.success(phoneCallService.querySoftPhoneRecord(softPhoneReqDTO));
        } catch (Exception e) {
            log.error("querySoftPhoneRecord 发生异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(value = Phone.SAVE_PHONE_SUMMARY_DETAIL)
    @ApiOperation(value = Phone.SAVE_PHONE_SUMMARY_DETAIL, notes = Phone.SAVE_PHONE_SUMMARY_DETAIL_DESC)
    @LogSaveToDb
    public Response<Void> savePhoneSummaryDetail(@RequestBody PhoneSummaryReqDTO reqDTO) {
        try {
            phoneCallService.savePhoneSummary(reqDTO);
            return Response.success();
        } catch (Exception e) {
            log.error("savePhoneSummaryDetail 发生异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(value = Phone.QUERY_PHONE_SUMMARY_DETAIL)
    @ApiOperation(value = Phone.QUERY_PHONE_SUMMARY_DETAIL, notes = Phone.QUERY_PHONE_SUMMARY_DETAIL_DESC)
    public Response<List<PhoneSummaryVO>> queryPhoneSummaryDetail(@RequestBody PhoneSummaryReqDTO reqDTO) {
        try {
            return Response.success(phoneCallService.queryPhoneSummaryByPage(reqDTO));
        } catch (Exception e) {
            log.warn("queryPhoneSummaryDetail 发生异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


    @PostMapping(Phone.PHONE_LOGIN_INFO_QUERY)
    @ApiOperation(value = Phone.PHONE_LOGIN_INFO_QUERY, notes = Phone.PHONE_LOGIN_INFO_QUERY_DESC)
    public Response<List<PhoneLoginInfoResVO>> queryPhoneLoginInfo(@Validated @RequestBody PhoneLoginInfoReqDTO reqDTO) {
        List<PhoneLoginInfoResVO> rootList = new ArrayList<PhoneLoginInfoResVO>();
        try {
            rootList = phoneCallService.getPhoneLoginInfo(new PhoneLoginInfoReqDTO());
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
        return Response.success(rootList);
    }


    @PostMapping(Phone.PHONE_LOGIN_INFO_QUERY_PAGE)
    @ApiOperation(value = Phone.PHONE_LOGIN_INFO_QUERY_PAGE, notes = Phone.PHONE_LOGIN_INFO_QUERY_PAGE_DESC)
    public Response<Page<PhoneLoginInfoResVO>> queryPhoneLoginInfoPage(@Validated @RequestBody PhoneLoginInfoReqDTO reqDTO) {
        try {
            return Response.success(phoneCallService.getLoginInfoByPage(reqDTO));
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


    @PostMapping(Phone.PHONE_LOGIN_INFO_ADD)
    @ApiOperation(value = Phone.PHONE_LOGIN_INFO_ADD, notes = Phone.PHONE_LOGIN_INFO_ADD_DESC)
    public Response<Void> addLoginInfo(@Validated @RequestBody PhoneLoginInfoReqDTO reqDTO) {
        try {
            phoneCallService.addPhoneLoginInfo(reqDTO);
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

        return Response.success();
    }

    @PostMapping(Phone.PHONE_LOGIN_INFO_UPDATE)
    @ApiOperation(value = Phone.PHONE_LOGIN_INFO_UPDATE, notes = Phone.PHONE_LOGIN_INFO_UPDATE_DESC)
    public Response<Void> updateLoginInfo(@Validated @RequestBody PhoneLoginInfoReqDTO reqDTO) {
        try {
            phoneCallService.updatePhoneLoginInfo(reqDTO);
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

        return Response.success();
    }

    @PostMapping(Phone.PHONE_LOGIN_INFO_DELETE)
    @ApiOperation(value = Phone.PHONE_LOGIN_INFO_DELETE, notes = Phone.PHONE_LOGIN_INFO_DELETE_DESC)
    public Response<Void> deleteLoginInfo(@Validated @RequestBody PhoneLoginInfoReqDTO reqDTO) {
        try {
            phoneCallService.deletePhoneLoginInfo(reqDTO);
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

        return Response.success();
    }

    @GetMapping(Phone.QUERY_IS_SUMMARY)
    @ApiOperation(value = Phone.QUERY_IS_SUMMARY_DESC, notes = Phone.QUERY_IS_SUMMARY_DESC)
    public Response<Boolean> isSummary(@RequestParam String cdrMainUniqueId) {
        try {
            return Response.success(phoneCallService.isSummary(cdrMainUniqueId));
        } catch (Exception e) {
            log.error("查询是否做电话小结异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

    }


    @GetMapping(Phone.LAST_PHONE_IS_SUMMARY)
    @ApiOperation(value = Phone.LAST_PHONE_IS_SUMMARY_DESC, notes = Phone.LAST_PHONE_IS_SUMMARY_DESC)
    public Response<IsSummaryVO> isSummary() {
        try {
            return Response.success(phoneCallService.lastPhoneIsSummary());
        } catch (Exception e) {
            log.error("查询是否做电话小结异常", e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }

    }

    @GetMapping(Phone.AGENT_MONITOR)
    @ApiOperation(value = Phone.AGENT_MONITOR_DESC, notes = Phone.AGENT_MONITOR_DESC)
    public Response<List<AgentVO>> agentMonitor() {
        return Response.success(phoneCallService.queryAgentList());
    }

    @PostMapping(Phone.UPDATE_SKILL)
    @ApiOperation(value = Phone.UPDATE_SKILL_DESC, notes = Phone.UPDATE_SKILL_DESC)
    public Response<Void> updateSkill(@RequestBody AgentSkillUpdateDTO dto) {
        phoneCallService.updateAgentSkill(dto);
        return Response.success();
    }


    @GetMapping(Phone.GET_AI_SUMMARY)
    @ApiOperation(value = Phone.GET_AI_SUMMARY_DESC, notes = Phone.GET_AI_SUMMARY_DESC)
    public Response<String> aiSummary(@RequestParam String cdrMainUniqueId) {
        return Response.success(phoneCallService.queryAiSummary(cdrMainUniqueId));
    }

    @GetMapping(Phone.GET_DIALOGUE_TEXT)
    @ApiOperation(value = Phone.GET_DIALOGUE_TEXT_DESC, notes = Phone.GET_DIALOGUE_TEXT_DESC)
    public Response<String> dialogueText(@RequestParam String cdrMainUniqueId) {
        return Response.success(phoneCallService.queryDialogueText(cdrMainUniqueId));
    }

    @GetMapping(Phone.GET_AI_SUMMARY_CHECK)
    @ApiOperation(value = Phone.GET_AI_SUMMARY_CHECK_DESC, notes = Phone.GET_AI_SUMMARY_CHECK_DESC)
    public Response<String> aiSummaryCheck(@RequestParam String cdrMainUniqueId) {
        return Response.success(phoneCallService.queryAiSummaryCheck(cdrMainUniqueId));
    }

    @GetMapping(Phone.GET_AI_SUMMARY_RETRY)
    @ApiOperation(value = Phone.GET_AI_SUMMARY_RETRY_DESC, notes = Phone.GET_AI_SUMMARY_RETRY_DESC)
    public Response<String> aiSummaryRetry(@RequestParam String cdrMainUniqueId) {
        return Response.success(phoneCallService.queryAiSummaryRetry(cdrMainUniqueId));
    }


}
