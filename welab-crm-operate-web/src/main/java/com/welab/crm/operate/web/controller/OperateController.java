package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.interview.dto.ApplicationOperateDTO;
import com.welab.crm.interview.service.LoansApplicationService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.UserInfoVo;
import com.welab.crm.operate.annotation.AesDecrypt;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.dto.operate.*;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.operate.CustHisOperateVO;
import com.welab.crm.operate.web.constants.Urls.Operate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/12 9:54
 */


@RestController
@RequestMapping(Operate.ROOT)
@Api(description = "用户、贷款相关操作服务")
@Slf4j
public class OperateController {


    @Resource
    private UserInfoService userInfoService;
    @Resource
    private LoansApplicationService loansApplicationService;
    @Resource
    private CustOperateService custOperateService;


    @PostMapping(Operate.APPLICATION)
    @ApiOperation(value = Operate.APPLICATION, notes = Operate.APPLICATION_DESC)
    public Response<Boolean> applicationOperate(@RequestBody @Validated ApplicationOperateReqDTO reqDTO) {
        try {
            ApplicationOperateDTO operateDTO = new ApplicationOperateDTO();
            BeanUtils.copyProperties(reqDTO, operateDTO);
            operateDTO.setCustServiceMobile(CommonUtils.getStaffMobile());
            operateDTO.setType(OperateTypeEnum.getNoByCode(reqDTO.getType()));
            operateDTO.setStaffName(CommonUtils.getCurrentloggedName());
            Boolean result = loansApplicationService.applicationOperate(operateDTO);
            if (result) {
                custOperateService.saveApplicationOperateHistory(reqDTO);
            }
            return Response.success(result);
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


    @PostMapping(Operate.USER_INFO_UPDATE)
    @ApiOperation(value = Operate.USER_INFO_UPDATE, notes = Operate.USER_INFO_UPDATE_DESC)
    @AesDecrypt
    public Response<String> userInfoUpdate(@RequestBody @Validated UserInfoModifyReqDTO reqDTO) {
        try {
            reqDTO.setAdminMobile(CommonUtils.getStaffMobile());
            UserInfoVo userInfoVo = new UserInfoVo();
            BeanUtils.copyProperties(reqDTO, userInfoVo);

            if(StringUtils.isNotBlank(reqDTO.getOldMobile())){
                String result = userInfoService.verifyUserInfo(reqDTO.getUserId(), userInfoVo);
                if (result != null) {
                    return new Response<>(ResponsCodeTypeEnum.PARAMETER_ERROR.getCode(), "三要素认证失败-" + result, null);
                }
            }
            
            Map<String, String> resultMap = userInfoService
                    .updateUserInfo(reqDTO.getAdminMobile(), userInfoVo, reqDTO.getUserId());
            if ("0".equals(resultMap.get("code"))) {
                custOperateService.saveUpdateUserInfoOperateHistory(reqDTO);
                return Response.success(resultMap.get("msg"));
            }
            return new Response<>(ResponsCodeTypeEnum.DATA_NOT_EXISTS.getCode(), resultMap.get("msg"), null);
        } catch (Exception e) {
            log.error("userInfoUpdate error: {}", e.getMessage(), e);
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Operate.USER_LOGOUT)
    @ApiOperation(value = Operate.USER_LOGOUT, notes = Operate.USER_LOGOUT_DESC)
    public Response<String> userLogout(@RequestBody @Validated BlockUserReqDTO reqDTO) {
        try {
            String result = userInfoService.blockUser(reqDTO.getUserId().toString());
            if ("success".equals(result)) {
                saveLogoutUserHisOperate(reqDTO);
                return Response.success(result);
            }
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), result, null);
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    @PostMapping(Operate.HISTORY_QUERY)
    @ApiOperation(value = Operate.HISTORY_QUERY, notes = Operate.HISTORY_QUERY_DESC)
    public Response<List<CustHisOperateVO>> historyQuery(@RequestBody @Validated OperateHistoryQueryReqDTO reqDTO) {
        try {
            return Response.success(custOperateService.queryOperateHistory(reqDTO));
        } catch (Exception e){
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }

    private void saveLogoutUserHisOperate(BlockUserReqDTO reqDTO) {
        CustHisOperate operate = new CustHisOperate();
        BeanUtils.copyProperties(reqDTO, operate);
        operate.setOperateTime(new Date());
        operate.setOperateType(OperateTypeEnum.CANCEL_USER.getCode());
        operate.setStaffId(CommonUtils.getCurrentlogged());
        operate.setGroupCode(CommonUtils.getCurrentloggedOrg());
        custOperateService.saveOperateHistory(operate);
    }

    @PostMapping(Operate.URGENT_APPROVAL)
    @ApiOperation(value = Operate.URGENT_APPROVAL, notes = Operate.URGENT_APPROVAL_DESC)
    public Response<String> urgentApproval(@RequestBody @Validated UrgentApprovalReqDTO urgentApprovalReqDTO) {
        try {

            String result = loansApplicationService.sendUrgentApproval(urgentApprovalReqDTO.getApplicationId());
            if (StringUtils.isBlank(result)) {
                custOperateService.saveUrgentApprovalHistory(urgentApprovalReqDTO);
            }
            return Response.success();
        } catch (Exception e) {
            return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
        }
    }


}
