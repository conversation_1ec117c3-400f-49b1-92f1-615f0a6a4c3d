package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.ai.AIPushConfigDTO;
import com.welab.crm.operate.dto.ai.AIPushStateDTO;
import com.welab.crm.operate.dto.ai.AiPushHistoryDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.AiPushService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.ai.AiTmkConfigVO;
import com.welab.crm.operate.vo.ai.AiTmkPushVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/2/22
 */
@RestController
@Api(description = "ai外呼推送服务")
@RequestMapping(Urls.AI.ROOT)
public class AiController {

    @Resource
    private AiPushService aiPushService;

    /**
     * ai外呼推送查询全部配置
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_QUERY_DESC, notes = Urls.AI.V1_SALE_AI_QUERY_DESC)
    @GetMapping(Urls.AI.V1_SALE_AI_QUERY)
    public Response<List<AiTmkConfigVO>> getAiConfig() {
        return Response.success(aiPushService.getAIPushConfig());
    }

    /**
     * ai外呼推送配置新增
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_ADD_DESC, notes = Urls.AI.V1_SALE_AI_ADD_DESC)
    @PostMapping(Urls.AI.V1_SALE_AI_ADD)
    public Response<Void> addAiConfig(@RequestBody @Validated AIPushConfigDTO addDTO) {
        aiPushService.addAIPushConfig(CommonUtils.getCurrentlogged(), addDTO);
        return Response.success();
    }

    /**
     * ai外呼推送配置更新操作
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_UPDATE_DESC, notes = Urls.AI.V1_SALE_AI_UPDATE_DESC)
    @PostMapping(Urls.AI.V1_SALE_AI_UPDATE)
    public Response<Void> updateAiConfig(@RequestBody @Validated AIPushConfigDTO updateDTO) {
        checkId(updateDTO.getId());
        aiPushService.updateAIPushConfig(CommonUtils.getCurrentlogged(), updateDTO);
        return Response.success();
    }

    /**
     * ai外呼推送数据启用状态全部数据更新或者单条更新
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_STATE_UPDATE_DESC, notes = Urls.AI.V1_SALE_AI_STATE_UPDATE_DESC)
    @PostMapping(Urls.AI.V1_SALE_AI_STATE_UPDATE)
    public Response<Void> updateAiConfigState(@RequestBody @Validated AIPushStateDTO stateDTO) {
        aiPushService.updatePushState(CommonUtils.getCurrentlogged(), stateDTO);
        return Response.success();
    }

    /**
     * ai外呼推送配置删除
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_DELETE_DESC, notes = Urls.AI.V1_SALE_AI_DELETE_DESC)
    @PostMapping(Urls.AI.V1_SALE_AI_DELETE)
    public Response<Void> deleteAiConfig(Long id) {
        checkId(id);
        aiPushService.deleteAIPushConfig(CommonUtils.getCurrentlogged(), id);
        return Response.success();
    }

    /**
     * ai外呼推送数据量历史查询
     */
    @ApiOperation(value = Urls.AI.V1_SALE_AI_HISTORY_DESC, notes = Urls.AI.V1_SALE_AI_HISTORY_DESC)
    @GetMapping(Urls.AI.V1_SALE_AI_HISTORY)
    public Response<Page<AiTmkPushVO>> getAiConfigPushHistory(@BeanParam @Validated AiPushHistoryDTO historyDTO) {
        Page<AiTmkPushVO> voPage = aiPushService.getAiConfigPushHistory(historyDTO);
        return Response.success(voPage);
    }

    private void checkId(Long id) {
        if (id == null) {
            throw new CrmOperateException("外呼配置主键id不能为空");
        }
    }
}

