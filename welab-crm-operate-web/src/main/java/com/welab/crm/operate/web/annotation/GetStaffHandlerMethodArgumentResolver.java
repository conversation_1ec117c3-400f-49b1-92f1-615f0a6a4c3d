package com.welab.crm.operate.web.annotation;

import com.welab.common.spring.SpringContextHolder;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.exception.CrmOperateException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 */
@Slf4j
public class GetStaffHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        if (parameter.hasParameterAnnotation(GetStaff.class)) {
            return true;
        }
        return false;
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
        NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        String staffMobile = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
            .getHeader("X-Mobile");
        StaffService staffService = SpringContextHolder.getBean(StaffService.class);
        if (StringUtils.isBlank(staffMobile)) {
            log.error("手机号不存在");
            throw new CrmOperateException("手机号不存在!");
        }
        StaffVO staffVO = staffService.getStaffByMobile(staffMobile);
        if (Objects.isNull(staffVO)) {
            log.error("员工不存在，staffMobile:{}", staffMobile);
            throw new CrmOperateException("员工不存在!");
        }
        return staffVO;
    }
}
