package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.interview.service.LabelService;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.service.CustomerService;
import com.welab.crm.operate.service.WorkOrderService;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.web.constants.Urls.Label;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 */
@RestController
@Api(description = "客户标签服务")
@RequestMapping(Label.ROOT)
public class LabelController {

    @Resource
    private LabelService labelService;

    @Resource
    private CustomerService customerService;

    @Resource
    private WorkOrderService orderService;

    @GetMapping(Label.V1_LABEL_NAME_ALL)
    @ApiOperation(value = Label.V1_LABEL_NAME_ALL_DESC, notes = Label.V1_LABEL_NAME_ALL_DESC)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "客户手机号", paramType = "query")
    })
    public Response<List<String>> getUserAllLabel(@Validated @NotBlank String mobile) {
        boolean matches = Pattern.matches(Constant.MOBILE, mobile);
        if (!matches) {
            String plaintextValue = AesUtils.decrypt(mobile);
            mobile = plaintextValue;
        }
        Long uuid = customerService.queryUserUuidByMobile(mobile);
        if (uuid == null) {
            String userUuid = orderService.getUuIdByMobile(mobile);
            if (StringUtils.isBlank(userUuid)) {
                return Response.success(new ArrayList<>());
            } else {
                uuid = Long.parseLong(userUuid);
            }
        }
        List<String> labels = labelService.getUserAllLabel(uuid);
        return Response.success(labels);
    }

}
