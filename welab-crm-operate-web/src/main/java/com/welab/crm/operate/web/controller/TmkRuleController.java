package com.welab.crm.operate.web.controller;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.dto.tmkRule.TmkInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustReqDTO;
import com.welab.crm.operate.service.TmkRuleService;
import com.welab.crm.operate.vo.tmkRule.TmkInfoVO;
import com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.TmkRule;
import com.welab.domain.vo.ResponseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2022-02-24
 */
@RestController
@Api(description = "电销数据分配规则服务") 
@RequestMapping(TmkRule.ROOT)
@Slf4j
public class TmkRuleController {

	@Resource
	private TmkRuleService tmkRuleService;

	@PostMapping(TmkRule.V1_TMK_RULE_QUERY)
	@ApiOperation(value = TmkRule.V1_TMK_RULE_QUERY, notes = TmkRule.V1_TMK_RULE_QUERY_DESC)
	public ResponseVo<?> queryTmkInfoList(@RequestBody TmkInfoReqDTO reqDTO){
		Page<TmkInfoVO> rootList=null;
		try {
			rootList = tmkRuleService.queryTmkInfoList(reqDTO);
		} catch (Exception e) {

			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(rootList);
	}
	
	/**
     * 统计电销数量
     *
     * @return
     */
    @PostMapping(value = TmkRule.V1_TMK_RULE_TYPETOTAL)
    @ApiOperation(value = TmkRule.V1_TMK_RULE_TYPETOTAL, notes = TmkRule.V1_TMK_RULE_TYPETOTAL_DESC)
    public ResponseVo<?> typeTotalTmkInfo(@RequestBody TmkInfoReqDTO reqDTO) {
    	List<TypeTotalTmkInfoVO> list = null;
		try {
			list = tmkRuleService.typeTotalTmkInfo(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<List<TypeTotalTmkInfoVO>>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(list);
    }
    
    @PostMapping(TmkRule.V1_TMK_RULE_ADJUST)
	@ApiOperation(value = TmkRule.V1_TMK_RULE_ADJUST, notes = TmkRule.V1_TMK_RULE_ADJUST_DESC)
	public ResponseVo<?> adjustTmkRule(@Validated @RequestBody TmkRuleAdjustReqDTO reqDTO,
		@GetStaff StaffVO staffVO){
		try {
			reqDTO.setOperatorId(String.valueOf(staffVO.getId()));
			reqDTO.setOperatorGroupCode(staffVO.getGroupCode());
			tmkRuleService.adjustTmkRule(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
	}
}
