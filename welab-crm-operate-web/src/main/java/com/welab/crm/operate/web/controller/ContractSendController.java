package com.welab.crm.operate.web.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.operate.annotation.AesDecrypt;
import com.welab.crm.operate.dto.AuditBatchInfoReqDTO;
import com.welab.crm.operate.dto.ContractSendDTO;
import com.welab.crm.operate.dto.ContractSendReqDTO;
import com.welab.crm.operate.service.ContractSendService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.ContractSendVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls;
import com.welab.crm.operate.web.response.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

@RestController
@RequestMapping(Urls.ContractSend.ROOT)
@Api(tags = "合同发送")
public class ContractSendController extends BaseController{
	
	@Resource
	private ContractSendService contractSendService;
	
	
	@PostMapping(Urls.ContractSend.QUERY)
	@ApiOperation(value = Urls.ContractSend.QUERY_DESC, notes = Urls.ContractSend.QUERY_DESC)
	public Response<Page<ContractSendVO>> queryContractSendPage(@RequestBody @Validated ContractSendReqDTO reqDTO) {
		return Response.success(contractSendService.queryContractSendPage(reqDTO));
	}


	@PostMapping(Urls.ContractSend.SEND)
	@ApiOperation(value = Urls.ContractSend.SEND_DESC, notes = Urls.ContractSend.SEND_DESC)
	@AesDecrypt
	public Response<Void> sendContract(@RequestBody ContractSendDTO reqDTO, @GetStaff StaffVO staffVO) {
		contractSendService.sendContract(reqDTO, staffVO);
		return Response.success();
	}
	
	@PostMapping(Urls.ContractSend.EXPORT)
	@ApiOperation(value = Urls.ContractSend.EXPORT_DESC, notes = Urls.ContractSend.EXPORT_DESC)
	public Response<Void> exportContract(@RequestBody ContractSendReqDTO reqDTO) throws IOException {
		CommonUtils.commonExport(baseResponse, "合同发送记录", ContractSendVO.class, contractSendService.queryContractSendList(reqDTO));
		return Response.success();
	}
	
	@PostMapping(Urls.ContractSend.AUDIT)
	@ApiOperation(value = Urls.ContractSend.AUDIT_DESC, notes = Urls.ContractSend.AUDIT_DESC)
	public Response<Void> auditContract(@RequestBody AuditBatchInfoReqDTO reqDTO) {
		contractSendService.auditContract(reqDTO);
		return Response.success();
	}

	@PostMapping(Urls.ContractSend.QUERY_SEND_TYPE)
	@ApiOperation(value = Urls.ContractSend.QUERY_SEND_TYPE_DESC, notes = Urls.ContractSend.QUERY_SEND_TYPE_DESC)
	public Response<JSONObject> querySendType() {
		return Response.success(contractSendService.querySendType());
	}
	
	
	
	
}
