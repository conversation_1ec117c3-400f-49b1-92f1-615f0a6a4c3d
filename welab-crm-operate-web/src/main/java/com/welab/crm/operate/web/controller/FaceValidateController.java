package com.welab.crm.operate.web.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.report.FaceReportDTO;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.FaceValidateService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.face.FaceCauseVO;
import com.welab.crm.operate.vo.face.FaceDayVO;
import com.welab.crm.operate.vo.face.FaceDetailVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.ws.rs.BeanParam;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/02/16
 */
@RestController
@Api(description = "人脸验证统计报表控制器")
@RequestMapping(Urls.Face.ROOT)
@Slf4j
public class FaceValidateController extends BaseController {

    @Resource
    private FaceValidateService validateService;


    @GetMapping(value = Urls.Face.V1_FACE_DETAIL)
    @ApiOperation(value = Urls.Face.V1_FACE_DETAIL_DESC, notes = Urls.Face.V1_FACE_DETAIL_DESC)
    public Response<Page<FaceDetailVO>> listDetails(@Validated @BeanParam FaceReportDTO dto) {
        Page<FaceDetailVO> voList = validateService.listDetailsByPage(dto);
        return Response.success(voList);
    }

    @GetMapping(value = Urls.Face.V1_FACE_DETAIL_EXPORT)
    @ApiOperation(value = Urls.Face.V1_FACE_DETAIL_EXPORT_DESC, notes = Urls.Face.V1_FACE_DETAIL_EXPORT_DESC)
    public void exportDetails(@Validated @BeanParam FaceReportDTO dto) {
        List<FaceDetailVO> voList = validateService.listDetails(dto);
        List<CellRangeAddress> cellRangeList = getCellRangeList(voList);
        commonExport("人脸验证明细数据", FaceDetailVO.class, voList, true, cellRangeList, dto);
    }

    @GetMapping(value = Urls.Face.V1_FACE_DAY)
    @ApiOperation(value = Urls.Face.V1_FACE_DAY_DESC, notes = Urls.Face.V1_FACE_DAY_DESC)
    public Response<Page<FaceDayVO>> listDayData(@Validated @BeanParam FaceReportDTO dto) {
        Page<FaceDayVO> voList = validateService.listDayDataByPage(dto);
        return Response.success(voList);
    }

    @GetMapping(value = Urls.Face.V1_FACE_DAY_EXPORT)
    @ApiOperation(value = Urls.Face.V1_FACE_DAY_EXPORT_DESC, notes = Urls.Face.V1_FACE_DAY_EXPORT_DESC)
    public void exportDayData(@Validated @BeanParam FaceReportDTO dto) {
        List<FaceDayVO> voList = validateService.listDayData(dto);
        commonExport("人脸验每日数据", FaceDayVO.class, voList, false, null, dto);
    }

    @GetMapping(value = Urls.Face.V1_FACE_CAUSE)
    @ApiOperation(value = Urls.Face.V1_FACE_CAUSE_DESC, notes = Urls.Face.V1_FACE_CAUSE_DESC)
    public Response<Page<FaceCauseVO>> listCause(@Validated @BeanParam FaceReportDTO dto) {
        Page<FaceCauseVO> voList = validateService.listCauseByPage(dto);
        return Response.success(voList);
    }

    @GetMapping(value = Urls.Face.V1_FACE_CAUSE_EXPORT)
    @ApiOperation(value = Urls.Face.V1_FACE_CAUSE_EXPORT_DESC, notes = Urls.Face.V1_FACE_CAUSE_EXPORT_DESC)
    public void exportCause(@Validated @BeanParam FaceReportDTO dto) {
        List<FaceCauseVO> voList = validateService.listCause(dto);
        commonExport("人脸验证失败原因", FaceCauseVO.class, voList, false, null, dto);
    }

    private List<CellRangeAddress> getCellRangeList(List<FaceDetailVO> exportVOList) {
        if (exportVOList.size() > 2) {
            // 1.准备需要合并的单元格的数字
            Map<String, Integer> sendTimeMap = new HashMap<>(exportVOList.size(), 1);
            for (int i = 1; i < exportVOList.size(); i++) {
                // 这里使用发送时间作为key是因为创研并发非常低之故
                String sendTime = exportVOList.get(i).getSendTime();
                if (Objects.equals(sendTime, exportVOList.get(i - 1).getSendTime())) {
                    Integer count = sendTimeMap.get(sendTime);
                    if (count != null) {
                        sendTimeMap.put(sendTime, count + 1);
                    } else {
                        sendTimeMap.put(sendTime, 2);
                    }
                }
            }
            // 2.构建具体需要合并的range列表
            if (!sendTimeMap.isEmpty()) {
                List<CellRangeAddress> addressList = new ArrayList<>(sendTimeMap.size() * 9);
                for (int row = 0; row < exportVOList.size(); row++) {
                    String sendTime = exportVOList.get(row).getSendTime();
                    Integer rangeNum = sendTimeMap.get(sendTime);
                    if (rangeNum != null) {
                        sendTimeMap.remove(sendTime);
                        // 添加9列合并项
                        for (int column = 0; column < 9; column++) {
                            addressList.add(new CellRangeAddress(row + 1, row + rangeNum, column, column));
                        }
                    }
                }
                return addressList;
            } else {
                return Collections.emptyList();
            }
        } else {
            return Collections.emptyList();
        }
    }

    private <T> void commonExport(String fileName, Class<T> t, List<T> dataList, boolean merge, List<CellRangeAddress> mergeList, FaceReportDTO dto) {
        try {
            if (merge) {
                // 需要做表格的合并操作
                CommonUtils.mergeExport(baseResponse, fileName, t, dataList, mergeList);
            } else {
                // 直接导出的按如下操作
                CommonUtils.commonExport(baseResponse, fileName, t, dataList);
            }
        } catch (Exception e) {
            log.error("导出人脸验证数据异常, 查询参数: {}, 错误消息: {}", dto, e.getMessage(), e);
            throw new CrmOperateException("导出错误");
        }
    }

}
