package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummaryReportQueryDTO;
import com.welab.crm.operate.dto.callbackSummary.CallbackSummarySaveDTO;
import com.welab.crm.operate.service.CallbackSummaryService;
import com.welab.crm.operate.util.EasyExcelUtils;
import com.welab.crm.operate.vo.callbackSummary.CallbackSummaryReportVO;
import com.welab.crm.operate.vo.callbackSummary.CallbackTypeVO;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 回电小结接口
 * 
 * <AUTHOR>
 * @module 客服项目
 */
@RestController
@RequestMapping(Urls.CallbackSummary.ROOT)
public class CallbackSummaryController extends BaseController {

    @Resource
    private CallbackSummaryService callbackSummaryService;

    @GetMapping(value = Urls.CallbackSummary.V1_DICT_QUERY)
    @ApiOperation(value = Urls.CallbackSummary.V1_DICT_QUERY_DESC, notes = Urls.CallbackSummary.V1_DICT_QUERY_DESC)
    public Response<Map<String, List<CallbackTypeVO>>> queryDict() {
        return Response.success(callbackSummaryService.queryCallbackSummaryDict2());
    }

    @PostMapping(value = Urls.CallbackSummary.V1_DICT_UPDATE)
    @ApiOperation(value = Urls.CallbackSummary.V1_DICT_UPDATE_DESC, notes = Urls.CallbackSummary.V1_DICT_UPDATE_DESC)
    public Response<Void> updateDict(@RequestBody Map<String, List<CallbackTypeVO>> map) {
        callbackSummaryService.updateCallbackSummaryDict2(map);
        return Response.success();
    }

    @PostMapping(value = Urls.CallbackSummary.V1_SAVE)
    @ApiOperation(value = Urls.CallbackSummary.V1_SAVE_DESC, notes = Urls.CallbackSummary.V1_SAVE_DESC)
    @LogSaveToDb
    public Response<Void> saveSummary(@RequestBody @Validated CallbackSummarySaveDTO dto) {
        callbackSummaryService.addCallbackSummary(dto);
        return Response.success();
    }

    @PostMapping(value = Urls.CallbackSummary.V1_REPORT_QUERY)
    @ApiOperation(value = Urls.CallbackSummary.V1_REPORT_QUERY_DESC, notes = Urls.CallbackSummary.V1_REPORT_QUERY_DESC)
    public Response<Page<CallbackSummaryReportVO>> queryReport(@RequestBody CallbackSummaryReportQueryDTO dto) {
        return Response.success(callbackSummaryService.queryCallbackSummaryReport(dto));
    }

    @PostMapping(value = Urls.CallbackSummary.V1_REPORT_EXPORT)
    @ApiOperation(value = Urls.CallbackSummary.V1_REPORT_EXPORT_DESC, notes = Urls.CallbackSummary.V1_REPORT_EXPORT_DESC)
    public Response<Void> exportReport(@RequestBody @Validated CallbackSummaryReportQueryDTO dto) throws IOException {
        dto.setCurrentPage(1);
        dto.setRowsPerPage(500000);
        EasyExcelUtils.export(baseResponse, callbackSummaryService.queryCallbackSummaryReport(dto).getRecords(),
            CallbackSummaryReportVO.class, "回电小结报表", "回电小结报表");

        return Response.success();
    }
}
