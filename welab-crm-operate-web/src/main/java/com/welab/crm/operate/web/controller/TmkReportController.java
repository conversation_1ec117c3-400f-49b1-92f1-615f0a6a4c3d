package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.vo.AssetsBoardVO;
import com.welab.crm.operate.dto.report.ReassignOutboundEfficiencyDTO;
import com.welab.crm.operate.service.TmkReportService;
import com.welab.crm.operate.vo.tmkReport.RedistributedOutboundEfficiencyVO;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(description = "电销报表统计服务")
@RequestMapping(Urls.TmkReport.ROOT)
public class TmkReportController {

    @Resource
    private TmkReportService tmkReportService;

    @Resource
    private FinanceService financeService;

    /**
     * 中央监控: 资金看板接口
     */
    @GetMapping(Urls.TmkReport.V1_TMK_REPORT_MONITOR_ASSETS)
    @ApiOperation(value = Urls.TmkReport.V1_TMK_REPORT_MONITOR_ASSETS_DESC,
            notes = Urls.TmkReport.V1_TMK_REPORT_MONITOR_ASSETS_DESC)
    public Response<List<AssetsBoardVO>> getSatisfactionReport() {
        return Response.success(financeService.getAssetsBoard());
    }

    /**
     * 中央监控: 再分配数据外呼效能
     */
    @GetMapping(Urls.TmkReport.V1_TMK_REPORT_MONITOR_REDISTRIBUTED_OUTBOUND_EFFICIENCY)
    @ApiOperation(value = Urls.TmkReport.V1_TMK_REPORT_MONITOR_REDISTRIBUTED_OUTBOUND_EFFICIENCY_DESC,
            notes = Urls.TmkReport.V1_TMK_REPORT_MONITOR_REDISTRIBUTED_OUTBOUND_EFFICIENCY_DESC)
    public Response<List<RedistributedOutboundEfficiencyVO>> getRedistributedOutboundEfficiency(
            @Validated @ModelAttribute ReassignOutboundEfficiencyDTO dto) {
        return Response.success(tmkReportService.getReassignOutboundEfficiency(dto));
    }
}
