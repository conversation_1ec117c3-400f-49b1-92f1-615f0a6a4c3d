## 把项目中需要根据不同环境配置不同参数的配置写到这里
server.port = 8080
server.context-path=/welab-crm-operate
management.port=9090
management.context-path=/welab-crm-operate/manage
server.tomcat.compressable-mime-types = text/html,text/xml,text/plain,text/javascript,application/json,application/xml
server.tomcat.compression = 2048 # is compression enabled (off, on, or an integer content length limit)
server.tomcat.max-threads = 500
server.tomcat.uri-encoding = UTF-8

spring.http.encoding.charset = UTF-8
spring.http.encoding.enabled = true
spring.http.encoding.force = true
spring.http.multipart.maxFileSize = 5Mb

swagger.enabled = true
swagger.title = '文档服务Restful API列表'
swagger.description = '该列表是文档服务提供给其他系统访问的Restful API'

tomcat.accessLogEnabled = false
tomcat.backgroundProcessorDelay = 30 # secs
tomcat.basedir = /data/logs/anti-fraud/
tomcat.protocolHeader = x-forwarded-proto
tomcat.remoteIpHeader = x-forwarded-for

#endpoints.health.sensitive=false
#management.endpoints.web.exposure.include=*
management.security.enabled=false



