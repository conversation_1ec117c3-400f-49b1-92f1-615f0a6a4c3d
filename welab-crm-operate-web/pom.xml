<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.welab</groupId>
    <artifactId>welab-crm-operate</artifactId>
    <version>1.1.1-RELEASE</version>
  </parent>

  <artifactId>welab-crm-operate-web</artifactId>
  <name>welab-crm-operate-web</name>
  <packaging>jar</packaging>
  <url>https://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <dependencies>

    <!-- 依赖的内部jar包 begin -->
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-base-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-operate-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-operate-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.welab.base</groupId>
      <artifactId>welab-springboot-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-websocket</artifactId>
      <version>8.5.15</version>
    </dependency>

    <!-- 依赖的内部jar包 end -->

    <!-- Swagger2.0依赖 begin -->
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>
    <!-- Swagger2.0依赖 end -->

    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>javax.ws.rs-api</artifactId>
    </dependency>
    <!-- 测试相关依赖 Begin -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test-autoconfigure</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-collection-interview-api</artifactId>
      <!--            <exclusions>-->
      <!--                <exclusion>-->
      <!--                    <groupId>com.welab</groupId>-->
      <!--                    <artifactId>thirdparty-open-api</artifactId>-->
      <!--                </exclusion>-->
      <!--            </exclusions>-->
    </dependency>
    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-crm-interview-api</artifactId>
    </dependency>
    <!-- 测试相关依赖 end -->

    <dependency>
      <groupId>com.welab</groupId>
      <artifactId>welab-authority-api</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>${parent.artifactId}-${parent.version}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass>com.welab.crm.operate.Application</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <tasks>
                <mkdir dir="../target"/>
                <copy todir="../target" overwrite="true">
                  <fileset dir="${project.build.directory}"
                    erroronmissingdir="false">
                    <include name="*.jar"/>
                  </fileset>
                </copy>
              </tasks>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
