<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.welab</groupId>
    <artifactId>welab-crm-operate</artifactId>
    <version>1.1.1-RELEASE</version>
  </parent>

    <artifactId>welab-crm-operate-api</artifactId>
    <name>welab-crm-operate-api</name>
    <packaging>jar</packaging>
    <url>http://maven.apache.org</url>

    <dependencies>
        <!-- mybatis-plus Begin -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-common</artifactId>
        </dependency>

        <!-- Swagger2.0依赖 begin -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!-- Swagger2.0依赖 end -->

        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-domain-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-crm-interview-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-crm-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-collection-interview-api</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.welab</groupId>-->
<!--                    <artifactId>thirdparty-open-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>marketing-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>bank-card-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.welab</groupId>
                    <artifactId>finance-capital-allocation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
