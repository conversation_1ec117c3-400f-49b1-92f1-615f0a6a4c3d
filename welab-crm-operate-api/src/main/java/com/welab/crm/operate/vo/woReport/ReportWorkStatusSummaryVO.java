package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/24
 */
@Data
@ApiModel(value = "工作状态统计报表响应对象")
@Excel(fileName="工作状态统计报表")
public class ReportWorkStatusSummaryVO implements Serializable {

    private static final long serialVersionUID = 7079885260322382L;

    @ApiModelProperty(value = "时间")
    @ExcelTitleMap(title = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String cno;

    @ApiModelProperty(value = "姓名")
    @ExcelTitleMap(title = "姓名")
    private String staffName;

    @ApiModelProperty(value = "首次签入时间")
    @ExcelTitleMap(title = "首次签入时间")
    private String loginTime;

    @ApiModelProperty(value = "最后签出时间")
    @ExcelTitleMap(title = "最后签出时间")
    private String logoutTime;

    @ApiModelProperty(value = "签入次数")
    @ExcelTitleMap(title = "签入次数")
    private Integer loginTimes = 0;

    @ApiModelProperty(value = "工作总时长")
    @ExcelTitleMap(title = "工作总时长")
    private String loginDuration = "00:00:00";

    @ApiModelProperty(value = "空闲总时长")
    @ExcelTitleMap(title = "空闲总时长")
    private String idleDuration = "00:00:00";

    @ApiModelProperty(value = "置忙次数")
    @ExcelTitleMap(title = "置忙次数")
    private Integer pauseTimes = 0;

    @ApiModelProperty(value = "置忙总时长")
    @ExcelTitleMap(title = "置忙总时长")
    private String pauseDuration = "00:00:00";

    @ApiModelProperty(value = "小休次数")
    @ExcelTitleMap(title = "小休次数")
    private Integer restTimes = 0;

    @ApiModelProperty(value = "小休总时长")
    @ExcelTitleMap(title = "小休总时长")
    private String restDuration = "00:00:00";

    @ApiModelProperty(value = "培训次数")
    @ExcelTitleMap(title = "培训次数")
    private Integer trainingTimes = 0;

    @ApiModelProperty(value = "培训总时长")
    @ExcelTitleMap(title = "培训总时长")
    private String trainingDuration = "00:00:00";

    @ApiModelProperty(value = "用餐次数")
    @ExcelTitleMap(title = "用餐次数")
    private Integer eatingTimes = 0;

    @ApiModelProperty(value = "用餐总时长")
    @ExcelTitleMap(title = "用餐总时长")
    private String eatingDuration = "00:00:00";

    @ApiModelProperty(value = "会议次数")
    @ExcelTitleMap(title = "会议次数")
    private Integer meetingTimes = 0;

    @ApiModelProperty(value = "会议总时长")
    @ExcelTitleMap(title = "会议总时长")
    private String meetingDuration = "00:00:00";

    @ApiModelProperty(value = "话后处理总时长")
    @ExcelTitleMap(title = "话后处理总时长")
    private String wrapUpDuration = "00:00:00";

    @ApiModelProperty(value = "外呼总数")
    @ExcelTitleMap(title = "外呼总数")
    private Integer outboundCount = 0;

    @ApiModelProperty(value = "外呼成功数")
    @ExcelTitleMap(title = "外呼成功数")
    private Integer outboundConnectedNumber = 0;

    @ApiModelProperty(value = "外呼通话总时长")
    @ExcelTitleMap(title = "外呼通话总时长")
    private String outboundDuration = "00:00:00";

    @ApiModelProperty(value = "呼入总数")
    @ExcelTitleMap(title = "呼入总数")
    private Integer callInCount = 0;

    @ApiModelProperty(value = "呼入接起数")
    @ExcelTitleMap(title = "呼入接起数")
    private Integer callInConnectedNumber = 0;

    @ApiModelProperty(value = "呼入通话总时长")
    @ExcelTitleMap(title = "呼入通话总时长")
    private String callInDuration = "00:00:00";

    @ApiModelProperty(value = "通话总时长")
    private Integer callTotal = 0;

    @ApiModelProperty(value = "通话总时长")
    @ExcelTitleMap(title = "通话总时长")
    private String callTotalDuration = "00:00:00";

    @ApiModelProperty(value = "工时利用率")
    @ExcelTitleMap(title = "工时利用率")
    private String workingHourUtilization = "0.00%";

    @ApiModelProperty(value = "工单提单时长")
    @ExcelTitleMap(title = "工单提单时长")
    private Integer submitOrderDuration = 0;

    @ApiModelProperty(value = "通话利用率")
    @ExcelTitleMap(title = "通话利用率")
    private String callUtilization = "0.00%";

    @ApiModelProperty(value = "客户满意度")
    @ExcelTitleMap(title = "客户满意度")
    private String satisfiedRate = "0.00%";

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "团队code")
    private String groupCode;

    /**
     * 团队名称
     */
    private String groupName;

    /**
     * 首问解决率
     */
    private String resolvedRate = "0.00%";
}
