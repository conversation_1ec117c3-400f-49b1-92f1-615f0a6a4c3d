package com.welab.crm.operate.dto.bankcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(description = "解绑卡请求对象")
public class BankCardUnbindReqDTO implements Serializable {

	@ApiModelProperty(value = "银行卡号")
	@NotBlank
	private String accountNo;
	@ApiModelProperty(value = "userId")
	@NotNull
	private Long userId;
	@ApiModelProperty(value = "customerId")
	@NotNull
	private Long customerId;
}
