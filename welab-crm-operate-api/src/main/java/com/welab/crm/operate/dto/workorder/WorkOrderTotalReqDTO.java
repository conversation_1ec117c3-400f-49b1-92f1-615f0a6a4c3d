package com.welab.crm.operate.dto.workorder;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单统计请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单统计请求对象")
public class WorkOrderTotalReqDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	@ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private Long orderOneClass;
	
	@ApiModelProperty(value = "处理组", name = "groupCode")
    private String groupCode;
    
    @ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;
    
    //状态
    private String status;
}
