package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "批量上传文件url对象")
public class BatchUploadUrlDTO {

	/**
	 * 批量上传文件url
	 */
	@ApiModelProperty(value = "批量上传文件url")
	private List<String> urlList;

	/**
	 * 工单主键id
	 */
	@ApiModelProperty(value = "工单主键id")
	private Long id;
	
	
}
