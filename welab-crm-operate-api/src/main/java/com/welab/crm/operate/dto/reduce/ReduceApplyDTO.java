package com.welab.crm.operate.dto.reduce;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 减免申请请求对象
 */
@Data
public class ReduceApplyDTO {

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 贷款号
	 */
	private String applicationId;


	/**
	 * 用户ID
	 */
	private String userId;


	/**
	 * 申请减免金额
	 */
	private BigDecimal amount;


	/**
	 * 系统可减免金额
	 */
	private BigDecimal reducibleAmount;


	/**
	 * 减免后费率
	 */
	private BigDecimal rateAfterReduce;


	/**
	 * 减免级别
	 */
	private String reductionType;


	/**
	 * 减免原因
	 */
	private String reductionReason;


	/**
	 * 投诉渠道
	 */
	private String complaintChannel;


	/**
	 * uuid
	 */
	private String uuid;

	/**
	 * 产品名称
	 */
	private String productName;


	/**
	 * 资金方名称
	 */
	private String partnerCode;

	/**
	 * 资金方编码
	 */
	private String partnerCodeNew;


	/**
	 * 费率
	 */
	private BigDecimal totalRate;

	/**
	 * 模式类型
	 */
	private String loanModelType;
}
