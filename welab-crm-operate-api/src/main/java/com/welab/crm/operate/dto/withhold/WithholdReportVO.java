package com.welab.crm.operate.dto.withhold;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 代扣报表返回对象
 * @date 2022/4/24 15:06
 */
@Data
@ApiModel(value = "代扣报表返回对象")
@Excel(fileName = "协催代扣统计报表", sheetName = "协催代扣统计报表")
public class WithholdReportVO implements Serializable {

    private static final Long serialVersionUID = 123L;

    @ApiModelProperty(value = "时间段", name = "time")
    @ExcelTitleMap(title = "日期")
    private String time;

    @ApiModelProperty(value = "客户数", name = "customerCount")
    @ExcelTitleMap(title = "客户数")
    private Integer customerCount;

    @ApiModelProperty(value = "订单数", name = "orderCount")
    @ExcelTitleMap(title = "订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "成功数", name = "successCount")
    @ExcelTitleMap(title = "成功数")
    private Integer successCount;

    @ApiModelProperty(value = "成功金额", name = "successAmount")
    @ExcelTitleMap(title = "成功金额")
    private BigDecimal successAmount;

    @ApiModelProperty(value = "成功率", name = "successAmount")
    @ExcelTitleMap(title = "成功率")
    private String successRate;

    @ApiModelProperty(value = "失败数", name = "failCount")
    @ExcelTitleMap(title = "失败数")
    private Integer failCount;

    @ApiModelProperty(value = "失败金额", name = "failAmount")
    @ExcelTitleMap(title = "失败金额")
    private BigDecimal failAmount;

    @ApiModelProperty(value = "失败率", name = "failRate")
    @ExcelTitleMap(title = "失败率")
    private String failRate;

}
