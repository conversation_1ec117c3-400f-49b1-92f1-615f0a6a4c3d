package com.welab.crm.operate.enums;

/**
 * websocket消息类型
 * <AUTHOR>
 */

public enum WebSocketMsgTypeEnum {

    URGE_ORDER("urgeOrder", "工单催单消息"),
    FACE_MENU_UPDATE("faceMenuUpdate", "人脸菜单更新消息");

    private String code;

    private String desc;

    WebSocketMsgTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
