package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/10
 */
@Data
@ApiModel(value = "工单效能报表接口响应对象")
@Excel(fileName = "工单效能统计报表", sheetName = "工单效能统计报表")
public class ReportEfficiencySummaryVO implements Serializable {

    private static final long serialVersionUID = 4138447608427825246L;

    @ApiModelProperty(value = "工单类型")
    @ExcelTitleMap(title = "工单类型")
    private String type;

    @ApiModelProperty(value = "话务组")
    @ExcelTitleMap(title = "话务组")
    private String groupName;

    @ApiModelProperty(value = "话务员")
    @ExcelTitleMap(title = "话务员")
    private String staffName;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String idNo;

    @ApiModelProperty(value = "提单量")
    @ExcelTitleMap(title = "提单量")
    private Integer createNum;

    @ApiModelProperty(value = "退单量")
    @ExcelTitleMap(title = "退单量")
    private Integer returnNum;

    @ApiModelProperty(value = "工单差错率")
    @ExcelTitleMap(title = "工单差错率")
    private String errorRate;

    @ApiModelProperty(value = "分单量")
    @ExcelTitleMap(title = "分单量")
    private Integer assignNum;

    @ApiModelProperty(value = "结案量")
    @ExcelTitleMap(title = "结案量")
    private Integer closeNum;

    @ApiModelProperty(value = "结案率")
    @ExcelTitleMap(title = "结案率")
    private String closeRate;

    @ApiModelProperty(value = "已解决结案量")
    @ExcelTitleMap(title = "已解决结案量")
    private Integer resolvedNum;

    @ApiModelProperty(value = "已解决结案率")
    @ExcelTitleMap(title = "已解决结案率")
    private String resolvedRate;

    @ApiModelProperty(value = "未解决结案量")
    @ExcelTitleMap(title = "未解决结案量")
    private Integer unresolvedNum;

    @ApiModelProperty(value = "未解决结案率")
    @ExcelTitleMap(title = "未解决结案率")
    private String unresolvedRate;

    @ApiModelProperty(value = "未接通结案量")
    @ExcelTitleMap(title = "未接通结案量")
    private Integer notConnectedNum;

    @ApiModelProperty(value = "未接通结案率")
    @ExcelTitleMap(title = "未接通结案率")
    private String notConnectedRate;

    @ApiModelProperty(value = "处理中量")
    @ExcelTitleMap(title = "处理中量")
    private Integer processingNum;

    @ApiModelProperty(value = "流转中量")
    @ExcelTitleMap(title = "流转中量")
    private Integer flowingNum;

    @ApiModelProperty(value = "被催单量")
    @ExcelTitleMap(title = "被催单量")
    private Integer reminderNum;

    @ApiModelProperty(value = "被催单率")
    @ExcelTitleMap(title = "被催单率")
    private String reminderRate;
}
