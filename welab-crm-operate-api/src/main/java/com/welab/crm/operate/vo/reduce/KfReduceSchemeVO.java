package com.welab.crm.operate.vo.reduce;

import com.welab.collection.interview.vo.reduceScheme.ReduceSchemeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "减免方案")
public class KfReduceSchemeVO extends ReduceSchemeVO {

	/**
	 * 背景颜色
	 */
	@ApiModelProperty(value = "背景颜色")
	private String backgroundColor;
}
