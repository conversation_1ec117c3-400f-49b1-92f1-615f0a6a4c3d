package com.welab.crm.operate.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SensitiveWorkOrderDTO extends BaseReqDTO{

	/**
	 * 字典id，即dict_info表主键
	 */
	private Long woTypeId;

	/**
	 * 工单大类描述
	 */
	private String woTypeDetail;

	/**
	 * 字典id，即dict_info表主键
	 */
	private Long woTypeFirId;

	/**
	 * 工单一类描述
	 */
	private String woTypeFirDetail;

	/**
	 * 字典id，即dict_info表主键
	 */
	private Long woTypeSecId;

	/**
	 * 工单二类描述
	 */
	private String woTypeSecDetail;

	/**
	 * 字典id，即dict_info表主键
	 */
	private Long woTypeThirId;

	/**
	 * 工单三类描述
	 */
	private String woTypeThirDetail;

	/**
	 * 资金方，多个用逗号隔开
	 */
	private String partnerNames;

	/**
	 * 跟进时效，单位：小时
	 */
	private Integer followUpTime;

	/**
	 * 提醒方式，robot:机器人，system:系统，多个用逗号隔开
	 */
	private String warnType;


	/**
	 * 创建时间
	 */
	private Date gmtCreate;

	/**
	 * 创建人
	 */
	private String createStaff;

	/**
	 * 备注
	 */
	private String remark;
}
