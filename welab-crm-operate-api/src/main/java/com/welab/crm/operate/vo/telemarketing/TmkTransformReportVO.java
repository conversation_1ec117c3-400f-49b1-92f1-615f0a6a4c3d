package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel(value = "TmkTransformReportVO", description = "电销转换报表返回对象")
@Excel(fileName = "电销转化报表", sheetName = "电销转化报表")
public class TmkTransformReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("话务员")
    @ExcelTitleMap(title = "话务员")
    private String staffName;

    @ApiModelProperty("话务组")
    @ExcelTitleMap(title = "话务组")
    private String groupCode;

    @ApiModelProperty("外呼量")
    @ExcelTitleMap(title = "外呼量")
    private Integer callOutCount;

    @ApiModelProperty("接通量")
    @ExcelTitleMap(title = "接通量")
    private Integer answerCount;

    @ApiModelProperty("接通率")
    @ExcelTitleMap(title = "接通率")
    private String answerRate;

    @ApiModelProperty("转化量")
    @ExcelTitleMap(title = "转化量")
    private Integer transformCount;

    @ApiModelProperty("转化率")
    @ExcelTitleMap(title = "转化率")
    private String transformRate;

    @ApiModelProperty("历史外呼量")
    @ExcelTitleMap(title = "历史外呼量")
    private Integer hisCallOutCount;

    @ApiModelProperty("历史接通量")
    @ExcelTitleMap(title = "历史接通量")
    private Integer hisAnswerCount;

    @ApiModelProperty("历史接通率")
    @ExcelTitleMap(title = "历史接通率")
    private String hisAnswerRate;

    @ApiModelProperty("历史转化量")
    @ExcelTitleMap(title = "历史转化量")
    private Integer hisTransformCount;

    @ApiModelProperty("历史转化率")
    @ExcelTitleMap(title = "历史转化率")
    private String hisTransformRate;


}
