package com.welab.crm.operate.vo.telemarketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel("电销UUID报表返回对象")
@ColumnWidth(value = 18)
public class TmkUuidReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @ExcelProperty(value = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty("号码包定义")
    @ExcelProperty(value = "号码包定义")
    private String packageDefine;

    @ApiModelProperty("进件时间")
    @ExcelProperty(value = "进件时间")
    private Date appliedAt;

    @ApiModelProperty("确认时间")
    @ExcelProperty(value = "确认时间")
    private Date confirmedAt;

    @ApiModelProperty("处理组")
    @ExcelProperty(value = "处理组")
    private String groupCode;

    @ApiModelProperty("处理人员")
    @ExcelProperty(value = "处理人员")
    private String staffId;

    @ApiModelProperty("联系结果")
    @ExcelProperty(value = "联系结果")
    private String resultCode;

    @ApiModelProperty("电话小结")
    @ExcelProperty(value = "电话小结")
    private String summaryContent;

    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String comment;

    @ApiModelProperty("小结时间")
    @ExcelProperty(value = "小结时间")
    private Date summaryAt;

    @ApiModelProperty("名单创建时间")
    @ExcelProperty(value = "名单创建时间")
    private Date gmtCreate;


    @ApiModelProperty("电话开始时间")
    @ExcelProperty(value = "电话开始时间")
    private Date callStartTime;

    @ApiModelProperty("电话结束时间")
    @ExcelProperty(value = "电话结束时间")
    private Date callEndTime;

    @ApiModelProperty("通话时长(s)")
    @ExcelProperty(value = "通话时长(s)")
    private Long callTime;
}
