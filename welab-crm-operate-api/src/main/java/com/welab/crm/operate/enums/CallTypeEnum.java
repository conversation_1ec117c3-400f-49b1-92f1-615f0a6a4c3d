package com.welab.crm.operate.enums;

/**
 * 呼叫类型: 1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫
 */
public enum CallTypeEnum {

    IN_CALL("1", "呼入"),
    CALL_OUT("4", "预览外呼"),
    ACTIVE_CALL("6", "主叫外呼"),
    INNER_CALL("9", "内部呼叫");

    private final String value;
    private final String text;

    CallTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
