package com.welab.crm.operate.dto.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/2
 */
@Data
@ApiModel(value = "操作历史通用请求对象")
public class HistoryOperationDTO implements Serializable {

    private static final long serialVersionUID = -5673323044903266446L;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "客服系统客户Id")
    private Long customerId;
}
