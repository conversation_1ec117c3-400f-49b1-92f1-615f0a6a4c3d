package com.welab.crm.operate.dto.telemarketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 保存电销电销小结请求参数对象
 * @date 2022/3/1 17:49
 */
@Data
@ApiModel(value = "保存电销电销小结请求参数对象")
public class TmkSummaryReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 电销任务唯一Id
     */
    @ApiModelProperty(value = "电销唯一任务Id")
    private String tmkTaskId;

    /**
     * 电话小结码
     */
    @ApiModelProperty(value = "电话小结唯一编码")
    private String contactCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "电销类型")
    private String tmkType;

    @ApiModelProperty(value = "天润唯一通话id")
    private String cdrMainUniqueId;
    @ApiModelProperty(value = "客服系统客户Id")
    private Long customerId;

}
