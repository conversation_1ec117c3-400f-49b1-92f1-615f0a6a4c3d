package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class WorkOrderCollectionUrgeDTO {

    @ApiModelProperty(value = "工单编号列表", name = "orderNoList")
    @NotNull
    @Size(min = 1)
    private List<String> orderNoList;
}
