package com.welab.crm.operate.vo.face;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ColumnWidth(value = 15)
public class FaceDayVO {

    @ExcelProperty(value = "日期")
    @ApiModelProperty(value = "日期", name = "日期")
    private String day;

    @ExcelProperty(value = "发送量")
    @ApiModelProperty(value = "发送量", name = "发送量")
    private Integer sendCount;

    @ExcelProperty(value = "验证量")
    @ApiModelProperty(value = "验证量", name = "验证量")
    private Integer validateCount;

    @ExcelProperty(value = "首次成功量")
    @ApiModelProperty(value = "首次成功量", name = "首次成功量")
    private Integer firstSuccessCount;

    @ExcelProperty(value = "成功量")
    @ApiModelProperty(value = "成功量", name = "成功量")
    private Integer successCount;

    @ExcelProperty(value = "成功率")
    @ApiModelProperty(value = "成功率", name = "成功率")
    private String successRate;

    @ExcelIgnore
    private Integer avgValidateCount;

    @ExcelIgnore
    private Integer unValidateCount;

    @ExcelProperty(value = "平均验证次数(成功)")
    @ApiModelProperty(value = "平均验证次数(成功)", name = "平均验证次数(成功)")
    private String avgValidateTimes;
}
