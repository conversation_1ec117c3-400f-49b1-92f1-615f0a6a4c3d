package com.welab.crm.operate.dto.blackProduction;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.welab.crm.operate.dto.BaseReqDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 黑产请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "黑产请求对象")
public class BlackProductionAddReqDTO extends BaseReqDTO {

	/**
	 * 客户姓名
	 * 新增必填
	 */
	@ApiModelProperty(value = "客户姓名")
	private String name;

	/**
	 * uuid
	 * 新增必填
	 */
	@ApiModelProperty(value = "uuid")
	private String uuid;

	/**
	 * 用户手机号
	 * 新增必填
	 */
	@ApiModelProperty(value = "用户手机号")
	@NotBlank(message = "用户手机号不能为空", groups = GroupInsert.class)
	private String userMobile;


	/**
	 * 黑产手机号
	 * 新增必填
	 */
	@ApiModelProperty(value = "黑产手机号")
	@NotBlank(message = "黑产手机号不能为空", groups = GroupInsert.class)
	private String blackProductionMobile;

	/**
	 * 黑产邮箱
	 * 新增选填
	 */
	@ApiModelProperty(value = "黑产邮箱")
	private String blackProductionEmail;

	/**
	 * 用户类型
	 * 1-确认黑产
	 * 2-疑似黑产
	 * 新增必填
	 */
	@ApiModelProperty(value = "用户类型")
	@NotNull(message = "用户类型不能为空", groups = GroupInsert.class)
	private Integer userType;


	/**
	 * 备注
	 * 选填
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	/**
	 * 文件名列表
	 * 选填
	 */
	@ApiModelProperty(value = "文件名列表")
	private List<String> fileNameList;


	
}
