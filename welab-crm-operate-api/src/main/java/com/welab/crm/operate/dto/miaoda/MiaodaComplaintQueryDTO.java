package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 喵达投诉单查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单查询请求对象")
public class MiaodaComplaintQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单状态 4-待回复 6-已回复 7-已完成
     */
    @ApiModelProperty(value = "投诉单状态", name = "status")
    private Integer status;

    /**
     * 起始时间（分配时间）
     * 格式：yyyy-MM-dd HH:mm:ss，例如：2025-01-01 11:00:00
     */
    @ApiModelProperty(value = "起始时间（格式：yyyy-MM-dd HH:mm:ss）", name = "st", example = "2025-01-01 11:00:00")
    private String st;

    /**
     * 结束时间（分配时间）
     * 格式：yyyy-MM-dd HH:mm:ss，例如：2025-01-01 23:59:59
     */
    @ApiModelProperty(value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）", name = "et", example = "2025-01-01 23:59:59")
    private String et;

    /**
     * 页码，默认第一页
     */
    @ApiModelProperty(value = "页码", name = "page")
    private Integer page = 1;

    /**
     * 每页数量，默认10条，最多30条
     */
    @ApiModelProperty(value = "每页数量", name = "pageSize")
    private Integer pageSize = 30;

    /**
     * 投诉单号
     */
    @ApiModelProperty(value = "投诉单号", name = "sn")
    private String sn;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号", name = "workOrderNo")
    private String workOrderNo;

    /**
     * 投诉人昵称
     */
    @ApiModelProperty(value = "投诉人昵称", name = "nickname")
    private String nickname;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式", name = "phone")
    private String phone;

    /**
     * 同步状态 0-待同步 1-已同步 2-同步失败
     */
    @ApiModelProperty(value = "同步状态", name = "syncStatus")
    private Integer syncStatus;

    /**
     * 自动回复状态 NULL-未处理 1-成功 2-失败
     */
    @ApiModelProperty(value = "自动回复状态", name = "autoReplyStatus")
    private Integer autoReplyStatus;


    @ApiModelProperty(value = "结案状态", name = "coCompleteStatus")
    private String coCompleteStatus;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "orderBy", example = "assigned_at")
    private String orderBy = "assigned_at";

    /**
     * 排序方向 ASC-升序 DESC-降序
     */
    @ApiModelProperty(value = "排序方向", name = "orderDirection", example = "DESC")
    private String orderDirection = "DESC";
}
