package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/26
 */
@Data
@ApiModel(value = "员工效能报表响应对象")
@Excel(fileName = "员工效能报表")
public class ReportStaffEfficiencyVO implements Serializable {

    private static final long serialVersionUID = -4425811026742123970L;

    @ApiModelProperty(value = "时间")
    @ExcelTitleMap(title = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String cno;

    @ApiModelProperty(value = "姓名")
    @ExcelTitleMap(title = "姓名")
    private String staffName;

    @ApiModelProperty(value = "团队")
    @ExcelTitleMap(title = "团队")
    private String groupName;

    @ApiModelProperty(value = "工作总时长")
    @ExcelTitleMap(title = "工作总时长")
    private String loginDuration = "00:00:00";

    @ApiModelProperty(value = "来电总量")
    @ExcelTitleMap(title = "来电总量")
    private Integer callInCount = 0;

    @ApiModelProperty(value = "呼入接起总量")
    @ExcelTitleMap(title = "呼入接起总量")
    private Integer callInConnectedNumber = 0;

    @ApiModelProperty(value = "呼入接通率")
    @ExcelTitleMap(title = "呼入接通率")
    private String callInConnectedRate = "0.00%";

    @ApiModelProperty(value = "呼入通话总时长")
    @ExcelTitleMap(title = "呼入通话总时长")
    private String callInDuration = "00:00:00";

    @ApiModelProperty(value = "呼入通话均长, 单位：秒")
    @ExcelTitleMap(title = "呼入通话均长")
    private Integer callInAvgDuration = 0;

    @ApiModelProperty(value = "呼入话后处理总时长")
    @ExcelTitleMap(title = "呼入话后处理总时长")
    private String wrapUpDuration = "00:00:00";

    @ApiModelProperty(value = "呼出总数")
    @ExcelTitleMap(title = "呼出总数")
    private Integer outboundCount = 0;

    @ApiModelProperty(value = "呼出接通量")
    @ExcelTitleMap(title = "呼出接通量")
    private Integer outboundConnectedNumber = 0;

    @ApiModelProperty(value = "呼出通话总时长")
    @ExcelTitleMap(title = "呼出通话总时长")
    private String outboundDuration = "00:00:00";

    @ApiModelProperty(value = "呼出通话均长, 单位：秒")
    @ExcelTitleMap(title = "呼出通话均长")
    private Integer outboundAvgDuration = 0;

    @ApiModelProperty(value = "呼出连通率")
    @ExcelTitleMap(title = "呼出连通率")
    private String outboundConnectedRate = "0.00%";

    @ApiModelProperty(value = "工时利用率")
    @ExcelTitleMap(title = "工时利用率")
    private String workingHourUtilization = "0.00%";


    @ApiModelProperty(value = "工单提单时长")
    @ExcelTitleMap(title = "工单提单时长")
    private Integer submitOrderDuration = 0;

    @ApiModelProperty(value = "通话利用率")
    @ExcelTitleMap(title = "通话利用率")
    private String callUtilization = "0.00%";

    @ApiModelProperty(value = "首问解决率")
    @ExcelTitleMap(title = "首问解决率")
    private String resolvedRate = "0.00%";

    @ApiModelProperty(value = "客户满意度")
    @ExcelTitleMap(title = "客户满意度")
    private String satisfiedRate = "0.00%";

    @ApiModelProperty(value = "质检分数,质检报表没做，因此没有数据")
    private String qualityScore;

    @ApiModelProperty(value = "质检合格率,质检报表没做，因此没有数据")
    private String qualifiedRate;

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "团队code")
    private String groupCode;

    @ApiModelProperty(value = "空闲总时长")
    private String idleDuration = "00:00:00";

    @ApiModelProperty(value = "用餐总时长")
    private String eatingDuration = "00:00:00";

    @ApiModelProperty(value = "会议总时长")
    private String meetingDuration = "00:00:00";

    @ApiModelProperty(value = "培训总时长")
    private String trainingDuration = "00:00:00";
}
