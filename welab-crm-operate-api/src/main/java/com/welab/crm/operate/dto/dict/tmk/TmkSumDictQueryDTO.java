package com.welab.crm.operate.dto.dict.tmk;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TmkSumDictQueryDTO extends BaseRequestDTO {

    /**
     * 业务产品类型:1.进件模式 2.额度模式 3.超级会员 4.钱夹谷谷 5.uuid(待定)
     * 用户可以选择多个,用','隔开
     */
    @ApiModelProperty(value = "业务类型", example = "income")
    private String businessType;

    /**
     * 营销小结内容
     */
    @ApiModelProperty(value = "营销小结内容")
    private String summaryContent;

    /**
     * 营销小结编号
     */
    @ApiModelProperty(value = "营销小结编号")
    private String summaryCode;

    /**
     * 联系结果对应的编码(与业务字典(detail字段)中配置的对应)
     */
    @ApiModelProperty(value = "联系结果编码", example = "200")
    private String resultCode;

    /**
     * 优先级(此字段表示的优先级低于resultCode字段)
     */
    @ApiModelProperty(value = "营销小结优先级", example = "1")
    private Integer sort;
}
