package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel("电销放款邀约报表返回对象")
@Excel(fileName = "电销放款邀约报表", sheetName = "电销放款邀约报表")
public class TmkLoanReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;
    
    @ApiModelProperty("合同号")
    @ExcelTitleMap(title = "合同号")
    private String applicationId;
    
    @ApiModelProperty("进件渠道")
    @ExcelTitleMap(title = "进件渠道")
    private String applyOrigin;
    
    @ApiModelProperty("产品名称")
    @ExcelTitleMap(title = "产品名称")
    private String productName;
    
    @ApiModelProperty("审批时间")
    @ExcelTitleMap(title = "审批时间")
    private Date approvedAt;
    
    @ApiModelProperty("状态")
    @ExcelTitleMap(title = "状态")
    private String state;
    
    @ApiModelProperty("审批金额")
    @ExcelTitleMap(title = "审批金额")
    private String amount;

    @ApiModelProperty("确认金额")
    @ExcelTitleMap(title = "确认金额")
    private String confirmedAmount;
    
    @ApiModelProperty("话务员")
    @ExcelTitleMap(title = "话务员")
    private String staffId;

    @ApiModelProperty("话务组")
    @ExcelTitleMap(title = "话务组")
    private String groupCode;
    
    @ApiModelProperty("联系结果")
    @ExcelTitleMap(title = "联系结果")
    private String resultCode;
    
    @ApiModelProperty("电话小结")
    @ExcelTitleMap(title = "电话小结")
    private String summaryContent;
    
    @ApiModelProperty("备注")
    @ExcelTitleMap(title = "备注")
    private String comment;
    
    @ApiModelProperty("小结时间")
    @ExcelTitleMap(title = "小结时间")
    private Date summaryAt;
    
    @ApiModelProperty("确认时间")
    @ExcelTitleMap(title = "确认时间")
    private Date confirmedAt;
    
    @ApiModelProperty("确认时间差")
    private Integer timeDif;

    @ApiModelProperty("确认时间差")
    @ExcelTitleMap(title = "确认时间差")
    private String intervals;
    
    @ApiModelProperty("创建时间")
    @ExcelTitleMap(title = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty("分流标签")
    @ExcelTitleMap(title = "分流标签")
    private String diversionTag;
    
    @ApiModelProperty("电话开始时间")
    @ExcelTitleMap(title = "电话开始时间")
    private Date callStartTime;
    
    @ApiModelProperty("电话结束时间")
    @ExcelTitleMap(title = "电话结束时间")
    private Date callEndTime;

    @ApiModelProperty("通话时长(s)")
    @ExcelTitleMap(title = "通话时长(s)")
    private Long callTime;



}
