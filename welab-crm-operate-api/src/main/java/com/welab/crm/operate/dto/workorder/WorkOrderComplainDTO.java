package com.welab.crm.operate.dto.workorder;

import com.welab.collection.interview.utils.DateUtils;
import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 工单统计报表-客户投诉报表参数对象
 */
@Getter
@Setter
public class WorkOrderComplainDTO extends PageQueryDTO {

    @DateTimeFormat(pattern = DateUtils.DATE_FORMAT)
    @ApiModelProperty(name = "开始时间", value = "startTime")
    private Date startTime;

    @DateTimeFormat(pattern = DateUtils.DATE_FORMAT)
    @ApiModelProperty(name = "结束时间", value = "endTime")
    private Date endTime;

    /**
     * 投诉级别
     */
    @ApiModelProperty(name = "投诉级别", value = "complainLevel")
    private String complainLevel;
}
