package com.welab.crm.operate.vo.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 喵达投诉单回复详情VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单回复详情对象")
public class MiaodaReplyDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 回复人 1-用户补充 2-商家回复
     */
    @ApiModelProperty(value = "回复人", name = "sender")
    private Integer sender;

    /**
     * 回复人描述
     */
    @ApiModelProperty(value = "回复人描述", name = "senderDesc")
    private String senderDesc;

    /**
     * 用户补充/商家回复内容
     */
    @ApiModelProperty(value = "回复内容", name = "content")
    private String content;

    /**
     * 回复是否隐藏 0-公开 1-隐藏
     */
    @ApiModelProperty(value = "回复是否隐藏", name = "contentHide")
    private Integer contentHide;

    /**
     * 附件是否隐藏 0-公开 1-隐藏
     */
    @ApiModelProperty(value = "附件是否隐藏", name = "attachHide")
    private Integer attachHide;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间", name = "replyedAt")
    private Date replyedAt;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表", name = "attaches")
    private List<MiaodaAttachmentVO> attaches;
}
