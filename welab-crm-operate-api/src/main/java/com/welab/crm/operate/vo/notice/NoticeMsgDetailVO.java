package com.welab.crm.operate.vo.notice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 通知公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@ApiModel(description = "消息详情返回对象")
public class NoticeMsgDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 通知标题
     */
    @ApiModelProperty(value = "标题", name = "title")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容", name = "content")
    private String content;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人", name = "sender")
    private String sender;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "接收者", name = "收件人")
    private String receiver;

    /**
     * 附件下载路径
     */
    @ApiModelProperty(value = "文件名", name = "fileName")
    private String fileName;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型", name = "type")
    private String type;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", name = "publishTime")
    private Date publishTime;


}
