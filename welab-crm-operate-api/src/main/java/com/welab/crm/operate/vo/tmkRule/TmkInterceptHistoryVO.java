package com.welab.crm.operate.vo.tmkRule;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销拦截规则响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "分单规则响应对象")
public class TmkInterceptHistoryVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;
	
	/**
     * 主键id
     */
	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;

    /**
     * 拦截规则ID
     */
	@ApiModelProperty(value = "拦截规则ID", name = "ruleId")
    private Long ruleId;

    /**
     * 拦截日期
     */
	@ApiModelProperty(value = "拦截日期", name = "interceptDate")
    private Date interceptDate;

    /**
     * 拦截数量
     */
	@ApiModelProperty(value = "拦截数量", name = "num")
    private Integer num;

    /**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;
}

