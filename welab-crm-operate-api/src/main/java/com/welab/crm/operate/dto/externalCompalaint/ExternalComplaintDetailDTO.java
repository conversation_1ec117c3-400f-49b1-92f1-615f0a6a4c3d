package com.welab.crm.operate.dto.externalCompalaint;

import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ExternalComplaintDetailDTO", description = "外部投诉DTO")
public class ExternalComplaintDetailDTO {
	
	@ApiModelProperty(value = "主键id", name = "id")
	private Long id;

	@ApiModelProperty(value = "登录名称", name = "loginName")
	private String loginName;

	@ApiModelProperty(value = "渠道单号", name = "channelOrderNo")
	private List<String> channelOrderNo;


	@ApiModelProperty(value = "借据号", name = "debtOrderNo")
	private List<String> debtOrderNo;


	@ApiModelProperty(value = "身份证号", name = "idNo")
	private String idNo;


	@ApiModelProperty(value = "客户姓名", name = "name")
	private String name;
	
	@ApiModelProperty(value = "客户姓名加密", name = "aesName")
	private String aesName;

	@ApiModelProperty(value = "手机号", name = "mobile")
	private String mobile;


	@ApiModelProperty(value = "备用手机号列表", name = "mobileBakList")
	private List<String> mobileBakList;

	@ApiModelProperty(value = "邮箱", name = "email")
	private String email;

	@ApiModelProperty(value = "工单编号", name = "orderNo")
	private String orderNo;

	@ApiModelProperty(value = "加急", name = "urgentFlag")
	private String urgentFlag;

	@ApiModelProperty(value = "回访", name = "callbackFlag")
	private String callbackFlag;


	@ApiModelProperty(value = "快捷工单id", name = "quickOrderId")
	private Long quickOrderId;
	
	@ApiModelProperty(value = "快捷工单", name = "quickOrder")
	private String quickOrder;


	@ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
	private String complaintsChannel;

	@ApiModelProperty(value = "资方简称", name = "fundName")
	private String fundName;

	@ApiModelProperty(value = "工单类型", name = "type")
	private String type;

	@ApiModelProperty(value = "工单大类", name = "orderOneClass")
	private Long orderOneClass;

	@ApiModelProperty(value = "工单二类", name = "orderTwoClass")
	private Long orderTwoClass;

	@ApiModelProperty(value = "工单三类", name = "orderThreeClass")
	private Long orderThreeClass;

	@ApiModelProperty(value = "子工单分类", name = "orderCase")
	private Long orderCase;

	@ApiModelProperty(value = "反馈内容", name = "description")
	private String description;
	
	@ApiModelProperty(value = "用户类型;source-渠道，partner-资金方", name = "userType")
	private String userType;
	
	@ApiModelProperty(value = "工单状态", name = "orderStatus")
	private String orderStatus;

	@ApiModelProperty(value = "附件名称列表", name = "fileNameList")
	private List<WoAttachmentVO> fileList;
}
