package com.welab.crm.operate.dto.workorder;

import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 *
 * 工单附件保存DTO
 * <AUTHOR>
 * @date 2021/12/22 11:34
 */

@Data
public class WoAttachmentReqDTO implements Serializable {

    private static final long serialVersionUID = -5664737425969824471L;


    @ApiModelProperty(value = "附件名", name = "fileName")
    private List<WoAttachmentVO> fileList;

    @ApiModelProperty(value = "工单表业务主键", name = "id")
    private Long id;
    
    @ApiModelProperty(value = "员工id")
    private Long staffId;
}
