package com.welab.crm.operate.vo.repay;

import lombok.Getter;
import lombok.Setter;

/**
 * 还款明细查询返回对象
 *
 * <AUTHOR>
 * @date 2022-11-05
 */
@Getter
@Setter
public class RepayQueryVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 下载文件路径
     */
    private String fileUrl;

    /**
     * 本次查询的人数量
     */
    private Integer userCount;

    /**
     * 贷款类型
     */
    private String loanType;

    /**
     * 任务状态: 0-执行中,1-已完成
     */
    private String taskState;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 申请时间
     */
    private String gmtCreate;
}
