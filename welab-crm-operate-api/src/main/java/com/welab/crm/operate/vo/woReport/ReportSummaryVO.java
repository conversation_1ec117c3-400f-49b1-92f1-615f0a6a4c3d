package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleGroup;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/24
 */
@Data
@ApiModel(value = "工单统计报表汇总响应对象")
@Excel(fileName = "工单统计报表汇总")
public class ReportSummaryVO implements Serializable {

    private static final long serialVersionUID = 6093561671969752117L;

    @ApiModelProperty(value = "工单类型")
    @ExcelTitleGroup(title = "工单类型",mergeNum = 1)
    @ExcelTitleMap(title = "工单类型")
    private String type;

    @ApiModelProperty(value = "工单总量")
    @ExcelTitleGroup(title = "工单总量",mergeNum = 1)
    @ExcelTitleMap(title = "工单总量")
    private Integer totalNumber;

    @ApiModelProperty(value = "结案量")
    @ExcelTitleGroup(title = "结案量",mergeNum = 1)
    @ExcelTitleMap(title = "结案量")
    private Integer closedNumber;

    @ApiModelProperty(value = "结案率")
    @ExcelTitleGroup(title = "结案率",mergeNum = 1)
    @ExcelTitleMap(title = "结案率")
    private String closedRate;

    @ApiModelProperty(value = "普通工单/现金分期")
    @ExcelTitleGroup(title = "普通工单/现金分期",subTitle = true)
    private ReportSummaryDetailsVO first;

    @ApiModelProperty(value = "投诉工单/钱夹谷谷")
    @ExcelTitleGroup(title = "投诉工单/钱夹谷谷",subTitle = true)
    private ReportSummaryDetailsVO second;

    @ApiModelProperty(value = "舆情工单/淘新机")
    @ExcelTitleGroup(title = "舆情工单/淘新机",subTitle = true)
    private ReportSummaryDetailsVO third;

    @ApiModelProperty(value = "监管工单/电商业务")
    @ExcelTitleGroup(title = "监管工单/电商业务",subTitle = true)
    private ReportSummaryDetailsVO fourth;

    @ApiModelProperty(value = "资方工单")
    @ExcelTitleGroup(title = "资方工单",subTitle = true)
    private ReportSummaryDetailsVO fifth;
}
