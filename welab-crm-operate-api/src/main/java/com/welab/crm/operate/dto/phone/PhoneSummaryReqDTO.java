package com.welab.crm.operate.dto.phone;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/22 16:46
 */
@Data
public class PhoneSummaryReqDTO extends BaseReqDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "用户Id", name = "userId", example = "234561")
    private Long customerId;

    @ApiModelProperty(value = "用户uuid", name = "uuid", example = "234561")
    private String uuid;

    @ApiModelProperty(value = "userId", name = "userId", example = "234561")
    private Integer userId;

    @ApiModelProperty(value = "电话小结内容", name = "callSummary", example = "钱夹谷谷,还款问题,还款绑卡问题")
    private String callSummary;

    @ApiModelProperty(value = "电话小结类型码", name = "callSummaryCode", example = "A1,A13,B123")
    private String callSummaryCode;

    @ApiModelProperty(value = "任务Id", name = "taskId", example = "A1231414")
    private String taskId;

    /**
     * 保存小结时间
     */
    private Date saveSummaryTime;

    @ApiModelProperty(value = "备注",name = "callComment", example = "已告知客户如何还款")
    private String callComment;

    @ApiModelProperty(value = "名单类型,外呼时才有这个字段",name = "mapName",example = "1yfas")
    private String mapName;

    @ApiModelProperty(value = "员工Id",name = "staffId",example = "zhangsan")
    private String staffId;

    @ApiModelProperty(value = "客户号码", name = "mobile", example = "130xxxxxxxx")
    private String mobile;

    @ApiModelProperty(value = "通话记录Id", name = "cdrRequestUniqueId", example = "sip-24-1638864387.123657")
    private String cdrMainUniqueId;

    /**
     * 客户Id列表
     */
    private List<Long> customerIdList;



    @ApiModelProperty(value = "咨询状态 1-已逾期；2-待还款")
    private Integer consultationStatus;

    @ApiModelProperty(value = "逾期标签", name = "逾期标签")
    private String overDueLabel;
}
