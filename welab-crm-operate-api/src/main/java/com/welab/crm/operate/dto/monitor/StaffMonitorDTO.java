package com.welab.crm.operate.dto.monitor;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * 平台用户监控请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "平台用户监控请求对象")
public class StaffMonitorDTO extends BaseReqDTO {
	
	@ApiModelProperty(value = "开始时间")
	@NotBlank
	private String startTime;
	
	@ApiModelProperty(value = "结束时间")
	@NotBlank
	private String endTime;
	
	@ApiModelProperty(value = "组别")
	private List<String> groupCodes;
	
	@ApiModelProperty(value = "时间间隔；day-日；range-周期")
	private String period;
}
