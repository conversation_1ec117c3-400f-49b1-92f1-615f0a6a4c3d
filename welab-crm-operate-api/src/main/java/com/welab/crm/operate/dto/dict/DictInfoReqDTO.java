package com.welab.crm.operate.dto.dict;

import java.io.Serializable;
import java.util.Collection;

import com.welab.crm.operate.dto.BaseReqDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DictInfoReqDTO extends BaseReqDTO implements Serializable{

	private static final long serialVersionUID = -4280882703338771503L;
	
	/**
	 * 属性分组
	 */
	@ApiModelProperty(value="属性分组", name="category")
	//@NotBlank(message="operate.dict.category.blank")
	private String category;
	
	/**
	 * 分组列表
	 */
	@ApiModelProperty(value="分组列表", name="collectionOrg",hidden=true)
	private Collection<String> categorys;
	
	/**
	 * 自定义类型
	 */
	@ApiModelProperty(value="自定义类型", name="type")
	private String type;

	/**
	 * 配置说明
	 */
	@ApiModelProperty(value="配置说明", name="content")
	private String content;
	/**
	 * 描述
	 */
	@ApiModelProperty(value="描述", name="detail")
	private String detail;

	/**
	 * 状态 0-有效 1-无效
	 */
	@ApiModelProperty(value="状态", name="status",example="0")
	private Integer status;
}
