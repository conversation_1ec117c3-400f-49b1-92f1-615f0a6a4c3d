package com.welab.crm.operate.vo.phone;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class CallInfoStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 1 座席接听; 2 已呼叫座席,座席未接听; 3 系统接听; 4 系统未接-IVR配置错误; 5 系统未接-停机;
     */
    private String cdrStatus;

    /**
     * 1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫
     */
    private String cdrCallType;

    /**
     * 接听的员工坐席号
     */
    private String cdrCalleeCno;


    /**
     * 双方接听时长
     */
    private String cdrEndBridgeTime;


    /**
     * 坐席工号
     */
    private String cdrCno;

    /**
     * 只有日期的时间,不包括时间点
     */
    private String day;
}
