package com.welab.crm.operate.vo.blacklist;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class KfBlackListApprovalVO implements Serializable {

    private static final long serialVersionUID = 4003307634229655465L;

    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 身份证号
     */
    private String idNo;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 黑名单类型
     */
    private String blackType;
    /**
     * 黑名单类型编码
     */
    private String blackTypeCode;
    /**
     * 添加原因
     */
    private String addReason;
    /**
     * 有效开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date validStartTime ;
    /**
     * 有效结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date validEndTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否有效，1-是 2-否
     */
    private String isValid;
    /**
     * 主键id
     */
    private String id;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建组
     */
    private String createGroup;
    /**
     * 数据来源，1-人工添加 2-批量导入
     */
    private String dataSource;
    /**
     * 有效开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private String startTime ;
    /**
     * 有效结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private String endTime;
    /**
     * 部门
     */
    private String department;
    /**
     * 用户ID
     */
    private String uuid;
    /**
     * 拉黑天数
     */
    private Integer blackDay;
    /**
     * 审批人
     */
    private String operateUser;
    /**
     * 审批状态 0初审 1复审 2审批完成
     */
    private Integer operateState;
    /**
     * 审批意见
     */
    private String approvalComment;
}
