package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/23
 */
@Data
@ApiModel(value = "工单批量回复意见请求对象")
public class WorkOrderBatchSaveDTO implements Serializable {

    private static final long serialVersionUID = 1375844604054246516L;

    @ApiModelProperty(value = "回复意见", name = "comment")
    private String comment;

    @ApiModelProperty(value = "工单信息列表", name = "list")
    @NotNull
    private List<WorkOrderBatchReplyDTO> list;
}
