package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: 代扣报表查询DTO
 * @date 2022/4/24 15:02
 */
@Data
@ApiModel(value = "报表查询基础DTO")
public class ReportBaseDTO implements Serializable {

    private static final Long serialVersionUID = 13L;

    @ApiModelProperty(value = "开始时间",name = "startTime")
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "结束时间", name = "endTime")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    @ApiModelProperty(value = "时间间隔", name = "period", example = "day week month")
    private String period;

    @ApiModelProperty(value = "是否钱包", name = "isWallet",example = "1 为钱包,0 为现金贷")
    private Integer isWallet;

    @ApiModelProperty(value = "还款方式")
    private String repayMethod;
}
