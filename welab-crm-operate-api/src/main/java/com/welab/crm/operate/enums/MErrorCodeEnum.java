package com.welab.crm.operate.enums;

import com.welab.common.response.Response;

/**
 * @description 返回码枚举类
 * <AUTHOR>
 * @date 2021-11-08 17:57:08
 * @version v1.0
 */
public enum MErrorCodeEnum {
    // 13110000 ~ 13110099
    IN_LOAN_OFFLINE_IMPORT_FILE_EMPTY("13110000", "未收到文件，请重新上传"),
    IN_LOAN_OFFLINE_IMPORT_FILE_SAVE_FAILED("13110001", "保存文件失败，请重新上传"),
    IN_LOAN_OFFLINE_IMPORT_HEAD_PARSE_FAILED("13110002", "解析excel表头失败，请检查文件表头，确保满足要求"),
    IN_LOAN_OFFLINE_IMPORT_FILE_SAVE_CLOUD_FAILED("13110003", "保存文件到云端失败，请重新上传"),
    IN_LOAN_OFFLINE_IMPORT_DATA_PARSE_FAILED("13110004", "解析excel数据失败，请检查文件，确保数据格式满足要求"),
    IN_LOAN_OFFLINE_IMPORT_TYPE_NOT_SUPPORT("13110005", "暂时不支持此种业务操作"),
    IN_LOAN_OFFLINE_IMPORT_FILE_TYPE_NOT_SUPPORT("13110006", "暂时不支持上传此种文件类型"),
    ;

    // 将当前枚举类的返回信息数据注入到统一返回的MAP对象里
    static {
        for (MErrorCodeEnum responsCodeTypeEnum : MErrorCodeEnum.values()) {
            Response.RESPONSE_MAP.put(responsCodeTypeEnum.getCode(),
                    responsCodeTypeEnum.getMessage());
        }
    }

    private String code;
    private String message;

    private MErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return String.valueOf(code);
    }

    public String getMessage() {
        return message;
    }

}
