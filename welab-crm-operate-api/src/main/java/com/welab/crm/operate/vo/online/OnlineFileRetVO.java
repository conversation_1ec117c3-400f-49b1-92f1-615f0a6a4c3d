package com.welab.crm.operate.vo.online;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class OnlineFileRetVO implements Serializable {
	
	private static final long serialVersionUID = 1L;

	/**
	 * 文件url地址
	 */
	private String url;

	/**
	 * 文件类型
	 * video - 视频
	 * image - 图片
	 * voice - 音频
	 * file - 文件
	 */
	private String type;

	/**
	 * 用户发送时间
	 * 格式:  yyyy-MM-dd HH:mm:ss
	 */
	private String date;
	
}
