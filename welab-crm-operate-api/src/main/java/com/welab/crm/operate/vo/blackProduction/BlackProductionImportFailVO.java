package com.welab.crm.operate.vo.blackProduction;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Data;

import java.io.Serializable;

/**
 * 黑产导入失败信息提示
 * <AUTHOR>
 */
@Data
@Excel(fileName="uuid失败明细数据")
public class BlackProductionImportFailVO implements Serializable {

	private static final long serialVersionUID = -4286874807964442977L;

	@ExcelTitleMap(title = "uuid失败明细数据")
	private String uuid;

}
