package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/25
 */
@Data
@ApiModel(value = "通话统计报表请求对象")
public class ReportPhoneSummaryDTO implements Serializable {

    private static final long serialVersionUID = 8016632687132990384L;

    @ApiModelProperty(value = "统计报表类型， 1：日报表 2：周报表 3：月报表 4：自定义时间", name = "报表类型")
    @NotNull(message = "统计报表类型不能为null")
    private Integer timeRangeType;

    @ApiModelProperty(value = "0:分时 1：分日 2：汇总 8：分半时", name = "统计方法")
    @NotNull(message = "统计方法不能为null")
    private Integer statisticMethod;

    @ApiModelProperty(value = "统计日期的开始时间，格式：yyyy-MM-dd", name = "开始时间")
    @NotBlank(message = "统计日期的开始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "当timeRangeType值为4时必选 说明：统计日期的结束时间，格式：yyyy-MM-dd", name = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "根据热线号码查询指定报表数据，支持按照多个热线号码进行查询，多个热线号码之间使用英文逗号','分隔，默认查询账户下所有热线号码的数据", name = "热线号码")
    private String hotline;

    @ApiModelProperty(value = "0~23,默认值为0，即0点。取值为实际时间小时数。例如4点，则取值为4", name = "统计时段开始时间")
    private Integer startHour;

    @ApiModelProperty(value = "0~23,默认值为23，包含endHour值那一个小时，比如默认值是23，包含23:00-24:00那个小时", name = "统计时段结束时间")
    private Integer endHour;
}
