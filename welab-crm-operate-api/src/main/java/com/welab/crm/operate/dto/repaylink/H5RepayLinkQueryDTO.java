package com.welab.crm.operate.dto.repaylink;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * h5还款链接列表查询请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "h5还款链接列表查询请求对象")
public class H5RepayLinkQueryDTO extends BaseReqDTO {


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;


    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String uuid;


    /**
     * 贷款号
     */
    @ApiModelProperty(value = "贷款号")
    private String appNo;

    /**
     * 还款方式
     */
    @ApiModelProperty(value = "还款方式")
    private String repayMethod;

    /**
     * 资金方名称
     */
    @ApiModelProperty(value = "资金方名称")
    private String partnerName;

    /**
     * 话务组
     */
    @ApiModelProperty(value = "话务组")
    private String groupName;
}
