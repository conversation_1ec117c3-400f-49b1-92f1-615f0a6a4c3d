package com.welab.crm.operate.vo.phone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/25
 */
@Data
@ApiModel(value = "通话统计报表汇总响应对象")
public class ReportPhoneSummaryVO implements Serializable {

    private static final long serialVersionUID = 7739603652263115841L;

    @ApiModelProperty(value = "每页记录数", name = "每页记录数")
    private String pageSize;

    @ApiModelProperty(value = "总记录数", name = "总记录数")
    private String totalCount;

    @ApiModelProperty(value = "总页数", name = "总页数")
    private String totalPageCount;

    @ApiModelProperty(value = "报表来电分析", name = "报表来电分析")
    private List<ReportPhoneSummaryItem> list;

    /*@Data
    @ApiModel(value = "通话统计报表响应对象")
    public static class ReportPhoneSummaryItem implements Serializable{

        private static final long serialVersionUID = 8306595874997073486L;

        @ApiModelProperty(value = "日期,格式为yyyy-MM-dd,当统计方法为分日时返回", name = "日期,格式为yyyy-MM-dd,当统计方法为分日时返回")
        private String day;

        @ApiModelProperty(value = "小时，取值 0-23，当统计方法为分时时返回", name = "小时，取值 0-23，当统计方法为分时时返回")
        private String hour;

        @ApiModelProperty(value = "分钟，取值 [0,30)记为30,[30,60)记为60,当统计方法为分半时时返回", name = "分钟，取值 [0,30)记为30,[30,60)记为60,当统计方法为分半时时返回")
        private String minute;

        @ApiModelProperty(value = "呼叫中心id", name = "呼叫中心id")
        private String enterpriseId;

        @ApiModelProperty(value = "热线号码", name = "热线号码")
        private String hotline;

        @ApiModelProperty(value = "呼入量", name = "呼入量")
        private String ibTotalCount;

        @ApiModelProperty(value = "呼入量按来电电话类型", name = "呼入量按来电电话类型")
        private String ibTotalTelLandline;

        @ApiModelProperty(value = "呼入量按来电电话类型", name = "呼入量按来电电话类型")
        private String ibTotalTelMobile;

        @ApiModelProperty(value = "IVR接通量", name = "IVR接通量")
        private String ibIvrCount;

        @ApiModelProperty(value = "呼入转移数（来电转移的次数，每转移一次计1次）", name = "呼入转移数（来电转移的次数，每转移一次计1次）")
        private String ibTransferCount;

        @ApiModelProperty(value = "直转电话/分机数", name = "直转电话/分机数")
        private String ibDirectTel;

        @ApiModelProperty(value = "直转队列数", name = "直转队列数")
        private String ibDirectQueue;

        @ApiModelProperty(value = "直转座席数", name = "直转座席数")
        private String ibDirectCno;

        @ApiModelProperty(value = "系统未接听数", name = "系统未接听数")
        private String ibSystemNoAnswer;

        @ApiModelProperty(value = "呼入量VIP", name = "呼入量VIP")
        private String ibTotalVip;

        @ApiModelProperty(value = "黑名单量", name = "黑名单量")
        private String ibRestrictCount;

        @ApiModelProperty(value = "技能组呼叫量", name = "技能组呼叫量")
        private String ibQueueCount;

        @ApiModelProperty(value = "接听量", name = "接听量")
        private String ibAnsweredCount;

        @ApiModelProperty(value = "未接数", name = "未接数")
        private String ibUnansweredCount;

        @ApiModelProperty(value = "总时长", name = "总时长")
        private String totalTime;

        @ApiModelProperty(value = "平均时长", name = "平均时长")
        private String avgTime;

        @ApiModelProperty(value = "总通话时间", name = "总通话时间")
        private String totalBridgeTime;

        @ApiModelProperty(value = "最长通话时间", name = "最长通话时间")
        private String maxBridgeTime;

        @ApiModelProperty(value = "最短通话时间", name = "最短通话时间")
        private String minBridgeTime;

        @ApiModelProperty(value = "平均通话时间", name = "平均通话时间")
        private String avgBridgeTime;

        @ApiModelProperty(value = "座席接听时长", name = "座席接听时长")
        private String ibAgentAnsweredTime;

        @ApiModelProperty(value = "客户接听时长", name = "客户接听时长")
        private String ibCustomerAnsweredTime;

        @ApiModelProperty(value = "呼入量数组，数组长度为11，代表呼入时间在：10秒内、10-30秒内、30-60秒内、1-2分钟内、2-5分钟内、5-10分钟内、10-20分钟内、20-30分钟内、30-60分钟内、1-2小时内、2小时以上的呼入个数", name = "呼入量数组，数组长度为11，代表呼入时间在：10秒内、10-30秒内、30-60秒内、1-2分钟内、2-5分钟内、5-10分钟内、10-20分钟内、20-30分钟内、30-60分钟内、1-2小时内、2小时以上的呼入个数")
        private List<String> ibTotalDurationArray;

        @ApiModelProperty(value = "接听量分布,数组长度为11，代表接听时间在：10秒内、10-30秒内、30-60秒内、1-2分钟内、2-5分钟内、5-10分钟内、10-20分钟内、20-30分钟内、30-60分钟内、1-2小时内、2小时以上的接听个数", name = "接听量分布,数组长度为11，代表接听时间在：10秒内、10-30秒内、30-60秒内、1-2分钟内、2-5分钟内、5-10分钟内、10-20分钟内、20-30分钟内、30-60分钟内、1-2小时内、2小时以上的接听个数")
        private List<String> ibAnsweredDurationArray;

        @ApiModelProperty(value = "呼损率=（总来电数-人工接听数）/总来电数", name = "呼损率=（总来电数-人工接听数）/总来电数")
        private String callLossRate;

        @ApiModelProperty(value = "重复来电数", name = "重复来电数")
        private String ibReCallCount;

        @ApiModelProperty(value = "时长", name = "时长")
        private String durationType;

        @ApiModelProperty(value = "比率", name = "比率")
        private String compareType;

        @ApiModelProperty(value = "热线重复来电率", name = "热线重复来电率")
        private String ibReCallRate;

        @ApiModelProperty(value = "重复来电3次以上号码数(半小时内同一号码重复来电次数大于3，值加1)", name = "重复来电3次以上号码数(半小时内同一号码重复来电次数大于3，值加1)")
        private String ibReCallMorethreeCount;

        @ApiModelProperty(value = "处理数", name = "处理数")
        private String processType;

        @ApiModelProperty(value = "重复来电3次号码数(半小时内同一号码重复来电次数为3，值加1)", name = "重复来电3次号码数(半小时内同一号码重复来电次数为3，值加1)")
        private String ibReCallThreeCount;

        @ApiModelProperty(value = "未重复来电号码数", name = "未重复来电号码数")
        private String ibReCallOneCount;

        @ApiModelProperty(value = "重复来电2次号码数(半小时内同一号码重复来电次数为2，值加1)", name = "重复来电2次号码数(半小时内同一号码重复来电次数为2，值加1)")
        private String ibReCallTwoCount;

        @ApiModelProperty(value = "计费分钟数", name = "计费分钟数")
        private String ibTotalMinute;

        @ApiModelProperty(value = "进入数", name = "进入数")
        private String enterType;

        @ApiModelProperty(value = "IVR放弃数", name = "IVR放弃数")
        private String ibIvrAbandonCount;

        @ApiModelProperty(value = "呼叫量", name = "呼叫量")
        private String callVolumeType;

        @ApiModelProperty(value = "汇总", name = "汇总")
        private String collectType;

        @ApiModelProperty(value = "人工接听率", name = "人工接听率")
        private String ibAnsweredRate;

        @ApiModelProperty(value = "进入队列放弃数", name = "进入队列放弃数")
        private String ibQnoAbandonCount;

        @ApiModelProperty(value = "呼入总等待时长 ：从系统接听时间到座席接听", name = "呼入总等待时长 ：从系统接听时间到座席接听")
        private String ibTotalWaitTime;

        @ApiModelProperty(value = "呼入最大等待时长", name = "呼入最大等待时长")
        private String ibMaxWaitTime;

        @ApiModelProperty(value = "呼入最小等待时长", name = "呼入最小等待时长")
        private String ibMinWaitTime;

        @ApiModelProperty(value = "呼入放弃总等待时长 ：从系统结束时间到系统接听时间", name = "呼入放弃总等待时长 ：从系统结束时间到系统接听时间")
        private String ibAbandonTotalWaitTime;

        @ApiModelProperty(value = "呼入放弃最大等待时长", name = "呼入放弃最大等待时长")
        private String ibAbandonMaxWaitTime;

        @ApiModelProperty(value = "呼入放弃最小等待时长", name = "呼入放弃最小等待时长")
        private String ibAbandonMinWaitTime;
    }*/
}
