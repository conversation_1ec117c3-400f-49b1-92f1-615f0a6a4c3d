package com.welab.crm.operate.vo.loan;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class LoanImportVO {

    /**
     * id
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 名单类型（0:委外,1:债转）
     */
    @ApiModelProperty(value = "名单类型（0:委外,1:债转）")
    private String type;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer count;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;
}
