package com.welab.crm.operate.dto.repay;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户还款明细查询传输对象
 *
 * <AUTHOR>
 * @date 2022-11-05
 */
@Getter
@Setter
public class RepayQueryDTO extends BaseReqDTO {

    /**
     * 申请时间: 开始时间
     */
    @ApiModelProperty(value = "申请开始时间", name = "startTime", example = "2022/11/05 18:20")
    private String startTime;

    /**
     * 申请时间: 结束时间
     */
    @ApiModelProperty(value = "申请结束时间", name = "endTime", example = "2022/11/05 18:25")
    private String endTime;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", name = "createUser", example = "staffName")
    private String createUser;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态", name = "taskState", example = "整形: 0-执行中, 1-已完成")
    private Integer taskState;

    /**
     * 贷款类型
     */
    @ApiModelProperty(value = "贷款类型", name = "loanType", example = "贷款类型: loan-现金贷, wallet-钱夹谷谷")
    private String loanType;
}
