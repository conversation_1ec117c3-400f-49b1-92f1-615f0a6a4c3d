package com.welab.crm.operate.dto.operate;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: UrgentApprovalReqDTO
 * @Description:
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2021/11/18 11:15
 */
@Data
public class UrgentApprovalReqDTO {


    @ApiModelProperty(value = "贷款号", name = "applicationId", example = "20213129903194190")
    @NotBlank(message = "贷款号不能为空")
    private String applicationId;

    @ApiModelProperty(value = "用户Id", name = "userId", example = "231241")
    @NotNull(message = "userId不能为空")
    private Integer userId;

    @ApiModelProperty(value = "备注", name = "remark", example = "备注")
    private String comment;

}
