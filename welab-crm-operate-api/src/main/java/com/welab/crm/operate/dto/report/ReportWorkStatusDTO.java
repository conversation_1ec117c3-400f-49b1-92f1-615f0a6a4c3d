package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/11
 */
@Data
@ApiModel(value = "员工工作状态报表请求对象")
public class ReportWorkStatusDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = 2203445990985238459L;

    @ApiModelProperty(value = "团队代码")
    private String groupCode;

    @ApiModelProperty(value = "团队代码数组")
    private List<String> groupListCodes;

    @ApiModelProperty(value = "团队名")
    private String groupName;

    @ApiModelProperty(value = "员工id")
    private Long staffId;

    @ApiModelProperty(value = "员工名")
    private String staffName;

    @ApiModelProperty(value = "时间间隔，日/周期, day/range")
    private String period;
}
