package com.welab.crm.operate.dto.operate;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/14 14:00
 */
@Data
public class BlockUserReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户Id", name = "userId")
    @NotNull(message = "userId不能为空")
    private Integer userId;

    @ApiModelProperty(value = "员工Id", name = "staffId")
    private String staffId;

    @ApiModelProperty(value = "员工所在组", name = "groupCode")
    private String groupCode;

    @ApiModelProperty(value = "备注", name = "comment")
    private String comment;
}
