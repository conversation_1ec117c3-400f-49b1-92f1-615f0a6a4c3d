package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 4006040888热线返回对象
 * @date 2022/3/11 14:31
 */
@Data
public class IvrKeyDetailWalletVO extends IvrKeyDetailBaseVO{

    @ApiModelProperty(value = "日期")
    @ExcelTitleMap(title = "日期")
    private String callDate;


    @ApiModelProperty(value = "用户名称")
    @ExcelTitleMap(title = "用户名称")
    private String username;


    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "UUID")
    private String uuid;

    @ApiModelProperty(value = "用户Id")
    @ExcelTitleMap(title = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "呼叫时间")
    @ExcelTitleMap(title = "呼叫时间")
    private String callTime;

    @ApiModelProperty(value = "按键开始时间")
    @ExcelTitleMap(title = "按键开始时间")
    private String keyStartTime;

    @ApiModelProperty(value = "人工接听时间")
    @ExcelTitleMap(title = "人工接听时间")
    private String csAnswerTime;

    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String hangUpTime;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Long talkDuration;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String staffName;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;

    @ExcelTitleMap(title = "产品介绍")
    private Integer wallet1;
    @ExcelTitleMap(title = "额度申请")
    private Integer wallet2;
    @ExcelTitleMap(title = "审核周期")
    private Integer wallet3;
    @ExcelTitleMap(title = "分期咨询")
    private Integer wallet4;
    @ExcelTitleMap(title = "绑卡咨询")
    private Integer wallet5;
    @ExcelTitleMap(title = "还款咨询")
    private Integer wallet6;
    @ExcelTitleMap(title = "账单查询")
    private Integer wallet7;
}
