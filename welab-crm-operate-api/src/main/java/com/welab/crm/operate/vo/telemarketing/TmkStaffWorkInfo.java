package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: TmkStaffWorkInfo
 * @Description: 电销员工通话统计对象
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2022/3/14 11:31
 */
@ApiModel(value = "电销外呼统计返回对象")
@Data
@Excel(fileName = "电销外呼统计报表",sheetName = "电销外呼统计报表")
public class TmkStaffWorkInfo implements Serializable {
    private static final Long serialVersionUID = 1L;


    @ApiModelProperty(value = "分组")
    @ExcelTitleMap(title = "分组")
    private String groupCode;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String agentName;


    @ApiModelProperty(value = "登陆系统时长")
    @ExcelTitleMap(title = "登陆系统时长")
    private String loginTime;

    @ApiModelProperty(value = "外呼量")
    @ExcelTitleMap(title = "外呼量")
    private String obCallingCount;

    @ApiModelProperty(value = "接通量")
    @ExcelTitleMap(title = "接通量")
    private String obBridgeCount;

    @ApiModelProperty(value = "接通率")
    @ExcelTitleMap(title = "接通率")
    private String agentRate;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private String obBridgeTime;

    @ApiModelProperty(value = "外呼平均通话时长")
    @ExcelTitleMap(title = "外呼评价通话时长")
    private String obAvgBridgeTime;

    @ApiModelProperty(value = "话后处理时长")
    @ExcelTitleMap(title = "话后处理时长")
    private String obWrapupTime;

    @ApiModelProperty(value = "置忙总时长")
    @ExcelTitleMap(title = "置忙总时长")
    private String pauseTime;

    @ApiModelProperty(value = "小休时长")
    @ExcelTitleMap(title = "小休时长")
    private String restTime;

    @ApiModelProperty(value = "工时利用率")
    @ExcelTitleMap(title = "工时利用率")
    private String operationRate;

    @ApiModelProperty(value = "通话利用率")
    @ExcelTitleMap(title = "通话利用率")
    private String callRate;


    /**
     * 预览外呼双方接听数
     */
    private String previewObAnsweredCount;


}
