package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 喵达投诉单申诉请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单申诉请求对象")
public class MiaodaAppealDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单号，多个单号用英文逗号连接
     */
    @ApiModelProperty(value = "投诉单号", name = "sns", required = true)
    @NotBlank(message = "投诉单号不能为空")
    private String sns;

    /**
     * 申诉内容
     */
    @ApiModelProperty(value = "申诉内容", name = "content", required = true)
    @NotBlank(message = "申诉内容不能为空")
    private String content;

    /**
     * 申诉类型 1-重复投诉 2-非本商户投诉
     */
    @ApiModelProperty(value = "申诉类型", name = "type", required = true)
    @NotNull(message = "申诉类型不能为空")
    private Integer type;

    /**
     * 所重复的投诉单号（当申诉类型为1时需要填写）
     */
    @ApiModelProperty(value = "重复的投诉单号", name = "dupSns")
    private String dupSns;

    /**
     * 上传图片的网络地址列表
     */
    @ApiModelProperty(value = "图片地址列表", name = "images")
    private List<String> images;

    /**
     * 上传视频的网络地址列表
     */
    @ApiModelProperty(value = "视频地址列表", name = "videos")
    private List<String> videos;
}
