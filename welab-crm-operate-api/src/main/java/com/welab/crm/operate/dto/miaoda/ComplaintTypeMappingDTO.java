package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 投诉类型映射保存/更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "投诉类型映射保存/更新请求对象")
public class ComplaintTypeMappingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键（更新时需要）
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 喵达投诉问题类型
     */
    @ApiModelProperty(value = "喵达投诉问题类型", name = "miaodaIssue", required = true)
    @NotBlank(message = "喵达投诉问题类型不能为空")
    private String miaodaIssue;

    /**
     * 工单组合配置ID，关联op_dict_info_conf表主键
     */
    @ApiModelProperty(value = "工单组合配置ID", name = "opDictInfoConfId", required = true)
    @NotNull(message = "工单组合配置ID不能为空")
    private Long opDictInfoConfId;

    /**
     * 是否启用 0-禁用 1-启用
     */
    @ApiModelProperty(value = "是否启用", name = "isActive")
    private Integer isActive = 1;

    /**
     * 优先级（数字越小优先级越高）
     */
    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority = 100;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明", name = "remark")
    private String remark;
}
