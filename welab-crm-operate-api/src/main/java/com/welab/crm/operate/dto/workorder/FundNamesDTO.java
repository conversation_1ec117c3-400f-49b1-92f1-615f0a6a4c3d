package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/7/21
 */
@Data
@ApiModel(value = "资金投诉统计响应对象")
public class FundNamesDTO implements Serializable {

    private static final long serialVersionUID = 6274817710100970L;

    @ApiModelProperty(value = "统计字段", name = "matchUp")
    private String matchUp;

    @ApiModelProperty(value = "解决量", name = "jieJueLiang")
    private int jieJueLiang;

}
