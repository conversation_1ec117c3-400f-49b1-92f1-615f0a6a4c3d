package com.welab.crm.operate.vo.reduce;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 减免统计报表对象
 */
@Data
public class ReduceStatisticsReportVO {

	/**
	 * 减免原因
	 */
	@ExcelProperty("减免原因")
	private String reduceReason;

	/**
	 * 减免金额
	 */
	@ExcelProperty("减免金额")
	private BigDecimal reduceAmount;

	/**
	 * 减免笔数
	 */
	@ExcelProperty("减免笔数")
	private Integer reduceCount;

	/**
	 * 退款金额
	 */
	@ExcelProperty("退款金额")
	private BigDecimal refundAmount;


	/**
	 * 减免类型(减免级别)
	 */
	@ExcelProperty("减免级别")
	private String reduceType;

	/**
	 * 退款级别
	 */
	@ExcelProperty("退款级别")
	private String refundType;

	/**
	 * 申请人
	 */
	@ExcelProperty("申请人")
	private String applyStaff;
	
	
}
