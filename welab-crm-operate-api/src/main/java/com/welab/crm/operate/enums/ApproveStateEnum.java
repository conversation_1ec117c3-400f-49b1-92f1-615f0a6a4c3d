package com.welab.crm.operate.enums;

/**
 * 审批状态枚举类
 */
public enum ApproveStateEnum {

    AGREE(1, "审批通过"),
    REJECT(2, "审批拒绝"),
    WAITING(0, "待审批");

    private final int value;
    private final String text;

    ApproveStateEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    public int getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getState(Integer value) {
        if (value == null) {
            return null;
        }
        for (ApproveStateEnum state : values()) {
            if (state.value == value) {
                return state.getText();
            }
        }
        return null;
    }
}
