package com.welab.crm.operate.vo.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单备用号码对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "工单备用号码对象")
public class WoMobileBakVO {


	@ApiModelProperty(value = "脱敏手机号")
	private String mobileMask;
	
	@ApiModelProperty(value = "加密手机号")
	private String mobileEncrypt;
	
	@ApiModelProperty(value = "黑产用户类型 确认黑产/疑似黑产")
	private String blackProductionUserType;
}
