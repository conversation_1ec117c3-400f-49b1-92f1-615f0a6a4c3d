package com.welab.crm.operate.vo.video;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频任务返回对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "视频任务返回对象")
public class VideoTaskVO implements Serializable {
    private static final Long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @ExcelProperty("用户ID")
    private Integer userId;


    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    @ExcelProperty("uuid")
    private Long uuid;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    @ExcelProperty("客户姓名")
    private String customerName;


    /**
     * 开始录制时间
     */
    @ApiModelProperty(value = "开始录制时间")
    @ExcelProperty("开始录制时间")
    private String startTime;

    /**
     * 结束录制时间
     */
    @ApiModelProperty(value = "结束录制时间")
    @ExcelProperty("结束录制时间")
    private String endTime;


    /**
     * 录制时长
     */
    @ApiModelProperty(value = "录制时长")
    @ExcelProperty("录制时长")
    private String videoTime;


    /**
     * 录制状态
     * 1-已录制
     * 0-未录制
     */
    @ApiModelProperty(value = "录制状态")
    @ExcelProperty("录制状态")
    private Boolean isVideo;


    /**
     * 人脸对比结果
     * 1-通过
     * 0-不通过
     */
    @ApiModelProperty(value = "人脸对比结果")
    @ExcelProperty("人脸对比结果")
    private Boolean faceCheckResult;


    /**
     * 声音对比结果
     * 1-通过
     * 0-不通过
     */
    @ApiModelProperty(value = "声音对比结果")
    @ExcelProperty("声音对比结果")
    private Boolean voiceCheckResult;

    /**
     * 是否收藏
     * 1-已收藏
     * 0-未收藏
     */
    @ApiModelProperty(value = "是否收藏")
    @ExcelProperty("是否收藏")
    private Boolean isCollect;

    /**
     * 视频文件名
     */
    @ApiModelProperty(value = "文件名")
    @ExcelProperty("文件名")
    private String fileName;

    /**
     * 源视频文件名
     */
    @ApiModelProperty(value = "源视频文件名")
    @ExcelProperty("源视频文件名")
    private String sourceFileName;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    @ExcelProperty("发送人")
    private String staffId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @ExcelProperty("发送时间")
    private String gmtCreate;

}
