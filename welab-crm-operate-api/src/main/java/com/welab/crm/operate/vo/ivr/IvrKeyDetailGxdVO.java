package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 10100518热线返回对象
 * @date 2022/3/11 14:31
 */
@Data
public class IvrKeyDetailGxdVO extends IvrKeyDetailBaseVO{

    @ApiModelProperty(value = "日期")
    @ExcelTitleMap(title = "日期")
    private String callDate;

    @ApiModelProperty(value = "用户名称")
    @ExcelTitleMap(title = "用户名称")
    private String username;

    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "UUID")
    private String uuid;

    @ApiModelProperty(value = "用户Id")
    @ExcelTitleMap(title = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "呼叫时间")
    @ExcelTitleMap(title = "呼叫时间")
    private String callTime;

    @ApiModelProperty(value = "按键开始时间")
    @ExcelTitleMap(title = "按键开始时间")
    private String keyStartTime;

    @ApiModelProperty(value = "人工接听时间")
    @ExcelTitleMap(title = "人工接听时间")
    private String csAnswerTime;

    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String hangUpTime;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Long talkDuration;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String staffName;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;

    @ExcelTitleMap(title = "贷款申请-无法提交申请")
    private Integer registered11;

    @ExcelTitleMap(title = "贷款申请-额度咨询")
    private Integer registered12;
    @ExcelTitleMap(title = "贷款申请-额度冻结")
    private Integer registered13;
    @ExcelTitleMap(title = "放款问题咨询-提现火爆")
    private Integer registered21;
    @ExcelTitleMap(title = "放款问题咨询-放款时效")
    private Integer registered22;
    @ExcelTitleMap(title = "放款问题咨询-提现页面异常")
    private Integer registered23;
    @ExcelTitleMap(title = "放款问题咨询-订单取消")
    private Integer registered24;
    @ExcelTitleMap(title = "还款相关问题-解绑或换卡")
    private Integer registered31;
    @ExcelTitleMap(title = "还款相关问题-延期还款咨询")
    private Integer registered32;
    @ExcelTitleMap(title = "还款相关问题-还款失败原因")
    private Integer registered33;
    @ExcelTitleMap(title = "还款相关问题-还款页面问题")
    private Integer registered34;
    @ExcelTitleMap(title = "还款相关问题-开具结清证明")
    private Integer registered35;
    @ExcelTitleMap(title = "还款相关问题-全额结清")
    private Integer registered36;
    @ExcelTitleMap(title = "个人信息修改-修改手机号")
    private Integer registered41;
    @ExcelTitleMap(title = "个人信息修改-注销账号")
    private Integer registered42;
    @ExcelTitleMap(title = "个人信息修改-其他信息修改")
    private Integer registered43;

    @ExcelTitleMap(title = "协商还款")
    private Integer overdue1;
    @ExcelTitleMap(title = "银行卡冻结或卡受限")
    private Integer overdue2;
    @ExcelTitleMap(title = "还款方式核实")
    private Integer overdue3;
    @ExcelTitleMap(title = "投诉催收")
    private Integer overdue4;

    @ExcelTitleMap(title = "债转公司咨询")
    private Integer transfer1;
    @ExcelTitleMap(title = "订单状态更新")
    private Integer transfer2;
    @ExcelTitleMap(title = "还款方式核实")
    private Integer transfer3;
    @ExcelTitleMap(title = "会员&特权卡用户")
    private Integer vip1;
    @ExcelTitleMap(title = "非注册用户")
    private Integer notRegistered1;

}
