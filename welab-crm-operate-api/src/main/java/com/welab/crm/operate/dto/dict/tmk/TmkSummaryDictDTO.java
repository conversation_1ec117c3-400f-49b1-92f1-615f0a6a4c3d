package com.welab.crm.operate.dto.dict.tmk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class TmkSummaryDictDTO {

    @ApiModelProperty(value = "营销小结编号")
    private String summaryCode;

    /**
     * 业务产品类型:1.进件模式 2.额度模式 3.超级会员 4.钱夹谷谷 5.uuid(待定)
     * 注意: 这里是可多选(只有新增能多选)的, 前端会用','分隔
     */
    @ApiModelProperty(value = "业务类型", example = "income,superVip,creditline,qjgg(只有新增可多选)")
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 营销小结
     */
    @ApiModelProperty(value = "营销小结")
    @NotBlank(message = "营销小结不能为空")
    private String summaryContent;

    /**
     * 联系结果对应的编码(与业务字典(detail字段)中配置的对应)
     */
    @ApiModelProperty(value = "联系结果编码", example = "200")
    @NotBlank(message = "联系结果编码不能为空")
    private String resultCode;

    /**
     * 优先级(此字段表示的优先级低于resultCode字段)
     */
    @ApiModelProperty(value = "营销小结优先级", example = "1")
    @NotNull(message = "优先级别不能为空")
    private Integer sort;

    /**
     * 状态: 0-关闭 1.开启
     */
    @ApiModelProperty(value = "状态", example = "1")
    private Boolean state;
}
