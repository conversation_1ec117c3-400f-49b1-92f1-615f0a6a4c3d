package com.welab.crm.operate.vo.face;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ColumnWidth(value = 15)
public class FaceDetailVO {

    /**
     * 用户uuid
     */
    @ExcelProperty(value = "uuid")
    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    /**
     * 用户Id
     */
    @ExcelProperty(value = "userId")
    @ApiModelProperty(value = "userId", name = "userId")
    private Long userId;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    @ApiModelProperty(value = "客户姓名", name = "客户姓名")
    private String customerName;

    /**
     * 所属组code
     */
    @ExcelProperty(value = "发送组别")
    @ApiModelProperty(value = "发送组别", name = "发送组别")
    private String sendGroup;

    /**
     * 发送人
     */
    @ExcelProperty(value = "发送人")
    @ApiModelProperty(value = "发送人", name = "发送人")
    private String sendUser;

    /**
     * 发送时间
     */
    @ExcelProperty(value = "发送时间")
    @ApiModelProperty(value = "发送时间", name = "发送时间")
    private String sendTime;

    /**
     * 验证类型
     */
    @ExcelProperty(value = "验证类型")
    @ApiModelProperty(value = "验证类型", name = "验证类型")
    private String validateType;

    /**
     * 验证次数
     */
    @ExcelProperty(value = "验证次数")
    @ApiModelProperty(value = "验证次数", name = "验证次数")
    private Integer validateCount;

    /**
     * 最终结果中文
     */
    @ExcelProperty(value = "最终结果")
    @ApiModelProperty(value = "最终结果", name = "最终结果")
    private String finalResult;

    /**
     * 验证时间
     */
    @ExcelProperty(value = "验证时间")
    @ApiModelProperty(value = "验证时间", name = "验证时间")
    private String validateTime;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商")
    @ApiModelProperty(value = "供应商", name = "供应商")
    private String vendor;

    /**
     * 验证结果
     */
    @ExcelProperty(value = "验证结果")
    @ApiModelProperty(value = "验证结果", name = "验证结果")
    private String validateResult;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    @ApiModelProperty(value = "失败原因", name = "失败原因")
    private String validateMsg;
}
