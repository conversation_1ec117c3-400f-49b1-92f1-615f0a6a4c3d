package com.welab.crm.operate.vo.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "OrderContactVO", description = "工单联系信息")
public class OrderContactVO {
	
	@ApiModelProperty(value = "主键ID")
	private Long id;
	
	@ApiModelProperty(value = "手机号")
	private String mobile;
	
	@ApiModelProperty(value = "呼叫时间")
	private String callTime;
	
	@ApiModelProperty(value = "保存小结时间")
	private String saveSummaryTime;
	
	@ApiModelProperty(value = "小结内容")
	private String callSummary;
	
	@ApiModelProperty(value = "原因类型")
	private String reasonType;
}
