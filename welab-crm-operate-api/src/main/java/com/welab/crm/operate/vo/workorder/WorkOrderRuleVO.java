package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 工单分类响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "分单规则响应对象")
public class WorkOrderRuleVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;
	
	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;

	/**
     * 规则名称
     */
	@ApiModelProperty(value = "规则名称", name = "name")
    private String name;

    /**
     * 工单类型
     */
	@ApiModelProperty(value = "工单类型", name = "workOrderType")
    private String workOrderType;

    /**
     * 子工单分类
     * 
     */
	@ApiModelProperty(value = "子工单分类", name = "orderCase")
    private String orderCase;

    /**
     * 处理组
     */
	@ApiModelProperty(value = "处理组", name = "handleGroupId")
    private String handleGroupId;

    /**
     * 处理人
     */
	@ApiModelProperty(value = "处理人", name = "handleStaffId")
    private String handleStaffId;
	
	/**
     * 分配日期
     */
	@ApiModelProperty(value = "分配日期", name = "assignDate")
    private String assignDate;

    /**
     * 开始时间
     */
	@ApiModelProperty(value = "开始时间", name = "startTime")
    private String startTime;
	
	/**
     * 结束时间
     */
	@ApiModelProperty(value = "结束时间", name = "endTime")
    private String endTime;
	
	/**
     * 分单间隔
     */
	@ApiModelProperty(value = "分单间隔", name = "assignInterval")
    private Integer assignInterval;

	/**
     * 开关状态（开启，关闭）
     */
	@ApiModelProperty(value = "开关状态（开启，关闭）", name = "status")
    private String status;
	
    /**
     * 优先级
     */
	@ApiModelProperty(value = "优先级", name = "level")
    private Integer level;
	
    /**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;
	
	/**
     * 创建人
     */
	@ApiModelProperty(value = "创建人", name = "createStaffId")
    private String createStaffId;

    /**
     * 修改人
     */
	@ApiModelProperty(value = "修改人", name = "modifyStaffId")
    private String modifyStaffId;
	
	/**
     * 规则类型：order工单规则，telemarket电销规则
     */
	@ApiModelProperty(value = "规则类型", name = "ruleType")
    private String ruleType;

    /**
     * 分配类型：date按日期，num按数量
     */
	@ApiModelProperty(value = "分配类型", name = "assignType")
    private String assignType;
	
	/**
     * 分配数量
     */
	@ApiModelProperty(value = "分配数量", name = "assignNum")
    private Integer assignNum;

	private String applyOrigin;
}

