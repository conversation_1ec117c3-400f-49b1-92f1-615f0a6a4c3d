package com.welab.crm.operate.vo.woReport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/11
 */
@Data
@ApiModel(value = "工单中央监控响应对象")
public class CentralMonitoringVO implements Serializable {

    private static final long serialVersionUID = 2558927216968432711L;

    @ApiModelProperty(value = "话务组")
    private String groupName;

    @ApiModelProperty(value = "工号")
    private String idNo;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "工单流")
    private String type;

    @ApiModelProperty(value = "客户总量")
    private Integer assignNum;

    @ApiModelProperty(value = "外呼客户量")
    private Integer callCount;

    @ApiModelProperty(value = "接通量")
    private Integer connectedNumber;

    @ApiModelProperty(value = "结案量")
    private Integer closedNum;

    @ApiModelProperty(value = "当月客户结案量")
    private Integer currentMonthClosedNum;

    @ApiModelProperty(value = "历史客户结案量")
    private Integer historyClosedNum;

    @ApiModelProperty(value = "总结案率")
    private String closedRate;

    @ApiModelProperty(value = "未结案量")
    private Integer unclosedNum;

    @ApiModelProperty(value = "流转中量")
    private Integer flowingNum;

    @ApiModelProperty(value = "已解决结案量")
    private Integer resolvedNum;

    @ApiModelProperty(value = "已解决结案率")
    private String resolvedRate;

    @ApiModelProperty(value = "未解决结案量")
    private Integer unresolvedNum;

    @ApiModelProperty(value = "未解决结案率")
    private String unresolvedRate;

    @ApiModelProperty(value = "未接通结案量")
    private Integer notConnectedNum;

    @ApiModelProperty(value = "未接通结案率")
    private String notConnectedRate;
    
    @ApiModelProperty(value = "接通率")
    private String connectedRate;
    
    @ApiModelProperty(value = "总通话时长")
    private String totalCallTime;
    
    @ApiModelProperty(value = "平均通话时长")
    private String avgCallTime;
}
