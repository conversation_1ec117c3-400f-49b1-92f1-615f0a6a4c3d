package com.welab.crm.operate.vo.settlement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 结清证明申请
 * </p>
 * <AUTHOR>
 * @since 2022-02-21
 */
@Data
@ApiModel(value = "结清记录响应对象")
public class SettleProofApplyRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long id;

    @ApiModelProperty(value="申请时间", name="applyTime",example = "2020-01-01")
    private Date applyTime;

    @ApiModelProperty(value="申请人", name="staffId",example = "xxx")
    private String staffId;

    @ApiModelProperty(value="申请人姓名", name="staffName",example = "xxx")
    private String staffName;

    @ApiModelProperty(value="贷款号", name="applicationId",example = "1321231564561")
    private String applicationId;

    @ApiModelProperty(value="资金方", name="partnerCode",example = "lz")
    private String partnerCode;

    @ApiModelProperty(value="状态", name="state",example = "procing")
    private String state;

    @ApiModelProperty(value="查看链接", name="filePath",example = "http://xxxx")
    private String filePath;

}
