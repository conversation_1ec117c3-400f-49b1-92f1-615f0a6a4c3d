package com.welab.crm.operate.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AiTmkConfigVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 产品名称(多个产品之间用,相隔)
     */
    private String productNames;


    /**
     * 进件渠道号(多个进件渠道号之间用,相隔)
     */
    private String loanChannels;
    
    /**
     * 标签编码(多个标签编码之间用,相隔)
     */
    private String labelCode;

    /**
     * 推送渠道代码
     */
    private String caCompany;

    /**
     * 话术名称
     */
    private String speechName;

    /**
     * 话术id
     */
    private Integer speechId;

    /**
     * 话术id下的任务id
     */
    private Integer taskId;

    /**
     * 推送方式: 0-按时间 1-按数量
     */
    private Integer pushWay;

    /**
     * 推送方式对应的数量值，当按时间时此值表示小时数，当按数量时表示个数
     */
    private Integer number;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 推送开始时间节点
     */
    private String startTime;

    /**
     * 推送结束时间节点
     */
    private String endTime;

    /**
     * 状态: 0-不启用 1-启用
     */
    private Boolean state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 最大审批金额
     */
    private String maxAmount;

    /**
     * 最小审批金额
     */
    private String minAmount;

    /**
     * 审批时段
     */
    private String approvedAt;

    /**
     * 状态标识(0:未分配；1,2：已分配)
     */
    private String flag;
}
