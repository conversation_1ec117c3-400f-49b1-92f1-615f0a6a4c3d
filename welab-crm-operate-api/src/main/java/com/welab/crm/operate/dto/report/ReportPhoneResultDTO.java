package com.welab.crm.operate.dto.report;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/2
 */
@Data
@ApiModel(value = "电话小结报表请求对象")
public class ReportPhoneResultDTO extends BaseReqDTO {

	private static final long serialVersionUID = 856464680746597770L;

	@ApiModelProperty(value = "开始时间，格式 yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "热线号码", name = "热线号码")
    private String cdrHotline;

    @ApiModelProperty(value = "热线号码(多选)", name = "热线号码(多选)")
    private List<String> cdrHotlines;

    @ApiModelProperty(value = "坐席工号", name = "坐席工号")
    private String cdrCno;

    @ApiModelProperty(value = "客户号码", name = "客户号码")
    private String cdrCustomerNumber;

    @ApiModelProperty(value = "呼叫类型", name = "呼叫类型")
    private String cdrCallType;

    @ApiModelProperty(value = "是否查询用户标签", name = "是否查询用户标签")
    private Boolean isQueryUserLabel;

    @ApiModelProperty(value = "小结一类", name = "小结一类")
    private String summaryOne;

    @ApiModelProperty(value = "小结二类", name = "小结二类")
    private String summaryTwo;

    @ApiModelProperty(value = "小结三类", name = "小结三类")
    private String summaryThree;

}
