package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.SensitiveWorkOrderDTO;

import java.util.List;

public interface SensitiveWorkOrderService {
	void add(SensitiveWorkOrderDTO dto);

	Page<SensitiveWorkOrderDTO> query(SensitiveWorkOrderDTO dto);

	void delete(List<Long> ids);

	void update(SensitiveWorkOrderDTO dto);
}
