package com.welab.crm.operate.vo.woReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/29
 */
@Data
@ApiModel(value = "工单明细报表导出对象")
public class ReportDetailsExportVO {

    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "工单类型")
    @ExcelProperty(value = "工单类型")
    private String type;

    @ApiModelProperty(value = "工单大类")
    @ExcelProperty(value = "工单大类")
    private String firstType;

    @ApiModelProperty(value = "工单二类")
    @ExcelProperty(value = "工单二类")
    private String secondType;

    @ApiModelProperty(value = "工单三类")
    @ExcelProperty(value = "工单三类")
    private String thirdType;

    @ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
    @ExcelProperty(value = "投诉渠道")
    private String complaintsChannel;

    @ApiModelProperty(value = "客户姓名")
    @ExcelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "年龄")
    @ExcelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号")
    private String cnid;

    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "备用号码")
    @ExcelProperty(value = "备用号码")
    private String mobileBak;

    @ApiModelProperty(value = "创建组")
    @ExcelProperty(value = "创建组")
    private String createGroupName;

    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间")
    private String gmtCreate;

    @ApiModelProperty(value = "反馈内容")
    @ExcelProperty(value = "反馈内容")
    private String description;

    @ApiModelProperty(value = "uuid")
    @ExcelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "userId")
    @ExcelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "成功贷款笔数", name = "sucLoanCount")
    @ExcelProperty(value = "成功贷款笔数")
    private Integer sucLoanCount;

    @ApiModelProperty(value = "分单类型")
    @ExcelProperty(value = "分单类型")
    private String assignType;

    @ApiModelProperty(value = "分单时间")
    @ExcelProperty(value = "分单时间")
    private String assignTime;

    @ApiModelProperty(value = "当前处理组")
    @ExcelProperty(value = "当前处理组")
    private String curGroupName;

    @ApiModelProperty(value = "当前处理人")
    @ExcelProperty(value = "当前处理人")
    private String curStaffName;

    @ApiModelProperty(value = "当前处理时间")
    @ExcelProperty(value = "当前处理时间")
    private String curProcessTime;

    @ApiModelProperty(value = "当前处理意见")
    @ExcelProperty(value = "当前处理意见")
    private String curComment;

    @ApiModelProperty(value = "上一处理组")
    @ExcelProperty(value = "上一处理组")
    private String preGroupName;

    @ApiModelProperty(value = "上一处理人")
    @ExcelProperty(value = "上一处理人")
    private String preStaffName;

    @ApiModelProperty(value = "上一处理时间")
    @ExcelProperty(value = "上一处理时间")
    private String preProcessTime;

    @ApiModelProperty(value = "上一处理意见")
    @ExcelProperty(value = "上一处理意见")
    private String preComment;

    @ApiModelProperty(value = "工单状态")
    @ExcelProperty(value = "工单状态")
    private String status;

    @ApiModelProperty(value = "结案类型")
    @ExcelProperty(value = "结案类型")
    private String closeType;

    @ApiModelProperty(value = "回访小结")
    @ExcelProperty(value = "回访小结")
    private String callbackNote;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "资方简称", name = "fundName")
    @ExcelProperty(value = "资方简称")
    private String fundName;
    
    @ApiModelProperty(value = "在途贷款笔数", name = "currentLoanCount")
    @ExcelProperty(value = "在途贷款笔数")
    private Integer currentLoanCount;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    @ExcelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "申请时间", name = "applyTime")
    @ExcelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "贷款状态", name = "state")
    @ExcelProperty(value = "订单状态")
    private String state;

    @ApiModelProperty(value = "渠道来源", name = "originName")
    @ExcelProperty(value = "渠道来源")
    private String originName;

    @ApiModelProperty(value = "渠道号", name = "origin")
    @ExcelProperty(value = "渠道号")
    private String origin;

    @ApiModelProperty(value = "审批时间", name = "approvalTime")
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    @ApiModelProperty(value = "申请金额", name = "applyAmount")
    @ExcelProperty(value = "申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "审批金额", name = "approvalAmount")
    @ExcelProperty(value = "审批金额")
    private BigDecimal approvalAmount;

    @ApiModelProperty(value = "放款时间", name = "disbursedTime")
    @ExcelProperty(value = "放款时间")
    private Date disbursedTime;

    @ApiModelProperty(value = "申请期限", name = "applyTenor")
    @ExcelProperty(value = "申请期数")
    private String applyTenor;

    @ApiModelProperty(value = "审批期限，用于绑卡查询", name = "approvalTenor")
    @ExcelProperty(value = "审批期数")
    private String tenor;

    @ApiModelProperty(value = "确认贷款时间", name = "confirmedAt")
    @ExcelProperty(value = "确认时间")
    private Date confirmedAt;

    @ApiModelProperty(value = "资金方", name = "partnerName")
    @ExcelProperty(value = "资金方")
    private String partnerName;
    
    @ApiModelProperty(value = "模式类型", name = "loanModelType")
    @ExcelProperty(value = "模式类型")
    private String loanModelType;

    @ApiModelProperty(value = "产品名称", name = "productName")
    @ExcelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "总利率", name = "totalRate")
    @ExcelProperty(value = "总利率")
    private String totalRate;

    @ApiModelProperty(value = "费率（用户等级）", name = "finalClass")
    @ExcelProperty(value = "用户等级")
    private String finalClass;

}
