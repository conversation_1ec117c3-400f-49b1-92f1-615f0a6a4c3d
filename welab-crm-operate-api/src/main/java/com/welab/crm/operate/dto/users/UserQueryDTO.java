package com.welab.crm.operate.dto.users;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: UserQueryDTO
 * @Description:
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2021/12/23 14:59
 */
@Getter
@Setter
public class UserQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 订单号
     */
    private String orderNumber;
    /**
     * 贷款号
     */
    private String applicationId;
    /**
     * 身份证号
     */
    private String cnid;
    /**
     * 用户类型
     */
    private String type;
    /**
     * 银行卡号
     */
    private String accountNo;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    @Override
    public String toString() {
        return "UserQueryDTO{" +
                "userId='" + userId + '\'' +
                ", uuid='" + uuid + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", type='" + type + '\'' +
                ", accountNo='" + accountNo + '\'' +
                ", channelOrderNo='" + channelOrderNo + '\'' +
                '}';
    }
}
