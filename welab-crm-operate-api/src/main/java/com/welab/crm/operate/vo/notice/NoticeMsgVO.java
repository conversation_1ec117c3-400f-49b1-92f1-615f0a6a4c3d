package com.welab.crm.operate.vo.notice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 通知公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@ApiModel(description = "消息列表返回对象")
public class NoticeMsgVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 消息Id，唯一索引
     */
    @ApiModelProperty(value = "消息Id唯一索引", name = "msgId")
    private String msgId;

    /**
     * 通知标题
     */
    @ApiModelProperty(value = "标题", name = "title")
    private String title;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人", name = "sender")
    private String sender;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", name = "fileName")
    private String fileName;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型", name = "type")
    private String type;

    /**
     * 已读人数
     */
    @ApiModelProperty(value = "已读数", name = "readCount")
    private Integer readCount;

    /**
     * 回复人数
     */
    @ApiModelProperty(value = "回复数", name = "replyCount")
    private Integer replyCount;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", name = "publishTime")
    private Date publishTime;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", name = "workOrderNo")
    private String workOrderNo;

    @ApiModelProperty(value = "传给前端的关键字", name = "jsonDetail")
    private String jsonDetail;


}
