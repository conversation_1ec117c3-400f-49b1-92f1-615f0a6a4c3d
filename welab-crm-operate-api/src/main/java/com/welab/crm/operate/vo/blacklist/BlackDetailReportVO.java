package com.welab.crm.operate.vo.blacklist;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 黑名单明细报表
 */
@Data
public class BlackDetailReportVO{


    /**
     * 逻辑ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private String createTime;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    private String custName;

    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证号码")
    private String idNo;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String mobile;

    /**
     * uuid
     */
    @ExcelProperty(value = "uuid")
    private String uuid;

    /**
     * userId
     */
    @ExcelProperty(value = "userId")
    private Integer userId;

    /**
     * 有效开始时间
     */
    @ExcelProperty(value = "有效开始时间")
    private String validStartTime;

    /**
     * 有效结束时间
     */
    @ExcelProperty(value = "有效结束时间")
    private String validEndTime;

    /**
     * 拉黑天数
     */
    @ExcelProperty(value = "拉黑天数")
    private Integer blackDay;

    /**
     * 添加原因
     */
    @ExcelProperty(value = "添加原因")
    private String reason;

    /**
     * 加黑类型
     */
    @ExcelProperty(value = "加黑类型")
    private String blackType;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String groupName;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型")
    private String operateType;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String comment;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String operateUser;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private String updateTime;
}