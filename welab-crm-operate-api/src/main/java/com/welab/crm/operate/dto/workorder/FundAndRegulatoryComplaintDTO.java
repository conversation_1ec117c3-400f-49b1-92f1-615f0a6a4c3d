package com.welab.crm.operate.dto.workorder;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/7/21
 */
@Data
@ApiModel(value = "资金投诉统计and监管投诉统计请求对象")
public class FundAndRegulatoryComplaintDTO extends PageQueryDTO {

    private static final long serialVersionUID = 5833140996897011390L;

    @ApiModelProperty(value = "开始时间", name = "starTime")
    private String startTime;

    @ApiModelProperty(value = "结束时间", name = "endTime")
    private String endTime;

    /*@ApiModelProperty(value = "资金方", name = "fundName")
    private String fundName;

    @ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
    private String complaintsChannel;*/
}
