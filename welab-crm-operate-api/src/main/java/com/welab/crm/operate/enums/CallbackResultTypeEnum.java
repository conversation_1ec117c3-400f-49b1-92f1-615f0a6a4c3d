package com.welab.crm.operate.enums;

/**
 * 回电结果类型枚举
 * <AUTHOR>
 */
public enum  CallbackResultTypeEnum {
    CONSENSUS("consensus","协商一致"),
    NOT_CONNECTED("not_connected","未接通"),
    UNDER_NEGOTIATION("under_negotiation","协商中"),
    NEGOTIATION_FAILED("negotiation_failed","协商失败")
    ;

    private String name;

    private String desc;

    CallbackResultTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static CallbackResultTypeEnum getEnumByDesc(String desc){
        for (CallbackResultTypeEnum anEnum : values()) {
            if (anEnum.desc.equals(desc)){
                return anEnum;
            }
        }
        return null;
    }
}
