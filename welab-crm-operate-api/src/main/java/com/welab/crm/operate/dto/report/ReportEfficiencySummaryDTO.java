package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/10
 */
@Data
@ApiModel(value = "工单效能统计报表请求对象")
public class ReportEfficiencySummaryDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = 4325739459004496192L;

    @ApiModelProperty(value = "话务组code")
    private String groupCode;

    @ApiModelProperty(value = "工单类型id")
    private String type;
}
