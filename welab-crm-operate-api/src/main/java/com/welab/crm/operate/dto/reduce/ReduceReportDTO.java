package com.welab.crm.operate.dto.reduce;

import com.welab.crm.interview.dto.PageQueryDTO;
import lombok.Data;

import java.util.List;

@Data
public class ReduceReportDTO extends PageQueryDTO {

	/**
	 * 开始时间
	 */
	private String startTime;


	/**
	 * 结束时间
	 */
	private String endTime;


	/**
	 * 申请类型(多选,用逗号隔开)
	 */
	private List<String> applyTypeList;


	/**
	 * 减免原因
	 */
	private List<String> reduceReasonList;


	/**
	 * 减免级别
	 */
	private List<String> reduceTypeList;


	/**
	 * 申请人
	 */
	private List<Long> applyStaffIds;
	
}
