package com.welab.crm.operate.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class AIPushStateDTO {

    /**
     * 任务配置id
     */
    @ApiModelProperty(value = "任务id")
    private Long id;

    /**
     * 状态: 0-不启用 1-启用
     */
    @ApiModelProperty(value = "启用状态: 0-不启用 1-启用")
    @NotNull(message = "状态不能为空")
    private Boolean state;
}
