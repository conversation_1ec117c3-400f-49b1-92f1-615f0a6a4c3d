package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 查询工单列表响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "查询未分配工单数量列表响应对象")
public class WorkOrderCountVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;


	@ApiModelProperty(value = "工单类型", name = "orderType")
    private String orderType;

    @ApiModelProperty(value = "数量", name = "count")
    private Integer count;
}

