package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/12
 */
@Data
@ApiModel(value = "工单统计报表工单类型报表请求对象")
public class ReportSummaryTypeDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = 117757288191665941L;

    @ApiModelProperty(value = "工单类型id")
    private String type;

    @ApiModelProperty(value = "工单大类id")
    private String firstType;

    @ApiModelProperty(value = "工单二类id")
    private String secondType;

    @ApiModelProperty(value = "工单三类id")
    private String thirdType;
}
