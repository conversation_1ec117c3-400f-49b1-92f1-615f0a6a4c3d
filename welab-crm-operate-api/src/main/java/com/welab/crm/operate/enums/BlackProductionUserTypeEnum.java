package com.welab.crm.operate.enums;

/**
 * 黑产用户类型枚举
 * <AUTHOR>
 */
public enum BlackProductionUserTypeEnum {
	CONFIRM(1,"确认黑产"),
	SUSPECTED(2,"疑似黑产"),
	;

	private Integer code;
	
	private String desc;

	BlackProductionUserTypeEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public Integer getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
	
	public static String getDescByCode(Integer code){
		for (BlackProductionUserTypeEnum userTypeEnum : values()) {
			if (userTypeEnum.code.equals(code)){
				return userTypeEnum.desc;
			}
		}
		return code + "";
	}

	public static Integer getCodeByDesc(String desc){
		for (BlackProductionUserTypeEnum userTypeEnum : values()) {
			if (userTypeEnum.desc.equals(desc)){
				return userTypeEnum.code;
			}
		}
		return null;
	}
}
