package com.welab.crm.operate.dto.ai;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class AiPushHistoryDTO extends BaseRequestDTO {

    /**
     * 外呼配置主键id
     */
    @ApiModelProperty(value = "外呼配置主键id")
    @NotNull(message = "外呼配置主键id为空")
    private Long configId;
}
