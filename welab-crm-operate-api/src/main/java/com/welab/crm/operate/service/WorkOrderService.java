package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigNodeDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunTaskDTO;
import com.welab.crm.operate.dto.InitCodeConfigDTO;
import com.welab.crm.operate.dto.workorder.*;
import com.welab.crm.operate.vo.online.OnlineFileRetVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.workorder.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 工单服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface WorkOrderService {

	/**
     * 分页查询工单列表
     * @param reqDTO
     * @return
     */
    Page<WorkOrderInfoVO> queryWorkOrderList(WorkOrderSearchReqDTO reqDTO);
    
    /**
     * 进入流程任务发起页面
     * @param reqDTO
     */
    WFConfigNodeDTO getInitNodeConfig(InitCodeConfigDTO reqDTO,  StaffVO staffVO);
    
    /**
     * 进入流程任务处理页
     * @param reqDTO
     */
    WFRunExecutionDTO getTaskNodeInfo(WFRunTaskDTO reqDTO);
    
    /**
     * 获取目标任务的指派信息
     */
    WFConfigAssignedDTO getAssignedStaffId(AssignedStaffIdDTO reqDTO);

    /**
     * 提交工单
     * @param reqDTO
     */
    String submitWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO);

    /**
     * 查询工单投诉历史数据
     *
     * @param workOrderNo 工单编号
     */
    List<WorkOrderComplainVO> getWorkOrderComplains(String workOrderNo);

    /**
     * 查询工单投诉历史数据列表
     */
    com.welab.xdao.context.page.Page<WorkOrderComplainVO> listComplains(WorkOrderComplainDTO reqDTO);

    /**
     * 查询工单投诉历史数据列表
     */
    List<WorkOrderComplainVO> exportComplains(WorkOrderComplainDTO reqDTO);

    /**
     * 工单投诉批量处理状态催促(推送到催收系统并且保存到本地)
     *
     * @param orderNoList 工单编号列表
     */
    void updateComplainUrgeStatus(List<String> orderNoList);

    /**
     * 处理客户投诉相关的代码，即如果符合推送到催收系统的条件，则做推送且记录投诉的操作
     *
     * @param reqDTO 包含工单号、投诉合同列表参数
     */
    void saveWorkOrderComplain(WorkOrderSubmitReqDTO reqDTO, Long staffId, String staffName);

    /**
     * 处理工单
     * @param reqDTO
     */
    void executeWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO);

    
    /**
     * 保存工单草稿
     * @param reqDTO
     */
    void saveWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO);
    
    /**
     * 分页查询工单列表
     * @param reqDTO
     * @return
     */
    List<WorkOrderCountVO> queryWorkCountList(WorkOrderSearchReqDTO reqDTO);

    /**
     * 统计工单数量
     * @param reqDTO
     * @return
     */
    WorkOrderTotalVO totalWorkOrder(WorkOrderTotalReqDTO reqDTO);
    
    /**
     * 工单标记
     * @param reqDTO
     * @return
     */
    void signWorkOrder(WorkOrderSignReqDTO reqDTO);
    
    /**
     * 查询工单详情
     * @param reqDTO
     * @return
     */
    WorkOrderDetailVO queryWorkOrderDetail(Long id, String executionId, String staffId);

    /**
     * 查询过河兵工单详情
     * @param id
     * @return
     */
    WorkOrderDetailVO queryEliteWorkOrderDetail(Long id);

    /**
     * 查询工单历史
     *
     * @param uuid        客户uuid
     * @param orderNo     工单编号，客户id和工单编号能且只能填一个
     * @param mobile      用户手机号
     * @param processCode 流程编号
     * @return
     */
    List<WorkOrderHisVO> queryWorkOrderHistory(String uuid, String mobile, String orderNo, String processCode);
    
    /**
     * 工单催单
     * @param reqDTO
     */
    void reminderWorkOrder(WorkOrderSubmitReqDTO reqDTO, String staffId);

    /**
     * 批量退单
     *
     * @param executionIds
     */
    void batchReturn(List<String> executionIds, String staffId);

    /**
     * 批量结案
     *
     * @param executionIds
     */
    void batchClose(List<String> executionIds, String staffId);

    /**
     * 批量返回结果
     *
     * @param executionIds
     */
    void batchBack(List<String> executionIds, String staffId);

    /**
     * 批量回复意见
     *
     * @param dto
     * @param staffId
     */
    void batchSave(WorkOrderBatchSaveDTO dto, String staffId);

    /**
     * 保存工单附件
     * @param woAttachmentReqDTO
     */
    void saveWoAttachment(WoAttachmentReqDTO woAttachmentReqDTO);

    /**
     * 删除附件
     * @param id
     */
    void deleteWoAttachment(Long id);

    /**
     * 获取下载链接并且保存附件
     *
     * @param fileNameList 附件名列表
     * @param id           工单号
     * @param staff
     * @return
     */
    List<WoAttachmentVO> getDownLoadUrl(List<String> fileNameList, Long id, ColStaffResVO staff);

    /**
     * 下载带水印的图片
     * @param path
     * @param fileName
     * @return
     */
    Response<byte[]> downloadWatermarkFile(String path,String fileName);

    /**
     * 查询工单历史数据中手机号对应的身份证号码
     * @param mobile 客户手机号码
     */
    String getUuIdByMobile(String mobile);

    /**
     * 接管工单
     * @param executionId
     * @param staffId
     * @param groupCode
     */
    void takeOverOrder(String executionId);

    /**
     * 是否有相同用户的相同工单
     * @param dto
     */
    void isCustSameOrder(WorkOrderSubmitReqDTO dto);

    /**
     * 外部系统相同工单检查
     * @param dto
     * @return
     */
    String externalSameOrderCheck(WorkOrderSubmitReqDTO dto, Long staffId);


    /**
     * 统计提交工单时间，开始提交工单时 status 传 start，结束提交工单时 status 传 end
     * @param status
     */
    void statisticsSubmitOrderTime(String status);

    /**
     * 资金投诉工单统计查询
     * @param dto
     * @return
     */
    List<FundNameResDTO> queryFundName(FundAndRegulatoryComplaintDTO dto);

    /**
     * 资金投诉工单统计查询
     * @param dto
     * @return
     */
    List<RegulatoryComplaintResDTO> queryComplaint(FundAndRegulatoryComplaintDTO dto);


    /**
     * 保存备用手机号
     * @param dto
     * @return
     */
    WoMobileBakVO saveMobileBak(saveDTO dto);

    /**
     * 保存邮箱号
     * @param dto
     * @return
     */
    WoEmailVO saveEmail(saveDTO dto);


    /**
     * 查询文件名
     * @param id
     * @return
     */
    String getFileNameById(Long id);

    /**
     * 查询文件名是否存在
     * @param fileName
     * @return
     */
    boolean fileNameIsExist(String fileName);

    /**
     * 查询在线系统文件
     * @param userId 用户id
     * @param interval 过去 interval 天内
     * @return 文件信息列表
     */
    List<OnlineFileRetVO> queryOnlineFile(Integer userId, Integer interval);

    /**
     * 查询工单日志列表
     * @param executionId 执行ID
     * @return 工单日志列表
     */
    List<WorkOrderLogVO> queryWorkOrderLogList(String executionId);

    /**
     * 根据工单号查询附件信息
     * @param orderNo
     * @return
     */
    List<WoAttachmentVO> queryAttachmentByOrderNo(String orderNo);


    WorkOrderInfoVO queryOrderInfoByOrderNo(String orderNo);


    /**
     * 工单文件上传
     * @param file
     * @return oss文件名
     */
    String uploadFile(MultipartFile file);

    /**
     * 校验这个文件是否是该工单下面的，并且是自己上传的
     * @param orderNo
     * @param fileName
     */
    void checkExternalOrderFile(String orderNo, String fileName, Long staffId);
}

