package com.welab.crm.operate.dto.repay;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022-11-06
 */
@Getter
@Setter
public class RepayImportDTO {

    @ExcelTitleMap(title = "uuid", require = false)
    private String uuid;

    @ExcelTitleMap(title = "userId", require = false)
    private String userId;

    @ExcelTitleMap(title = "applicationId", require = false)
    private String applicationId;

    @ExcelTitleMap(title = "*填写英文:loan-现金贷,wallet-钱夹谷谷")
    private String loanType;
}
