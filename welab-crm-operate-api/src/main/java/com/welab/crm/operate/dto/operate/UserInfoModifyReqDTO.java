package com.welab.crm.operate.dto.operate;

import com.welab.bank.card.validator.MobilePhoneValidation;
import com.welab.crm.operate.annotation.AesDecryptParam;
import com.welab.validator.Cnid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户信息修改DTO
 *
 * <AUTHOR>
 * @date 2021/9/28 15:06
 */
@Data
public class UserInfoModifyReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户Id", name = "userId")
    @NotNull(message = "用户Id不能为空")
    private Integer userId;

    @ApiModelProperty(value = "原用户姓名", name = "oldName")
    private String oldName;

    @ApiModelProperty(value = "用户姓名", name = "name")
    private String name;

    @ApiModelProperty(value = "原用户身份证号", name = "oldCnid")
    private String oldCnid;

    @ApiModelProperty(value = "用户身份证号", name = "cnid")
    @Cnid
    private String cnid;

    @ApiModelProperty(value = "原用户手机号", name = "oldMobile")
    @AesDecryptParam
    private String oldMobile;

    @ApiModelProperty(value = "用户手机号", name = "mobile")
    @MobilePhoneValidation
    private String mobile;

    @ApiModelProperty(value = "客服手机号", name = "adminMobile")
    private String adminMobile;

    @ApiModelProperty(value = "备注", name = "comment")
    private String comment;

    @ApiModelProperty(value = "员工Id", name = "staffId")
    private String staffId;

    @ApiModelProperty(value = "员工所在组", name = "groupCode")
    private String groupCode;



}
