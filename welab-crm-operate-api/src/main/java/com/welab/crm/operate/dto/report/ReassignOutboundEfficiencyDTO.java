package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "中央监控再分配外呼效能报表请求对象")
public class ReassignOutboundEfficiencyDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = -5878910924468805998L;

    @ApiModelProperty(value = "话务组code")
    private String groupCode;

    @ApiModelProperty(value = "业务类型, loan 进件模式，credit 额度模式，cjhy 超级会员")
    private String businessType;
}
