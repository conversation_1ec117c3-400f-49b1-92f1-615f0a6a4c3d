package com.welab.crm.operate.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @date 2021/9/30 10:22
 */
public enum OperateTypeEnum {
    USER_INFO_MODIFY("user_info_modify","用户信息修改",""),
    CANCEL_USER("cancel_user","注销用户",""),
    APPROVAL_URGENT("approval_urgent","加急审批",""),
    TEL_LOSS("tel_loss","遗漏电话","1"),
    RETURN_ORDER("return_order","退回订单","2"),
    CANCEL_ORDER("cancel_order","取消订单","3"),
    MODIFY_PERIOD("modify_period","修改期数","5"),
    CHANGE_AMOUNT("change_amount","修改金额","4"),
    BANK_CARD_RELEASE_AUTHORIZATION("bank_card_release_authorization","银行卡解除授权"),
    QUOTA_WRITE_OFF("quota_write_off","注销额度"),
    BANK_CARD_CHANGE("bank_card_change","银行卡换卡"),
    COUPON_CARD_SEND("coupon_card_send","发送红包"),
    LOAN_EARLY_SETTLE_OUTSTANDING_ALLOW("loan_early_settle_outstanding_allow","现金贷在途贷款开启提前结清"),
    LOAN_EARLY_SETTLE_OUTSTANDING("loan_early_settle_outstanding","现金贷在途贷款提前结清"),
    WALLET_EARLY_SETTLE_ALLOW("wallet_early_settle_allow","钱包分期开启提前结清"),
    WALLET_EARLY_SETTLE("wallet_early_settle","钱包分期提前结清"),
    WALLET_EARLY_SETTLE_BATCH("wallet_early_settle_batch","钱包分期批量提前结清"),
    WALLET_EARLY_SETTLE_OUTSTANDING_ALLOW("wallet_early_settle_outstanding_allow","钱包在途贷款开启提前结清"),
    WALLET_EARLY_SETTLE_OUTSTANDING("wallet_early_settle_outstanding","钱包在途贷款提前结清"),
    MESSAGE_SEND("message_send","短信发送"),
    BANK_CARD_OPEN_UNPIN("bank_card_open_unpin","银行卡打开解绑开关"),
    ;

    public static String getDescByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (OperateTypeEnum value : values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return null;
    }

    /**
     * 用户贷款操作，返回数字，因为审批接口的类型是1-5
     * @param code
     * @return
     */
    public static String getNoByCode(String code){
        for (OperateTypeEnum value : values()) {
            if (value.getCode().equals(code)){
                return value.getNo();
            }
        }
        return code;
    }

    private String code;
    private String desc;
    private String no;

    OperateTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    OperateTypeEnum(String code, String desc, String no) {
        this.code = code;
        this.desc = desc;
        this.no = no;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getNo() {
        return no;
    }
}
