package com.welab.crm.operate.dto.reduce;

import lombok.Data;

import java.util.Date;

/**
 * 减免附件请求对象
 * <AUTHOR>
 */
@Data
public class ReduceAttachmentDTO {

	/**
	 * 文件名
	 */
	private String fileName;

	/**
	 * 文件路径
	 */
	private String uniqueFileName;

	/**
	 * 上传人id
	 */
	private Long staffId;

	/**
	 * 上传人姓名
	 */
	private String staffName;

	/**
	 * 减免请求流水号
	 */
	private String requestNo;

	/**
	 * 创建时间
	 */
	private Date gmtCreate;
}
