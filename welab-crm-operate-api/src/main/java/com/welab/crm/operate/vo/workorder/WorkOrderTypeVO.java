package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 工单分类响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "工单分类响应对象")
public class WorkOrderTypeVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;
	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;

	/**
     * 工单编码
     */
	@ApiModelProperty(value = "工单编码", name = "code")
    private String code;

    /**
     * 工单名称
     */
	@ApiModelProperty(value = "工单名称", name = "name")
    private String name;

    /**
     * 工单父类编码
     * 
     */
	@ApiModelProperty(value = "工单父类编码", name = "pcode")
    private String pcode;

    /**
     * 可用标识：0,不可用;1,可用
     */
	@ApiModelProperty(value = "可用标识：0,不可用;1,可用", name = "userflag")
    private String userflag;

    /**
     * 工单类型；1工单大类,2工单二类,3工单三类
     */
	@ApiModelProperty(value = "工单类型；1工单大类,2工单二类,3工单三类", name = "type")
    private Integer type;

    /**
     * 优先级
     */
	@ApiModelProperty(value = "优先级", name = "level")
    private Integer level;
    
    /**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;
}

