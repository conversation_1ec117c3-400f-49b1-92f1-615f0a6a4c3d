package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021-11-25
 */
@Data
@ApiModel(value = "手动领取配置请求对象")
public class OpOrderConfigVO implements Serializable {


    private static final long serialVersionUID = 1L;
    
    /**
     * 主键id
     */
    private Integer id;
    
    /**
     * 工单类型
     */
    private String type;

    /**
     * 工单大类编码
     */
    private String orderOneClass;

    /**
     * 每次领取数量
     */
    private Integer getNum;

    /**
     * 领取次数
     */
    private Integer getCount;

    /**
     * 领取组
     */
    private String getGroupCode;

    /**
     * 任务状态,0关闭,1开启
     */
    private String status;
}
