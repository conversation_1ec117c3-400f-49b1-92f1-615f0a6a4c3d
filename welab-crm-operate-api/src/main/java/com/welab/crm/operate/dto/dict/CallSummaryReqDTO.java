package com.welab.crm.operate.dto.dict;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description 电话小结请求类
 * @date 2021/12/7
 */
@Getter
@Setter
public class CallSummaryReqDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = 1891982506233132392L;

    /**
     * 手机
     */
    @ApiModelProperty(value="手机", name="手机")
    private String staffMobile;

    /**
     * 描述
     */
    @ApiModelProperty(value="收藏节点内容", name="收藏节点内容")
    private String description;

    /**
     * 子类id
     */
    @ApiModelProperty(value="子类id", name="子类id")
    private Long lastDictId;

    /**
     * 状态 0-无 1-置顶
     */
    @ApiModelProperty(value="置顶标识，0-无 1-置顶", name="置顶标识，0-无 1-置顶")
    private Integer topStatus;

}
