package com.welab.crm.operate.dto.workorder;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单查询请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单查询请求对象")
public class WorkOrderSearchReqDTO extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	@ApiModelProperty(value = "自增ID", name = "id")
    private Long id;
	
	@ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;
	
	@ApiModelProperty(value = "流程实例ID", name = "executionId")
    private String executionId;
	
	@ApiModelProperty(value = "工单类型", name = "orderType")
    private String orderType;
	
	@ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private Long orderOneClass;

    @ApiModelProperty(value = "工单二类", name = "orderTwoClass")
    private Long orderTwoClass;

    @ApiModelProperty(value = "工单三类", name = "orderThreeClass")
    private Long orderThreeClass;
	
    @ApiModelProperty(value = "电话号码", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;
    
    @ApiModelProperty(value = "身份证号码", name = "cnid")
    private String cnid;
    
    @ApiModelProperty(value="分单开始时间", name="distributeStartDate")
	private String distributeStartDate;
    
    @ApiModelProperty(value="分单结束时间", name="distributeEndDate")
	private String distributeEndDate;
    
    @ApiModelProperty(value="提单开始时间", name="submitStartDate")
	private String submitStartDate;
    
    @ApiModelProperty(value="提单结束时间", name="submitEndDate")
	private String submitEndDate;

    @ApiModelProperty(value = "客户类型", name = "custType")
    private String custType;

    @ApiModelProperty(value = "是否催单", name = "reminderFlag")
    private String reminderFlag;

    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "申请开始时间", name = "applyStartDate")
    private String applyStartDate;
    
    @ApiModelProperty(value = "申请结束时间", name = "applyEndDate")
    private String applyEndDate;
    
    @ApiModelProperty(value = "审批开始时间", name = "approvalStartDate")
    private String approvalStartDate;
    
    @ApiModelProperty(value = "审批结束时间", name = "approvalEndDate")
    private String approvalEndDate;
    
    @ApiModelProperty(value = "确认开始时间", name = "confirmStartDate")
    private String confirmStartDate;
    
    @ApiModelProperty(value = "确认结束时间", name = "confirmEndDate")
    private String confirmEndDate;
    
    @ApiModelProperty(value = "放款开始时间", name = "loanStartDate")
    private String loanStartDate;
    
    @ApiModelProperty(value = "放款结束时间", name = "loanEndDate")
    private String loanEndDate;

    @ApiModelProperty(value = "渠道号", name = "channelCode")
    private String channelCode;

    @ApiModelProperty(value = "资金方", name = "partnerCode")
    private String partnerCode;

    @ApiModelProperty(value = "分单状态", name = "assignStatus")
    private String assignStatus;
    
    @ApiModelProperty(value = "工单状态", name = "status")
    private String status;
    
    @ApiModelProperty(value = "处理组", name = "groupCode")
    private String groupCode;
    
    @ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;
    
    @ApiModelProperty(value = "是否标记", name = "sign")
    private String sign;
    
    @ApiModelProperty(value = "是否vip", name = "vip")
    private String vip;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    @ApiModelProperty(value = "投诉处理状态", name = "complainStatus")
    private Integer complainStatus;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private String userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    @ApiModelProperty(value = "加急标识,1-加急,0-未加急", name = "urgentFlag")
    private String urgentFlag;
    
    
    @ApiModelProperty(value = "超时未联系。24-48，48-72，72-96，96")
    private String noContactTime;
}
