package com.welab.crm.operate.vo.workorder;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EscalationOrderVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	private Long custId;

	/**
	 * 普通工单id
	 */
	private Long ordinaryId;

	/**
	 * 普通工单提交时间
	 */
	private Date ordinaryDate;

	/**
	 * 升级工单id
	 */
	private Long escalatedId;

	/**
	 * 升级工单提交时间
	 */
	private Date escalatedDate;


	/**
	 * 普通工单三类
	 */
	private String ordinaryOrderThreeType;

}
