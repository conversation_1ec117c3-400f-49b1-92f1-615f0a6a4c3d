package com.welab.crm.operate.vo.transferReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TransferReportDetailVO {


	@ApiModelProperty(value = "工单编号")
	@ExcelProperty(value = "工单编号")
	private String orderNo;

	@ApiModelProperty(value = "工单类型")
	@ExcelProperty(value = "工单类型")
	private String type;

	@ApiModelProperty(value = "工单大类")
	@ExcelProperty(value = "工单大类")
	private String firstType;

	@ApiModelProperty(value = "工单二类")
	@ExcelProperty(value = "工单二类")
	private String secondType;

	@ApiModelProperty(value = "工单三类")
	@ExcelProperty(value = "工单三类")
	private String thirdType;


	@ApiModelProperty(value = "投诉渠道")
	@ExcelProperty(value = "投诉渠道")
	private String complaintsChannel;

	@ApiModelProperty(value = "客户姓名")
	@ExcelProperty(value = "客户姓名")
	private String customerName;

	@ApiModelProperty(value = "性别")
	@ExcelProperty(value = "性别")
	private String gender;

	@ApiModelProperty(value = "年龄")
	@ExcelProperty(value = "年龄")
	private Integer age;

	@ApiModelProperty(value = "身份证号")
	@ExcelProperty(value = "身份证号")
	private String cnid;

	@ApiModelProperty(value = "手机号码")
	@ExcelProperty(value = "手机号码")
	private String mobile;

	@ApiModelProperty(value = "反馈内容")
	@ExcelProperty(value = "反馈内容")
	private String description;

	@ApiModelProperty(value = "uuid")
	@ExcelProperty(value = "uuid")
	private String uuid;

	@ApiModelProperty(value = "userId")
	@ExcelProperty(value = "userId")
	private String userId;

	@ApiModelProperty(value = "资方简称")
	@ExcelProperty(value = "资方简称")
	private String fundName;

	@ApiModelProperty(value = "贷款号")
	@ExcelProperty(value = "贷款号")
	private String applicationId;
	
	@ApiModelProperty(value = "债转公司")
	@ExcelProperty(value = "债转公司")
	private String transferCompany;
}
