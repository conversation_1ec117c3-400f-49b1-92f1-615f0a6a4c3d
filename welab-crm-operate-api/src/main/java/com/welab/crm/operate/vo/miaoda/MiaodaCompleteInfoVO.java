package com.welab.crm.operate.vo.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 喵达投诉单结案信息VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单结案信息对象")
public class MiaodaCompleteInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结案解决方案
     */
    @ApiModelProperty(value = "结案解决方案", name = "coCompleteSolution")
    private String coCompleteSolution;

    /**
     * 结案申请原因
     */
    @ApiModelProperty(value = "结案申请原因", name = "coCompleteReason")
    private String coCompleteReason;

    /**
     * 结案提交时间
     */
    @ApiModelProperty(value = "结案提交时间", name = "coCompleteBegin")
    private Long coCompleteBegin;

    /**
     * 结案附件列表
     */
    @ApiModelProperty(value = "结案附件列表", name = "coCompleteAttaches")
    private List<MiaodaAttachmentVO> coCompleteAttaches;
}
