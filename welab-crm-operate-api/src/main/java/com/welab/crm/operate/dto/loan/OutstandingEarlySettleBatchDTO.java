package com.welab.crm.operate.dto.loan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/1
 */
@Data
@ApiModel(value = "在途贷款批量提前结清请求对象")
public class OutstandingEarlySettleBatchDTO implements Serializable {

    private static final long serialVersionUID = -6468693596684430195L;

    @ApiModelProperty(value = "贷款号")
    @NotNull
    private List<String> applicationId;

    @ApiModelProperty(value = "客户id")
    @NotNull
    private Integer userId;

    @ApiModelProperty(value = "备注")
    @NotBlank
    private String comment;
}
