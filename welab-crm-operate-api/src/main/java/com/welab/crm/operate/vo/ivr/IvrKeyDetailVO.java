package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: ivr案件明细报表返回对象
 * @date 2022/3/8 14:28
 */
@Data
public class IvrKeyDetailVO extends IvrKeyDetailBaseVO{


    @ApiModelProperty(value = "唯一Id，给前端用,合计字段则改值为 合计")
    private String recordId;

    @ApiModelProperty(value = "日期")
    @ExcelTitleMap(title = "日期")
    private String callDate;

    @ApiModelProperty(value = "用户名称")
    @ExcelTitleMap(title = "用户名称")
    private String username;

    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "UUID")
    private String uuid;

    @ApiModelProperty(value = "用户Id")
    @ExcelTitleMap(title = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "呼叫时间")
    @ExcelTitleMap(title = "呼叫时间")
    private String callTime;

    @ApiModelProperty(value = "按键开始时间")
    @ExcelTitleMap(title = "按键开始时间")
    private String keyStartTime;

    @ApiModelProperty(value = "人工接听时间")
    @ExcelTitleMap(title = "人工接听时间")
    private String csAnswerTime;

    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String hangUpTime;

    @ApiModelProperty(value = "通话时长(s)")
    @ExcelTitleMap(title = "通话时长(s)")
    private Long talkDuration;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String staffName;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;

    @ExcelTitleMap(title = "贷款申请-无法提交申请")
    @ApiModelProperty(value = "贷款申请-无法提交申请")
    private Integer registered11;

    @ExcelTitleMap(title = "贷款申请-额度咨询")
    @ApiModelProperty(value = "贷款申请-额度咨询")
    private Integer registered12;
    @ExcelTitleMap(title = "贷款申请-额度冻结")
    @ApiModelProperty(value = "贷款申请-额度冻结")
    private Integer registered13;
    @ExcelTitleMap(title = "放款问题咨询-提现火爆")
    @ApiModelProperty(value = "放款问题咨询-提现火爆")
    private Integer registered21;
    @ExcelTitleMap(title = "放款问题咨询-放款时效")
    @ApiModelProperty(value = "放款问题咨询-放款时效")
    private Integer registered22;
    @ExcelTitleMap(title = "放款问题咨询-提现页面异常")
    @ApiModelProperty(value = "放款问题咨询-提现页面异常")
    private Integer registered23;
    @ExcelTitleMap(title = "放款问题咨询-订单取消")
    @ApiModelProperty(value = "放款问题咨询-订单取消")
    private Integer registered24;
    @ExcelTitleMap(title = "还款相关问题-解绑或换卡")
    @ApiModelProperty(value = "还款相关问题-解绑或换卡")
    private Integer registered31;
    @ExcelTitleMap(title = "还款相关问题-延期还款咨询")
    @ApiModelProperty(value = "还款相关问题-延期还款咨询")
    private Integer registered32;
    @ExcelTitleMap(title = "还款相关问题-还款失败原因")
    @ApiModelProperty(value = "还款相关问题-还款失败原因")
    private Integer registered33;
    @ExcelTitleMap(title = "还款相关问题-还款页面问题")
    @ApiModelProperty(value = "还款相关问题-还款页面问题")
    private Integer registered34;
    @ExcelTitleMap(title = "还款相关问题-开具结清证明")
    @ApiModelProperty(value = "还款相关问题-开具结清证明")
    private Integer registered35;
    @ExcelTitleMap(title = "还款相关问题-全额结清")
    @ApiModelProperty(value = "还款相关问题-全额结清")
    private Integer registered36;
    @ExcelTitleMap(title = "个人信息修改-修改手机号")
    @ApiModelProperty(value = "个人信息修改-修改手机号")
    private Integer registered41;
    @ExcelTitleMap(title = "个人信息修改-注销账号")
    @ApiModelProperty(value = "个人信息修改-注销账号")
    private Integer registered42;
    @ExcelTitleMap(title = "个人信息修改-其他信息修改")
    @ApiModelProperty(value = "个人信息修改-其他信息修改")
    private Integer registered43;

    @ExcelTitleMap(title = "协商还款")
    @ApiModelProperty(value = "协商还款")
    private Integer overdue1;
    @ExcelTitleMap(title = "银行卡冻结或卡受限")
    @ApiModelProperty(value = "银行卡冻结或卡受限")
    private Integer overdue2;
    @ExcelTitleMap(title = "还款方式核实")
    @ApiModelProperty(value = "还款方式核实")
    private Integer overdue3;
    @ExcelTitleMap(title = "投诉催收")
    @ApiModelProperty(value = "投诉催收")
    private Integer overdue4;

    @ExcelTitleMap(title = "债转公司咨询")
    @ApiModelProperty(value = "债转公司咨询")
    private Integer transfer1;
    @ExcelTitleMap(title = "订单状态更新")
    @ApiModelProperty(value = "订单状态更新")
    private Integer transfer2;
    @ExcelTitleMap(title = "还款方式核实")
    @ApiModelProperty(value = "还款方式核实")
    private Integer transfer3;
    @ExcelTitleMap(title = "会员&特权卡用户")
    @ApiModelProperty(value = "会员&特权卡用户")
    private Integer vip1;
    @ExcelTitleMap(title = "非注册用户")
    @ApiModelProperty(value = "非注册用户")
    private Integer notRegistered1;


    @ExcelTitleMap(title = "产品介绍")
    @ApiModelProperty(value = "产品介绍")
    private Integer ds1;
    @ExcelTitleMap(title = "物流及订单状态")
    @ApiModelProperty(value = "物流及订单状态")
    private Integer ds2;
    @ExcelTitleMap(title = "商家入驻")
    @ApiModelProperty(value = "商家入驻")
    private Integer ds3;


    @ExcelTitleMap(title = "产品介绍")
    @ApiModelProperty(value = "产品介绍")
    private Integer zl1;
    @ExcelTitleMap(title = "租赁计费问题")
    @ApiModelProperty(value = "租赁计费问题")
    private Integer zl2;
    @ExcelTitleMap(title = "绑卡及发货问题")
    @ApiModelProperty(value = "绑卡及发货问题")
    private Integer zl3;
    @ExcelTitleMap(title = "还款问题")
    @ApiModelProperty(value = "还款问题")
    private Integer zl4;
    @ExcelTitleMap(title = "订单状态查询")
    @ApiModelProperty(value = "订单状态查询")
    private Integer zl5;

    @ExcelTitleMap(title = "产品介绍")
    @ApiModelProperty(value = "产品介绍")
    private Integer wallet1;
    @ExcelTitleMap(title = "额度申请")
    @ApiModelProperty(value = "额度申请")
    private Integer wallet2;
    @ExcelTitleMap(title = "审核周期")
    @ApiModelProperty(value = "审核周期")
    private Integer wallet3;
    @ExcelTitleMap(title = "分期咨询")
    @ApiModelProperty(value = "分期咨询")
    private Integer wallet4;
    @ExcelTitleMap(title = "绑卡咨询")
    @ApiModelProperty(value = "绑卡咨询")
    private Integer wallet5;
    @ExcelTitleMap(title = "还款咨询")
    @ApiModelProperty(value = "还款咨询")
    private Integer wallet6;
    @ExcelTitleMap(title = "账单查询")
    @ApiModelProperty(value = "账单查询")
    private Integer wallet7;


    @ExcelTitleMap(title = "账户问题 - 开户问题")
    @ApiModelProperty(value = "账户问题 - 开户问题")
    private Integer jiafenZf1;

    @ExcelTitleMap(title = "账户问题 - 更新风险测评")
    @ApiModelProperty(value = "账户问题 - 更新风险测评")
    private Integer jiafenZf2;


    @ExcelTitleMap(title = "交易及资金问题 - 银行卡支付额度问题")
    @ApiModelProperty(value = "交易及资金问题 - 银行卡支付额度问题")
    private Integer jiafenJy1;

    @ExcelTitleMap(title = "交易及资金问题 - 跨组合支付失败")
    @ApiModelProperty(value = "交易及资金问题 - 跨组合支付失败")
    private Integer jiafenJy2;

    @ExcelTitleMap(title = "交易及资金问题 - 基金确认时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金确认时间")
    private Integer jiafenJy3;

    @ExcelTitleMap(title = "交易及资金问题 - 基金赎回到账时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金赎回到账时间")
    private Integer jiafenJy4;


    @ExcelTitleMap(title = "活动问题 - 愿望功能")
    @ApiModelProperty(value = "活动问题 - 愿望功能")
    private Integer jiafenHd1;

    @ExcelTitleMap(title = "活动问题 - 新手福利")
    @ApiModelProperty(value = "活动问题 - 新手福利")
    private Integer jiafenHd2;


    @ExcelTitleMap(title = "功能介绍 - 愿望功能")
    @ApiModelProperty(value = "功能介绍 - 愿望功能")
    private Integer jiafenGn1;

    @ExcelTitleMap(title = "功能介绍 - 现金钱包")
    @ApiModelProperty(value = "功能介绍 - 现金钱包")
    private Integer jiafenGn2;

    @ExcelTitleMap(title = "功能介绍 - 目标盈")
    @ApiModelProperty(value = "功能介绍 - 目标盈")
    private Integer jiafenGn3;



}
