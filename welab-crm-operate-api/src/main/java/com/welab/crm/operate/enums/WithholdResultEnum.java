package com.welab.crm.operate.enums;

/**
 * <AUTHOR>
 * @Description: 代扣结果状态枚举
 * @date 2022/5/5 16:31
 */
public enum WithholdResultEnum {

    SUCCESS("success","成功"),
    FAIL("fail","失败"),
    PROCESSING("processing","进行中"),
    ;

    private String code;

    private String value;

    WithholdResultEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public static String getValueByCode(String code){
        for (WithholdResultEnum resultEnum : values()) {
            if (resultEnum.code.equals(code)){
                return resultEnum.value;
            }
        }

        return "";
    }
}
