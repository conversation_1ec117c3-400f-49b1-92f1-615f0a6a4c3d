package com.welab.crm.operate.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 时间间隔枚举类
 */
public enum PeriodEnum {

    DAY("day", "天"),
    RANGE("range", "周期");

    private final String value;
    private final String text;

    PeriodEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static PeriodEnum getValue(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        for (PeriodEnum period : values()) {
            if (period.value.equals(param)) {
                return period;
            }
        }
        return null;
    }
}
