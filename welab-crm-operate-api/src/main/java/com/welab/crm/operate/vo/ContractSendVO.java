package com.welab.crm.operate.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "合同发送记录")
public class ContractSendVO {


	/**
	 * 0-待审核；1-审核通过；2-审核失败；3-发送成功；4-发送失败
	 */
	@ApiModelProperty(value = "状态 0-待审核；1-审核通过；2-审核失败；3-发送成功；4-发送失败")
	@ExcelProperty(value = "状态")
	private String status;

	/**
	 * 发送状态
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "发送状态 success,failure")
	private String sendStatus;


	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "操作时间")
	@ExcelProperty(value = "操作时间")
	private String createTime;


	@ApiModelProperty(value = "发送时间")
	@ExcelProperty(value = "发送时间")
	private String sendTime;


	/**
	 * 组名称
	 */
	@ApiModelProperty(value = "发送组")
	@ExcelProperty(value = "发送组")
	private String groupName;


	/**
	 * 员工姓名
	 */
	@ApiModelProperty(value = "发送员工")
	@ExcelProperty(value = "发送员工")
	private String staffName;

	/**
	 * 客户名称
	 */
	@ApiModelProperty(value = "客户名称")
	@ExcelProperty(value = "客户名称")
	private String customerName;


	/**
	 * uuid
	 */
	@ApiModelProperty(value = "uuid")
	@ExcelProperty(value = "uuid")
	private String uuid;


	/**
	 * 合同号
	 */
	@ApiModelProperty(value = "合同号")
	@ExcelProperty(value = "合同号")
	private String appNo;


	/**
	 * 资金方名称
	 */
	@ApiModelProperty(value = "资金方名称")
	@ExcelProperty(value = "资金方名称")
	private String partnerName;


	@ApiModelProperty(value = "手机号")
	@ExcelProperty(value = "手机号")
	private String mobile;


	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键id")
	@ExcelIgnore
	private Long id;


	/**
	 * 1-查看且下载；2-仅查看
	 */
	@ApiModelProperty(value = "发送类型 1-查看且下载；2-仅查看")
	@ExcelProperty(value = "发送类型")
	private String sendType;

}
