package com.welab.crm.operate.vo.settlement;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/22
 */
@Data
@ApiModel(value = "结清记录请求对象")
public class SettlementProofReqDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="申请开始时间", name="starApplyTime",example = "2020-01-01")
    private String starApplyTime;

    @ApiModelProperty(value="申请结束时间", name="endApplyTime",example = "2020-01-01")
    private String endApplyTime;

    @ApiModelProperty(value="申请人", name="staffId",example = "xxx")
    private String staffId;

    @ApiModelProperty(value="贷款号", name="applicationId",example = "1113214561564")
    private String applicationId;

    @ApiModelProperty(value="资金方", name="partnerCode",example = "lz")
    private String partnerCode;

    @ApiModelProperty(value="状态", name="state",example = "procing")
    private String state;

}
