package com.welab.crm.operate.vo.staff;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @description TODO 组织查询VO
 * <AUTHOR>
 * @date 2021-11-03 17:03:35
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class ColOrgInfoResVO implements Serializable {
	private static final long serialVersionUID = -7881920500487571499L;

	/**
	 * 主键
	 */
    @ApiModelProperty(value = "id", name = "id", example = "id")
	private Long id;

	/**
	 * 机构名称
	 */
    @ApiModelProperty(value = "机构名称", name = "name", example = "kf")
	private String name;

	/**
	 * 机构code
	 */
    @ApiModelProperty(value = "机构code", name = "code", example = "111")
	private String code;

	/**
	 * 父机构代码
	 */
    @ApiModelProperty(value = "父机构代码", name = "父机构代码", example = "111")
	private String pcode;
	
	/**
	 * 层级
	 */
    @ApiModelProperty(value = "层级->是否管理组", name = "level", example = "0")
    private int level = 0;
    
    private List<ColOrgInfoResVO> children;
}
