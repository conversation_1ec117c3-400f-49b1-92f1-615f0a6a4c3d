package com.welab.crm.operate.dto.video;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 视频任务查询请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(value = "视频任务查询请求对象")
public class VideoTaskReqDTO extends BaseReqDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;


    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String uuid;


    /**
     * 短信发送时间(开始)
     */
    @ApiModelProperty(value = "短信发送时间(开始)")
    private String startTime;


    /**
     * 短信发送时间(结束)
     */
    @ApiModelProperty(value = "短信发送时间(结束)")
    private String endTime;

    /**
     * 短信发送时间(开始)
     */
    @ApiModelProperty(value = "通话时间(开始)")
    private String callStartTime;


    /**
     * 短信发送时间(结束)
     */
    @ApiModelProperty(value = "通话时间(结束)")
    private String callEndTime;

    /**
     * 是否收藏
     * 1-收藏
     * 0-未收藏
     */
    @ApiModelProperty(value = "是否收藏")
    private Boolean isCollect;


    @ApiModelProperty(value = "人脸对比")
    private Integer imgCode;

    @ApiModelProperty(value = "声音对比")
    private Integer voiceCode;

    @ApiModelProperty(value = "录制状态")
    private Integer isVideo;

    @ApiModelProperty(value = "发送人")
    private String staffId;
}
