package com.welab.crm.operate.vo.earlySettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "结清客户统计表")
public class EarlySettledRecordUserSummaryVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "组别")
    private String groupName;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "开通客户量")
    private String userCount;

    @ApiModelProperty(value = "开通订单笔数")
    private String orderCount;

    @ApiModelProperty(value = "结清订单笔数")
    private String earlySettledOrderCount;

    @ApiModelProperty(value = "结清率")
    private String earlySettledRate;

    @ApiModelProperty(value = "结清金额")
    private String debitAmount;
}
