package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单统计请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单统计请求对象")
public class WorkOrderTotalVO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	@ApiModelProperty(value = "总工单量", name = "totalCount")
    private Integer totalCount;
	
	@ApiModelProperty(value = "待处理", name = "pendingCount")
    private Integer pendingCount;
	
	@ApiModelProperty(value = "处理中", name = "processingCount")
    private Integer processingCount;

    @ApiModelProperty(value = "已流转", name = "movingCount")
    private Integer movingCount;

    @ApiModelProperty(value = "已过期", name = "expiredCount")
    private Integer expiredCount;
}
