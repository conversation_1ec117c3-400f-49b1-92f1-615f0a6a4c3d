package com.welab.crm.operate.dto.wallet;

import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/10
 */
@Data
@ApiModel(value = "钱包提前结清请求对象")
public class WalletEarlySettleDTO extends HistoryOperationDTO {

    private static final long serialVersionUID = -3149673290788117960L;

    /**
     * 费率
     */
    private String loanRate;

    /**
     * 资金方
     */
    private String partnerCode;


    /**
     * 当前期数
     */
    private String tenor;


    /**
     * 结清原因
     */
    private String reason;

}
