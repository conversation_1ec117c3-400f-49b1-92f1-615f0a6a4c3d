package com.welab.crm.operate.dto.workorder;

import org.hibernate.validator.constraints.NotBlank;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单标记请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单标记请求对象")
public class WorkOrderSignReqDTO extends BaseRequestDTO{

	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "工单编号", name = "orderNo")
	@NotBlank
    private String orderNo;
	
	@ApiModelProperty(value = "员工ID", name = "staffId")
	@NotBlank
    private String staffId;
	
	@ApiModelProperty(value = "工单标记，0未收藏，1收藏", name = "sign")
	@NotBlank
    private String sign;
}
