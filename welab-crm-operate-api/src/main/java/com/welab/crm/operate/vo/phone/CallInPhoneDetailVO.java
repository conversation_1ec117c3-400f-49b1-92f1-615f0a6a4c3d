package com.welab.crm.operate.vo.phone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/2
 */
@Data
@ApiModel(value = "通话详情响应对象")
public class CallInPhoneDetailVO implements Serializable {

    private static final long serialVersionUID = -524724043738790009L;

    @ApiModelProperty(value = "记录号", name = "记录号")
    private Long id;

//    @ApiModelProperty(value = "录音编号", name = "录音编号")
//    private String cdrRecordFile;

    @ApiModelProperty(value = "呼出坐席工号", name = "呼出坐席工号")
    private String cdrCno;

    @ApiModelProperty(value = "呼入坐席工号", name = "呼入坐席工号")
    private String cdrCalleeCno;

    @ApiModelProperty(value = "话务员id", name = "话务员id")
    private String staffId;

    @ApiModelProperty(value = "话务员姓名", name = "话务员姓名")
    private String staffName;

    @ApiModelProperty(value = "业务类型", name = "业务类型")
    private String cdrCallType;

    @ApiModelProperty(value = "开始时间", name = "开始时间")
    private Date cdrStartTime;

    @ApiModelProperty(value = "结束时间", name = "结束时间")
    private Date cdrEndTime;

    @ApiModelProperty(value = "持续时间", name = "持续时间")
    private String cdrEndBridgeTime;

    @ApiModelProperty(value = "电话类型", name = "电话类型")
    private String cdrCustomerNumberType;

    @ApiModelProperty(value = "分机号", name = "分机号")
    private String cdrAgentNumber;

    @ApiModelProperty(value = "主叫号", name = "主叫号")
    private String cdrClid;

    @ApiModelProperty(value = "被叫号", name = "被叫号")
    private String cdrCustomerNumber;

    @ApiModelProperty(value = "结束原因", name = "结束原因")
    private String cdrEndReason;

    @ApiModelProperty(value = "客户姓名", name = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private Integer userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    @ApiModelProperty(value = "组织编码", name = "groupCode")
    private String groupCode;
    
    @ApiModelProperty(value = "满意度评价")
    private String satisfactionEvaluation;

    @ApiModelProperty(value = "录音对应小结内容")
    private PhoneSummaryVO phoneSummaryVO;
}
