package com.welab.crm.operate.dto.workorder;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单调剂请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "分单调剂规则请求对象")
public class WorkOrderRuleAdjustReqDTO extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;
	
    /**
     * 查询方式
     */
	@ApiModelProperty(value = "查询方式", name = "method")
    private String method;
	
    /**
     * 处理组
     */
	@ApiModelProperty(value = "处理组", name = "groupCode")
    private List<String> groupCode;

    /**
     * 处理人
     */
	@ApiModelProperty(value = "处理人", name = "staffId")
    private List<String> staffId;
	
	/**
     * 工单状态
     */
	@ApiModelProperty(value = "工单状态", name = "status")
    private String status;
	
	/**
     * 贷款号
     */
	@ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;
	
	/**
     * 工单类型
     */
	@ApiModelProperty(value = "工单类型", name = "type")
    private List<String> type;

    /**
     * 子工单分类
     * 
     */
	@ApiModelProperty(value = "子工单分类", name = "orderCase")
    private List<String> orderCase;
	
	/**
     * 分配开始日期
     */
	@ApiModelProperty(value = "分配开始日期", name = "distributeStartDate")
	@JsonFormat(pattern="yyyy-MM-dd")
    private Date distributeStartDate;
	
	/**
     * 分配结束日期
     */
	@ApiModelProperty(value = "分配开始日期", name = "distributeEndDate")
	@JsonFormat(pattern="yyyy-MM-dd")
    private Date distributeEndDate;
	
	/**
     * 提单开始时间
     */
	@ApiModelProperty(value = "提单开始时间", name = "submitStartDate")
	@JsonFormat(pattern="yyyy-MM-dd")
    private Date submitStartDate;
	
	/**
     * 提单结束时间
     */
	@ApiModelProperty(value = "提单结束时间", name = "submitEndDate")
	@JsonFormat(pattern="yyyy-MM-dd")
    private Date submitEndDate;
	
	/**
     * 手机号码
     */
	@ApiModelProperty(value = "手机号码", name = "mobile")
    private String mobile;
	
	/**
     * 客户姓名
     */
	@ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;
	
	/**
     * 重新分配列表
     */
	@ApiModelProperty(value = "重新分配列表", name = "assignList")
    private List<AdjustWorkOrderInfo> assignList;
	
	/**
     * 详单列表
     */
	@ApiModelProperty(value = "详单列表", name = "idList")
    private List<String> idList;

	@ApiModelProperty(value = "操作人id", name = "operatorId")
	private String operatorId;
	
	/**
     * 是否分配
     */
	@ApiModelProperty(value = "是否分配", name = "isAssigned")
    private Integer isAssigned;
	
	/**
     * 工单编号
     */
	@ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

	@ApiModelProperty(value = "用户Id", name = "userId")
	private String userId;

	@ApiModelProperty(value = "uuid", name = "uuid")
	private String uuid;
}
