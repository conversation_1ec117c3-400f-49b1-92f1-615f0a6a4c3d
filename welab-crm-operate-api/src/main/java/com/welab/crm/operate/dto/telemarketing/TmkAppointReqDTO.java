package com.welab.crm.operate.dto.telemarketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description: 电销预约回电请求类
 * @date 2022/2/25 17:11
 */
@Data
@ApiModel(value = "电销预约回电请求类")
public class TmkAppointReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电销唯一任务Id")
    @NotBlank
    private String tmkTaskId;

    @ApiModelProperty(value = "预约时间")
    private String appointTime;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "用户Id")
    @NotNull
    private Integer userId;
}
