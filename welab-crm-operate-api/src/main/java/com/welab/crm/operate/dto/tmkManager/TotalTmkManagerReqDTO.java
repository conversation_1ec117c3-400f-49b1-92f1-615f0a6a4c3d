package com.welab.crm.operate.dto.tmkManager;

import java.util.List;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据管理请求参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据管理请求参数")
public class TotalTmkManagerReqDTO extends BaseRequestDTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	@ApiModelProperty(value = "话务组", name = "groupCode")
    private List<String> groupCode;
    
    @ApiModelProperty(value = "话务人", name = "staffId")
    private List<String> staffId;
}

