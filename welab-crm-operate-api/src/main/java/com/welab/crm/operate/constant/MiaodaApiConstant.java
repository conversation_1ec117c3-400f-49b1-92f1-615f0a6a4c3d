package com.welab.crm.operate.constant;

/**
 * 喵达API常量类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public class MiaodaApiConstant {

    /**
     * API接口路径
     */
    public static final String LOGIN_PATH = "/auth/token";
    public static final String QUERY_PATH = "/complaint/detail_v2";
    public static final String UPDATE_PATH = "/complaint/update";
    public static final String REPLY_PATH = "/complaint/reply";
    public static final String APPEAL_PATH = "/complaint/appeal";
    public static final String APPEAL_STATUS_PATH = "/complaint/appeal_status";
    public static final String COMPLETE_PATH = "/complaint/complete";
    public static final String RAPID_SOLVE_PATH = "/complaint/rapid_solve";
    public static final String PLAN_INFO_PATH = "/complaint/plan_info";
    public static final String CLAIM_PATH = "/complaint/claim";

    /**
     * HTTP请求头
     */
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String AUTHORIZATION_PREFIX = "MiaoDa ";
    public static final String CONTENT_TYPE_JSON = "application/json";

    /**
     * 投诉单状态
     */
    public static final int STATUS_PENDING_REVIEW = 0;      // 刚提交待审核
    public static final int STATUS_APPROVED = 1;           // 审核通过
    public static final int STATUS_REJECTED = 2;           // 审核不通过
    public static final int STATUS_PENDING_ASSIGN = 3;     // 待分配
    public static final int STATUS_PROCESSING = 4;         // 处理中（分配成功）
    public static final int STATUS_ASSIGN_FAILED = 5;      // 分配失败
    public static final int STATUS_REPLIED = 6;            // 投诉对象已经回复
    public static final int STATUS_COMPLETED = 7;          // 投诉已完成
    public static final int STATUS_CLOSED = 8;             // 投诉已关闭
    public static final int STATUS_REMOVED = 9;            // 投诉已移除

    /**
     * 申诉状态
     */
    public static final int APPEAL_STATUS_NOT_STARTED = 0; // 未发起申诉
    public static final int APPEAL_STATUS_PROCESSING = 1;  // 申诉中
    public static final int APPEAL_STATUS_REJECTED = 2;    // 申诉驳回
    public static final int APPEAL_STATUS_APPROVED = 3;    // 申诉通过

    /**
     * 申诉类型
     */
    public static final int APPEAL_TYPE_DUPLICATE = 1;     // 重复投诉
    public static final int APPEAL_TYPE_NOT_BELONG = 2;    // 非本商户投诉

    /**
     * 结案类型
     */
    public static final int COMPLETE_TYPE_AGREEMENT = 1;   // 已与用户沟通并达成一致
    public static final int COMPLETE_TYPE_NO_CONTACT = 2;  // 联系不上用户
    public static final int COMPLETE_TYPE_FINAL_SOLUTION = 3; // 最终解决方案


    /**
     * 喵达最大结案次数
     */
    public static final int MAX_COMPLETE_TIME = 2;

    /**
     * 回复人类型
     */
    public static final int SENDER_USER = 1;              // 用户补充
    public static final int SENDER_MERCHANT = 2;          // 商家回复

    /**
     * 隐藏标识
     */
    public static final int HIDE_NO = 0;                  // 不隐藏
    public static final int HIDE_YES = 1;                 // 隐藏

    /**
     * 公开状态
     */
    public static final int EXPOSED_NO = 0;               // 未公开
    public static final int EXPOSED_YES = 1;              // 已公开

    /**
     * 同步状态
     */
    public static final int SYNC_STATUS_PENDING = 0;      // 待同步
    public static final int SYNC_STATUS_SUCCESS = 1;      // 已同步
    public static final int SYNC_STATUS_FAILED = 2;       // 同步失败

    /**
     * 映射状态
     */
    public static final int MAPPING_DISABLED = 0;         // 禁用
    public static final int MAPPING_ENABLED = 1;          // 启用

    /**
     * Token过期时间（3小时，单位：毫秒）
     */
    public static final long TOKEN_EXPIRE_TIME = 3 * 60 * 60 * 1000L;

    /**
     * Token提前刷新时间（30分钟，单位：毫秒）
     */
    public static final long TOKEN_REFRESH_ADVANCE_TIME = 30 * 60 * 1000L;

    /**
     * 批量操作最大数量
     */
    public static final int BATCH_MAX_SIZE = 30;

    /**
     * 分页默认大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 分页最大大小
     */
    public static final int MAX_PAGE_SIZE = 30;

    /**
     * 文件上传限制
     */
    public static final int MAX_IMAGE_COUNT = 30;         // 最多上传30张图片
    public static final int MAX_VIDEO_COUNT = 3;          // 最多上传3个视频
    public static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024L;    // 图片最大5MB
    public static final long MAX_VIDEO_SIZE = 100 * 1024 * 1024L;  // 视频最大100MB
    public static final long MAX_AUDIO_SIZE = 10 * 1024 * 1024L;   // 音频最大10MB

    /**
     * 支持的文件格式
     */
    public static final String[] SUPPORTED_IMAGE_FORMATS = {"png", "jpeg", "jpg"};
    public static final String[] SUPPORTED_VIDEO_FORMATS = {"mp3", "mp4"};

    /**
     * 错误码
     */
    public static final int ERROR_CODE_PARAM_ERROR = 30001;       // http请求参数错误
    public static final int ERROR_CODE_AUTH_ERROR = 30002;        // 身份校验错误
    public static final int ERROR_CODE_JSON_PARSE_ERROR = 30003;  // JSON解析失败
    public static final int ERROR_CODE_DB_ERROR = 30004;          // 数据库操作失败
    public static final int ERROR_CODE_NETWORK_ERROR = 30005;     // 网络连接错误
    public static final int ERROR_CODE_NO_DATA = 30006;           // 此次查询结果为空
    public static final int ERROR_CODE_ATTACHMENT_ERROR = 30007;  // 附件格式/参数错误

    /**
     * 默认投诉类型映射优先级
     */
    public static final int DEFAULT_MAPPING_PRIORITY = 100;

    /**
     * Redis缓存Key前缀
     */
    public static final String REDIS_TOKEN_KEY = "miaoda:token";
    public static final String REDIS_COMPLAINT_LOCK_KEY = "miaoda:complaint:lock:";
    public static final String REDIS_SYNC_LOCK_KEY = "miaoda:sync:lock";

    /**
     * 锁过期时间（分钟）
     */
    public static final int LOCK_EXPIRE_MINUTES = 10;
}
