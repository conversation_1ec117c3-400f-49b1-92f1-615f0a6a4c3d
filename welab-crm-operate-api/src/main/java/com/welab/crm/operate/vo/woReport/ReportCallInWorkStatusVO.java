package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/10
 */
@Data
@ApiModel(value = "客服呼入工作状态报表响应对象")
@Excel(fileName = "客服呼入工作状态报表")
public class ReportCallInWorkStatusVO implements Serializable {

    private static final long serialVersionUID = 2898389753336121730L;

    @ApiModelProperty(value = "时间")
    @ExcelTitleGroup(title = "时间", mergeNum = 1)
    private String date;

    @ApiModelProperty(value = "工号")
    @ExcelTitleGroup(title = "工号", mergeNum = 1)
    private String cno;

    @ApiModelProperty(value = "姓名")
    @ExcelTitleGroup(title = "姓名", mergeNum = 1)
    private String staffName;

    @ApiModelProperty(value = "团队")
    @ExcelTitleGroup(title = "团队", mergeNum = 1)
    private String groupName;

    @ApiModelProperty(value = "小休")
    @ExcelTitleGroup(title = "小休", subTitle = true)
    private ReportWorkStatusDetailsVO rest;

    @ApiModelProperty(value = "置忙")
    @ExcelTitleGroup(title = "置忙", subTitle = true)
    private ReportWorkStatusDetailsVO pause;

    @ApiModelProperty(value = "培训")
    @ExcelTitleGroup(title = "培训", subTitle = true)
    private ReportWorkStatusDetailsVO training;

    @ApiModelProperty(value = "就餐")
    @ExcelTitleGroup(title = "就餐", subTitle = true)
    private ReportWorkStatusDetailsVO eating;

    @ApiModelProperty(value = "会议")
    @ExcelTitleGroup(title = "会议", subTitle = true)
    private ReportWorkStatusDetailsVO meeting;

    @ApiModelProperty(value = "工单提单")
    @ExcelTitleGroup(title = "工单提单", subTitle = true)
    private ReportWorkStatusDetailsVO submitOrder;

    @ApiModelProperty(value = "员工id")
    private String staffId;

    @ApiModelProperty(value = "团队code")
    private String groupCode;
}
