package com.welab.crm.operate.dto.externalCompalaint;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@Data
@ApiModel(value = "ExternalComplaintDTO", description = "外部投诉DTO")
public class ExternalComplaintQueryDTO extends BaseReqDTO {

	@ApiModelProperty(value = "登录名称", name = "loginName")
	private String loginName;

	@ApiModelProperty(value = "渠道单号", name = "channelOrderNo")
	private String channelOrderNo;


	@ApiModelProperty(value = "借据号", name = "debtOrderNo")
	private String debtOrderNo;


	@ApiModelProperty(value = "身份证号", name = "idNo")
	private String idNo;
	
	@ApiModelProperty(value = "id列表", name = "ids")
	private List<Long> ids;
	

	@ApiModelProperty(value = "手机号", name = "mobile")
	private String mobile;
	
	@ApiModelProperty(value = "开始时间", name = "startTime")
	@NotBlank(message = "开始时间不能为空")
	private String startTime;

	@ApiModelProperty(value = "结束时间", name = "endTime")
	@NotBlank(message = "结束时间不能为空")
	private String endTime;

}
