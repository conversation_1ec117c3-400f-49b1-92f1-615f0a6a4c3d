package com.welab.crm.operate.vo.phone;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/2
 */
@Data
@ApiModel(value = "来电明细报表响应对象")
@Excel(fileName="来电明细报表")
public class ReportPhoneVO implements Serializable {

    private static final long serialVersionUID = -524724043738790009L;

    @ApiModelProperty(value = "日期", name = "日期")
    @ExcelTitleMap(title = "日期")
    private Date gmtCreate;

    @ApiModelProperty(value = "客户姓名", name = "客户姓名")
    @ExcelTitleMap(title = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "uuid", name = "uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ApiModelProperty(value = "userId", name = "userId")
    @ExcelTitleMap(title = "userId")
    private Long userId;

    @ApiModelProperty(value = "来电号码", name = "来电号码")
    @ExcelTitleMap(title = "来电号码")
    private String cdrCustomerNumber;

    @ApiModelProperty(value = "号码归属地", name = "号码归属地")
    @ExcelTitleMap(title = "号码归属地")
    private String area;

    @ApiModelProperty(value = "呼叫类型", name = "呼叫类型")
    @ExcelTitleMap(title = "呼叫类型")
    private String cdrCallType;

    @ApiModelProperty(value = "呼入时间", name = "呼入时间")
    @ExcelTitleMap(title = "呼入时间")
    private Date cdrStartTime;

    @ApiModelProperty(value = "接听时间", name = "接听时间")
    @ExcelTitleMap(title = "接听时间")
    private Date cdrBridgeTime;

    @ApiModelProperty(value = "挂机时间", name = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private Date cdrEndTime;

    @ApiModelProperty(value = "客户挂机(是/否)", name = "客户挂机(是/否)")
    @ExcelTitleMap(title = "客户挂机(是/否)")
    private String cdrEndReason;

    @ApiModelProperty(value = "通话时长", name = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private String cdrEndBridgeTime;

    @ApiModelProperty(value = "接听员工", name = "接听员工")
    @ExcelTitleMap(title = "接听员工")
    private Long staffId;

    @ApiModelProperty(value = "话务员姓名", name = "话务员姓名")
    @ExcelTitleMap(title = "话务员姓名")
    private String staffName;

    @ApiModelProperty(value = "呼出坐席", name = "呼出坐席")
    @ExcelTitleMap(title = "呼出坐席")
    private String cdrCno;

    @ApiModelProperty(value = "呼入坐席", name = "呼入坐席")
    @ExcelTitleMap(title = "呼入坐席")
    private String cdrCalleeCno;

    @ApiModelProperty(value = "处理状态", name = "处理状态")
    @ExcelTitleMap(title = "处理状态")
    private String cdrStatus;

    @ApiModelProperty(value = "技能组", name = "技能组")
    @ExcelTitleMap(title = "技能组")
    private String groupCode;

    @ApiModelProperty(value = "热线号码", name = "热线号码")
    @ExcelTitleMap(title = "热线号码")
    private String cdrHotline;

    @ApiModelProperty(value = "vip用户(是/否)", name = "vip用户(是/否)")
    @ExcelTitleMap(title = "vip用户(是/否)")
    private String vip;

}
