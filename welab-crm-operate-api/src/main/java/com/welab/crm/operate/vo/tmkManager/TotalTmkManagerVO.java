package com.welab.crm.operate.vo.tmkManager;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据统计响应对象
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据管理统计响应对象")
public class TotalTmkManagerVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	@ApiModelProperty(value = "话务组", name = "groupCode")
    private String groupCode;
    
    @ApiModelProperty(value = "话务人", name = "staffId")
    private String staffId;

    /**
     * 数量
     */
	@ApiModelProperty(value = "数量", name = "count")
    private Integer count;
}

