package com.welab.crm.operate.dto.repaylink;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * h5自定义还款dto
 * <AUTHOR>
 */
@Data
@ApiModel(description = "h5还款链接发送请求对象")
public class H5RepayMentDTO implements Serializable {

    private static final long serialVersionUID = -3343956492759946012L;

    @ApiModelProperty(value = "贷款号", required = true)
    private String applicationId;

    @ApiModelProperty(value = "userId", required = true)
    private Long userId;

}
