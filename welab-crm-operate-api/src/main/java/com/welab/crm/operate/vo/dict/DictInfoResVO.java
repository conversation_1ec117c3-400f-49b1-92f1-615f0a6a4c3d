package com.welab.crm.operate.vo.dict;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * 字典数据响应信息
 */
@Getter
@Setter
public class DictInfoResVO implements Serializable {
	private static final long serialVersionUID = 8581288562623600537L;
	
	private Long id;
	/**
	 * 属性分组
	 */
	private String category;

	/**
	 * 自定义类型
	 */
	private String type;

	/**
	 * 配置说明
	 */
	private String content;
	/**
	 * 描述
	 */
	private String detail;
	/**
	 * 状态 0-有效 1-无效
	 */
	private Integer status;
	
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 排序字段，越大排在越前
	 */
	private Integer sort;
}
