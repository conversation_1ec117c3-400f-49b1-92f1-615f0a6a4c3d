package com.welab.crm.operate.dto.workorder;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/7/24
 */
@Data
@ApiModel(value = "监管投诉统计响应对象")
@Excel(fileName = "监管投诉统计报表", sheetName = "监管投诉统计报表")
public class RegulatoryComplaintResDTO implements Serializable {

    private static final long serialVersionUID = -3650899442044372811L;

    @ApiModelProperty(value = "日期", name = "times")
    @ExcelTitleMap(title = "日期")
    private String times;

    @ApiModelProperty(value = "12345热线", name = "reXian")
    @ExcelTitleMap(title = "12345")
    private Integer reXian;

    @ApiModelProperty(value = "国满件", name = "guoManJian")
    @ExcelTitleMap(title = "国满件")
    private Integer guoManJian;

    @ApiModelProperty(value = "初件-普通件", name = "puTongJian")
    @ExcelTitleMap(title = "初件-普通件")
    private Integer puTongJian;

    @ApiModelProperty(value = "重复件", name = "chongFuJian")
    @ExcelTitleMap(title = "重复件")
    private Integer chongFuJian;

    @ApiModelProperty(value = "税务局", name = "shuiWuJu")
    @ExcelTitleMap(title = "税务局")
    private Integer shuiWuJu;

    @ApiModelProperty(value = "派出所", name = "paiChuSuo")
    @ExcelTitleMap(title = "派出所")
    private Integer paiChuSuo;

    @ApiModelProperty(value = "南山金融", name = "nanShan")
    @ExcelTitleMap(title = "南山金融专班")
    private Integer nanShan;

    @ApiModelProperty(value = "北海金融", name = "beiHai")
    @ExcelTitleMap(title = "北海金融办")
    private Integer beiHai;

    @ApiModelProperty(value = "质量监督管理局", name = "zhiLiang")
    @ExcelTitleMap(title = "质量监督管理局")
    private Integer zhiLiang;

    @ApiModelProperty(value = "基金小镇前台", name = "xiaoZhen")
    @ExcelTitleMap(title = "基金小镇前台")
    private Integer xiaoZhen;

    @ApiModelProperty(value = "市政一体化", name = "shiZheng")
    @ExcelTitleMap(title = "市政一体化")
    private Integer shiZheng;

    @ApiModelProperty(value = "诉讼/举报", name = "suSong")
    @ExcelTitleMap(title = "诉讼/举报")
    private Integer suSong;

    @ApiModelProperty(value = "合计", name = "total")
    @ExcelTitleMap(title = "合计")
    private Integer total;

    @ApiModelProperty(value = "解决量", name = "jieJueLiang")
    @ExcelTitleMap(title = "解决量")
    private Integer jieJueLiang;

    @ApiModelProperty(value = "解决率", name = "jieJuePercent")
    @ExcelTitleMap(title = "解决率")
    private Double jieJuePercent;

}
