package com.welab.crm.operate.dto.tmkRule;

import java.util.List;

import com.welab.crm.operate.dto.BaseRequestDTO;
import com.welab.crm.operate.dto.workorder.AdjustWorkOrderInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 电销数据调剂请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "电销数据调剂规则请求对象")
public class TmkRuleAdjustReqDTO extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;
	
	/**
     * 电销重新分配列表
     */
	@ApiModelProperty(value = "电销重新分配列表", name = "assignList")
    private List<AdjustWorkOrderInfo> assignList;
	
	/**
     * 电销需要分配详单列表
     */
	@ApiModelProperty(value = "电销需要分配详单列表", name = "taskList")
    private List<TmkRuleAdjustInfo> taskList;

	@ApiModelProperty(value = "操作人id", name = "operatorId")
	private String operatorId;
	
	@ApiModelProperty(value = "操作人组Code", name = "operatorGroupCode")
	private String operatorGroupCode;
}
