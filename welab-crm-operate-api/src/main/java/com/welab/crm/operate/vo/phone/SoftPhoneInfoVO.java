package com.welab.crm.operate.vo.phone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/22 15:50
 */

@Data
@ApiModel(description = "软电话记录返回对象")
public class SoftPhoneInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关联通话详情小结的主键
     */
    @ApiModelProperty(value = "关联通话详情小结的主键")
    private Long callDetailGuid;

    /**
     * 话务员
     */
    @ApiModelProperty(value = "话务员")
    private String staffId;

    /**
     * 所在组
     */
    @ApiModelProperty(value = "所在组")
    private String groupCode;

    /**
     * 企业Id
     */
    @ApiModelProperty(value = "企业Id")
    private String cdrEnterpriseId;

    /**
     * 中继号码
     */
    @ApiModelProperty(value = "中继号码")
    private String cdrNumberTrunk;

    /**
     * 热线号码
     */
    @ApiModelProperty(value = "热线号码")
    private String cdrHotline;

    /**
     * 通话标识
     */
    @ApiModelProperty(value = "通话标识")
    private String cdrMainUniqueId;

    /**
     * 客户号码
     */
    @ApiModelProperty(value = "客户号码")
    private String cdrCustomerNumber;

    /**
     * 客户号码所属区号
     */
    @ApiModelProperty(value = "客户号码所属区号")
    private String cdrCustomerAreaCode;

    /**
     * 客户号码所属城市
     */
    @ApiModelProperty(value = "客户号码所属城市")
    private String cdrCustomerCity;

    /**
     * 客户号码所属省份
     */
    @ApiModelProperty(value = "客户号码所属省份")
    private String cdrCustomerProvince;

    /**
     * 客户号码类型 1 固话 2 手机
     */
    @ApiModelProperty(value = "客户号码类型 1 固话 2 手机")
    private String cdrCustomerNumberType;

    /**
     * 1 座席接听; 2 已呼叫座席,座席未接听; 3 系统接听; 4 系统未接-IVR配置错误; 5 系统未接-停机;
     */
    @ApiModelProperty(value = "1 座席接听; 2 已呼叫座席,座席未接听; 3 系统接听; 4 系统未接-IVR配置错误; 5 系统未接-停机;")
    private String cdrStatus;

    /**
     * 1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫
     */
    @ApiModelProperty(value = "1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫")
    private String cdrCallType;

    /**
     * 接听的员工坐席号
     */
    @ApiModelProperty(value = "接听的员工坐席号")
    private String cdrCalleeCno;

    /**
     * 呼入进入队列时间
     */
    @ApiModelProperty(value = "呼入进入队列时间")
    private Date cdrJoinQueueTime;

    /**
     * 呼入座席接听时间/外呼客户接听时间
     */
    @ApiModelProperty(value = "呼入座席接听时间/外呼客户接听时间")
    private Date cdrBridgeTime;

    /**
     * 进入系统时间/座席发起外呼时间
     */
    @ApiModelProperty(value = "进入系统时间/座席发起外呼时间")
    private Date cdrStartTime;

    /**
     * 系统接听时间/外呼座席接听时间
     */
    @ApiModelProperty(value = "系统接听时间/外呼座席接听时间")
    private Date cdrAnswerTime;

    /**
     * 呼入/外呼挂机时间
     */
    @ApiModelProperty(value = "呼入/外呼挂机时间")
    private Date cdrEndTime;

    /**
     * 录音文件名称
     */
    @ApiModelProperty(value = "录音文件名称")
    private String cdrRecordFile;

    /**
     * 挂机原因1000 主通道挂机; 1001 非主通道挂机; 1002 被强拆
     */
    @ApiModelProperty(value = "挂机原因1000 主通道挂机; 1001 非主通道挂机; 1002 被强拆")
    private String cdrEndReason;

    /**
     * 双方接听时长
     */
    @ApiModelProperty(value = "双方接听时长")
    private String cdrEndBridgeTime;

    /**
     * 队列号
     */
    @ApiModelProperty(value = "队列号")
    private String cdrQueue;

    /**
     * 号码状态识别结果。710:忙 711:超时 712:拒接 713:空号 714:关机 715:暂时无法接听 716:停机
     */
    @ApiModelProperty(value = "号码状态识别结果。710:忙 711:超时 712:拒接 713:空号 714:关机 715:暂时无法接听 716:停机")
    private String cdrDetailSipCause;

    /**
     * 满意度调查开始时间，如果没有进行满意度调查值为0
     */
    @ApiModelProperty(value = "满意度调查开始时间，如果没有进行满意度调查值为0")
    private Date cdrInvestigation;

    /**
     * 坐席工号
     */
    @ApiModelProperty(value = "坐席工号")
    private String cdrCno;

    /**
     * 虚拟小号
     */
    @ApiModelProperty(value = "虚拟小号")
    private String cdrXNumber;

    /**
     * 客户振铃时间
     */
    @ApiModelProperty(value = "客户振铃时间")

    private Date calleeRingingTime;

    /**
     * 通话请求id
     */
    @ApiModelProperty(value = "通话请求id")
    private String cdrRequestUniqueId;

    /**
     * 座席绑定电话
     */
    @ApiModelProperty(value = "座席绑定电话")
    private String cdrAgentNumber;

    /**
     * 客户侧外显号码
     */
    @ApiModelProperty(value = "客户侧外显号码")
    private String custCalleeClid;

    /**
     * 座席侧外显号码
     */
    @ApiModelProperty(value = "座席侧外显号码")
    private String cdrClid;

}
