package com.welab.crm.operate.vo.loan;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Excel(fileName = "债转结清合同列表", sheetName = "债转结清合同列表")
public class LoanContactVO {

    /**
     * 债转结清对应的每一条合同数据的id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 贷款合同号
     */
    @ApiModelProperty(value = "贷款合同号")
    @ExcelTitleMap(title = "贷款合同号")
    private String contractNo;

    /**
     * 推送状态
     */
    @ApiModelProperty(value = "推送状态")
    @ExcelTitleMap(title = "推送状态")
    private String pushState;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelTitleMap(title = "创建时间")
    private String gmtCreate;

    @ApiModelProperty(value = "处理状态")
    @ExcelTitleMap(title = "处理状态")
    private String processStatus;

    @ApiModelProperty(value = "失败原因")
    @ExcelTitleMap(title = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "短信状态")
    @ExcelTitleMap(title = "短信状态")
    private String smsState;
}
