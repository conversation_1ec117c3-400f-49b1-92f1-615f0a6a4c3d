package com.welab.crm.operate.dto.telemarketing;

import com.welab.crm.interview.dto.PageQueryDTO;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 电销名单分配明细报表请求体
 * @date 2022/3/15 16:45
 */
@Data
@ApiModel("电销名单分配明细报表请求体")
public class TmkAssignDetailReportReqDTO extends TmkReportBaseReqDTO {

    @ApiModelProperty("业务类型")
    private String tmkType;


    @ApiModelProperty("业务类型")
    private List<String> tmkTypeList;

}
