package com.welab.crm.operate.dto.loan;


import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
public class LoanCancelDTO extends BaseRequestDTO {

    /**
     * 将要取消的贷款号
     */
    @ApiModelProperty(value = "贷款号")
    private String contractNo;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    private Integer approveState;

    /**
     * 创建开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建开始时间")
    private Date startCreateTime;

    /**
     * 创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建结束时间")
    private Date endCreateTime;
}
