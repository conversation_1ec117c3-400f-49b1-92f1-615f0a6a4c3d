package com.welab.crm.operate.enums;

/**
 * 呼叫状态枚举类
 * 1 座席接听, 2 已呼叫座席,座席未接听 (呼入相关)
 * 3 系统接听, 4 系统未接-IVR配置错误, 5 系统未接-停机 (系统相关)
 * 30 座席未接听,31 座席接听,未呼叫客户, 32 座席接听,客户未接听, 33 双方接听 (预览外呼相关)
 * 50 主叫外呼接听, 51 主叫外呼,客户未接听, 52 主叫外呼,双方接听 (主叫外呼相关)
 */
public enum CallStatusEnum {

    IN_CONNECTED("1", "座席接听"),
    OUT_CONNECTED("33", "预览外呼-双方接听");

    private final String value;
    private final String text;

    CallStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
