package com.welab.crm.operate.dto.earlySettle;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "结清统计查询对象")
public class EarlySettledReqDTO  {


    @ApiModelProperty(value = "开始时间")
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    @ApiModelProperty(value = "产品类型列表；cash-现金贷,wallet-钱包")
    private List<String> loanType;

    @ApiModelProperty(value = "组别列表")
    private List<String> groupCode;

    @ApiModelProperty(value = "客服id列表")
    private List<Long> staffIds;

    @ApiModelProperty(value = "环比开始时间")
    private String roundStartTime;

    @ApiModelProperty(value = "环比结束时间")
    private String roundEndTime;

}
