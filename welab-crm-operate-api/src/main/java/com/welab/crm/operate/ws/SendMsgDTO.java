package com.welab.crm.operate.ws;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/3/1 13:47
 */
@Data
@ApiModel(value = "发送消息请求体")
public class SendMsgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "手机号")
    @NotBlank
    private String mobile;

    @ApiModelProperty(value = "消息体")
    @NotNull
    private JSONObject messageJson;



}
