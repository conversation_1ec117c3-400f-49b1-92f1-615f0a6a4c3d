package com.welab.crm.operate.dto.callbackSummary;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * 回电小结报表查询对象
 * <AUTHOR>
 */

@Data
@ApiModel(description = "回电小结报表查询对象")
public class CallbackSummaryReportQueryDTO extends PageQueryDTO {

    private static final Long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd")
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    /**
     * 业务类型列表
     */
    @ApiModelProperty(value = "业务类型")
    private List<String> businessTypeList;
}
