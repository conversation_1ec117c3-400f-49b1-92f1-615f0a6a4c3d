package com.welab.crm.operate.dto.report;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class ReportDupCallDTO extends PageQueryDTO {

    /**
     * 热线号码，可多选
     */
    @ApiModelProperty(value = "热线号码")
    @NotNull(message = "热线号码不能为空")
    @Size(min = 1, message = "热线号码最少包含一个值")
    private List<String> hotline;

    /**
     * 时间间隔
     */
    @ApiModelProperty(value = "时间间隔: 天-day,周期-range")
    private String period;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;


    @ApiModelProperty(value = "员工Id")
    private List<Long> staffId;
}
