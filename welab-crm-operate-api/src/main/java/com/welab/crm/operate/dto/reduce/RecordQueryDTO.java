package com.welab.crm.operate.dto.reduce;

import com.welab.crm.operate.dto.BaseRequestDTO;
import lombok.Data;

/**
 * 记录查询对象
 */
@Data
public class RecordQueryDTO extends BaseRequestDTO {

	private static final long serialVersionUID = -7242449924138641242L;
	/**
	 * 申请时间开始
	 */
	private String applyTimeStart;


	/**
	 * 申请时间结束
	 */
	private String applyTimeEnd;


	/**
	 * 用户id
	 */
	private Integer userId;


	/**
	 * 贷款号
	 */
	private String applicationId;


	/**
	 * 审批状态
	 */
	private String approvalStatus;


	/**
	 * 任务状态
	 */
	private String taskStatus;


	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 申请人
	 */
	private String applyName;
	
}
