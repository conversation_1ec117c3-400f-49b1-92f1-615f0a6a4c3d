package com.welab.crm.operate.vo.loan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LoanTranAttachmentVO {

    /**
     * 债转结清对应的每一条附件数据的id
     */
    @ApiModelProperty(value = "债转结清附件表主键id")
    private Long id;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件下载链接
     */
    @ApiModelProperty(value = "附件下载链接")
    private String downloadUrl;
}
