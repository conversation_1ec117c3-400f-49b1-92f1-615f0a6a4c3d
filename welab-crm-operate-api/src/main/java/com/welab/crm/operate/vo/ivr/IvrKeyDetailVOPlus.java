package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: ivr案件明细报表返回对象
 * @date 2022/3/8 14:28
 */
@Data
public class IvrKeyDetailVOPlus extends IvrKeyDetailBaseVO{


    private String recordId;

    private String callDate;

    private String username;

    private String uuid;

    private Integer userId;

    private String callTime;

    private String keyStartTime;

    private String csAnswerTime;

    private String hangUpTime;

    private Long talkDuration;

    private String staffName;

    private String cno;

    private Integer registered11;
    private Integer registered12;
    private Integer registered13;
    private Integer registered21;
    private Integer registered22;
    private Integer registered23;
    private Integer registered24;
    private Integer registered31;
    private Integer registered32;
    private Integer registered33;
    private Integer registered34;
    private Integer registered35;
    private Integer registered36;
    private Integer registered41;
    private Integer registered42;
    private Integer registered43;
    
    private Integer overdue1;
    private Integer overdue2;
    private Integer overdue3;
    private Integer overdue4;


    private Integer transfer1;
    private Integer transfer2;
    private Integer transfer3;
    private Integer vip1;
    private Integer notRegistered1;



    private Integer ds1;
    private Integer ds2;
    private Integer ds3;


    private Integer zl1;
    private Integer zl2;
    private Integer zl3;
    private Integer zl4;
    private Integer zl5;

    private Integer wallet1;
    private Integer wallet2;
    private Integer wallet3;
    private Integer wallet4;
    private Integer wallet5;
    private Integer wallet6;
    private Integer wallet7;


    /**
     * 工作时间按键
     */
    private Integer jiafenZf1Work;

    /**
     * 非工作时间按键
     */
    private Integer jiafenZf1Free;

    private Integer jiafenZf2Work;

    private Integer jiafenZf2Free;


    private Integer jiafenJy1Work;

    private Integer jiafenJy1Free;

    private Integer jiafenJy2Work;
    private Integer jiafenJy2Free;

    private Integer jiafenJy3Work;
    private Integer jiafenJy3Free;

    private Integer jiafenJy4Work;
    private Integer jiafenJy4Free;


    private Integer jiafenHd1Work;
    private Integer jiafenHd1Free;

    private Integer jiafenHd2Work;
    private Integer jiafenHd2Free;


    private Integer jiafenGn1Work;
    private Integer jiafenGn1Free;

    private Integer jiafenGn2Work;
    private Integer jiafenGn2Free;

    private Integer jiafenGn3Work;
    private Integer jiafenGn3Free;



}
