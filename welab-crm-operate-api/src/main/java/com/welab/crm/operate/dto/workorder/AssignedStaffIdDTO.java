package com.welab.crm.operate.dto.workorder;

import java.io.Serializable;

import org.hibernate.validator.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021-11-25
 */
@Data
@ApiModel(value = "目标任务指派请求对象")
public class AssignedStaffIdDTO implements Serializable {

    private static final long serialVersionUID = -7234706972802469614L;

    @ApiModelProperty(value = "流程实例编号，为空则默认为发起新的流程")
    private String executionId;

    @ApiModelProperty(value = "流程定义编号")
    @NotBlank
    private String processCode;

    @ApiModelProperty(value = "指派的目标节点编号，非当前节点")
    private String nodeCode;
    
    @ApiModelProperty(value = "路径回转标识")
    private String tarnsBackFlag;
    
    @ApiModelProperty(value = "当前用户ID，用户或者直属上级")
    private String curStaffId;
}
