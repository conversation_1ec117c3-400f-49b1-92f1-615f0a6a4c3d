package com.welab.crm.operate.vo.repaylink;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: h5还款统计报表
 * @date 2022/4/24 15:06
 */
@Data
@ApiModel(value = "h5还款统计报表")
public class H5RepaySummaryReportVO implements Serializable {

    private static final Long serialVersionUID = 123L;

    @ApiModelProperty(value = "时间段")
    @ExcelProperty("日期")
    private String time;

    @ApiModelProperty(value = "客户数")
    @ExcelProperty("客户数")
    private Integer customerCount;

    @ApiModelProperty(value = "还款订单数")
    @ExcelProperty("还款订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "还款成功数")
    @ExcelProperty("还款成功数")
    private Integer successCount;

    @ApiModelProperty(value = "还款成功金额")
    @ExcelProperty("还款成功金额")
    private BigDecimal successAmount;

    @ApiModelProperty(value = "还款成功率")
    @ExcelProperty("还款成功率")
    private String successRate;

    @ApiModelProperty(value = "还款方式")
    @ExcelProperty("还款方式")
    private String repayMethod;

    @ApiModelProperty(value = "还款失败数")
    @ExcelProperty("还款失败数")
    private Integer failCount;

    @ApiModelProperty(value = "还款失败金额")
    @ExcelProperty("还款失败金额")
    private BigDecimal failAmount;

    @ApiModelProperty(value = "还款失败率")
    @ExcelProperty("还款失败率")
    private String failRate;

}
