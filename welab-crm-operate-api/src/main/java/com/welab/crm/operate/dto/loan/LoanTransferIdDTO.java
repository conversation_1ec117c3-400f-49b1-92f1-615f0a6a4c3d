package com.welab.crm.operate.dto.loan;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class LoanTransferIdDTO extends BaseRequestDTO {

    /**
     * 债转结清主键id
     */
    @ApiModelProperty(value = "债转结清主键id")
    @NotNull(message = "债转结清主键id不能为空")
    private Long id;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态", example = "2 (审批状态说明: 1-审批通过,2-审批拒绝)")
    private Integer approveState;

    /**
     * 审批说明
     */
    @ApiModelProperty(value = "审批说明")
    private String remark;
}
