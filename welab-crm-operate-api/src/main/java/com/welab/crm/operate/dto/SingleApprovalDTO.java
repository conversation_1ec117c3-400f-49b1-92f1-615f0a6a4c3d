package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 单个审批对象
 * 
 * <AUTHOR> 
 */
@Data
@ApiModel(description = "审批对象")
public class SingleApprovalDTO {
	
	@ApiModelProperty(value = "主键ID")
	@NotNull(message = "id不能为空")
	private Long id;
	
	@ApiModelProperty(value = "审批状态 1-通过；2-拒绝")
	@NotNull(message = "审批状态不能为空")
	private Integer approvalStatus;
}
