package com.welab.crm.operate.dto.telemarketing;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.NumberFormat;

/**
 * <AUTHOR>
 * @Description: 电销任务查询DTO
 * @date 2022/2/22 11:20
 */
@Data
@ApiModel(value = "电销任务查询DTO")
public class TmkReqDTO extends BaseRequestDTO {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "电销名单类型")
    private String tmkType;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "合同号")
    private String applicationId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "拨打状态;1:已拨打,0:未拨打")
    private String callStatus;

    @ApiModelProperty(value = "联系结果 联系结果唯一编码")
    private String contactResult;

    @ApiModelProperty(value = "电话小结唯一编码，由电销模式、联系结果、电话小结确定的唯一编码")
    private String contactCode;

    @ApiModelProperty(value = "分配时间开始")
    @NotBlank(message = "开始分配时间不能为空")
    private String distributedAtStart;

    @ApiModelProperty(value = "分配时间结束")
    @NotBlank(message = "结束分配时间不能为空")
    private String distributedAtEnd;

    @ApiModelProperty(value = "审批时间开始")
    private String approvedAtStart;

    @ApiModelProperty(value = "审批时间结束")
    private String approvedAtEnd;

    @ApiModelProperty(value = "上次拨打时间开始")
    private String lastCalledAtStart;

    @ApiModelProperty(value = "上次拨打时间结束")
    private String lastCalledAtEnd;


    @ApiModelProperty(value = "贷款状态 aip:未转化 confirmed:已转化")
    private String state;

    @ApiModelProperty(value = "用户Id")
    private String userId;

    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "额度状态 normal freezed")
    private String creditStatus;

    @ApiModelProperty(value = "可用额度")
    @Digits(integer = 7, fraction = 2, message = "请输入正确格式的数字!")
    private String avlCredit;

    @ApiModelProperty(value = "可用额度起始")
    @Digits(integer = 7, fraction = 2, message = "请输入正确格式的数字!")
    private String avlCreditStart;

    @ApiModelProperty(value = "可用额度起始")
    @Digits(integer = 7, fraction = 2, message = "请输入正确格式的数字!")
    private String avlCreditEnd;

    @ApiModelProperty(value = "vip订单金额")
    private String orderAmount;

    @ApiModelProperty(value = "vip订单号")
    private String orderNo;

    @ApiModelProperty(value = "vip订单支付状态 4:成功;5:失败;6:支付进行时;0:未支付;-2:未下单")
    private String orderStatus;

    @ApiModelProperty(value = "渠道号")
    private String source;

    @ApiModelProperty(value = "钱包是否消费.'1' 已消费；'0' 未消费 ")
    private String isConsu;

    @ApiModelProperty(value = "创建时间开始")
    private String gmtCreateStart;

    @ApiModelProperty(value = "创建时间结束")
    private String gmtCreateEnd;

    @ApiModelProperty(value = "电销唯一任务Id")
    private String tmkTaskId;

    @ApiModelProperty(value = "员工Id")
    private String staffId;

    @ApiModelProperty(value = "号码包定义")
    private String packageDefine;

    @ApiModelProperty(value = "是否进件;0:否；1:是 ")
    private String isIncome;

    @ApiModelProperty(value = "是否提现;0:否；1:是")
    private String isWithdrawal;

    /**
     * 是否需要查询age字段，后端自用
     */
    private Boolean isAge;

    /**
     * D:电销详情查询；
     * L:查询列表
     */
    private String queryType;

    private List<String> contactResultList;


    @ApiModelProperty(value = "拨打次数")
    private String callNum;

    @ApiModelProperty(value = "付费模式: pay_later是先享后付、pay_first是先付费")
    private String memberPayMode;

    /**
     * uuid标签名称
     */
    @ApiModelProperty(value = "uuid标签名称")
    private List<String> uuidLabelNames;
}
