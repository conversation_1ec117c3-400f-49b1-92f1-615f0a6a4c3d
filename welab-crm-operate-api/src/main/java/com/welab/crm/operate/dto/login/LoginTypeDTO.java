package com.welab.crm.operate.dto.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

@Data
@ApiModel(value = "LoginTypeDTO", description = "登录类型DTO")
public class LoginTypeDTO implements Serializable {
	private static final long serialVersionUID = -20576229200514493L;
	/**
	 * 登录类型
	 * face-人脸
	 * code-验证码
	 */
	@NotBlank(message = "登录类型不能为空")
	@ApiModelProperty(value = "登录类型", required = true)
	private String loginType;
	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String mobile;
	/**
	 * 登录结果
	 * success-成功
	 * fail-失败
	 */
	@NotBlank(message = "登录结果不能为空")
	@ApiModelProperty(value = "登录结果", required = true)
	private String result;

}
