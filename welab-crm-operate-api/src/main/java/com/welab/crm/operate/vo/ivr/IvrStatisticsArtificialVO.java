package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/3/9 11:11
 */
@Data
public class IvrStatisticsArtificialVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    private String callTime;
    @ExcelTitleMap(title = "贷款申请-无法提交申请")
    @ApiModelProperty(value = "贷款申请-无法提交申请")
    private String registered11;

    @ExcelTitleMap(title = "贷款申请-额度咨询")
    @ApiModelProperty(value = "贷款申请-额度咨询")
    private String registered12;
    @ExcelTitleMap(title = "贷款申请-额度冻结")
    @ApiModelProperty(value = "贷款申请-额度冻结")
    private String registered13;
    @ExcelTitleMap(title = "放款问题咨询-提现火爆")
    @ApiModelProperty(value = "放款问题咨询-提现火爆")
    private String registered21;
    @ExcelTitleMap(title = "放款问题咨询-放款时效")
    @ApiModelProperty(value = "放款问题咨询-放款时效")
    private String registered22;
    @ExcelTitleMap(title = "放款问题咨询-提现页面异常")
    @ApiModelProperty(value = "放款问题咨询-提现页面异常")
    private String registered23;
    @ExcelTitleMap(title = "放款问题咨询-订单取消")
    @ApiModelProperty(value = "放款问题咨询-订单取消")
    private String registered24;
    @ExcelTitleMap(title = "还款相关问题-解绑或换卡")
    @ApiModelProperty(value = "还款相关问题-解绑或换卡")
    private String registered31;
    @ExcelTitleMap(title = "还款相关问题-延期还款咨询")
    @ApiModelProperty(value = "还款相关问题-延期还款咨询")
    private String registered32;
    @ExcelTitleMap(title = "还款相关问题-还款失败原因")
    @ApiModelProperty(value = "还款相关问题-还款失败原因")
    private String registered33;
    @ExcelTitleMap(title = "还款相关问题-还款页面问题")
    @ApiModelProperty(value = "还款相关问题-还款页面问题")
    private String registered34;
    @ExcelTitleMap(title = "还款相关问题-开具结清证明")
    @ApiModelProperty(value = "还款相关问题-开具结清证明")
    private String registered35;
    @ExcelTitleMap(title = "还款相关问题-全额结清")
    @ApiModelProperty(value = "还款相关问题-全额结清")
    private String registered36;
    @ExcelTitleMap(title = "个人信息修改-修改手机号")
    @ApiModelProperty(value = "个人信息修改-修改手机号")
    private String registered41;
    @ExcelTitleMap(title = "个人信息修改-注销账号")
    @ApiModelProperty(value = "个人信息修改-注销账号")
    private String registered42;
    @ExcelTitleMap(title = "个人信息修改-其他信息修改")
    @ApiModelProperty(value = "个人信息修改-其他信息修改")
    private String registered43;

    @ExcelTitleMap(title = "协商还款")
    @ApiModelProperty(value = "协商还款")
    private String overdue1;
    @ExcelTitleMap(title = "银行卡冻结或卡受限")
    @ApiModelProperty(value = "银行卡冻结或卡受限")
    private String overdue2;
    @ExcelTitleMap(title = "还款方式核实")
    @ApiModelProperty(value = "还款方式核实")
    private String overdue3;
    @ExcelTitleMap(title = "投诉催收")
    @ApiModelProperty(value = "投诉催收")
    private String overdue4;

    @ExcelTitleMap(title = "债转公司咨询")
    @ApiModelProperty(value = "债转公司咨询")
    private String transfer1;
    @ExcelTitleMap(title = "订单状态更新")
    @ApiModelProperty(value = "订单状态更新")
    private String transfer2;
    @ExcelTitleMap(title = "还款方式核实")
    @ApiModelProperty(value = "还款方式核实")
    private String transfer3;
    @ExcelTitleMap(title = "会员&特权卡用户")
    @ApiModelProperty(value = "会员&特权卡用户")
    private String vip1;
    @ExcelTitleMap(title = "非注册用户")
    @ApiModelProperty(value = "非注册用户")
    private String notRegistered1;



    @ExcelTitleMap(title = "产品介绍")
    private String ds1;
    @ExcelTitleMap(title = "物流及订单状态")
    private String ds2;
    @ExcelTitleMap(title = "商家入驻")
    private String ds3;


    @ExcelTitleMap(title = "产品介绍")
    private String zl1;
    @ExcelTitleMap(title = "租赁计费问题")
    private String zl2;
    @ExcelTitleMap(title = "绑卡及发货问题")
    private String zl3;
    @ExcelTitleMap(title = "还款问题")
    private String zl4;
    @ExcelTitleMap(title = "订单状态查询")
    private String zl5;

    @ExcelTitleMap(title = "产品介绍")
    private String wallet1;
    @ExcelTitleMap(title = "额度申请")
    private String wallet2;
    @ExcelTitleMap(title = "审核周期")
    private String wallet3;
    @ExcelTitleMap(title = "分期咨询")
    private String wallet4;
    @ExcelTitleMap(title = "绑卡咨询")
    private String wallet5;
    @ExcelTitleMap(title = "还款咨询")
    private String wallet6;
    @ExcelTitleMap(title = "账单查询")
    private String wallet7;

    @ExcelTitleMap(title = "账户问题 - 开户问题")
    @ApiModelProperty(value = "账户问题 - 开户问题")
    private String jiafenZf1;

    @ExcelTitleMap(title = "账户问题 - 更新风险测评")
    @ApiModelProperty(value = "账户问题 - 更新风险测评")
    private String jiafenZf2;


    @ExcelTitleMap(title = "交易及资金问题 - 银行卡支付额度问题")
    @ApiModelProperty(value = "交易及资金问题 - 银行卡支付额度问题")
    private String jiafenJy1;

    @ExcelTitleMap(title = "交易及资金问题 - 跨组合支付失败")
    @ApiModelProperty(value = "交易及资金问题 - 跨组合支付失败")
    private String jiafenJy2;

    @ExcelTitleMap(title = "交易及资金问题 - 基金确认时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金确认时间")
    private String jiafenJy3;

    @ExcelTitleMap(title = "交易及资金问题 - 基金赎回到账时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金赎回到账时间")
    private String jiafenJy4;


    @ExcelTitleMap(title = "活动问题 - 愿望功能")
    @ApiModelProperty(value = "活动问题 - 愿望功能")
    private String jiafenHd1;

    @ExcelTitleMap(title = "活动问题 - 新手福利")
    @ApiModelProperty(value = "活动问题 - 新手福利")
    private String jiafenHd2;


    @ExcelTitleMap(title = "功能介绍 - 愿望功能")
    @ApiModelProperty(value = "功能介绍 - 愿望功能")
    private String jiafenGn1;

    @ExcelTitleMap(title = "功能介绍 - 现金钱包")
    @ApiModelProperty(value = "功能介绍 - 现金钱包")
    private String jiafenGn2;

    @ExcelTitleMap(title = "功能介绍 - 目标盈")
    @ApiModelProperty(value = "功能介绍 - 目标盈")
    private String jiafenGn3;

    /**
     * 合计
     */
    private String total;





}
