package com.welab.crm.operate.vo.phone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/22 16:46
 */
@Data
public class PhoneSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "用户Id", name = "userId")
    private Long customerId;

    @ApiModelProperty(value = "任务号", name = "taskId")
    private String taskId;

    @ApiModelProperty(value = "电话小结内容", name = "callSummary")
    private String callSummary;

    @ApiModelProperty(value = "电话小结类型码", name = "callSummaryCode")
    private String callSummaryCode;

    @ApiModelProperty(value = "保存电话小结时间", name = "saveSummaryTime")
    private Date saveSummaryTime;

    @ApiModelProperty(value = "备注", name = "callComment")
    private String callComment;

    @ApiModelProperty(value = "名单类型,外呼时才有这个字段", name = "mapName")
    private String mapName;

    @ApiModelProperty(value = "呼叫类型，呼入or呼出", name = "callType")
    private String callType;

    @ApiModelProperty(value = "来/致电时间", name = "callTime")
    private Date callTime;

    @ApiModelProperty(value = "手机号", name = "phoneNumber")
    private String phoneNumber;

    @ApiModelProperty(value = "逾期标签", name = "overDueLabel")
    private String overDueLabel;

    @ApiModelProperty(value = "员工名称", name = "staffName")
    private String staffName;

    @ApiModelProperty(value = "组别", name = "groupName")
    private String groupName;

    @ApiModelProperty(value = "通话Id", name = "cdrMainUniqueId")
    private String cdrMainUniqueId;

    @ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;
    
    @ApiModelProperty(value = "坐席工号", name = "cdrCno")
    private String cdrCno;
    
    @ApiModelProperty(value = "ai小结")
    private String aiSummary;
    
    @ApiModelProperty(value = "处理方案")
    private String resolveContent;
}
