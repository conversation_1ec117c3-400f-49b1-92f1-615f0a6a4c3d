package com.welab.crm.operate.dto.blackProduction;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 黑产请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "黑产请求对象")
public class BlackProductionQueryReqDTO extends BlackProductionAddReqDTO {

	/**
	 * 审批状态 0-待审批；1-通过；2-拒绝
	 */
	@ApiModelProperty(value = "审批状态 0-待审批；1-通过；2-拒绝")
	private Integer approvalStatus;

	/**
	 * 申请开始时间
	 */
	@ApiModelProperty(value = "申请开始时间")
	private String applyStart;

	/**
	 * 申请结束时间
	 */
	@ApiModelProperty(value = "申请结束时间")
	private String applyEnd;

	/**
	 * 身份证号
	 */
	@ApiModelProperty(value = "身份证号")
	private String idNo;


	/**
	 * 数据来源
	 * 1-AIF联盟
	 * 2-客服系统
	 */
	@ApiModelProperty(value = "数据来源")
	private Integer source;
}
