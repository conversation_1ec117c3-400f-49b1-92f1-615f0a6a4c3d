package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2022/3/9 11:11
 */
@Data
public class IvrStatisticsArtificialVOPlus implements Serializable {

    private static final Long serialVersionUID = 1L;

    private String callTime;

    @ExcelTitleMap(title = "贷款申请-无法提交申请")
    private String registered11;

    @ExcelTitleMap(title = "贷款申请-额度咨询")
    private String registered12;
    @ExcelTitleMap(title = "贷款申请-额度冻结")
    private String registered13;
    @ExcelTitleMap(title = "放款问题咨询-提现火爆")
    private String registered21;
    @ExcelTitleMap(title = "放款问题咨询-放款时效")
    private String registered22;
    @ExcelTitleMap(title = "放款问题咨询-提现页面异常")
    private String registered23;
    @ExcelTitleMap(title = "放款问题咨询-订单取消")
    private String registered24;
    @ExcelTitleMap(title = "还款相关问题-解绑或换卡")
    private String registered31;
    @ExcelTitleMap(title = "还款相关问题-延期还款咨询")
    private String registered32;
    @ExcelTitleMap(title = "还款相关问题-还款失败原因")
    private String registered33;
    @ExcelTitleMap(title = "还款相关问题-还款页面问题")
    private String registered34;
    @ExcelTitleMap(title = "还款相关问题-开具结清证明")
    private String registered35;
    @ExcelTitleMap(title = "还款相关问题-全额结清")
    private String registered36;
    @ExcelTitleMap(title = "个人信息修改-修改手机号")
    private String registered41;
    @ExcelTitleMap(title = "个人信息修改-注销账号")
    private String registered42;
    @ExcelTitleMap(title = "个人信息修改-其他信息修改")
    private String registered43;

    @ExcelTitleMap(title = "协商还款")
    private String overdue1;
    @ExcelTitleMap(title = "银行卡冻结或卡受限")
    private String overdue2;
    @ExcelTitleMap(title = "还款方式核实")
    private String overdue3;
    @ExcelTitleMap(title = "投诉催收")
    private String overdue4;

    @ExcelTitleMap(title = "债转公司咨询")
    private String transfer1;
    @ExcelTitleMap(title = "订单状态更新")
    private String transfer2;
    @ExcelTitleMap(title = "还款方式核实")
    private String transfer3;
    @ExcelTitleMap(title = "会员&特权卡用户")
    private String vip1;
    @ExcelTitleMap(title = "非注册用户")
    private String notRegistered1;



    @ExcelTitleMap(title = "产品介绍")
    private String ds1;
    @ExcelTitleMap(title = "物流及订单状态")
    private String ds2;
    @ExcelTitleMap(title = "商家入驻")
    private String ds3;


    @ExcelTitleMap(title = "产品介绍")
    private String zl1;
    @ExcelTitleMap(title = "租赁计费问题")
    private String zl2;
    @ExcelTitleMap(title = "绑卡及发货问题")
    private String zl3;
    @ExcelTitleMap(title = "还款问题")
    private String zl4;
    @ExcelTitleMap(title = "订单状态查询")
    private String zl5;

    @ExcelTitleMap(title = "产品介绍")
    private String wallet1;
    @ExcelTitleMap(title = "额度申请")
    private String wallet2;
    @ExcelTitleMap(title = "审核周期")
    private String wallet3;
    @ExcelTitleMap(title = "分期咨询")
    private String wallet4;
    @ExcelTitleMap(title = "绑卡咨询")
    private String wallet5;
    @ExcelTitleMap(title = "还款咨询")
    private String wallet6;
    @ExcelTitleMap(title = "账单查询")
    private String wallet7;

    /**
     * 工作时间按键
     */
    private Integer jiafenZf1Work;

    /**
     * 非工作时间按键
     */
    private Integer jiafenZf1Free;

    private Integer jiafenZf2Work;

    private Integer jiafenZf2Free;


    private Integer jiafenJy1Work;

    private Integer jiafenJy1Free;

    private Integer jiafenJy2Work;
    private Integer jiafenJy2Free;

    private Integer jiafenJy3Work;
    private Integer jiafenJy3Free;

    private Integer jiafenJy4Work;
    private Integer jiafenJy4Free;


    private Integer jiafenHd1Work;
    private Integer jiafenHd1Free;

    private Integer jiafenHd2Work;
    private Integer jiafenHd2Free;


    private Integer jiafenGn1Work;
    private Integer jiafenGn1Free;

    private Integer jiafenGn2Work;
    private Integer jiafenGn2Free;

    private Integer jiafenGn3Work;
    private Integer jiafenGn3Free;

    /**
     * 合计
     */
    private String total;





}
