package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * 喵达投诉单回复请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单回复请求对象")
public class MiaodaReplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单号，多个单号用英文逗号连接
     */
    @ApiModelProperty(value = "投诉单号", name = "sns", required = true)
    @NotBlank(message = "投诉单号不能为空")
    private String sns;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容", name = "content", required = true)
    @NotBlank(message = "回复内容不能为空")
    private String content;

    /**
     * 是否隐藏附件 0-不隐藏 1-隐藏
     */
    @ApiModelProperty(value = "是否隐藏附件", name = "hideAttach")
    private Integer hideAttach = 0;

    /**
     * 是否隐藏内容 0-不隐藏 1-隐藏
     */
    @ApiModelProperty(value = "是否隐藏内容", name = "hideContent")
    private Integer hideContent = 0;

    /**
     * 上传图片的网络地址列表
     */
    @ApiModelProperty(value = "图片地址列表", name = "images")
    private List<String> images;

    /**
     * 上传视频的网络地址列表
     */
    @ApiModelProperty(value = "视频地址列表", name = "videos")
    private List<String> videos;
}
