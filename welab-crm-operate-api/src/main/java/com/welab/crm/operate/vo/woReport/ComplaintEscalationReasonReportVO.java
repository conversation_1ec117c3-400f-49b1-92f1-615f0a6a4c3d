package com.welab.crm.operate.vo.woReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ComplaintEscalationReasonReportVO {
	
	
	@ApiModelProperty(value = "普通工单三类")
	@ExcelProperty(value = "普通工单三类")
	private String ordinaryOrderThreeType;
	
	@ApiModelProperty(value = "0天")
	@ExcelProperty(value = "0天")
	private Integer zeroDayCount;
	
	@ApiModelProperty(value = "3天")
	@ExcelProperty(value = "3天")
	private Integer threeDayCount;
	
	@ApiModelProperty(value = "7天")
	@ExcelProperty(value = "7天")
	private Integer sevenDayCount;
	
	@ApiModelProperty(value = "10天")
	@ExcelProperty(value = "10天")
	private Integer tenDayCount;
	
	@ApiModelProperty(value = "20天")
	@ExcelProperty(value = "20天")
	private Integer twentyDayCount;
	
	@ApiModelProperty(value = "30天")
	@ExcelProperty(value = "30天")
	private Integer thirtyDayCount;

	public ComplaintEscalationReasonReportVO() {
	}

	public ComplaintEscalationReasonReportVO(String ordinaryOrderThreeType, Integer zeroDayCount, Integer threeDayCount, Integer sevenDayCount, Integer tenDayCount, Integer twentyDayCount, Integer thirtyDayCount) {
		this.ordinaryOrderThreeType = ordinaryOrderThreeType;
		this.zeroDayCount = zeroDayCount;
		this.threeDayCount = threeDayCount;
		this.sevenDayCount = sevenDayCount;
		this.tenDayCount = tenDayCount;
		this.twentyDayCount = twentyDayCount;
		this.thirtyDayCount = thirtyDayCount;
	}
	
	public void addZeroDayCount(){
		this.zeroDayCount += 1;
	}
	
	public void addThreeDayCount(){
		this.threeDayCount += 1;
	}
	
	public void addSevenDayCount(){
		this.sevenDayCount += 1;
	}
	
	public void addTenDayCount(){
		this.tenDayCount += 1;
	}
	
	public void addTwentyDayCount(){
		this.twentyDayCount += 1;
	}
	
	public void addThirtyDayCount(){
		this.thirtyDayCount += 1;
	}
	
	public ComplaintEscalationReasonReportVO add(ComplaintEscalationReasonReportVO vo){
		this.zeroDayCount += vo.getZeroDayCount();
		this.threeDayCount += vo.getThreeDayCount();
		this.sevenDayCount += vo.getSevenDayCount();
		this.tenDayCount += vo.getTenDayCount();
		this.twentyDayCount += vo.getTwentyDayCount();
		this.thirtyDayCount += vo.getThirtyDayCount();
		return this;
	}
	
}
