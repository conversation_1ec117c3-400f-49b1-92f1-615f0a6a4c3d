package com.welab.crm.operate.dto.report;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/24
 */
@Data
@ApiModel(value = "工单报表请求对象")
public class WoReportDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 3548706230524591363L;

    @ApiModelProperty(value = "开始时间，格式 yyyy-MM-dd HH:mm:ss")
    @NotBlank
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    @NotBlank
    private String endTime;

    private List<String> orderTypeList;

    private List<String> groupCodeList;

    private List<String> orderStateList;

    @ApiModelProperty(value = "工单类型(可多选)")
    private String orderTypes;

    @ApiModelProperty(value = "组别(可多选)")
    private String groupCodes;

    @ApiModelProperty(value = "工单状态(可多选)")
    private String orderStates;

    /**
     * 工单id列表
     */
    private List<Long> orderIdList;
}
