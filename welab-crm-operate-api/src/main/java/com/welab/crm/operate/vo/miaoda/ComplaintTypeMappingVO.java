package com.welab.crm.operate.vo.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 投诉类型映射响应VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "投诉类型映射响应对象")
public class ComplaintTypeMappingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 喵达投诉问题类型
     */
    @ApiModelProperty(value = "喵达投诉问题类型", name = "miaodaIssue")
    private String miaodaIssue;

    /**
     * 工单组合配置ID
     */
    @ApiModelProperty(value = "工单组合配置ID", name = "opDictInfoConfId")
    private Long opDictInfoConfId;

    /**
     * 工单大类名称
     */
    @ApiModelProperty(value = "工单大类名称", name = "woTypeName")
    private String woTypeName;

    /**
     * 工单一类名称
     */
    @ApiModelProperty(value = "工单一类名称", name = "woTypeFirName")
    private String woTypeFirName;

    /**
     * 工单二类名称
     */
    @ApiModelProperty(value = "工单二类名称", name = "woTypeSecName")
    private String woTypeSecName;

    /**
     * 工单三类名称
     */
    @ApiModelProperty(value = "工单三类名称", name = "woTypeThirName")
    private String woTypeThirName;

    /**
     * 工单子类名称
     */
    @ApiModelProperty(value = "工单子类名称", name = "woTypeChildName")
    private String woTypeChildName;

    /**
     * 是否启用 0-禁用 1-启用
     */
    @ApiModelProperty(value = "是否启用", name = "isActive")
    private Integer isActive;

    /**
     * 启用状态描述
     */
    @ApiModelProperty(value = "启用状态描述", name = "isActiveDesc")
    private String isActiveDesc;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明", name = "remark")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "updateBy")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;
}
