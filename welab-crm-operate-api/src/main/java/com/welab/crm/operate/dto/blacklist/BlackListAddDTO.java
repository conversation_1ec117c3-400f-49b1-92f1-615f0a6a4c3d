package com.welab.crm.operate.dto.blacklist;

import lombok.Data;

import java.util.List;

/**
 * 添加黑名单请求对象
 *       催收黑名单：uuid必填
 *       联系人黑名单：手机号和uuid必填
 */
@Data
public class BlackListAddDTO extends BlackListQueryReqDTO{

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系人手机号，多个用逗号隔开
     */
    private String mobiles;

    /**
     * 拉黑天数
     */
    private Integer blackDay;

    /**
     * 审批人
     */
    private String operateUser;

    /**
     * 审批状态 0初审 1复审 2审批完成 3拒绝
     */
    private Integer operateState;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 待审批ids
     */
    private List<Integer> ids;

    /**
     * 是否告知
     */
    private Boolean isInform;
}
