package com.welab.crm.operate.dto.tmkRule;

import java.util.Date;
import java.util.List;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据请求参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据请求参数")
public class TmkInfoReqDTO extends BaseRequestDTO {

	/**
	 *
	 */
	private static final long serialVersionUID = -102517210654657594L;

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型", name = "typeList")
	private List<String> typeList;


	@ApiModelProperty(value = "单个业务类型", name = "tmkType")
	private String tmkType;

	@ApiModelProperty(value = "uuid", name = "uuid")
	private String uuid;

	@ApiModelProperty(value = "用户Id", name = "userId")
	private String userId;

	@ApiModelProperty(value = "产品名称列表", name = "productNameList")
	private List<String> productNameList;

	@ApiModelProperty(value = "产品名称列表", name = "originList")
	private List<String> originList;

	@ApiModelProperty(value = "审批时间开始", name = "approvedAtStart")
	private String approvedAtStart;

	@ApiModelProperty(value = "审批时间结束", name = "approvedAtEnd")
	private String approvedAtEnd;

	@ApiModelProperty(value = "分流标签", name = "diversionTag")
	private String diversionTag;

	@ApiModelProperty(value = "是否查询分流标签", name = "1是，0否")
	private Integer isQueryTag;

	@ApiModelProperty(value = "开始创建时间", name = "2022-09-04")
	private String beginCreateTime;

	@ApiModelProperty(value = "截止创建时间", name = "2022-09-05")
	private String endCreateTime;

	/**
	 * 号码包定义
	 */
	@ApiModelProperty(value = "号码包定义")
	private String numPackageDef;

	/**
	 * 外呼说明
	 */
	@ApiModelProperty(value = "外呼说明")
	private String callInstruction;

}

