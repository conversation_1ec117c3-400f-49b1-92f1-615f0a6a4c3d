package com.welab.crm.operate.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * @description dto
 * <AUTHOR>
 * @date 2021-10-19 16:32:01
 * @version v1.0
 */
@Getter
@Setter
public class BaseReqDTO implements Serializable {
	private static final long serialVersionUID = -1393659871508121655L;




	/**插入记录校验分组*/
	public interface GroupInsert {}

	/**更新记录校验分组*/
	public interface GroupUpdate {}

	/**删除记录校验分组*/
	public interface GroupDelete {}

	/**列表查询校验分组*/
	public interface GroupQuery {}

	/**查看记录详情校验分组*/
	public interface GroupDetail {}
	
	   /**
     * 逻辑id
     */
    @ApiModelProperty(value="逻辑id", name="id")
    //@NotNull(message="{system.logical.id.error.null}")
    private Long id;

	/**
     * 当前页
     */
    @ApiModelProperty(value="当前页码", name="curPage",example="1")
    private Integer curPage;
    
    /**
     * 每页显示条数
     */
    @ApiModelProperty(value="每页显示条数", name="pageSize",example="20")
    private Integer pageSize;
}
