package com.welab.crm.operate.dto.tmkManager;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据分单历史请求参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据分单历史请求参数")
public class TmkAssignReqDTO extends BaseRequestDTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

    
    @ApiModelProperty(value = "用户姓名", name = "username")
    private String username;
    
    @ApiModelProperty(value = "电话号码", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "合同号", name = "applicationId")
    private String applicationId;
	
	@ApiModelProperty(value="分配开始时间", name="distributeStartDate")
	private String distributeStartDate;
    
    @ApiModelProperty(value="分配结束时间", name="distributeEndDate")
	private String distributeEndDate;

    @ApiModelProperty(value="用户Id", name="userId")
    private String userId;

    @ApiModelProperty(value="uuid", name="uuid")
    private String uuid;
}

