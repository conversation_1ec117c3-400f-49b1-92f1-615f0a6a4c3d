package com.welab.crm.operate.dto.tmkManager;

import java.util.List;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据管理请求参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据管理请求参数")
public class TmkManagerReqDTO extends BaseRequestDTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	@ApiModelProperty(value = "话务组", name = "groupCode")
    private List<String> groupCode;
    
    @ApiModelProperty(value = "话务人", name = "staffId")
    private List<String> staffId;
    
    @ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;
    
    @ApiModelProperty(value = "电话号码", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "合同号", name = "applicationId")
    private String applicationId;
	
	@ApiModelProperty(value = "业务类型", name = "tmkType")
    private String tmkType;
	
	@ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;
	
	@ApiModelProperty(value="分配开始时间", name="distributeStartDate")
	private String distributeStartDate;
    
    @ApiModelProperty(value="分配结束时间", name="distributeEndDate")
	private String distributeEndDate;
	
    @ApiModelProperty(value = "审批开始时间", name = "approvalStartDate")
    private String approvalStartDate;
    
    @ApiModelProperty(value = "审批结束时间", name = "approvalEndDate")
    private String approvalEndDate;
    
    @ApiModelProperty(value = "进件开始时间", name = "appliedStartDate")
    private String appliedStartDate;
    
    @ApiModelProperty(value = "进件结束时间", name = "appliedEndDate")
    private String appliedEndDate;
    
    @ApiModelProperty(value = "是否转化", name = "isChange")
    private String isChange;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private String userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;
}

