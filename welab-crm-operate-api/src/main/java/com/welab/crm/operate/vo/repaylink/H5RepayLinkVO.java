package com.welab.crm.operate.vo.repaylink;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * h5还款链接返回对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "h5还款链接返回对象")
public class H5RepayLinkVO implements Serializable {

    private static final long serialVersionUID = -7920642677484963997L;
    /**
     * 逻辑ID
     */
    @ApiModelProperty(value = "逻辑ID")
    @ExcelIgnore
    private Long id;
    
    
    
    @ApiModelProperty(value = "咨询状态")
    @ExcelProperty("咨询状态")
    private String consultationStatus;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @ExcelProperty("操作时间")
    private Date sendTime;

    /**
     * 发送员工
     */
    @ApiModelProperty(value = "发送员工")
    @ExcelProperty("发送员工")
    private String staffName;

    /**
     * 发送组
     */
    @ApiModelProperty(value = "发送组")
    @ExcelProperty("发送组")
    private String groupName;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty("客户姓名")
    private String customerName;


    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    @ExcelProperty("uuid")
    private String uuid;
    
    @ApiModelProperty(value = "userId")
    @ExcelProperty("userId")
    private Integer userId;

    /**
     * 贷款号
     */
    @ApiModelProperty(value = "贷款号")
    @ExcelProperty("贷款号")
    private String applicationId;

    /**
     * 资金方名称
     */
    @ApiModelProperty(value = "资金方")
    @ExcelProperty("资金方")
    private String partnerName;

    /**
     * 资金方code
     */
    @ApiModelProperty(value = "资金方")
    @ExcelIgnore
    private String partnerCode;

    /**
     * 发送号码
     */
    @ApiModelProperty(value = "发送号码")
    @ExcelProperty("发送号码")
    private String mobile;


    /**
     * 还款方式
     */
    @ApiModelProperty(value = "还款方式")
    @ExcelProperty("还款方式")
    private String repayMode;


    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额")
    @ExcelProperty("还款金额")
    private String repayAmount;


    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    @ExcelProperty("支付渠道")
    private String paymentChannel;


    /**
     * 还款完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @ExcelProperty("完成时间")
    private Date repayTime;


    /**
     * 还款结果
     */
    @ApiModelProperty(value = "还款结果")
    @ExcelProperty("还款结果")
    private String repayResult;

    /**
     * 还款方式
     */
    @ApiModelProperty(value = "还款方式")
    @ExcelProperty("还款方式")
    private String repayMethod;
}
