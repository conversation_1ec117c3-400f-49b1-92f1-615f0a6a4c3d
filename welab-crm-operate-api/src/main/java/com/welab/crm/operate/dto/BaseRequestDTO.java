package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/22 15:55
 */

@Data
public class BaseRequestDTO implements Serializable {

    private static final long serialVersionUID = -1393659871508121655L;

    /**
     * 当前页
     */
    @ApiModelProperty(value="当前页码", name="curPage",example="1")
    private Integer curPage;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value="每页显示条数", name="pageSize",example="20")
    private Integer pageSize;

}
