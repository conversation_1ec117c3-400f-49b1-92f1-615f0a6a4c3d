package com.welab.crm.operate.vo.blacklist;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class KfBlackListVO implements Serializable {

    private static final long serialVersionUID = 4003307634229655465L;

    @ExcelProperty(value="合同号")
    private String contractNo;

    /**
     * 身份证号
     */
    @ExcelProperty(value="身份证号")
    private String idNo;

    /**
     * 手机号
     */
    @ExcelProperty(value="手机号")
    private String mobile;
    
    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value="用户ID")
    private String userId;

    /**
     * 黑名单类型
     */
    @ExcelProperty(value="黑名单类型(coll_black-催收黑名单,ivr_black-ivr黑名单,contact_black联系人电话黑名单,sms_black短信黑名单)")
    private String blackType;

    /**
     * 黑名单类型编码
     */
    @ExcelIgnore
    private String blackTypeCode;

    /**
     * 添加原因
     */
    @ExcelProperty(value="添加原因(1-投诉,2-死亡,3-拘留)")
    private String addReason;

    /**
     * 有效开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ExcelIgnore
    private Date validStartTime;

    /**
     * 有效结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ExcelIgnore
    private Date validEndTime;

    /**
     * 备注
     */
    @ExcelProperty(value="备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date createTime;

    /**
     * 修改时间
     */
    @ExcelIgnore
    private Date updateTime;

    /**
     * 是否有效，1-是 2-否
     */
    @ExcelIgnore
    private String isValid;

    /**
     * 主键id
     */
    @ExcelIgnore
    private String id;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建组
     */
    @ExcelProperty(value = "创建组")
    private String createGroup;

    /**
     * 修改人
     */
    @ExcelProperty(value = "更新人")
    private String lstUpdUser;

    /**
     * 更新组
     */
    @ExcelProperty(value = "更新组")
    private String updateGroup;

    /**
     * 数据来源，1-人工添加 2-批量导入
     */
    @ExcelIgnore
    private String dataSource;

    /**
     * 有效开始时间
     */
    @ExcelProperty(value="有效开始时间(yyyy/MM/dd)*")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private String startTime ;

    /**
     * 有效结束时间
     */
    @ExcelProperty(value="有效结束时间(yyyy/MM/dd)*")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private String endTime;

    /**
     * 部门
     */
    @ExcelProperty(value="部门")
    private String department;

    /**
     * 用户ID
     */
    @ExcelProperty(value="uuid")
    private String uuid;

    /**
     * 拉黑天数
     */
    @ExcelIgnore
    private Integer blackDay;

    /**
     * 审批人
     */
    @ExcelIgnore
    private String operateUser;

    /**
     * 是否告知
     */
    @ExcelIgnore
    private Boolean isInform;
}
