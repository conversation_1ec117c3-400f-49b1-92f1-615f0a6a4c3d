package com.welab.crm.operate.vo.woReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 投诉升级报表返回对象
 */
@Data
@NoArgsConstructor
public class ComplaintEscalationStatisticsReportVO {

	@ApiModelProperty("时间")
	@ExcelProperty(value = "时间")
	private String time;

	@ApiModelProperty("资方投诉总量")
	@ExcelProperty(value = "资方投诉总量")
	private Integer fundComplaintCount;

	@ApiModelProperty("监管投诉总量")
	@ExcelProperty(value = "监管投诉总量")
	private Integer monitorComplaintCount;

	@ApiModelProperty("合计投诉量")
	@ExcelProperty(value = "合计投诉量")
	private Integer totalComplaintCount;

	@ApiModelProperty("投诉客户量")
	@ExcelProperty(value = "投诉客户量")
	private Integer complaintUserCount;

	@ApiModelProperty("升级客户量")
	@ExcelProperty(value = "升级客户量")
	private Integer escalationUserCount;

	@ApiModelProperty("无联系记录数")
	@ExcelProperty(value = "无联系记录数")
	private Integer noContactUserCount;

	@ApiModelProperty("升级率")
	@ExcelProperty(value = "升级率")
	private String escalationRate;

	public ComplaintEscalationStatisticsReportVO(Integer fundComplaintCount, Integer monitorComplaintCount, Integer totalComplaintCount, Integer complaintUserCount, Integer escalationUserCount, Integer noContactUserCount) {
		this.fundComplaintCount = fundComplaintCount;
		this.monitorComplaintCount = monitorComplaintCount;
		this.totalComplaintCount = totalComplaintCount;
		this.complaintUserCount = complaintUserCount;
		this.escalationUserCount = escalationUserCount;
		this.noContactUserCount = noContactUserCount;
	}
	
	public ComplaintEscalationStatisticsReportVO add(ComplaintEscalationStatisticsReportVO vo){
		this.fundComplaintCount += vo.getFundComplaintCount();
		this.monitorComplaintCount += vo.getMonitorComplaintCount();
		this.totalComplaintCount += vo.getTotalComplaintCount();
		this.complaintUserCount += vo.getComplaintUserCount();
		this.escalationUserCount += vo.getEscalationUserCount();
		this.noContactUserCount += vo.getNoContactUserCount();
		return this;
	}
}
