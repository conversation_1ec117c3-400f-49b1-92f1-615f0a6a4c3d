package com.welab.crm.operate.dto.transferReport;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class TransferReportRangeDTO extends BaseReqDTO {

	@ApiModelProperty(value = "开始时间")
	@NotBlank
	private String startTime;

	@ApiModelProperty(value = "结束时间")
	@NotBlank
	private String endTime;

	@ApiModelProperty(value = "环比开始时间")
	private String roundStartTime;

	@ApiModelProperty(value = "环比结束时间")
	private String roundEndTime;
}
