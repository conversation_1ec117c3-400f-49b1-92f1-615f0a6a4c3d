package com.welab.crm.operate.vo.personalPanel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人看板总体数据
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "个人看板总体数据")
public class OverAllData {

    /**
     * 排队用户数
     */
    @ApiModelProperty(value = "排队用户数")
    private Integer queuedUsersNumber;

    /**
     * 空闲坐席数
     */
    @ApiModelProperty(value = "空闲坐席数")
    private Integer freeAgentNumber;

    /**
     * 接通率
     */
    @ApiModelProperty(value = "接通率")
    private String answerRate;

    public OverAllData() {
        queuedUsersNumber = 0;
        freeAgentNumber = 0;
        answerRate = "0.00%";
    }
}
