package com.welab.crm.operate.dto.loan;

import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/10
 */
@Data
@ApiModel(value = "在途贷款提前结清请求对象")
public class OutstandingEarlySettleDTO extends HistoryOperationDTO {

    private static final long serialVersionUID = 5967676903902093839L;



    /**
     * 费率
     */
    private String loanRate;


    /**
     * 渠道来源
     */
    private String source;


    /**
     * 资金方
     */
    private String partnerCode;


    /**
     * 当前期数
     */
    private String tenor;


    /**
     * 到期日
     */
    private String dueDate;


    /**
     * 结清原因
     */
    private String reason;


}
