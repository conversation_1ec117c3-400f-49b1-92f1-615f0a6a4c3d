package com.welab.crm.operate.vo.reduce;

import lombok.Data;


/**
 * 减免、退款记录查询返回对象
 */
@Data
public class ReduceAndRefundVO {

	/**
	 * 投诉渠道
	 */
	private String complaintChannel;

	/**
	 * 姓名
	 */
	private String name;


	/**
	 * 用户ID
	 */
	private String userId;


	/**
	 * 申请时间
	 */
	private String applyTime;


	/**
	 * 申请人
	 */
	private String applyName;


	/**
	 * 处理时间
	 */
	private String processTime;


	/**
	 * 审批状态
	 */
	private String approvalStatus;


	/**
	 * 任务状态
	 */
	private String taskStatus;


	/**
	 * 流水号，用于查询详情
	 */
	private String requestNo;


	/**
	 * 审批状态code
	 */
	private String approvalStatusCode;
}
