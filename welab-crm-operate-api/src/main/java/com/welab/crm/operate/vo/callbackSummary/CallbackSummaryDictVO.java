package com.welab.crm.operate.vo.callbackSummary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 回电小结维护界面返回结果
 * <AUTHOR>
 */
@Data
@ApiModel(description = "回电小结维护界面返回结果")
public class CallbackSummaryDictVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型列表
     */
    @ApiModelProperty(value = "业务类型列表")
    private List<CallbackTypeVO> businessTypeList;

    /**
     * 回电结果列表
     */
    @ApiModelProperty(value = "回电结果列表")
    private List<CallbackTypeVO> callbackResultList;

    /**
     * 未接通类型列表
     */
    @ApiModelProperty(value = "未接通类型")
    private List<CallbackTypeVO> notConnectedTypeList;

    /**
     * 协商中类型列表
     */
    @ApiModelProperty(value = "协商中类型")
    private List<CallbackTypeVO> underNegotiationTypeList;

    /**
     * 协商一致类型列表
     */
    @ApiModelProperty(value = "协商一致类型")
    private List<CallbackTypeVO> consensusTypeList;

    /**
     * 协商失败类型列表
     */
    @ApiModelProperty(value = "协商失败类型")
    private List<CallbackTypeVO> negotiationFailedTypeList;



}
