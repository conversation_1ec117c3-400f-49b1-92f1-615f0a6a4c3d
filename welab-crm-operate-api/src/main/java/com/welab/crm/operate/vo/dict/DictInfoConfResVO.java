package com.welab.crm.operate.vo.dict;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *工单组合响应数据 
 */
@Getter
@Setter
public class DictInfoConfResVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value="id", name="id")
    private Long id;

    /**
     * 字典id，即dict_info表主键
     */
    @ApiModelProperty(value="字典表id", name="字典表id")
    private Long woTypeId;

    /**
     * 工单大类描述
     */
    @ApiModelProperty(value="工单大类", name="工单大类")
    private String woTypeDetail;

    /**
     * 字典id，即dict_info表主键
     */
    @ApiModelProperty(value="字典表id", name="字典表id")
    private Long woTypeFirId;

    /**
     * 工单一类描述
     */
    @ApiModelProperty(value="工单一类", name="工单一类")
    private String woTypeFirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    @ApiModelProperty(value="字典表id", name="字典表id")
    private Long woTypeSecId;

    /**
     * 工单二类描述
     */
    @ApiModelProperty(value="工单二类", name="工单二类")
    private String woTypeSecDetail;

    /**
     * 字典id，即dict_info表主键
     */
    @ApiModelProperty(value="字典表id", name="字典表id")
    private Long woTypeThirId;

    /**
     * 工单三类描述
     */
    @ApiModelProperty(value="工单三类", name="工单三类")
    private String woTypeThirDetail;

    /**
     * 字典id，即dict_info表主键
     */
    @ApiModelProperty(value="字典表id", name="字典表id")
    private Long woTypeChildId;

    /**
     * 子工单描述
     */
    @ApiModelProperty(value="子工单", name="子工单")
    private String woTypeChildDetail;

    /**
     * 工单模板描述
     */
    @ApiModelProperty(value="工单模板描述", name="工单模板描述")
    private String description;

    /**
     * 投诉级别: aNormal-正常, highRisk-高危投诉, complain-投诉
     */
    @ApiModelProperty(value="工单投诉级别", name="工单投诉级别")
    private String complainLevel;

    /**
     * 需要核实结果: 0-不需要核实,1-需要核实
     */
    @ApiModelProperty(value="投诉核实结果", name="投诉核实结果")
    private Boolean mustReview;

    /**
     * 需要处理结果: 0-不需要处理,1-需要处理
     */
    @ApiModelProperty(value="投诉处理结果", name="投诉处理结果")
    private Boolean mustApprove;

    /**
     * 排序字段
     */
    @ApiModelProperty(value="排序字段", name="排序字段")
    private Integer sort;

    /**
     * 状态；1-有效，2-无效
     */
    @ApiModelProperty(value="状态 1-有效，2-无效", name="状态 1-有效，2-无效")
    private Integer isStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间", name="创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间", name="修改时间")
    private Date gmtModify;

    /**
     * 快捷键描述
     */
    @ApiModelProperty(value="快捷键描述", name="快捷键描述")
    private String detail;

    /**
     * 快捷键标识 Y/N
     */
    @ApiModelProperty(value="快捷键标识 Y/N", name="快捷键标识 Y/N")
    private String fastStatus;

    /**
     * 所属组
     */
    @ApiModelProperty(value="所属组", name="所属组")
    private String groupCode;


    @ApiModelProperty(value = "百融问题类型")
    private String bairongQuestionType;
    
    @ApiModelProperty(value = "快捷工单目标用户")
    private String fastTargetUser;


}
