package com.welab.crm.operate.dto.blacklist.callout;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 外呼拦截规则请求对象
 */
@Data
public class CalloutInterceptionRulesReqDTO extends BaseReqDTO {


	/**
	 * 订单状态
	 */
	@ApiModelProperty(value = "订单状态")
	private List<String> orderStatus;


	/**
	 * 订单状态名称
	 */
	@ApiModelProperty(value = "订单状态")
	private List<String> orderStatusName;
	/**
	 * 标签名称
	 */
	@ApiModelProperty(value = "标签名称")
	private List<String> labelName;

	/**
	 * 生效组别
	 */
	@ApiModelProperty(value = "生效组别")
	private List<String> groupCodes;

	/**
	 * 生效组别名称
	 */
	@ApiModelProperty(value = "生效组别")
	private List<String> groupNames;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	/**
	 * 任务状态(新增的时候不用传，更新的时候要传)
	 */
	@ApiModelProperty(value = "任务状态")
	private Boolean isDeleted;

	/**
	 * 创建时间(新增、更新的时候都不用传)
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 创建人(新增、更新的时候的都不用传)
	 */
	@ApiModelProperty(value = "创建人")
	private String createStaff;
	
}
