package com.welab.crm.operate.vo.reduce;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 减免和退款时，提交界面展示的数据
 */
@Data
public class ReduceAndRefundSubmitVO {


	/**
	 * 贷款号
	 */
	private String applicationId;


	/**
	 * 已还本金
	 */
	private BigDecimal principalRepaid = BigDecimal.ZERO;


	/**
	 * 已还息费(除本金外的所有费用)
	 */
	private BigDecimal otherFeeRepaid = BigDecimal.ZERO;


	/**
	 * 已还期数
	 */
	private Integer tenorRepaid = 0;


	/**
	 * 已还款总金额
	 */
	private BigDecimal totalFeeRepaid = BigDecimal.ZERO;


	/**
	 * 还款日期(最近一期)
	 */
	private Date lastRepaidAt;


	/**
	 * 结清金额
	 */
	private BigDecimal settlementAmount = BigDecimal.ZERO;


	/**
	 * 待还本金
	 */
	private BigDecimal principalRemain = BigDecimal.ZERO;


	/**
	 * 待还期数
	 */
	private Integer tenorRemain = 0;


	/**
	 * 除本金外的其他金额之和，用于后续减免查询可减免金额
	 */
	private BigDecimal otherFee = BigDecimal.ZERO;

	/**
	 * 历史减免金额
	 */
	private BigDecimal historyReduceAmount = BigDecimal.ZERO;

	/**
	 * 历史减免部门
	 */
	private String historyReduceDepartment;
	
	
	
	
	
	
	
	
}
