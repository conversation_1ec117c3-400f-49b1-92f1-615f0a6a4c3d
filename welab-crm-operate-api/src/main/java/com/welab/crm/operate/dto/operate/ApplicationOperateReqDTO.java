package com.welab.crm.operate.dto.operate;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 贷款操作DTO
 *
 * <AUTHOR>
 * @date 2021/9/29 15:02
 */
@Data
public class ApplicationOperateReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户Id", name = "userId", example = "123456")
    private Integer userId;

    @ApiModelProperty(value = "贷款号", name = "applicationId", example = "20213129903194190")
    @NotBlank(message = "贷款号不能为空")
    private String applicationId;

    @ApiModelProperty(value = "操作类型", name = "type", example = "return_order")
    @NotBlank(message = "操作类型不能为空")
    private String type;

    @ApiModelProperty(value = "备注", name = "remark", example = "备注")
    private String comment;

    @ApiModelProperty(value = "客服手机号",name = "custServiceMobile", example = "130xxxxxxxx")
    private String custServiceMobile;

    @ApiModelProperty(value = "员工Id", name = "staffId", example = "leon.li")
    private String staffId;

    @ApiModelProperty(value = "员工所在组", name = "groupCode", example = "test")
    private String groupCode;

}
