package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/11
 */
@Data
@ApiModel(value = "工单中央监控请求对象")
public class CentralMonitoringDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = -2646664235460998675L;

    @ApiModelProperty(value = "话务组code,以逗号分隔")
    private String groupCode;

    @ApiModelProperty(value = "话务组，后台使用")
    private List<String> groupCodesList;

    @ApiModelProperty(value = "话务员id,以逗号分隔")
    private String staffId;

    @ApiModelProperty(value = "话务员，后台使用")
    private List<String> staffIds;

    @ApiModelProperty(value = "工单流（工单类型id）")
    private String type;
}
