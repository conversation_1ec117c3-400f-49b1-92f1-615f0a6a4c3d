package com.welab.crm.operate.vo.telemarketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 电销任务详情返回结果
 * @date 2022/2/22 15:24
 */
@Data
@ApiModel(value = "电销任务详情返回结果")
public class TmkTaskDetailVO implements Serializable {

    public static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "电销唯一任务Id")
    private String tmkTaskId;

    @ApiModelProperty(value = "客户姓名")
    private String username;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "身份证")
    private String cnid;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "是否注销")
    private Boolean blocked;

    @ApiModelProperty(value = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "产品号")
    private String productCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "贷款状态")
    private String state;

    @ApiModelProperty(value = "审批时间")
    private Date approvedAt;

    @ApiModelProperty(value = "注册时间")
    private Date registerAt;

    @ApiModelProperty(value = "注册渠道")
    private String regOrigin;

    @ApiModelProperty(value = "申请金额")
    private String appliedAmount;

    @ApiModelProperty(value = "申请期数")
    private String appliedTenor;

    @ApiModelProperty(value = "申请时间")
    private Date appliedAt;

    @ApiModelProperty(value = "进件渠道")
    private String applyOrigin;

    @ApiModelProperty(value = "审批金额")
    private String amount;

    @ApiModelProperty(value = "审批期数")
    private String tenor;

    @ApiModelProperty(value = "额度状态")
    private String creditStatus;

    @ApiModelProperty(value = "会员订单号")
    private String orderNo;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "超级会员渠道号")
    private String source;

    @ApiModelProperty(value = "超级会员订单支付状态")
    private String orderStatus;

    @ApiModelProperty(value = "会员订单金额")
    private String orderAmount;

    @ApiModelProperty(value = "会员订单支付失败原因")
    private String repayMessage;

    @ApiModelProperty(value = "信用额度")
    private BigDecimal creditLine;

    @ApiModelProperty(value = "可用额度")
    private BigDecimal avlCredit;

    @ApiModelProperty(value = "学历")
    private String education;

    @ApiModelProperty(value = "是否消费")
    private String isConsu;

    @ApiModelProperty(value = "分配时间")
    private Date distributedAt;

    @ApiModelProperty(value = "上次拨打时间")
    private Date lastCalledAt;

    @ApiModelProperty(value = "号码包定义")
    private String packageDefine;

    @ApiModelProperty(value = "是否进件")
    private String isIncome;

    @ApiModelProperty(value = "是否提现")
    private String isWithdrawal;

    @ApiModelProperty(value = "联系结果")
    private String contactResult;

    @ApiModelProperty(value = "电话小结")
    private String resultCode;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "客服系统客户Id")
    private Long id;

    @ApiModelProperty(value = "预约时间")
    private Date appointTime;

    @ApiModelProperty(value = "预约备注")
    private String appointComment;

    @ApiModelProperty(value = "申请与审批金额之差")
    private String amountDiff;

    @ApiModelProperty(value = "最近登陆时间")
    private Date lastSignInAt;

    @ApiModelProperty(value = "是否锁定,有电销预约消息未读的单将会锁定")
    private Boolean isLock;

    @ApiModelProperty(value = "拨打次数")
    private Integer callNum;

    @ApiModelProperty(value = "分流标志")
    private String diversionTag;

    @ApiModelProperty(value = "是否推送过ai外呼数据")
    private Boolean aiPushFlag;

    @ApiModelProperty(value = "ai外呼推送时间")
    private Date aiPushTime;

    @ApiModelProperty(value = "运营侧外呼说明")
    private String callInstruction;

    @ApiModelProperty(value = "付费模式: pay_later是先享后付、pay_first是先付费")
    private String memberPayMode;
    
    @ApiModelProperty(value = "脱敏的手机号")
    private String mobileMask;
    
    @ApiModelProperty(value = "uuid标签名称")
    private String uuidLabelName;
}
