package com.welab.crm.operate.dto.ivr;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description: ivr报表请求对象
 * @date 2022/3/8 14:19
 */
@Data
@ApiModel("ivr报表请求对象")
public class IvrReportReqDTO extends PageQueryDTO {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空", groups = Common.class)
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空", groups = Common.class)
    private Date endTime;

    @ApiModelProperty(value = "热线号码")
    @NotBlank(message = "热线号码不能为空", groups = Common.class)
    private String hotline;

    @ApiModelProperty(value = "时间间隔", example = "day,range")
    @NotBlank(message = "时间间隔不能为空",groups = Period.class)
    private String period;

    /**
     * 转人工的按键
     */
    private List<String> artificialKey;


    /**
     * 非转人工的按键
     */
    private String notArtificialKey;


    /**
     * 非转人工按键编码
     */
    private String code;

    /**
     * 热线列表
     */
    private List<String> hotlineList;

    /**
     * 是否导出
     */
    private Boolean export;

    public interface Common{}

    public interface Period{}

}
