package com.welab.crm.operate.vo.withhold;

import com.alibaba.excel.annotation.ExcelProperty;
import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 代扣返回结果
 * @date 2022/4/21 14:57
 */

@Data
@ApiModel("代扣返回结果")
@Excel(fileName = "代扣还款查询")
public class WithholdVO implements Serializable {

    private static final Long serialVersionUID = 123L;



    @ApiModelProperty(value = "咨询状态")
    @ExcelTitleMap(title = "咨询状态")
    private String consultationStatus;
    
    @ApiModelProperty(value = "客服接听电话时间", name = "callTime")
    @ExcelTitleMap(title = "来电时间")
    private Date callTime;

    @ApiModelProperty(value = "客服发起时间", name = "applyTime")
    @ExcelTitleMap(title = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "客服组别", name = "groupName")
    @ExcelTitleMap(title = "所属组")
    private String groupName;

    @ApiModelProperty(value = "客服姓名", name = "staffName")
    @ExcelTitleMap(title = "客服姓名")
    private String staffName;

    @ApiModelProperty(value = "客户姓名", name = "customerName")
    @ExcelTitleMap(title = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    @ExcelTitleMap(title = "贷款号")
    private String applicationId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ApiModelProperty(value = "用户Id", name = "userId")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty(value = "还款方式", name = "repayMode")
    @ExcelTitleMap(title = "还款方式")
    private String repaymentMode;

    @ApiModelProperty(value = "还款金额", name = "amount")
    @ExcelTitleMap(title = "还款金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "完成时间", name = "updateTime")
    @ExcelTitleMap(title = "完成时间")
    private Date updateTime;

    @ApiModelProperty(value = "支付渠道", name = "repayOrigin")
    @ExcelTitleMap(title = "支付渠道")
    private String repayOrigin;

    @ApiModelProperty(value = "代扣结果", name = "result")
    @ExcelTitleMap(title = "代扣结果")
    private String result;

    @ApiModelProperty(value = "失败原因", name = "failReason")
    @ExcelTitleMap(title = "原因释义")
    private String failReason;



    private Long staffId;

    private Long customerId;

}
