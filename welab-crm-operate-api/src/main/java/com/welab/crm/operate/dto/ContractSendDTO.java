package com.welab.crm.operate.dto;

import com.welab.crm.interview.dto.message.MessageSendDTO;
import com.welab.crm.operate.dto.message.SmsSendDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "合同发送请求参数")
public class ContractSendDTO extends SmsSendDTO {
	
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "uuid")
	private String uuid;
	
	@ApiModelProperty(value = "资金方名称")
	private String partnerName;
	
	@ApiModelProperty(value = "资金方编码")
	private String partnerCode;
	
	@ApiModelProperty(value = "发送类型 1-查看且下载；2-仅查看")
	private Integer sendType;
	

}
