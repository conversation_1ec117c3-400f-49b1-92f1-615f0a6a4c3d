package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/4
 */
@Data
@ApiModel(value = "分单明细报表响应对象")
@Excel(fileName = "分单明细报表", sheetName = "分单明细报表")
public class ReportAssignmentDetailsVO implements Serializable {

    private static final long serialVersionUID = 5931568368323710667L;

    @ApiModelProperty(value = "工单编号")
    @ExcelTitleMap(title = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "工单类型")
    @ExcelTitleMap(title = "工单类型")
    private String orderType;

    @ApiModelProperty(value = "工单大类")
    @ExcelTitleMap(title = "工单大类")
    private String firstType;

    @ApiModelProperty(value = "工单二类")
    @ExcelTitleMap(title = "工单二类")
    private String secondType;

    @ApiModelProperty(value = "工单三类")
    @ExcelTitleMap(title = "工单三类")
    private String thirdType;

    @ApiModelProperty(value = "反馈内容")
    @ExcelTitleMap(title = "分组")
    private String description;

    @ApiModelProperty(value = "分单时间")
    @ExcelTitleMap(title = "分单时间")
    private Date assignTime;

    @ApiModelProperty(value = "分单类型")
    @ExcelTitleMap(title = "分单类型")
    private String assignType;

    @ApiModelProperty(value = "规则名称")
    @ExcelTitleMap(title = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "分配组")
    @ExcelTitleMap(title = "分配组")
    private String groupName;

    @ApiModelProperty(value = "分配人")
    @ExcelTitleMap(title = "分配人")
    private String staffName;

    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ApiModelProperty(value = "userId")
    @ExcelTitleMap(title = "userId")
    private Long userId;
}
