package com.welab.crm.operate.vo.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 喵达投诉单响应VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单响应对象")
public class MiaodaComplaintVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", name = "id")
    private Long id;

    /**
     * 喵达投诉单号
     */
    @ApiModelProperty(value = "喵达投诉单号", name = "sn")
    private String sn;

    /**
     * 关联工单编号
     */
    @ApiModelProperty(value = "关联工单编号", name = "workOrderNo")
    private String workOrderNo;

    /**
     * 投诉标题
     */
    @ApiModelProperty(value = "投诉标题", name = "title")
    private String title;

    /**
     * 投诉人昵称
     */
    @ApiModelProperty(value = "投诉人昵称", name = "nickname")
    private String nickname;

    /**
     * 联系方式（脱敏）
     */
    @ApiModelProperty(value = "联系方式", name = "phone")
    private String phone;

    /**
     * 投诉时预留手机号（脱敏）
     */
    @ApiModelProperty(value = "投诉时预留手机号", name = "compPhone")
    private String compPhone;

    /**
     * 涉诉单号
     */
    @ApiModelProperty(value = "涉诉单号", name = "privacy")
    private String privacy;

    /**
     * 投诉内容
     */
    @ApiModelProperty(value = "投诉内容", name = "content")
    private String content;

    /**
     * 投诉问题
     */
    @ApiModelProperty(value = "投诉问题", name = "issue")
    private String issue;

    /**
     * 投诉要求
     */
    @ApiModelProperty(value = "投诉要求", name = "appeal")
    private String appeal;

    /**
     * 涉诉金额
     */
    @ApiModelProperty(value = "涉诉金额", name = "cost")
    private String cost;

    /**
     * 投诉状态
     */
    @ApiModelProperty(value = "投诉状态", name = "status")
    private String status;

    /**
     * 状态版本号
     */
    @ApiModelProperty(value = "状态版本号", name = "statusNo")
    private Integer statusNo;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间", name = "createdAt")
    private Date createdAt;

    /**
     * 分配时间
     */
    @ApiModelProperty(value = "分配时间", name = "assignedAt")
    private Date assignedAt;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", name = "completedAt")
    private Date completedAt;

    /**
     * 投诉链接
     */
    @ApiModelProperty(value = "投诉链接", name = "uri")
    private String uri;

    /**
     * 是否已公开
     */
    @ApiModelProperty(value = "是否已公开", name = "exposed")
    private Integer exposed;

    /**
     * 剩余申诉次数
     */
    @ApiModelProperty(value = "剩余申诉次数", name = "appealChance")
    private Integer appealChance;

    /**
     * 剩余结案次数
     */
    @ApiModelProperty(value = "剩余结案次数", name = "coCompleteChance")
    private Integer coCompleteChance;

    /**
     * 结案状态
     */
    @ApiModelProperty(value = "结案状态", name = "coCompleteStatus")
    private String coCompleteStatus;

    /**
     * 结案完成时间
     */
    @ApiModelProperty(value = "结案完成时间", name = "coCompleteAt")
    private Date coCompleteAt;

    /**
     * 自动完成时间
     */
    @ApiModelProperty(value = "自动完成时间", name = "autoCompleteAt")
    private Date autoCompleteAt;

    /**
     * 主动完成时间
     */
    @ApiModelProperty(value = "主动完成时间", name = "userCompleteAt")
    private Date userCompleteAt;

    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称", name = "service")
    private String service;

    /**
     * 用户评价-服务态度
     */
    @ApiModelProperty(value = "用户评价-服务态度", name = "attitude")
    private Integer attitude;

    /**
     * 用户评价-处理速度
     */
    @ApiModelProperty(value = "用户评价-处理速度", name = "process")
    private Integer process;

    /**
     * 用户评价-满意度
     */
    @ApiModelProperty(value = "用户评价-满意度", name = "satisfaction")
    private Integer satisfaction;

    /**
     * 用户评价-评价内容
     */
    @ApiModelProperty(value = "用户评价-评价内容", name = "evalContent")
    private String evalContent;

    @ApiModelProperty(value = "用户评价-评价时间", name = "evalAt")
    private Date evalAt;

    /**
     * 同步状态
     */
    @ApiModelProperty(value = "同步状态", name = "syncStatus")
    private Integer syncStatus;

    /**
     * 同步失败原因
     */
    @ApiModelProperty(value = "同步失败原因", name = "syncFailReason")
    private String syncFailReason;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;

    /**
     * 回复详情列表
     */
    @ApiModelProperty(value = "回复详情列表", name = "replyDetails")
    private List<MiaodaReplyDetailVO> replyDetails;

    /**
     * 结案信息列表
     */
    @ApiModelProperty(value = "结案信息列表", name = "coCompleteInfo")
    private List<MiaodaCompleteInfoVO> coCompleteInfo;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表", name = "attaches")
    private List<MiaodaAttachmentVO> attaches;

    @ApiModelProperty(value = "申诉状态", name = "appealStatus")
    private String appealStatus;

}
