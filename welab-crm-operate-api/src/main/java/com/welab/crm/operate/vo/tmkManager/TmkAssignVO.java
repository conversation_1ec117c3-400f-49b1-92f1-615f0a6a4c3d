package com.welab.crm.operate.vo.tmkManager;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据分配历史响应参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据分配历史响应对象")
public class TmkAssignVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;
	
	/**
     * 主键id
     */
	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;
	
	/**
     * 分单规则id
     */
	@ApiModelProperty(value = "分单规则id", name = "ruleId")
	private Long ruleId;
	
	/**
     * 用户Id
     */
	@ApiModelProperty(value = "用户Id", name = "userId")
    private Integer userId;

    /**
     * 用户UUID
     */
	@ApiModelProperty(value = "用户UUID", name = "uuid")
    private String uuid;
    
    /**
     * 产品名称
     */
	@ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    /**
     * 进件渠道
     */
	@ApiModelProperty(value = "进件渠道", name = "applyOrigin")
    private String applyOrigin;
    
    /**
     * 用户名
     */
	@ApiModelProperty(value = "用户名", name = "username")
    private String username;

    /**
     * 手机号
     */
	@ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;
    
    /**
     * 合同号
     */
	@ApiModelProperty(value = "合同号", name = "applicationId")
    private String applicationId;
    
    /**
     * 原来分单组
     */
	@ApiModelProperty(value = "原来分单组", name = "preGroupCode")
    private String preGroupCode;

    /**
     * 原来分单人id
     */
	@ApiModelProperty(value = "原来分单人id", name = "preStaffId")
    private String preStaffId;

    /**
     * 分单组
     */
	@ApiModelProperty(value = "分单组", name = "groupCode")
    private String groupCode;

    /**
     * 分单人id
     */
	@ApiModelProperty(value = "分单人id", name = "staffId")
    private String staffId;

    /**
     * 电销唯一ID
     */
	@ApiModelProperty(value = "电销唯一任务ID", name = "分单时间")
    private String tmkTaskId;

    /**
     * 分单时间
     */
	@ApiModelProperty(value = "分单时间", name = "distributionTime")
    private Date distributionTime;

    /**
     * 分单类型
     */
	@ApiModelProperty(value = "分单类型", name = "distributionType")
    private String distributionType;

    /**
     * 创建人
     */
	@ApiModelProperty(value = "创建人", name = "createStaffId")
    private String createStaffId;
    
    /**
     * 创建人组
     */
	@ApiModelProperty(value = "创建人组", name = "createGroupCode")
    private String createGroupCode;

    /**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtModify")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;

    @ApiModelProperty(value = "分流标签", name = "diversionTag")
    private String diversionTag;

}

