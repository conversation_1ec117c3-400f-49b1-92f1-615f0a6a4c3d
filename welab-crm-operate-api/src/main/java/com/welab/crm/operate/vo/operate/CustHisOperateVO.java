package com.welab.crm.operate.vo.operate;


import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 操作历史表
 * </p>
 *
 * <AUTHOR> 请勿手动修改
 * @since 2021-09-30
 */
@Data
public class CustHisOperateVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Integer userId;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String staffName;

    /**
     * 操作人所在组
     */
    @ApiModelProperty(value = "操作人所在组")
    private String groupCode;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型(中文描述)")
    private String operateType;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operateTime;

    /**
     * 贷款号(部分针对贷款的操作才有，比如结清)
     */
    @ApiModelProperty(value = "贷款号")
    private String loanId;

    /**
     * 修改之前的手机号
     */
    @ApiModelProperty(value = "更新手机号时是更新前的手机号，其他操作时就是现手机号")
    private String oldMobile;


    /**
     * 客服操作时所填的备注
     */
    @ApiModelProperty(value = "客服操作时所填的备注")
    private String comment;


}
