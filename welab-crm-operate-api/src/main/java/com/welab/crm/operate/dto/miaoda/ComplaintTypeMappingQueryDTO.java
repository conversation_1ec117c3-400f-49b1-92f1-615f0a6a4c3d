package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 投诉类型映射查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "投诉类型映射查询请求对象")
public class ComplaintTypeMappingQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 喵达投诉问题类型
     */
    @ApiModelProperty(value = "喵达投诉问题类型", name = "miaodaIssue")
    private String miaodaIssue;

    /**
     * 工单组合配置ID
     */
    @ApiModelProperty(value = "工单组合配置ID", name = "opDictInfoConfId")
    private Long opDictInfoConfId;

    /**
     * 是否启用 0-禁用 1-启用
     */
    @ApiModelProperty(value = "是否启用", name = "isActive")
    private Integer isActive;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", name = "page")
    private Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", name = "pageSize")
    private Integer pageSize = 10;
}
