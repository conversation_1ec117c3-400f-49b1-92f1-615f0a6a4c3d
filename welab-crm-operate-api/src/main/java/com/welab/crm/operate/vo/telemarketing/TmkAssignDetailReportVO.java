package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel("电销名单分配明细报表返回对象")
@Excel(fileName = "电销名单分配明细报表", sheetName = "电销名单分配明细报表")
public class TmkAssignDetailReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;
    
    @ApiModelProperty("产品名称")
    @ExcelTitleMap(title = "产品名称")
    private String productName;

    @ApiModelProperty("号码包定义")
    @ExcelTitleMap(title = "号码包定义")
    private String packageDefine;
    
    @ApiModelProperty("进件渠道")
    @ExcelTitleMap(title = "进件渠道")
    private String applyOrigin;
    
    @ApiModelProperty("创建时间")
    @ExcelTitleMap(title = "创建时间")
    private Date gmtCreate;
    
    @ApiModelProperty("状态")
    @ExcelTitleMap(title = "状态")
    private String status;
    
    @ApiModelProperty("分配类型")
    @ExcelTitleMap(title = "分配类型")
    private String distributionType;
    
    @ApiModelProperty("分配时间")
    @ExcelTitleMap(title = "分配时间")
    private Date distributionTime;
    
    @ApiModelProperty("分配组")
    @ExcelTitleMap(title = "分配组")
    private String groupCode;
    
    @ApiModelProperty("分配人")
    @ExcelTitleMap(title = "分配人")
    private String staffId;

    @ApiModelProperty("分流标签")
    @ExcelTitleMap(title = "分流标签")
    private String diversionTag;
}
