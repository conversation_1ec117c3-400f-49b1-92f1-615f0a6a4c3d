package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 工单调剂规则响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "分单调剂规则响应对象")
public class WorkOrderRuleAdjustVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;
	
	@ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;
	
	@ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

	/**
     * 提单时间
     */
	@ApiModelProperty(value = "提单时间", name = "submitTime")
    private Date submitTime;
	
	/**
     * 客户姓名
     * 
     */
	@ApiModelProperty(value = "客户姓名", name = "custName")
    private String customerName;
	
	/**
     * 手机号码
     * 
     */
	@ApiModelProperty(value = "手机号码", name = "mobile")
    private String mobile;
	
	/**
     * 贷款号
     * 
     */
	@ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;
	
    /**
     * 工单类型
     */
	@ApiModelProperty(value = "工单类型", name = "type")
    private String type;

    /**
     * 子工单分类
     * 
     */
	@ApiModelProperty(value = "子工单分类", name = "orderCase")
    private String orderCase;
	
	/**
     * 工单状态
     */
	@ApiModelProperty(value = "工单状态", name = "status")
    private String status;
	
	/**
     * 分配日期
     */
	@ApiModelProperty(value = "分配日期", name = "distributeTime")
    private Date distributeTime;

    /**
     * 处理组
     */
	@ApiModelProperty(value = "处理组", name = "groupCode")
    private String groupCode;

    /**
     * 处理人
     */
	@ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;
	
    /**
     * 是否首次联系
     */
	@ApiModelProperty(value = "是否首次联系", name = "firstFlag")
    private String firstFlag;
	
	/**
     * 数量
     */
	@ApiModelProperty(value = "数量", name = "number")
    private Integer number;
	
	/**
     * 处理组中文名
     */
	@ApiModelProperty(value = "处理组中文名", name = "groupName")
    private String groupName;

    /**
     * 处理人中文名
     */
	@ApiModelProperty(value = "处理人中文名", name = "staffName")
    private String staffName;

	@ApiModelProperty(value = "用户Id", name = "userId")
	private Integer userId;

	@ApiModelProperty(value = "uuid", name = "uuid")
	private String uuid;
}

