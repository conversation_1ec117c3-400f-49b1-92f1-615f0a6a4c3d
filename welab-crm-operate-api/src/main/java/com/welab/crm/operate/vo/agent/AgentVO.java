package com.welab.crm.operate.vo.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 坐席工作状态
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "坐席工作状态对象")
public class AgentVO {

    /**
     * 坐席工号
     */
    @ApiModelProperty(value = "坐席工号")
    private String cno;

    /**
     * 坐席姓名
     */
    @ApiModelProperty(value = "坐席姓名")
    private String name;

    /**
     * 组别
     */
    @ApiModelProperty(value = "组别")
    private String groupName;

    /**
     * 坐席状态
     */
    @ApiModelProperty(value = "坐席状态")
    private String state;
}
