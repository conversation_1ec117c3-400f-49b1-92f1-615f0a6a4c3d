package com.welab.crm.operate.vo.workorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Excel(fileName = "催记交互明细表")
public class WorkOrderComplainVO {

    /**
     * 逻辑主键
     */
    @ApiModelProperty(value = "主键id", name = "id")
    private Long id;

    /**
     * 客户手机号
     */
    @ApiModelProperty(value = "客户手机号", name = "mobile")
    private String mobile;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号", name = "custNo")
    private String custNo;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", name = "customerName")
    @ExcelTitleMap(title = "客户姓名")
    private String customerName;

    /**
     * 反馈时间
     */
    @ExcelTitleMap(title = "反馈时间")
    private String gmtCreateExport;

    /**
     * 投诉内容
     */
    @ApiModelProperty(value = "投诉内容", name = "content")
    @ExcelTitleMap(title = "投诉内容")
    private String content;

    /**
     * 投诉级别: aNormal-正常, highRisk-高危投诉, complain-投诉
     */
    @ApiModelProperty(value = "投诉级别: aNormal-正常, highRisk-高危投诉, complain-投诉", name = "complainLevel")
    @ExcelTitleMap(title = "投诉分级")
    private String complainLevel;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号", name = "contractNo")
    @ExcelTitleMap(title = "合同号")
    private String contractNo;

    /**
     * 催收组编码
     */
    @ApiModelProperty(value = "催收组编码", name = "groupCode")
    @ExcelTitleMap(title = "机构名称")
    private String groupCode;

    /**
     * 催收员工编码
     */
    @ApiModelProperty(value = "催收员工编码", name = "staffId")
    @ExcelTitleMap(title = "催收员")
    private String staffId;

    /**
     * 核实结果
     */
    @ApiModelProperty(value = "核实结果", name = "reviewResult")
    @ExcelTitleMap(title = "核实结果")
    private String reviewResult;

    /**
     * 核实时间
     */
    @ExcelTitleMap(title = "核实时间")
    private String reviewTimeExport;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果", name = "approveResult")
    @ExcelTitleMap(title = "处理结果")
    private String approveResult;

    /**
     * 处理时间
     */
    @ExcelTitleMap(title = "处理时间")
    private String approveTimeExport;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    @ExcelTitleMap(title = "备注")
    private String remark;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    @ExcelTitleMap(title = "处理状态")
    private String approveStatusExport;

    /**
     * 机构号
     */
    @ApiModelProperty(value = "机构号", name = "org")
    private String org;

    /**
     * 需要核实结果: 0-不需要核实,1-需要核实
     */
    @ApiModelProperty(value = "核实结果: 0-不需要核实,1-需要核实", name = "mustReview")
    private Boolean mustReview;

    /**
     * 需要处理结果: 0-不需要处理,1-需要处理
     */
    @ApiModelProperty(value = "处理结果: 0-不需要处理,1-需要处理", name = "mustApprove")
    private Boolean mustApprove;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    @ApiModelProperty(value = "处理状态: 0-处理中,1-已完成", name = "approveStatus")
    private Integer approveStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "投诉时间", name = "gmtCreate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 核实时间
     */
    @ApiModelProperty(value = "核实时间", name = "reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date reviewTime;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间", name = "approveTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date approveTime;
}
