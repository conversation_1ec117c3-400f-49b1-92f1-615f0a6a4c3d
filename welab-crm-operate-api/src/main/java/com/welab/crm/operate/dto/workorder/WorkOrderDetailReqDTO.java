package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单详情请求参数参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单详情请求对象")
public class WorkOrderDetailReqDTO implements Serializable {

    private static final long serialVersionUID = 4398893436884293034L;

    @NotBlank(message = "labelType不能为空")
    @ApiModelProperty(value = "标签类型", name = "labelType")
    private String labelType;

    @NotBlank(message = "labelCode不能为空")
    @ApiModelProperty(value = "标签代码", name = "labelCode")
    private String labelCode;

    @NotBlank(message = "manageDept不能为空")
    @ApiModelProperty(value = "标签管理部门", name = "manageDept")
    private String manageDept;

    @NotBlank(message = "custName不能为空")
    @ApiModelProperty(value = "客户姓名", name = "custName")
    private String custName;

    @NotBlank(message = "idNo不能为空")
    @ApiModelProperty(value = "客户身份证号码", name = "idNo")
    private String idNo;

    @NotBlank(message = "custMobile不能为空")
    @ApiModelProperty(value = "客户手机号", name = "custMobile")
    private String custMobile;

    @NotBlank(message = "operatorId不能为空")
    @ApiModelProperty(value = "申请人id", name = "operatorId")
    private String operatorId;

    @ApiModelProperty(value = "进件号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "申请人姓名", name = "operatorName")
    private String operatorName;

    @ApiModelProperty(value = "申请人手机号", name = "operatorMobile")
    private String operatorMobile;

    @ApiModelProperty(value = "申请人邮箱", name = "operatorEmail")
    private String operatorEmail;

    @ApiModelProperty(value = "上传文件路径", name = "url")
    private String url;

    @ApiModelProperty(value = "备注", name = "remark")
    @NotBlank(message = "remark不能为空")
    private String remark;

    @ApiModelProperty(value = "合同号", name = "contractNo")
    private String contractNo;

    @ApiModelProperty(value = "有效期，为空表示永久", name = "validMonth")
    private String validMonth;
}
