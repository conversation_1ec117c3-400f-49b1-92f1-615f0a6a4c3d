package com.welab.crm.operate.dto.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.Max;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Title: AddCustReqDTO
 * @date 2021/11/12 16:33
 */

@Data
@ApiModel(description = "新增用户信息请求对象")
public class AddCustReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名", name = "name")
    @Length(max = 15)
    private String name;

    @ApiModelProperty(value = "手机号", name = "mobile")
    @Length(max = 11)
    private String mobile;

    @ApiModelProperty(value = "性别，男or女", name = "gender")
    private String gender;

    @ApiModelProperty(value = "类型，钱包为wallet，现金贷为cash", name = "type")
    private String type;

}
