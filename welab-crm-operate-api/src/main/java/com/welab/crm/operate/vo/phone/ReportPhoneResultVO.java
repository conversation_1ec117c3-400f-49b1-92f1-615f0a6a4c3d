package com.welab.crm.operate.vo.phone;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/2
 */
@Data
@ApiModel(value = "电话小结报表响应对象")
@Excel(fileName="电话小结报表")
public class ReportPhoneResultVO implements Serializable {

    private static final long serialVersionUID = -524724043738790009L;


    @ApiModelProperty(value = "咨询状态")
    @ExcelTitleMap(title = "咨询状态")
    private String consultationStatus;

    @ApiModelProperty(value = "热线号码", name = "热线号码")
    @ExcelTitleMap(title = "热线号码")
    private String cdrHotline;

    @ApiModelProperty(value = "来电时间", name = "来电时间")
    @ExcelTitleMap(title = "来电时间")
    private Date cdrStartTime;

    @ApiModelProperty(value = "结束时间", name = "结束时间")
    @ExcelTitleMap(title = "结束时间")
    private Date cdrEndTime;

    @ApiModelProperty(value = "通话时长", name = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private String cdrEndBridgeTime;

    @ApiModelProperty(value = "uuid", name = "uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ApiModelProperty(value = "userId", name = "userId")
    @ExcelTitleMap(title = "userId")
    private Long userId;

    @ApiModelProperty(value = "客户姓名", name = "客户姓名")
    @ExcelTitleMap(title = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "逾期标签", name = "逾期标签")
    @ExcelTitleMap(title = "逾期标签")
    private String overDueLabel;

    @ApiModelProperty(value = "用户标签", name = "用户标签")
    @ExcelTitleMap(title = "用户标签")
    private String userLabel;

    @ApiModelProperty(value = "员工工号", name = "员工工号")
    @ExcelTitleMap(title = "员工工号")
    private Long staffId;

    @ApiModelProperty(value = "员工姓名", name = "员工姓名")
    @ExcelTitleMap(title = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "电话小结内容(合并小结123类)", name = "电话小结内容(合并小结123类)")
    private String callSummary;

    @ApiModelProperty(value = "电话小结内容一项", name = "电话小结内容一项")
    @ExcelTitleMap(title = "电话小结内容一项")
    private String callSummaryOne;

    @ApiModelProperty(value = "电话小结内容二项", name = "电话小结内容二项")
    @ExcelTitleMap(title = "电话小结内容二项")
    private String callSummaryTwo;

    @ApiModelProperty(value = "电话小结内容三项", name = "电话小结内容三项")
    @ExcelTitleMap(title = "电话小结内容三项")
    private String callSummaryThree;

    @ApiModelProperty(value = "人工小结备注", name = "人工小结备注")
    @ExcelTitleMap(title = "人工小结备注")
    private String callComment;

    @ApiModelProperty(value = "ai小结备注", name = "ai小结备注")
    @ExcelTitleMap(title = "ai小结备注")
    private String aiSummary;
}
