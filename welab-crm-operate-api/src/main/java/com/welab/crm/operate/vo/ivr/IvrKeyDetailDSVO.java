package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 4006000799热线返回对象
 * @date 2022/3/11 14:31
 */
@Data
public class IvrKeyDetailDSVO extends IvrKeyDetailBaseVO{

    @ApiModelProperty(value = "日期")
    @ExcelTitleMap(title = "日期")
    private String callDate;

    @ApiModelProperty(value = "用户名称")
    @ExcelTitleMap(title = "用户名称")
    private String username;

    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "UUID")
    private String uuid;

    @ApiModelProperty(value = "用户Id")
    @ExcelTitleMap(title = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "呼叫时间")
    @ExcelTitleMap(title = "呼叫时间")
    private String callTime;

    @ApiModelProperty(value = "按键开始时间")
    @ExcelTitleMap(title = "按键开始时间")
    private String keyStartTime;

    @ApiModelProperty(value = "人工接听时间")
    @ExcelTitleMap(title = "人工接听时间")
    private String csAnswerTime;

    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String hangUpTime;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Long talkDuration;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String staffName;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;

    @ExcelTitleMap(title = "产品介绍")
    private Integer ds1;
    @ExcelTitleMap(title = "物流及订单状态")
    private Integer ds2;
    @ExcelTitleMap(title = "商家入驻")
    private Integer ds3;


    @ExcelTitleMap(title = "产品介绍")
    private Integer zl1;
    @ExcelTitleMap(title = "租赁计费问题")
    private Integer zl2;
    @ExcelTitleMap(title = "绑卡及发货问题")
    private Integer zl3;
    @ExcelTitleMap(title = "还款问题")
    private Integer zl4;
    @ExcelTitleMap(title = "订单状态查询")
    private Integer zl5;
}
