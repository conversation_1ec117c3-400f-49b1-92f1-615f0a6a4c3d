package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.workorder.TemplateInfoReqDTO;
import com.welab.crm.operate.vo.workorder.TemplateInfoVO;

/**
 * 工单审批模板服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface TemplateInfoService {

	/**
     * 分页查询工单审批模板
     * @param reqDTO
     * @return
     */
    Page<TemplateInfoVO> queryTemplateInfoList(TemplateInfoReqDTO reqDTO);

    /**
     * 新增工单审批模板
     * @param reqDTO
     */
    void addTemplateInfo(TemplateInfoReqDTO reqDTO);

    /**
     * 更新工单审批模板
     * @param reqDTO
     */
    void updateTemplateInfo(TemplateInfoReqDTO reqDTO);
    
    /**  
	 * 删除工单审批模板
	 * @param reqDTO
	 * @return  
	 */
	boolean deleteTemplateInfo(BatchInfoReqDTO reqDTO);
}
