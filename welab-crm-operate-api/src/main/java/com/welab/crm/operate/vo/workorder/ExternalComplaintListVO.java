package com.welab.crm.operate.vo.workorder;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "ExternalComplaintListVO", description = "外部投诉列表VO")
public class ExternalComplaintListVO implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "主键id")
	@ExcelIgnore
	private Long id;
	
	@ApiModelProperty(value = "工单号")
	@ExcelProperty(value = "工单号")
	private String orderNo;
	
	@ApiModelProperty(value = "自适应单号")
	@ExcelProperty(value = "单号")
	private String adaptiveOrderNo;
	
	@ApiModelProperty(value = "快捷工单")
	@ExcelProperty(value = "快捷工单")
	private String quickOrder;
	
	@ApiModelProperty(value = "姓名")
	@ExcelProperty(value = "客户姓名")
	private String name;
	
	@ApiModelProperty(value = "工单状态")
	@ExcelProperty(value = "工单状态")
	private String orderStatus;
	
	@ApiModelProperty(value = "创建时间")
	@ExcelProperty(value = "创建时间")
	private String createTime;
	
	@ApiModelProperty(value = "结案时间")
	@ExcelProperty(value = "结案时间")
	private String completeTime;
	
	@ApiModelProperty(value = "处理方案")
	@ExcelProperty(value = "处理方案")
	private String resolveContent;
	
	@ApiModelProperty(value = "工号")
	@ExcelProperty(value = "工号")
	private String cno;
	
	
}
