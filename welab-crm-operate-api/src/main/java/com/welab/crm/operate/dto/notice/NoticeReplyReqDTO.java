package com.welab.crm.operate.dto.notice;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Title: NoticeReplyReqDTO
 * @date 2021/10/28 17:20
 */
@Data
public class NoticeReplyReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "消息Id", name = "msgId", example = "msgxxxxxx")
    @NotBlank(message = "消息Id不能为空")
    private String msgId;


    @ApiModelProperty(value = "回复内容", name = "content", example = "你好，回复")
    @NotBlank(message = "回复内容不能为空")
    private String content;


    /**
     * 回复人
     */
    private String staffId;
}
