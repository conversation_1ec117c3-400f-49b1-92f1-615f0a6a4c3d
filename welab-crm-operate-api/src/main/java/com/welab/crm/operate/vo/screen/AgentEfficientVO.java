package com.welab.crm.operate.vo.screen;

import lombok.Data;

import java.io.Serializable;

/**
 * 坐席大屏工作效率
 * <AUTHOR>
 */
@Data
public class AgentEfficientVO implements Serializable {

    /**
     * 姓名
     */
    private String name;

    /**
     * 接待量=座席来电接听数
     */
    private Integer ibAnsweredCount;

    /**
     * 平均呼入时长
     */
    private Long avgCallTime;

    /**
     * 工时利用率
     */
    private String workUtilizationRate;

}
