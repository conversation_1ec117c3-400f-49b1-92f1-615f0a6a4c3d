package com.welab.crm.operate.dto.workorder;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/7/21
 */
@Data
@ApiModel(value = "资金投诉统计响应对象")
@Excel(fileName = "资金投诉统计报表", sheetName = "资金投诉统计报表")
public class FundNameResDTO implements Serializable {

    private static final long serialVersionUID = 6274817710100970L;

    @ApiModelProperty(value = "资金方简称", name = "fundName")
    @ExcelTitleMap(title = "资金方简称")
    private String fundName;

    @ApiModelProperty(value = "资方内部", name = "ziFangNeiBu")
    @ExcelTitleMap(title = "资方内部")
    private int ziFangNeiBu;

    @ApiModelProperty(value = "内部占比", name = "neiBuPercent")
    @ExcelTitleMap(title = "内部占比")
    private double neiBuPercent;

    @ApiModelProperty(value = "银保监", name = "yinBaoJian")
    @ExcelTitleMap(title = "银保监")
    private int yinBaoJian;

    @ApiModelProperty(value = "信访", name = "xinFang")
    @ExcelTitleMap(title = "信访")
    private int xinFang;

    @ApiModelProperty(value = "举报件", name = "juBaoJian")
    @ExcelTitleMap(title = "举报件")
    private int juBaoJian;

    @ApiModelProperty(value = "监管占比", name = "jianGuanPercent")
    @ExcelTitleMap(title = "监管占比")
    private double jianGuanPercent;

    @ApiModelProperty(value = "合计", name = "total")
    @ExcelTitleMap(title = "合计")
    private int total;

    @ApiModelProperty(value = "总占比", name = "totalPercent")
    @ExcelTitleMap(title = "总占比")
    private double totalPercent;

    @ApiModelProperty(value = "解决量", name = "jieJueLiang")
    @ExcelTitleMap(title = "解决量")
    private int jieJueLiang;

    @ApiModelProperty(value = "解决率", name = "jieJuePercent")
    @ExcelTitleMap(title = "解决率")
    private double jieJuePercent;
}
