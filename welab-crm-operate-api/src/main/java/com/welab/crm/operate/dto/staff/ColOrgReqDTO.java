package com.welab.crm.operate.dto.staff;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description TODO 组织管理req
 * <AUTHOR>
 * @date 2021-11-03 16:59:59
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class ColOrgReqDTO  extends BaseReqDTO implements Serializable {

	private static final long serialVersionUID = -8373484083909373046L;

	/**
	 * 机构名称
	 */
	@ApiModelProperty(value="机构名称", name="name",example = "kf")
	private String name;

	/**
	 * 机构code
	 */
	@ApiModelProperty(value=" 机构code", name="code",example = "111")
	private String code;

	/**
	 * 催收组织父机构代码
	 */
	@ApiModelProperty(value="父机构代码", name="pcode",example = "wld")
	private String pcode;

	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级->是否管理组", name = "level", example = "0")
	private int level = 0;

}
