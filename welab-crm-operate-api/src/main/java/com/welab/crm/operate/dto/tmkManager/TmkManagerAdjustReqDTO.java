package com.welab.crm.operate.dto.tmkManager;

import com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO;
import java.util.List;

import com.welab.crm.operate.dto.BaseRequestDTO;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustInfo;
import com.welab.crm.operate.dto.workorder.AdjustWorkOrderInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 电销数据调剂请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "电销数据调剂规则请求对象")
public class TmkManagerAdjustReqDTO extends TmkManagerReqDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;
	
	/**
     * 分配前数量列表
     */
	@ApiModelProperty(value = "分配前数量列表", name = "assignList")
    private List<TotalTmkManagerVO> assignList;
	
	/**
     * 分配前详单列表
     */
	@ApiModelProperty(value = "分配前详单列表", name = "taskList")
    private List<TmkRuleAdjustInfo> taskList;
	
	/**
     * 分配后列表
     */
	@ApiModelProperty(value = "分配后列表", name = "adjustList")
    private List<AdjustWorkOrderInfo> adjustList;

	@ApiModelProperty(value = "操作人id", name = "operatorId")
	private String operatorId;
	
	@ApiModelProperty(value = "操作人组Code", name = "operatorGroupCode")
	private String operatorGroupCode;
	
}
