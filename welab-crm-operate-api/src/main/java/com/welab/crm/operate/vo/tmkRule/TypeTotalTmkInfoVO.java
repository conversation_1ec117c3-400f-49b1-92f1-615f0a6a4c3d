package com.welab.crm.operate.vo.tmkRule;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据统计响应对象
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据统计响应对象")
public class TypeTotalTmkInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	/**
     * 类型
     */
	@ApiModelProperty(value = "类型", name = "type")
    private String type;

    /**
     * 数量
     */
	@ApiModelProperty(value = "数量", name = "count")
    private Integer count;
}

