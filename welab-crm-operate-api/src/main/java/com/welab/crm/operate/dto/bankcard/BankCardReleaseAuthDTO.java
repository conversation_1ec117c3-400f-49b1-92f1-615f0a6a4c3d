package com.welab.crm.operate.dto.bankcard;

import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/10
 */
@Data
@ApiModel(value = "银行卡解除授权请求对象")
public class BankCardReleaseAuthDTO extends HistoryOperationDTO {

    private static final long serialVersionUID = 7188419680618902635L;
    
    @ApiModelProperty(value = "银行名称")
    @NotBlank(message = "银行名称不能为空")
    private String bankName;

}
