package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: ai外呼营销小结表
 * @date 2022/3/18 11:17
 */
@Data
@ApiModel("ai外呼营销小结表返回对象")
@Excel(fileName = "AI外呼营销小结表")
public class AiPushReportVO implements Serializable {
    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("逻辑Id")
    private Long id;

    @ApiModelProperty("用户id")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ApiModelProperty("外呼任务")
    @ExcelTitleMap(title = "外呼任务")
    private String taskName;

    @ApiModelProperty("外呼时间")
    @ExcelTitleMap(title = "外呼时间")
    private Date callTime;

    @ApiModelProperty("联系结果")
    @ExcelTitleMap(title = "联系结果")
    private String contactResult;

    @ApiModelProperty("进件时间")
    @ExcelTitleMap(title = "进件时间")
    private Date appliedAt;

    @ApiModelProperty("确认时间")
    @ExcelTitleMap(title = "确认时间")
    private Date confirmedAt;

    @ApiModelProperty("确认金额")
    @ExcelTitleMap(title = "确认金额")
    private String confirmedAmount;

    @ApiModelProperty("导入时间")
    @ExcelTitleMap(title = "导入时间")
    private Date importTime;

    @ApiModelProperty("通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Integer billSec;


}

