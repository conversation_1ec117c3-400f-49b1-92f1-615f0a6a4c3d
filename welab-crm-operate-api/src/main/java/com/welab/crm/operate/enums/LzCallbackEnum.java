package com.welab.crm.operate.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/18
 */
public enum LzCallbackEnum {

    SUCCESS("00", "success"),
    PROCESSING("01", "processing"),
    FAIL("02", "fail");

    private String code;

    private String msg;

    LzCallbackEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code) {
        for (LzCallbackEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getMsg();
            }
        }
        return code;
    }
}
