package com.welab.crm.operate.vo.workorder;

import java.util.Date;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单模板请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单模板请求对象")
public class TemplateInfoVO extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3752033840277764047L;

	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;
    
	/**
     * 模板分类ID
     */
	@ApiModelProperty(value = "模板分类ID", name = "caseId")
    private String caseId;
	
	/**
     * 员工ID
     */
	@ApiModelProperty(value = "员工ID", name = "staffId")
    private String staffId;

    /**
     * 模板标题
     * 
     */
	@ApiModelProperty(value = "模板标题", name = "title")
    private String title;

    /**
     * 模板内容
     */
	@ApiModelProperty(value = "模板内容", name = "content")
    private String content;

    /**
     * 关键字
     */
	@ApiModelProperty(value = "关键字", name = "keyword")
    private String keyword;

    /**
     * 类型：0个人，1企业
     */
	@ApiModelProperty(value = "类型：0个人，1企业", name = "type")
    private String type;
	
	/**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;
}
