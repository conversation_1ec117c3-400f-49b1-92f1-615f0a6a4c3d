package com.welab.crm.operate.vo.tmkRule;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销拦截规则响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "分单规则响应对象")
public class TmkInterceptRuleVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;
	
	/**
     * 主键id
     */
	@ApiModelProperty(value = "主键id", name = "id")
    private Long id;

    /**
     * 规则名称
     */
	@ApiModelProperty(value = "规则名称", name = "name")
    private String name;

    /**
     * 业务类型
     */
	@ApiModelProperty(value = "业务类型", name = "tmkType")
    private String tmkType;

    /**
     * 拦截类型，push推送拦截，assign分配拦截
     */
	@ApiModelProperty(value = "拦截类型，push推送拦截，assign分配拦截", name = "interceptType")
    private String interceptType;

    /**
     * 开关状态（1开启，0关闭）
     */
	@ApiModelProperty(value = "开关状态（1开启，0关闭）", name = "status")
    private String status;

    /**
     * 产品编码
     */
	@ApiModelProperty(value = "产品编码", name = "productCode")
    private String productCode;

    /**
     * 进件渠道
     */
	@ApiModelProperty(value = "进件渠道", name = "channelCode")
    private String channelCode;
	
	/**
     * 标签编码
     */
	@ApiModelProperty(value = "标签编码", name = "labelCode")
    private String labelCode;

    /**
     * 有效期，单位天
     */
	@ApiModelProperty(value = "有效期，单位天", name = "validDays")
    private Integer validDays;

    /**
     * 备注
     */
	@ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    /**
     * 创建时间
     */
	@ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    /**
     * 修改时间
     */
	@ApiModelProperty(value = "修改时间", name = "gmtModify")
    private Date gmtModify;

    /**
     * 创建人
     */
	@ApiModelProperty(value = "创建人", name = "createStaffId")
    private String createStaffId;

    /**
     * 修改人
     */
	@ApiModelProperty(value = "修改人", name = "modifyStaffId")
    private String modifyStaffId;
	
	/**
     * 创建人姓名
     */
	@ApiModelProperty(value = "创建人姓名", name = "createStaffName")
    private String createStaffName;

    /**
     * 修改人姓名
     */
	@ApiModelProperty(value = "修改人姓名", name = "modifyStaffName")
    private String modifyStaffName;
}

