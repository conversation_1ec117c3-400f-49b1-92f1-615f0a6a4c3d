package com.welab.crm.operate.dto.phone;

import com.welab.crm.operate.dto.BaseReqDTO;
import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class PhoneLoginInfoReqDTO extends BaseReqDTO {
	private static final long serialVersionUID = -4274919204977502656L;

	/**
	 * 呼叫中心编号
	 */
	@ApiModelProperty(value="呼叫中心编号", name="org")
	private String org;

	/**
	 * 坐席号
	 */
	@ApiModelProperty(value="坐席号", name="idNo")
	private String idNo;

	/**
	 * 登录密码
	 */
	@ApiModelProperty(value="登录密码", name="pwd")
	private String pwd;

	/**
	 * 绑定电话
	 */
	@ApiModelProperty(value="绑定电话", name="tel")
	private String tel;

	/**
	 * 电话类型
	 */
	@ApiModelProperty(value="电话类型", name="telType")
	private String telType;

	/**
	 * 初始状态
	 */
	@ApiModelProperty(value="初始状态", name="initState")
	private String initState;
    /**
      * 员工Id
     */
	@ApiModelProperty(value="staff表的loginName", name="userTel")
	private String userTel;
}