package com.welab.crm.operate.vo.ivr;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 4006040888热线返回对象
 * @date 2022/3/11 14:31
 */
@Data
public class IvrKeyDetailJiafenVO extends IvrKeyDetailBaseVO{

    @ApiModelProperty(value = "日期")
    @ExcelTitleMap(title = "日期")
    private String callDate;


    @ApiModelProperty(value = "用户名称")
    @ExcelTitleMap(title = "用户名称")
    private String username;


    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "UUID")
    private String uuid;

    @ApiModelProperty(value = "用户Id")
    @ExcelTitleMap(title = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "呼叫时间")
    @ExcelTitleMap(title = "呼叫时间")
    private String callTime;

    @ApiModelProperty(value = "按键开始时间")
    @ExcelTitleMap(title = "按键开始时间")
    private String keyStartTime;

    @ApiModelProperty(value = "人工接听时间")
    @ExcelTitleMap(title = "人工接听时间")
    private String csAnswerTime;

    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String hangUpTime;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Long talkDuration;

    @ApiModelProperty(value = "坐席名称")
    @ExcelTitleMap(title = "坐席名称")
    private String staffName;

    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cno;


    @ExcelTitleMap(title = "账户问题 - 开户问题")
    @ApiModelProperty(value = "账户问题 - 开户问题")
    private Integer jiafenZf1;

    @ExcelTitleMap(title = "账户问题 - 更新风险测评")
    @ApiModelProperty(value = "账户问题 - 更新风险测评")
    private Integer jiafenZf2;


    @ExcelTitleMap(title = "交易及资金问题 - 银行卡支付额度问题")
    @ApiModelProperty(value = "交易及资金问题 - 银行卡支付额度问题")
    private Integer jiafenJy1;

    @ExcelTitleMap(title = "交易及资金问题 - 跨组合支付失败")
    @ApiModelProperty(value = "交易及资金问题 - 跨组合支付失败")
    private Integer jiafenJy2;

    @ExcelTitleMap(title = "交易及资金问题 - 基金确认时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金确认时间")
    private Integer jiafenJy3;

    @ExcelTitleMap(title = "交易及资金问题 - 基金赎回到账时间")
    @ApiModelProperty(value = "交易及资金问题 - 基金赎回到账时间")
    private Integer jiafenJy4;


    @ExcelTitleMap(title = "活动问题 - 愿望功能")
    @ApiModelProperty(value = "活动问题 - 愿望功能")
    private Integer jiafenHd1;

    @ExcelTitleMap(title = "活动问题 - 新手福利")
    @ApiModelProperty(value = "活动问题 - 新手福利")
    private Integer jiafenHd2;


    @ExcelTitleMap(title = "功能介绍 - 愿望功能")
    @ApiModelProperty(value = "功能介绍 - 愿望功能")
    private Integer jiafenGn1;

    @ExcelTitleMap(title = "功能介绍 - 现金钱包")
    @ApiModelProperty(value = "功能介绍 - 现金钱包")
    private Integer jiafenGn2;

    @ExcelTitleMap(title = "功能介绍 - 目标盈")
    @ApiModelProperty(value = "功能介绍 - 目标盈")
    private Integer jiafenGn3;
}
