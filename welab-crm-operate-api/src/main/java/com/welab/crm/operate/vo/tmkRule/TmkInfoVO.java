package com.welab.crm.operate.vo.tmkRule;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据明细响应参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据明细响应对象")
public class TmkInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	/**
     * 电销唯一ID
     */
	@ApiModelProperty(value = "电销唯一ID", name = "tmkTaskId")
    private String tmkTaskId;

    /**
     * 电销类型
     */
	@ApiModelProperty(value = "电销类型", name = "tmkType")
    private String tmkType;
	
	/**
     * uuid
     */
	@ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    /**
     * 用户ID
     */
	@ApiModelProperty(value = "用户ID", name = "userId")
    private String userId;

    /**
     * 产品名称
     */
	@ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

    /**
     * 进件渠道
     */
	@ApiModelProperty(value = "进件渠道", name = "channelName")
    private String applyOrigin;

    /**
     * 审批时间
     */
	@ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

	private String customerName;

	private String mobile;

	@ApiModelProperty(value = "分流标签", name = "diversionTag")
	private String diversionTag;

	private Date gmtCreate;

	/**
	 * 号码包定义
	 */
	@ApiModelProperty(value = "号码包定义")
	private String numPackageDef;

	/**
	 * 外呼说明
	 */
	@ApiModelProperty(value = "外呼说明")
	private String callInstruction;
}

