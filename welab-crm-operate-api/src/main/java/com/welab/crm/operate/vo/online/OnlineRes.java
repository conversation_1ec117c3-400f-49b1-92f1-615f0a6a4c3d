package com.welab.crm.operate.vo.online;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class OnlineRes<T> implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private Integer ret;
	
	private String msg;
	
	private T data;
	
	
	public static boolean isSuccess(OnlineRes<?> res) {
		return res != null && res.getRet() != null && res.getRet() == 0;
	}
	
	
}
