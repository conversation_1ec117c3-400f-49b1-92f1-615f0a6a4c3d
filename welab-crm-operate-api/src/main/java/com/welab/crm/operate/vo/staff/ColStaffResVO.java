package com.welab.crm.operate.vo.staff;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description 员工VO
 * <AUTHOR>
 * @date 2021-10-22 15:13:04
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class ColStaffResVO implements Serializable {

	private static final long serialVersionUID = -3771702779140689539L;

	/**
	 * 主键
	 */
	private Long id;

	/**
     * 登录名
     */
    @ApiModelProperty(value="登录名", name="登录名")
    private String loginName;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value="员工姓名", name="员工姓名")
    private String staffName;

    /**
     * 员工手机
     */
    @ApiModelProperty(value="员工手机", name="员工手机")
    private String staffMobile;

    /**
     * 所属组code
     */
    @ApiModelProperty(value="所属组code", name="所属组code")
    private String groupCode;

    /**
     * 所属组名
     */
    @ApiModelProperty(value="所属组名", name="所属组名")
    private String groupName;

    /**
     * 所属公司
     */
    @ApiModelProperty(value="所属公司", name="所属公司")
    private String company;

    /**
     * 员工状态，休息，在线
     */
    private String staffStatus;
    
    /**
     * 是否管理员
     */
    @ApiModelProperty(value="0:否，1:是", name="是否管理员")
    private Integer isManager;

    /**
     * 记录状态，1：有效，2：无效
     */
    @ApiModelProperty(value="1：有效，2：无效", name="记录状态")
    private Integer isStatus;


    @ApiModelProperty(value="邮箱地址", name="邮箱地址")
    private String email;

    @ApiModelProperty(value="休息时间区间", name="休息时间区间")
    private String restTime;
    
    @ApiModelProperty(value="是否上传底照")
    private String isUploadBasePhoto;
    
    @ApiModelProperty(value="登录方式")
    private String loginType;

    @ApiModelProperty(value="是否显示上传底照菜单")
    private String isDisplayUploadFaceMenu;

    
    @ApiModelProperty(value = "密码")
    private String password;
    
    @ApiModelProperty(value = "座席号")
    private String cno;

    public String translateIsDisplayUploadFaceMenu(Boolean isDisplayUploadFaceMenu) {
        if (null == isDisplayUploadFaceMenu) {
            return "是";
        }
        if (isDisplayUploadFaceMenu) {
            return "是";
        } else {
            return "否";
        }
    }
}
