package com.welab.crm.operate.dto.phone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 坐席技能组更新请求对象
 * <AUTHOR> 
 */
@Data
@ApiModel(description = "坐席技能组更新请求对象")
public class AgentSkillUpdateDTO {

	/**
	 * 呼叫中心编号
	 */
	@ApiModelProperty(value = "呼叫中心编号")
	private String enterpriseId;


	/**
	 * 坐席号
	 */
	@ApiModelProperty(value = "坐席号")
	private String cno;

	/**
	 * 技能组ID，多个用逗号隔开
	 */
	@ApiModelProperty(value = "技能组ID")
	private String skillIds;
}
