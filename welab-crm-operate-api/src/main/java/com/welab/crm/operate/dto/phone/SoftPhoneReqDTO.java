package com.welab.crm.operate.dto.phone;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 软电话请求DTO
 *
 * <AUTHOR>
 * @date 2021/10/21 14:56
 */
@Data
public class SoftPhoneReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "通话小结的主键", name = "callDetailGuid", example = "1234")
    private Long callDetailGuid;

    @ApiModelProperty(value = "话务员Id", name = "staffId", example = "leon.li")
    private String staffId;


    @ApiModelProperty(value = "话务员所在组", name = "groupCode", example = "oa")
    private String groupCode;


    @ApiModelProperty(value = "客户号码", name = "cdrCustomerNumber", example = "130xxxxxxxx")
    private String cdrCustomerNumber;


    @ApiModelProperty(value = "天润通话唯一Id", name = "cdrMainUniqueId", example = "1231412")
    private String cdrMainUniqueId;


    @ApiModelProperty(value = "1 座席接听; 2 已呼叫座席,座席未接听; 3 系统接听; 4 系统未接-IVR配置错误; 5 系统未接-停机", name = "cdrStatus", example = "1")
    private String cdrStatus;


    @ApiModelProperty(value = "通话类型;1 呼入,4 预览外呼,6 主叫外呼,9 内部呼叫", name = "cdrCallType", example = "1")
    private String cdrCallType;

    @ApiModelProperty(value = "坐席号", name = "cno", example = "2001")
    private String cno;

    @ApiModelProperty(value = "任务号", name = "taskId", example = "wo1421414")
    private String taskId;

    @ApiModelProperty(value = "热线号码", name = "cdrHotline")
    private String cdrHotline;

}
