package com.welab.crm.operate.vo.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/15
 */
@Data
@ApiModel(value = "查询工单历史列表响应对象")
public class WorkOrderHisVO implements Serializable {

    private static final long serialVersionUID = 6543473462934621213L;

    @ApiModelProperty(value = "主键ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;

    @ApiModelProperty(value = "工单名称", name = "type")
    private String type;

    @ApiModelProperty(value = "工单三类", name = "type")
    private String thirdType;

    @ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;

    @ApiModelProperty(value = "工单状态", name = "status")
    private String status;

    @ApiModelProperty(value = "创建时间", name = "gmtCreate")
    private Date gmtCreate;

    @ApiModelProperty(value = "结案时间", name = "completeTime")
    private Date completeTime;

    @ApiModelProperty(value = "本步处理意见", name = "opinion")
    private String opinion;

    @ApiModelProperty(value = "当前处理组", name = "groupName")
    private String groupName;

    @ApiModelProperty(value = "当前处理人", name = "staffName")
    private String staffName;

    @ApiModelProperty(value = "流程实例id", name = "executionId")
    private String executionId;

    @ApiModelProperty(value = "员工id", name = "staffId")
    private String staffId;

    @ApiModelProperty(value = "是否是过河兵数据", name = "isElite")
    private Boolean isElite;
    
    @ApiModelProperty(value = "处理方案", name = "resolveContent")
    private String resolveContent;
}
