package com.welab.crm.operate.vo.personalPanel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人效能数据
 * <AUTHOR>
 */
@ApiModel(value = "个人效能数据")
@Data
public class IndividualEffectiveness {
	
	
	@ApiModelProperty(value = "来电量")
	private Integer callInNumber;
	
	@ApiModelProperty(value = "接通量")
	private Integer answerNumber;
	
	@ApiModelProperty(value = "接通率")
	private String answerRate;
	
	@ApiModelProperty(value = "首问解决率")
	private String resolvedRate;
	
	@ApiModelProperty(value = "满意度")
	private String satisfiedRate;

	public IndividualEffectiveness() {
		callInNumber = 0;
		answerNumber = 0;
		answerRate = "0.00%";
		resolvedRate = "0.00%";
		satisfiedRate = "0.00%";
	}
}
