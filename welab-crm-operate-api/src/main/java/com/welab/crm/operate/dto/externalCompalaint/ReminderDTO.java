package com.welab.crm.operate.dto.externalCompalaint;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 催单DTO
 */
@ApiModel(value = "ReminderDTO", description = "催单DTO")
@Data
public class ReminderDTO {
	
	@ApiModelProperty(value = "主键id", name = "id")
	@NotNull(message = "id不能为空")
	private Long id;
	
	@ApiModelProperty(value = "催单内容", name = "opinion")
	private String opinion;
}
