package com.welab.crm.operate.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 电销分单类型
 * @date 2021/9/30
 */
public enum TmkDistributionTypeEnum {
	MANUAL("manual", "手工调剂"),
    AUTO("auto", "自动分配"),
    REDISTRIBUTION("redistribution", "数据再分配"),
    RETRIEVE("retrieve", "数据回收再分配");

    private String value;
    private String text;

    private TmkDistributionTypeEnum(String value, String text) {
        this.value = value; 
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getState(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        for (TmkDistributionTypeEnum state : values()) {
            if (state.value.equals(value)) {
                return state.getText();
            }
        }
        return null;
    }
}
