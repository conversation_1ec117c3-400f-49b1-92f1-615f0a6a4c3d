package com.welab.crm.operate.vo.repay;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Data;

/**
 * 导入还款明细对象
 *
 * <AUTHOR>
 * @date 2022-11-05
 */
@Data
@Excel(fileName = "customer-repay-import-file", sheetName = "客户还款明细导入uuid、userId、合同号列表")
public class RepayImportVO {

    @ExcelTitleMap(title = "uuid")
    private String uuid;

    @ExcelTitleMap(title = "userId")
    private String userId;

    @ExcelTitleMap(title = "applicationId")
    private String applicationId;

    @ExcelTitleMap(title = "*填写英文:loan-现金贷,wallet-钱夹谷谷")
    private String loanType;
}
