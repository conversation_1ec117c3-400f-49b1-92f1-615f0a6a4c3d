package com.welab.crm.operate.dto.report;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
@ApiModel(value = "人脸验证查询请求对象")
public class FaceReportDTO extends PageQueryDTO {

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private String endTime;

    @ApiModelProperty(value = "供应商")
    private String vendor;

    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "发送人")
    private String sendUser;

    @ApiModelProperty(value = "发送组")
    private String sendGroup;
}
