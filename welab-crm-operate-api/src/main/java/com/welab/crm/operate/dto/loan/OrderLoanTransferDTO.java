package com.welab.crm.operate.dto.loan;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * 工单债转明细查询请求对象
 */
@Data
@ApiModel(description = "工单债转明细查询请求对象")
public class OrderLoanTransferDTO extends BaseRequestDTO {


	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTime;


	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTime;


	/**
	 * 处理状态 1-处理成功，0-处理失败
	 */
	@ApiModelProperty(value = "处理状态 1-处理成功，0-处理失败")
	private Integer processState;

	/**
	 * 贷款号
	 */
	@ApiModelProperty(value = "贷款号")
	private String applicationId;
}
