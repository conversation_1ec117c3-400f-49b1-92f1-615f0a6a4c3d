package com.welab.crm.operate.dto.notice;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <p>
 * 通知公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
public class NoticeMsgReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "通知标题", name = "title", example = "【通知】关于周末值班调整")
    private String title;

    @ApiModelProperty(value = "内容", name = "content", example = "周六值班：xx,xxx,周日值班：xx,xxx")
    private String content;

    @ApiModelProperty(value = "发送者", name = "sender", example = "leon.li")
    private String sender;

    @ApiModelProperty(value = "接收者loginName,多个用逗号隔开", name = "receiver", example = "brian.rao, mirone.wang")
    private String receiver;

    @ApiModelProperty(value = "类型", name = "type", example = "通知为notice，消息为msg")
    private String type;

    @ApiModelProperty(value = "附件文件名", name = "fileName", example = "fsfsfsdfs#abc.png")
    private String fileName;

    @ApiModelProperty(value = "排序参数", name = "fileName", example = "asc 或者 desc")
    private String sort;

    @ApiModelProperty(value = "是否跑马灯", name = "isBanner", example = "true or false")
    private Boolean isBanner;

    @ApiModelProperty(value = "是否接收人为组", name = "isGroup", example = "true")
    private Boolean isGroup;

    @ApiModelProperty(value = "通知类型(system:系统通知,enterprise_wechat:企业微信通知)", name = "noticeType", example = "system")
    private String noticeType;

    private String jsonDetail;


}
