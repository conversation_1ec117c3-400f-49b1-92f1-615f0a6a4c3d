package com.welab.crm.operate.vo.loan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class LoanCancelVO {

    /**
     * 将要取消的贷款号
     */
    @ApiModelProperty(value = "贷款号")
    private String contractNo;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态", example = "审批状态: 0-待审批,1-审批成功,2-审批拒绝")
    private String approveState;

    /**
     * 操作者
     */
    @ApiModelProperty(value = "操作者")
    private String createUser;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private String gmtCreate;

    /**
     * 贷款取消原因
     */
    @ApiModelProperty(value = "贷款取消原因")
    private String reason;

    /**
     * 审批说明
     */
    @ApiModelProperty(value = "审批说明")
    private String remark;
}
