package com.welab.crm.operate.vo.tmkManager;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 电销数据管理明细响应参数
 * @date 2022/02/24
 */
@Data
@ApiModel(value = "电销数据管理明细响应对象")
public class TmkManagerVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -102517210654657594L;

	/**
     * 电销唯一任务ID
     */
	@ApiModelProperty(value = "电销唯一任务ID", name = "tmkTaskId")
    private String tmkTaskId;
	
	/**
     * 业务类型
     */
	@ApiModelProperty(value = "业务类型", name = "tmkType")
    private String tmkType;

    /**
     * 姓名
     */
	@ApiModelProperty(value = "姓名", name = "customerName")
    private String customerName;
	
	@ApiModelProperty(value = "电话号码", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "合同号", name = "applicationId")
    private String applicationId;

    /**
     * 注册渠道
     */
	@ApiModelProperty(value = "注册渠道", name = "applyOrigin")
    private String applyOrigin;
	
	@ApiModelProperty(value = "产品名称", name = "productName")
    private String productName;

	/**
     * 进件时间
     */
	@ApiModelProperty(value = "进件时间", name = "appliedAt")
    private Date appliedAt;
	
	@ApiModelProperty(value = "处理组", name = "groupCode")
    private String groupCode;
	
	@ApiModelProperty(value = "处理组名称", name = "groupName")
    private String groupName;
    
    @ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;
    
    @ApiModelProperty(value = "处理人姓名", name = "staffName")
    private String staffName;
	
    /**
     * 贷款状态
     */
	@ApiModelProperty(value = "贷款状态", name = "isChange")
    private String isChange;
	
	/**
     * 分单时间
     */
	@ApiModelProperty(value = "分单时间", name = "distributionTime")
    private Date distributionTime;
	
	/**
     * 审批时间
     */
	@ApiModelProperty(value = "审批时间", name = "approvedAt")
    private Date approvedAt;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;


    @ApiModelProperty(value = "用户Id", name = "userId")
    private Integer userId;

    @ApiModelProperty(value = "分流标签", name = "diversionTag")
    private String diversionTag;

}

