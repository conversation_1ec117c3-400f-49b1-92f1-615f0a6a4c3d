package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 查询工单流水响应对象
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "查询工单流水响应对象")
public class WorkOrderLogVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;

    @ApiModelProperty(value = "工单号", name = "orderNo")
    private String orderNo;

	@ApiModelProperty(value = "处理组代码", name = "groupCode")
    private String groupCode;

    @ApiModelProperty(value = "处理组名称", name = "groupName")
    private String groupName;

    @ApiModelProperty(value = "处理人", name = "staffId")
    private String staffId;
    
    @ApiModelProperty(value = "处理时间", name = "gmtCreate")
    private String gmtCreate;
    
    @ApiModelProperty(value = "处理意见", name = "comment")
    private String comment;

    @ApiModelProperty(value = "操作", name = "operate")
    private String operate;
	
	@ApiModelProperty(value = "处理方案", name = "resolveContent")
	private String resolveContent;
	
	@ApiModelProperty(value = "工号", name = "cno")
	private String cno;
	
	@ApiModelProperty(value = "登录名称", name = "loginName")
	private String loginName;
}

