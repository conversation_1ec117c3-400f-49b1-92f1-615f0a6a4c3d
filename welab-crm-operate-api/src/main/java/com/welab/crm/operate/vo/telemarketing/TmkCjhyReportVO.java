package com.welab.crm.operate.vo.telemarketing;

import java.io.Serializable;
import java.util.Date;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel("电销超级会员报表返回对象")
@Excel(fileName = "电销超级会员报表", sheetName = "电销超级会员报表")
public class TmkCjhyReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;
    
    @ApiModelProperty("名单类型")
    @ExcelTitleMap(title = "名单类型")
    private String tmkType;

    @ApiModelProperty(value = "付费模式")
    @ExcelTitleMap(title = "付费模式")
    private String memberPayMode;
    
    @ApiModelProperty("话务员")
    @ExcelTitleMap(title = "话务员")
    private String staffId;

    @ApiModelProperty("话务组")
    @ExcelTitleMap(title = "话务组")
    private String groupCode;
    
    @ApiModelProperty("联系结果")
    @ExcelTitleMap(title = "联系结果")
    private String resultCode;
    
    @ApiModelProperty("电话小结")
    @ExcelTitleMap(title = "电话小结")
    private String summaryContent;
    
    @ApiModelProperty("备注")
    @ExcelTitleMap(title = "备注")
    private String comment;
    
    @ApiModelProperty("小结时间")
    @ExcelTitleMap(title = "小结时间")
    private Date summaryAt;
    
    @ApiModelProperty("订单时间")
    @ExcelTitleMap(title = "订单时间")
    private Date orderdAt;
    
    @ApiModelProperty("订单状态")
    @ExcelTitleMap(title = "订单状态")
    private String orderdStatus;
    
    @ApiModelProperty("支付失败原因")
    @ExcelTitleMap(title = "支付失败原因")
    private String repayMessage;
    
    @ApiModelProperty("创建时间")
    @ExcelTitleMap(title = "创建时间")
    private Date gmtCreate;
    
    @ApiModelProperty("金额")
    @ExcelTitleMap(title = "金额")
    private String paymentAmount;

    @ApiModelProperty("推送状态")
    @ExcelTitleMap(title = "推送状态")
    private String pushStatus;

    @ApiModelProperty("电话开始时间")
    @ExcelTitleMap(title = "电话开始时间")
    private Date callStartTime;

    @ApiModelProperty("电话结束时间")
    @ExcelTitleMap(title = "电话结束时间")
    private Date callEndTime;

    @ApiModelProperty("通话时长(s)")
    @ExcelTitleMap(title = "通话时长(s)")
    private Long callTime;
}
