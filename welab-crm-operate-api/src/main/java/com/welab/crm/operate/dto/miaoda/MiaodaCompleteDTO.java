package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 喵达投诉单结案请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单结案请求对象")
public class MiaodaCompleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单号
     */
    @ApiModelProperty(value = "投诉单号", name = "sn", required = true)
    @NotBlank(message = "投诉单号不能为空")
    private String sn;

    /**
     * 结案类型 1-已与用户沟通并达成一致 2-联系不上用户 3-最终解决方案
     */
    @ApiModelProperty(value = "结案类型", name = "reason", required = true)
    @NotNull(message = "结案类型不能为空")
    private Integer reason;

    /**
     * 所提供的解决方案细节
     */
    @ApiModelProperty(value = "解决方案细节", name = "solution", required = true)
    @NotBlank(message = "解决方案细节不能为空")
    private String solution;

    /**
     * 上传的附件是否隐藏 0-不隐藏 1-隐藏
     */
    @ApiModelProperty(value = "是否隐藏附件", name = "hideAttach")
    private Integer hideAttach = 0;

    /**
     * 上传图片的网络地址列表
     */
    @ApiModelProperty(value = "图片地址列表", name = "images")
    private List<String> images;

    /**
     * 上传视频的网络地址列表
     */
    @ApiModelProperty(value = "视频地址列表", name = "videos")
    private List<String> videos;
}
