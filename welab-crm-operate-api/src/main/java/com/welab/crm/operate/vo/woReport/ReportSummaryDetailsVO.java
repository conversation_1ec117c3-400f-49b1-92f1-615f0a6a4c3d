package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/24
 */
@Data
@ApiModel(value = "工单统计报表汇总子分类详情响应对象")
public class ReportSummaryDetailsVO implements Serializable {

    private static final long serialVersionUID = -5689132927456177980L;

    @ApiModelProperty(value = "类型")
    @ExcelTitleMap(title = "类型")
    private String type;

    @ApiModelProperty(value = "数量")
    @ExcelTitleMap(title = "数量")
    private Integer number;

    @ApiModelProperty(value = "占比")
    @ExcelTitleMap(title = "占比")
    private String rate;

    @ApiModelProperty(value = "结案量")
    @ExcelTitleMap(title = "结案量")
    private Integer closedNumber;

    @ApiModelProperty(value = "结案率")
    @ExcelTitleMap(title = "结案率")
    private String closedRate;
}
