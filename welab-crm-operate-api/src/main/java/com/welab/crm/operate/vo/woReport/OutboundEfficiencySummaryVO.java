package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/6
 */
@Data
@ApiModel(value = "外呼效能报表接口响应对象")
@Excel(fileName = "外呼效能统计报表", sheetName = "外呼效能统计报表")
public class OutboundEfficiencySummaryVO implements Serializable {

    private static final long serialVersionUID = -7920665167787634296L;

    @ApiModelProperty(value = "组别")
    @ExcelTitleMap(title = "组别")
    private String groupName;

    @ApiModelProperty(value = "工号")
    @ExcelTitleMap(title = "工号")
    private String idNo;

    @ApiModelProperty(value = "姓名")
    @ExcelTitleMap(title = "姓名")
    private String staffName;

    @ApiModelProperty(value = "人均外呼量")
    @ExcelTitleMap(title = "人均外呼量")
    private Integer callCount = 0;

    @ApiModelProperty(value = "平均通话时长")
    @ExcelTitleMap(title = "平均通话时长")
    private String avgCallDuration = "00:00:00";

    @ApiModelProperty(value = "话后处理总时长")
    @ExcelTitleMap(title = "话后处理总时长")
    private String wrapUpDuration = "00:00:00";

    @ApiModelProperty(value = "置忙总时长")
    @ExcelTitleMap(title = "置忙总时长")
    private String pauseTime = "00:00:00";

    @ApiModelProperty(value = "小休总时长")
    @ExcelTitleMap(title = "小休总时长")
    private String restTime = "00:00:00";

    @ApiModelProperty(value = "总登录时长")
    @ExcelTitleMap(title = "总登录时长")
    private String loginTime = "00:00:00";

    @ApiModelProperty(value = "工时利用率")
    @ExcelTitleMap(title = "工时利用率")
    private String workingHourUtilization = "0.00%";

    @ApiModelProperty(value = "通话利用率")
    @ExcelTitleMap(title = "通话利用率")
    private String callUtilization = "0.00%";

    @ApiModelProperty(value = "空闲总时长")
    @ExcelTitleMap(title = "空闲总时长")
    private String idleTime = "00:00:00";

    @ApiModelProperty(value = "接通量")
    @ExcelTitleMap(title = "接通量")
    private Integer connectedNumber = 0;

    @ApiModelProperty(value = "接通率")
    @ExcelTitleMap(title = "接通率")
    private String connectedRate = "0.00%";

    @ApiModelProperty(value = "未结案量")
    @ExcelTitleMap(title = "未结案量")
    private Integer unclosedNumber = 0;

    @ApiModelProperty(value = "结案量")
    @ExcelTitleMap(title = "结案量")
    private Integer closedNumber = 0;

    @ApiModelProperty(value = "质检分数,质检报表没做，因此没有数据")
    @ExcelTitleMap(title = "质检分数")
    private String qualityScore;

    @ApiModelProperty(value = "质检合格率,质检报表没做，因此没有数据")
    @ExcelTitleMap(title = "质检合格率")
    private String qualifiedRate;

}
