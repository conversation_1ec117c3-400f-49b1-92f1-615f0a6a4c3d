package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.workorder.WorkOrderTypeReqDTO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeVO;

/**
 * 工单分类服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface WorkOrderTypeService {

	/**
     * 分页查询工单分类列表
     * @param reqDTO
     * @return
     */
    Page<WorkOrderTypeVO> queryWorkOrderTypeList(WorkOrderTypeReqDTO reqDTO);

    /**
     * 新增保存
     * @param reqDTO
     */
    void addWorkOrderType(WorkOrderTypeReqDTO reqDTO);

    /**
     * 更新工单分类
     * @param reqDTO
     */
    void updateWorkOrderType(WorkOrderTypeReqDTO reqDTO);
}
