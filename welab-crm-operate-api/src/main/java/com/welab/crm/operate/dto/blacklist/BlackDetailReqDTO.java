package com.welab.crm.operate.dto.blacklist;

import com.welab.crm.interview.dto.PageQueryDTO;
import com.welab.crm.operate.dto.BaseReqDTO;
import lombok.Data;

import java.util.List;

@Data
public class BlackDetailReqDTO extends PageQueryDTO {

    /**
     * 开始时间
     */
    private String startTime;


    /**
     * 结束时间
     */
    private String endTime;


    /**
     * 组别列表
     */
    private List<String> groupCode;


    /**
     * 催收员id列表
     */
    private List<Long> staffId;


    /**
     * 环比开始时间
     */
    private String roundStartTime;


    /**
     * 环比结束时间
     */
    private String roundEndTime;
}
