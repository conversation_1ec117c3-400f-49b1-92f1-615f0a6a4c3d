package com.welab.crm.operate.dto.settlement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/18
 */
@Getter
@Setter
@Accessors(chain = true)
public class SettleProofApplyRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 贷款号
     */
    @ApiModelProperty(value=" 贷款号", name="applicationId",example = "111")
    private String applicationId;

}
