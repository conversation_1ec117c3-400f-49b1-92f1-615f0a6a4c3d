package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/4
 */
@Data
@ApiModel(value = "分单统计报表响应对象")
@Excel(fileName = "分单统计报表", sheetName = "分单统计报表")
public class ReportAssignmentSummaryVO implements Serializable {

    private static final long serialVersionUID = 5931568368323710667L;

    @ApiModelProperty(value = "分单类型")
    @ExcelTitleMap(title = "分单类型")
    private String assignType;

    @ApiModelProperty(value = "规则名称")
    @ExcelTitleMap(title = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "分配组")
    @ExcelTitleMap(title = "分配组")
    private String groupName;

    @ApiModelProperty(value = "分配人")
    @ExcelTitleMap(title = "分配人")
    private String staffName;

    @ApiModelProperty(value = "合计量")
    @ExcelTitleMap(title = "合计量")
    private Integer count;

}
