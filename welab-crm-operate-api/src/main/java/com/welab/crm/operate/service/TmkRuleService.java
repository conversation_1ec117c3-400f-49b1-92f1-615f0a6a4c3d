package com.welab.crm.operate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.tmkRule.TmkInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkRuleAdjustReqDTO;
import com.welab.crm.operate.vo.tmkRule.TmkInfoVO;
import com.welab.crm.operate.vo.tmkRule.TypeTotalTmkInfoVO;

/**
 * 电销数据分配规则服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface TmkRuleService {

	/**
     * 电销数据查询列表
     * @param reqDTO
     * @return
     */
    Page<TmkInfoVO> queryTmkInfoList(TmkInfoReqDTO reqDTO);
    
    /**
     * 统计电销类型数量
     * @param reqDTO
     * @return
     */
    List<TypeTotalTmkInfoVO> typeTotalTmkInfo(TmkInfoReqDTO reqDTO);

    /**
     * 手动调剂数据分配
     * @param reqDTO
     */
    void adjustTmkRule(TmkRuleAdjustReqDTO reqDTO);
}
