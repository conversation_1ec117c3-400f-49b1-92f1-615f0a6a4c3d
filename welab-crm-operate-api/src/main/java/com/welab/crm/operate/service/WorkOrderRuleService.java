package com.welab.crm.operate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.workorder.OpOrderConfigDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleAdjustReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderRuleReqDTO;
import com.welab.crm.operate.vo.workorder.OpOrderConfigVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleAdjustVO;
import com.welab.crm.operate.vo.workorder.WorkOrderRuleVO;
import com.welab.crm.operate.vo.workorder.WorkOrderTypeTotalVO;

/**
 *  规则服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface WorkOrderRuleService {

	/**
     * 分单规则任务列表
     * @param reqDTO
     * @return
     */
    Page<WorkOrderRuleVO> queryWorkOrderRuleList(WorkOrderRuleReqDTO reqDTO);

    /**
     * 新增分单规则
     * @param reqDTO
     */
    void addWorkOrderRule(WorkOrderRuleReqDTO reqDTO);

    /**
     * 更新分单规则
     * @param reqDTO
     */
    void updateWorkOrderRule(WorkOrderRuleReqDTO reqDTO);
    
    /**
     * 分单规则总开关
     * @param reqDTO
     */
    void updateWorkOrderRuleAll(WorkOrderRuleReqDTO reqDTO);
    
    /**  
	 * 删除分单规则
	 * @param reqDTO
	 * @return  
	 */
	public boolean deleteWorkOrderRule(BatchInfoReqDTO reqDTO);
	
	/**
     * 分单规则调剂详单列表
     * @param reqDTO
     * @return
     */
    Page<WorkOrderRuleAdjustVO> queryWorkOrderRuleAdjustList(WorkOrderRuleAdjustReqDTO reqDTO);
    
    /**
     * 确认分配
     * @param reqDTO
     * @return
     */
    void adjustWorkOrderRule(WorkOrderRuleAdjustReqDTO reqDTO);
    
    /**
     * 手动领取配置查询
     * @param reqDTO
     * @return
     */
    OpOrderConfigVO queryOrderRuleConfig();
    
    /**
     * 手动领取配置
     * @param reqDTO
     * @return
     */
    void updateOrderRuleConfig(OpOrderConfigDTO reqDTO);
    
    /**
     * 统计工单类型数量
     * @param reqDTO
     * @return
     */
    List<WorkOrderTypeTotalVO> typeTotalWorkOrder();
}
