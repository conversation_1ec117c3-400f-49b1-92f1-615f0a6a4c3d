package com.welab.crm.operate.vo.tmkReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 员工效能报表实时
 * @date 2022/6/1 16:48
 */
@Data
@ApiModel("员工效能报表实时")
@Excel(fileName = "员工实时效能报表")
public class StaffEfficiencyVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组")
    @ExcelTitleMap(title = "分组")
    private String groupName;

    @ApiModelProperty(value = "坐席号")
    @ExcelTitleMap(title = "坐席号")
    private String cno;


    @ApiModelProperty(value = "姓名")
    @ExcelTitleMap(title = "姓名")
    private String staffName;

    @ApiModelProperty(value = "呼出平均通话均长(s)")
    @ExcelTitleMap(title = "呼出平均通话均长(s)")
    private String avgCalloutTime;

    @ApiModelProperty(value = "话后处理时长")
    @ExcelTitleMap(title = "话后处理时长")
    private String acwTime;

    @ApiModelProperty(value = "置忙总时长")
    @ExcelTitleMap(title = "置忙总时长")
    private String busyTime;

    @ApiModelProperty(value = "小休时长")
    @ExcelTitleMap(title = "小休时长")
    private String breakTime;


    @ApiModelProperty(value = "工时利用率")
    @ExcelTitleMap(title = "工时利用率")
    private String workTimeRate;

    @ApiModelProperty(value = "通话利用率")
    @ExcelTitleMap(title = "通话利用率")
    private String callTimeRate;

    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private String callTime;

    @ApiModelProperty(value = "人均外呼量")
    @ExcelTitleMap(title = "人均外呼量")
    private String avgCalloutCount;

    @ApiModelProperty(value = "人均接通量")
    @ExcelTitleMap(title = "人均接通量")
    private String avgBridgeCount;

    @ApiModelProperty(value = "接通率")
    @ExcelTitleMap(title = "接通率")
    private String bridgeRate;

    @ApiModelProperty(value = "登陆时长")
    @ExcelTitleMap(title = "登陆时长")
    private String loginTime;

    /**
     * 呼入电话时间
     */
    private String ibTime;







}
