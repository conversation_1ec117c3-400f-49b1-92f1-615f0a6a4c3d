package com.welab.crm.operate.dto.bankcard;

import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/19
 */
@Data
@ApiModel(value = "银行卡换卡请求对象")
public class BankCardChangeDTO extends HistoryOperationDTO {

    @ApiModelProperty(value = "银行卡id")
    @NotBlank
    private String bankCardId;
}
