package com.welab.crm.operate.dto.telemarketing;

import com.welab.crm.interview.dto.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: TmkReportReqDTO
 * @Description: 电销报表请求体
 * @Copyright: © 2020 ***
 * @Company: ***有限公司
 * @date 2022/3/14 14:09
 */

@Data
@ApiModel(value = "电销报表请求体")
public class TmkReportBaseReqDTO extends PageQueryDTO {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private String endTime;


    @ApiModelProperty(value = "话务组多个用逗号隔开")
    private String groupCode;

    @ApiModelProperty(value = "话务员多个用逗号隔开")
    private String staffId;

    /**
     * 坐席号，多个用逗号隔开，前端不用传
     */
    private String cnos;

    /**
     * staffId列表，前端不用传
     */
    private List<String> staffIdList;

    /**
     * 组列表，前端不用传
     */
    private List<String> groupCodeList;

    @ApiModelProperty(value = "规则id")
    private Long ruleId;
}
