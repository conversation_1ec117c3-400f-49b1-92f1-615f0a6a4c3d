package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintDetailDTO;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintQueryDTO;
import com.welab.crm.operate.dto.externalCompalaint.ReminderDTO;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;
import com.welab.crm.operate.vo.workorder.ExternalComplaintListVO;
import com.welab.crm.operate.vo.workorder.OrderContactVO;
import com.welab.crm.operate.vo.workorder.WorkOrderLogVO;

import java.util.List;

public interface ExternalComplaintService {
	void addExternalComplaint(ExternalComplaintDetailDTO externalComplaintDetailDTO);

	ExternalComplaintDetailDTO queryNameByOrderNo(String channelOrderNo, String debtOrderNo);
	
	Page<ExternalComplaintListVO> queryPage(ExternalComplaintQueryDTO dto);

	ExternalComplaintDetailDTO queryDetail(Long id, String loginName);
	
	
	List<ExternalComplaintListVO> queryList(ExternalComplaintQueryDTO dto);
	/**
	 * 获取快速工单选项
	 *
	 * @param userType 用户类型
	 * @return 快速下单选项列表
	 */
	List<DictInfoConfResVO> getQuickOrderOption(String userType);

	/**
	 * 查询工单联系记录
	 * @param orderNo
	 * @return
	 */
	List<OrderContactVO> queryOrderContactList(Long id, String loginName);

	/**
	 * 查询工单流水
	 * @param id
	 * @param loginName
	 * @return
	 */
	List<WorkOrderLogVO> queryOrderLogList(Long id, String loginName);


	/**
	 * 催单
	 * @param reminderDTO
	 * @param mobile
	 */
	void reminderOrder(ReminderDTO reminderDTO, String loginName);

	/**
	 * 更新工单内容
	 * @param dto
	 * @param name
	 */
	void updateOrder(ExternalComplaintDetailDTO dto, String name);

	/**
	 * 校验工单所属
	 *
	 * @param loginName
	 * @param id
	 * @return
	 */
	String checkOrderNo(String loginName, Long id);
}
	