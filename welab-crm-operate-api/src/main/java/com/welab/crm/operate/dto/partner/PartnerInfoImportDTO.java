package com.welab.crm.operate.dto.partner;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 资金方信息导入对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "资金方信息导入对象")
public class PartnerInfoImportDTO {

    /**
     * 主键
     */
    @ExcelIgnore
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 机构号
     */
    @ExcelProperty(value = "机构号")
    @ApiModelProperty(value = "机构号")
    private String institutionNumber;

    /**
     * 资金方名称
     */
    @ExcelProperty(value = "资金方名称")
    @ApiModelProperty(value = "资金方名称")
    private String partnerName;
    

    /**
     * 放款卡
     */
    @ExcelProperty(value = "放款卡")
    @ApiModelProperty(value = "放款卡")
    private String loanCard;

    /**
     * 系统批扣时间-还款日
     */
    @ExcelProperty(value = "系统批扣时间")
    @ApiModelProperty(value = "系统批扣时间")
    private String systemRepayTimeNormal;

    /**
     * 宽限期
     */
    @ExcelProperty(value = "宽限期")
    @ApiModelProperty(value = "宽限期")
    private String gracePeriod;

    /**
     * h5还款
     */
    @ExcelProperty(value = "H5还款")
    @ApiModelProperty(value = "H5还款")
    private String h5Repay;

    /**
     * 一期一期提前还款/账单展示
     */
    @ExcelProperty(value = "一期一期提前还款/账单展示")
    @ApiModelProperty(value = "一期一期提前还款/账单展示")
    private String earlyRepayEachInstallment;

    /**
     * 提前结清说明
     */
    @ExcelProperty(value = "提前结清说明")
    @ApiModelProperty(value = "提前结清说明")
    private String earlySettlementMsg;

    /**
     * 结清证明
     */
    @ExcelProperty(value = "结清证明")
    @ApiModelProperty(value = "结清证明")
    private String settlementProof;

    @ExcelProperty(value = "关闭征信授信额度")
    @ApiModelProperty(value = "关闭征信授信额度")
    private String quotaStatus;
    
    @ExcelProperty(value = "发票开具")
    @ApiModelProperty(value = "发票开具")
    private String invoiceIssue;

    @ExcelProperty(value = "结清获取")
    @ApiModelProperty(value = "结清获取")
    private String closeGet;

    @ExcelProperty(value = "发票获取")
    @ApiModelProperty(value = "发票获取")
    private String invoiceGet;

    @ExcelProperty(value = "关闭授信")
    @ApiModelProperty(value = "关闭授信")
    private String closeQuota;

    @ExcelProperty(value = "发送标识")
    @ApiModelProperty(value = "发送标识 Y/N")
    private String sendStatus;
}
