package com.welab.crm.operate.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class AIPushConfigDTO {

    /**
     * 外呼配置主键id
     */
    @ApiModelProperty(value = "外呼配置主键id", notes = "更新或者删除必须传递此值")
    private Long id;

    /**
     * 产品名称(多个产品之间用,相隔)
     */
    @ApiModelProperty(value = "产品名称(多个产品之间用,相隔)")
    private String productNames;

    /**
     * 进件渠道号(多个渠道号之间用,相隔)
     */
    @ApiModelProperty(value = "进件渠道号(多个渠道号之间用,相隔)")
    private String loanChannels;
    
    /**
     * 标签编码(多个标签编码之间用,相隔)
     */
    @ApiModelProperty(value = "标签编码(多个标签编码之间用,相隔)")
    private String labelCode;

    /**
     * 推送渠道代码
     */
    @ApiModelProperty(value = "推送渠道代码")
    @NotBlank(message = "推送渠道代码不能为空")
    private String caCompany;

    /**
     * ai话术id
     */
    @ApiModelProperty(value = "ai话术id")
    @NotNull(message = "ai话术id不能为空")
    private Integer speechId;

    /**
     * ai话术对应的任务id
     */
    @ApiModelProperty(value = "ai话术对应的任务id")
    @NotNull(message = "ai话术对应的任务id不能为空")
    private Integer taskId;

    /**
     * 推送方式(预留此字段): 0-按时间
     */
    @ApiModelProperty(value = "推送方式(预留此字段): 0-按时间, 固定传0")
    private Integer pushWay;

    /**
     * 推送方式对应的数量值，当按时间时此值表示小时数，当按数量时表示个数
     */
    @ApiModelProperty(value = "推送方式对应的数量值")
    @NotNull(message = "数量值不能为空")
    private Integer number;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 推送开始时间节点
     */
    @ApiModelProperty(value = "推送开始时间节点,例如 09:00")
    @NotBlank(message = "推送开始时间节点不能为空")
    private String startTime;

    /**
     * 推送结束时间节点
     */
    @ApiModelProperty(value = "推送结束时间节点,例如 19:00")
    @NotBlank(message = "推送结束时间节点不能为空")
    private String endTime;

    /**
     * 是否分配 0:未分配；1,2：已分配
     */
    @ApiModelProperty(value = "是否分配")
    @NotBlank(message = "是否分配必选")
    private String flag;

    /**
     * 金额最小值
     */
    @ApiModelProperty(value = "金额最小值")
    private String minAmount;

    /**
     * 金额最大值
     */
    @ApiModelProperty(value = "金额最大值")
    private String maxAmount;

    /**
     * 审批时段
     */
    @ApiModelProperty(value = "审批时段")
    private String approvedAt;
}
