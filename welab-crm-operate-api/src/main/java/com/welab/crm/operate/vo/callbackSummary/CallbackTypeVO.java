package com.welab.crm.operate.vo.callbackSummary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 回电小结类型对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "回电小结类型对象")
public class CallbackTypeVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * "该字段有多个含义,如果是回电结果则该字段表示最多勾选几个.其他时候表示是否需要备注,1-需要备注,0-不需要"
     */
    @ApiModelProperty(value = "该字段有多个含义,如果是回电结果则该字段表示最多勾选几个.其他时候表示是否需要备注,1-需要备注,0-不需要")
    private Integer remark;

    public String concatStr(){
        return name + remark;
    }
}
