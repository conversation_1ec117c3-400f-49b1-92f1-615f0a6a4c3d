package com.welab.crm.operate.dto.tmkRule;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单调剂请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "电销需要分配请求对象")
public class TmkRuleAdjustInfo extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	/**
     * 电销唯一ID
     */
	@ApiModelProperty(value = "电销唯一ID", name = "tmkTaskId")
    private String tmkTaskId;

    /**
     * 电销类型
     */
	@ApiModelProperty(value = "电销类型", name = "tmkType")
    private String tmkType;
	
	 /**
     * 用户名
     */
    private String customerName;

    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 合同号
     */
    private String applicationId;
    
    /**
     * 原来分单组
     */
    private String groupCode;

    /**
     * 原来分单人id
     */
    private String staffId;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * uuid
     */
    private String uuid;


    private String productName;

    private String applyOrigin;


}
