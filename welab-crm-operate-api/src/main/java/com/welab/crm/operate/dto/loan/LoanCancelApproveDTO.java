package com.welab.crm.operate.dto.loan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class LoanCancelApproveDTO {

    /**
     * 将要被审批的贷款号列表
     */
    @ApiModelProperty(value = "贷款号列表")
    @NotEmpty
    private List<String> contractNoList;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态", example = "2 (审批状态说明: 1-审批通过,2-审批拒绝)")
    @NotNull(message = "审批状态不能为空")
    private Integer approveState;

    /**
     * 审批说明
     */
    @ApiModelProperty(value = "审批说明")
    private String remark;
}
