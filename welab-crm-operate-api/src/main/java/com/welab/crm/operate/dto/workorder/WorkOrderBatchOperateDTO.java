package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/21
 */
@Data
@ApiModel(value = "工单批量操作请求对象")
public class WorkOrderBatchOperateDTO implements Serializable {

    private static final long serialVersionUID = -3460596751890482244L;

    @ApiModelProperty(value = "流程实例id列表", name = "executionIds")
    @NotNull
    private List<String> executionIds;
}
