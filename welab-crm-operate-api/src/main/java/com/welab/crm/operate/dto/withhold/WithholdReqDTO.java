package com.welab.crm.operate.dto.withhold;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 代扣查询对象
 * @date 2022/4/21 15:11
 */

@Data
@ApiModel("代扣查询对象")
public class WithholdReqDTO extends BaseReqDTO {

    private static final Long serialVersionUID = 123L;

    @ApiModelProperty(value = "开始时间", name = "startTime")
    private String startTime;

    @ApiModelProperty(value = "结束时间", name = "endTime")
    private String endTime;

    @ApiModelProperty(value = "组编码，多选用 , 隔开", name = "groupCode")
    private String groupCode;

    private List<String> groupCodeList;

    @ApiModelProperty(value = "人员Id，多选用 ，隔开", name = "staffId")
    private String staffId;

    private List<Long> staffIdList;

    @ApiModelProperty(value = "贷款号", name = "applicationId")
    private String applicationId;

    @ApiModelProperty(value = "代扣模式；YD:按期还款；YH:还清所有逾期", name = "repaymentMode")
    private String repaymentMode;

    @ApiModelProperty(value = "代扣结果,success,fail,processing", name = "result")
    private String result;

    @ApiModelProperty(value = "客服工号", name = "cno")
    private String cno;

    @ApiModelProperty(value = "查询类型 如果是从客服界面贷款详情信息里面查询的则赋值为 D，在协催界面查询则不用赋值", name = "queryType")
    private String queryType;

    @ApiModelProperty(value = "是否钱包", name = "1 是，0 否")
    private Integer isWallet;

}
