package com.welab.crm.operate.vo.loginReport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "登录报表详情")
public class LoginReportDetailVO {
	
	@ApiModelProperty(value = "主键")
	@ExcelIgnore
	private Long id;
	
	@ApiModelProperty(value = "座席姓名")
	@ExcelProperty(value = "座席姓名")
	private String staffName;
	
	@ApiModelProperty(value = "座席工号")
	@ExcelProperty(value = "座席工号")
	private String cno;
	
	@ApiModelProperty(value = "座席组")
	@ExcelProperty(value = "座席组")
	private String groupName;
	
	@ApiModelProperty(value = "登录方式")
	@ExcelProperty(value = "登录方式")
	private String loginType;
	
	@ApiModelProperty(value = "登录时间")
	@ExcelProperty(value = "验证时间")
	private String loginTime;
	
	@ApiModelProperty(value = "登录结果")
	@ExcelProperty(value = "验证状态")
	private String loginResult;


	public void translateLoginType() {
		if ("code".equals(loginType)) {
			this.loginType = "验证码";
		} else if ("face".equals(loginType)) {
			this.loginType = "人脸";
		}
	}
	
	public void translateLoginResult() {
		if ("success".equals(loginResult)) {
			this.loginResult = "成功";
		} else if ("fail".equals(loginResult)) {
			this.loginResult = "失败";
		}
	}
	
	
}
