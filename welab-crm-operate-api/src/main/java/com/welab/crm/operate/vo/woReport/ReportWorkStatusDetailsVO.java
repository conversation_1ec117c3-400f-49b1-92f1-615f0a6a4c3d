package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/10
 */
@Data
@ApiModel(value = "工作状态详情响应对象")
public class ReportWorkStatusDetailsVO implements Serializable {

    private static final long serialVersionUID = 8015702042477914779L;

    @ApiModelProperty(value = "开始时间")
    @ExcelTitleMap(title = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @ExcelTitleMap(title = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "持续时间，单位：秒")
    @ExcelTitleMap(title = "持续时间(秒)")
    private Integer duration;
}
