package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class AuditBatchInfoReqDTO implements Serializable  {

	private static final long serialVersionUID = -1669269247621738895L;
	@ApiModelProperty(value="配置id", name="ids")
	private List<Long> ids;

	/**
	 * @see com.welab.crm.operate.enums.ApproveStateEnum
	 */
	private Integer status;
}
