package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/5
 */
@Data
@ApiModel(value = "分单统计报表请求对象")
public class ReportAssignmentSummaryDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = 1061571755885433983L;

    @ApiModelProperty(value = "分单类型code")
    private String assignType;

    @ApiModelProperty(value = "规则id")
    private String ruleId;
}
