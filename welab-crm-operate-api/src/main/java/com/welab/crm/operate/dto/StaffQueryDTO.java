package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "StaffQueryDTO", description = "员工查询DTO")
public class StaffQueryDTO extends BaseReqDTO{
	
	@ApiModelProperty(value = "开始时间")
	@NotBlank(message = "开始时间不能为空")
	private String startTime;
	
	@ApiModelProperty(value = "结束时间")
	@NotBlank(message = "结束时间不能为空")
	private String endTime;
	
	@ApiModelProperty(value = "员工组别列表")
	private List<String> groupCodes;
	
	@ApiModelProperty(value = "员工姓名列表")
	private List<String> staffNames;
}
