package com.welab.crm.operate.vo.transferReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 月度债转报表返回结果
 */
@Data
@ApiModel(description = "月度债转报表返回结果")
public class MonthTransferReportVO {
	
	
	@ApiModelProperty(value = "债转公司")
	@ExcelProperty(value = "债转公司")
	private String transferCompany;
	
	@ApiModelProperty(value = "月份1")
	@ExcelProperty
	private String month1;

	@ApiModelProperty(value = "月份2")
	@ExcelProperty
	private String month2;
	
	@ApiModelProperty(value = "月份3")
	@ExcelProperty
	private String month3;
	
	
	@ApiModelProperty(value = "月份4")
	@ExcelProperty
	private String month4;
	
	
	@ApiModelProperty(value = "月份5")
	@ExcelProperty
	private String month5;
	
	@ApiModelProperty(value = "月份6")
	@ExcelProperty
	private String month6;
	
	
	@ApiModelProperty(value = "月份7")
	@ExcelProperty
	private String month7;
	
	
	@ApiModelProperty(value = "月份8")
	@ExcelProperty
	private String month8;
	
	@ApiModelProperty(value = "月份9")
	@ExcelProperty
	private String month9;
	
	@ApiModelProperty(value = "月份10")
	@ExcelProperty
	private String month10;
	
	@ApiModelProperty(value = "月份11")
	@ExcelProperty
	private String month11;
	
	@ApiModelProperty(value = "月份12")
	@ExcelProperty
	private String month12;
	
	@ApiModelProperty(value = "合计")
	@ExcelProperty
	private String total;
}
