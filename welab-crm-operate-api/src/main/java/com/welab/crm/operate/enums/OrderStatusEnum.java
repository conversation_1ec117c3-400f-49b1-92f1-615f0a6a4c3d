package com.welab.crm.operate.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单状态
 * @date 2021/9/30
 */
public enum OrderStatusEnum {
	SUBMIT("submit", "提交"),
    SAVE("save", "保存"),
    REVOKE("revoke", "撤回"),
    PENDING("pending", "待处理"),
    PROCESS("process", "处理中"),
    FLOW("flow", "流转中"),
    RETURN("return", "退回"),
    EXPIRE("expire", "已过期"),
    CLOSE("close", "结案"),
    WASTE("waste", "废单");

    private String value;
    private String text;

    private OrderStatusEnum(String value, String text) {
        this.value = value; 
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public static String getState(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        for (OrderStatusEnum state : values()) {
            if (state.value.equals(value)) {
                return state.getText();
            }
        }
        return null;
    }
}
