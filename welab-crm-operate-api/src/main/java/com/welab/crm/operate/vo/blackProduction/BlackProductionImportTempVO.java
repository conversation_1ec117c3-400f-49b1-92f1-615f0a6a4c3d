package com.welab.crm.operate.vo.blackProduction;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Data;

/**
 * 黑产导入模板
 * <AUTHOR> 
 */
@Data
@Excel(fileName = "黑产导入模板", sheetName = "黑产导入模板列表")
public class BlackProductionImportTempVO {
	
	@ExcelTitleMap(title = "uuid", require = false)
	private String uuid;
	
	@ExcelTitleMap(title = "黑产手机号码*")
	private String blackProductionMobile;
	
	@ExcelTitleMap(title = "黑产邮箱号", require = false)
	private String blackProductionEmail;
	
	@ExcelTitleMap(title = "用户类型*")
	private String userType;
}
