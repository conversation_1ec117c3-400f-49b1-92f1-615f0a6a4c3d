package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单类型统计个数请求对象
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单类型统计个数请求对象")
public class WorkOrderTypeTotalVO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	@ApiModelProperty(value = "工单个数", name = "totalCount")
    private Integer totalCount;
	
	@ApiModelProperty(value = "工单类型", name = "orderType")
    private String orderType;
}
