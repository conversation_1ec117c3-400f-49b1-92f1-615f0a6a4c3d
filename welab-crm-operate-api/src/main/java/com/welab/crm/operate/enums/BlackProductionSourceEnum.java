package com.welab.crm.operate.enums;

/**
 * 黑产来源枚举
 * <AUTHOR>
 */
public enum BlackProductionSourceEnum {
	AIF(1,"AIF联盟"),
	CS(2,"客服系统"),
	;

	private Integer code;
	
	private String desc;

	BlackProductionSourceEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public Integer getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
	
	public static String getDescByCode(Integer code){
		for (BlackProductionSourceEnum userTypeEnum : values()) {
			if (userTypeEnum.code.equals(code)){
				return userTypeEnum.desc;
			}
		}
		return code + "";
	}
}
