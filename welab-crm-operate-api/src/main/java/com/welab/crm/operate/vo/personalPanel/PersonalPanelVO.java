package com.welab.crm.operate.vo.personalPanel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 个人看板返回对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "个人看板返回对象")
public class PersonalPanelVO{

    @ApiModelProperty(value = "个人看板总体数据")
    private OverAllData overAllData;

    @ApiModelProperty(value = "工时利用率数据")
    private HourUtilisation hourUtilisation;

    @ApiModelProperty(value = "个人效能数据")
    private IndividualEffectiveness individualEffectiveness;
    

}
