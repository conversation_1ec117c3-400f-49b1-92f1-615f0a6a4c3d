package com.welab.crm.operate.dto.reduce;

import com.welab.crm.operate.vo.reduce.ReduceApprovalLog;
import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import lombok.Data;

import java.util.List;

/**
 * 减免、退款申请提交对象
 */
@Data
public class ReduceAndRefundApplySubmitDTO {

	/**
	 * 减免申请列表
	 */
	private List<ReduceApplyDTO> reduceApplyList;


	/**
	 * 退款申请列表
	 */
	private List<RefundApplyDTO> refundApplyList;


	/**
	 * 处理日志(提交审批的时候不需要传)
	 */
	private List<ReduceApprovalLog> logs; 


	/**
	 * 处理意见
	 */
	private String opinion;


	/**
	 * 减免原因
	 */
	private String reduceReason;


	/**
	 * 投诉渠道
	 */
	private String complaintChannel;
	
	
	private List<WoAttachmentVO> attachmentList;
}
