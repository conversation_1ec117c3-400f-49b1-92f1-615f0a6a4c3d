package com.welab.crm.operate.vo.phone;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 总体呼入数据
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CallInfoTotalData {

	/**
	 * 总来电数
	 */
	private Integer totalCount;


	/**
	 * 转人工服务数
	 */
	private Integer toQueueCount;

	/**
	 * 人工接起数
	 */
	private Integer answerCount;


	/**
	 * 接通率
	 */
	private String answerRate;

	
}
