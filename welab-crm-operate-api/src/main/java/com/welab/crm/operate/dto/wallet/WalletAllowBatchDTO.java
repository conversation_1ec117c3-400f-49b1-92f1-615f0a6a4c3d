package com.welab.crm.operate.dto.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/29
 */
@Data
@ApiModel(value = "钱包批量开启提前结清请求对象")
public class WalletAllowBatchDTO implements Serializable {

    @ApiModelProperty(value = "用户id")
    @NotNull
    private Integer userId;

    @ApiModelProperty(value = "贷款号列表")
    @NotNull
    private List<WalletEarlySettleDTO> applicationIdList;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "结清原因")
    private String reason;
}
