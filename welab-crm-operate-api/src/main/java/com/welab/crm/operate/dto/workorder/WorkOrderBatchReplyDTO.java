package com.welab.crm.operate.dto.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/23
 */
@Data
@ApiModel(value = "工单批量回复意见工单信息请求对象")
public class WorkOrderBatchReplyDTO implements Serializable {

    private static final long serialVersionUID = -2879511167146193543L;

    @ApiModelProperty(value = "工单主键ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID", name = "executionId")
    private String executionId;

    @ApiModelProperty(value = "当前任务节点", name = "nodeCode")
    private String nodeCode;
}
