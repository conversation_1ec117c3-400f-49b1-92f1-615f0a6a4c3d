package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "客户重复来电总量报表返回对象")
@Excel(fileName = "客户重复来电总量报表")
public class ReportDupCallTotalVO {

    /**
     * 来电日期,可能是个区间段,取决于用户选择的时间间隔
     */
    @ApiModelProperty(value = "时间")
    @ExcelTitleMap(title = "时间")
    private String callDay;

    /**
     * 热线号码
     */
    @ApiModelProperty(value = "热线号码")
    @ExcelTitleMap(title = "热线号码")
    private String hotline;

    /**
     * 总来电量
     */
    @ApiModelProperty(value = "总来电量")
    @ExcelTitleMap(title = "总来电量")
    private Integer callQuantities;

    /**
     * 真实客户数
     */
    @ApiModelProperty(value = "真实客户数")
    @ExcelTitleMap(title = "真实客户数")
    private Integer realCustomers;

    /**
     * 总转人工服务数
     */
    @ApiModelProperty(value = "总转人工服务数")
    @ExcelTitleMap(title = "总转人工服务数")
    private Integer manServiceCounts;

    /**
     * 转人工真实客户数
     */
    @ApiModelProperty(value = "转人工真实客户数")
    @ExcelTitleMap(title = "转人工真实客户数")
    private Integer manRealCounts;

    /**
     * 2小时内重复进线量
     */
    @ApiModelProperty(value = "2小时内重复进线量")
    @ExcelTitleMap(title = "2小时内重复进线量")
    private Integer duplicateCalls;

    /**
     * 客户来电号码
     */
    private String customerPhone;
}
