package com.welab.crm.operate.enums;

/**
 * 审批状态枚举类
 */
public enum DepartmentEnum {

	KEFU("kefu", "客服"),
	CUISHOU("cuishou", "催收"),
	;

	private final String value;
	private final String text;

	DepartmentEnum(String value, String text) {
		this.value = value;
		this.text = text;
	}

	public String getValue() {
		return value;
	}

	public String getText() {
		return text;
	}

	public static String getTextByValue(String value) {
		for (DepartmentEnum departmentEnum : DepartmentEnum.values()) {
			if (departmentEnum.getValue().equals(value)) {
				return departmentEnum.getText();
			}
		}

		return value;
	}
}
