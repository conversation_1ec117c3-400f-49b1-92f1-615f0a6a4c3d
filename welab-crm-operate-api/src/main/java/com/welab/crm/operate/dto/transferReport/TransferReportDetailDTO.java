package com.welab.crm.operate.dto.transferReport;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class TransferReportDetailDTO extends BaseReqDTO {

	@ApiModelProperty(value = "开始时间")
	@NotBlank
	private String startTime;

	@ApiModelProperty(value = "结束时间")
	@NotBlank
	private String endTime;

	@ApiModelProperty(value = "债转公司名称")
	private String transferCompany;
}
