package com.welab.crm.operate.enums;

/**
 * h5还款链接还款类型
 */
public enum RepayLinkModel {
    YD("YD", "单期还款"),
    YH("YH", "所有逾期"),
    YB("YB", "结清还款"),
    YC("YC", "自定义还款");

    private String code;
    private String desc;

    RepayLinkModel(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code){
        for (RepayLinkModel model : values()) {
            if (code.equals(model.code)){
                return model.desc;
            }
        }

        return "";
    }
}
