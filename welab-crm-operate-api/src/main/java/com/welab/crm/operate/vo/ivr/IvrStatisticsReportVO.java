package com.welab.crm.operate.vo.ivr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: ivr分流明细报表返回对象
 * @date 2022/3/10 14:21
 */
@Data
@ApiModel(value = "ivr分流明细报表返回对象")
public class IvrStatisticsReportVO {


    @ApiModelProperty(value = "第一列,总按键合计,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> totalMapFirst;

    @ApiModelProperty(value = "第二列,转人工数,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> artificialMapFirst;

    @ApiModelProperty(value = "第三列,分流率,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> shuntRateMapFirst;

    @ApiModelProperty(value = "第四列,总按键数,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> totalMap;

    @ApiModelProperty(value = "第五列,转人工数,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> artificialMap;

    @ApiModelProperty(value = "第六列,分流率,key 为时间")
    private Map<String, IvrStatisticsArtificialVO> shuntRateMap;


}
