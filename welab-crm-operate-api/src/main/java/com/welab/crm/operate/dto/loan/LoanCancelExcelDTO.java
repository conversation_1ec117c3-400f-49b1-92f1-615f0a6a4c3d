package com.welab.crm.operate.dto.loan;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import lombok.Data;

@Data
@Excel(fileName = "贷款取消导入贷款号", sheetName = "贷款号列表")
public class LoanCancelExcelDTO {

    @ExcelTitleMap(title = "贷款号列表(必填)")
    private String contractNo;

    @ExcelTitleMap(title = "取消贷款原因", require = false)
    private String reason;
}
