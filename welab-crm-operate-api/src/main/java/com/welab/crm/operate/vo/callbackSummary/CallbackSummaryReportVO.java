package com.welab.crm.operate.vo.callbackSummary;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 回电小结报表对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "回电小结明细报表")
public class CallbackSummaryReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;


    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty("业务类型")
    private String businessType;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    @ExcelProperty("用户姓名")
    private String customerName;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    @ExcelProperty("uuid")
    private String uuid;

    /**
     * userId
     */
    @ApiModelProperty(value = "userId")
    @ExcelProperty("userId")
    private Integer userId;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    @ExcelProperty("员工姓名")
    private String staffName;

    /**
     * 联系时间
     */
    @ApiModelProperty(value = "联系时间")
    @ExcelProperty("联系时间")
    private Date callTime;

    /**
     * 回电结果
     */
    @ApiModelProperty(value = "回电结果")
    @ExcelProperty("回电结果")
    private String callbackResult;

    /**
     * 联系结果
     */
    @ApiModelProperty(value = "联系结果")
    @ExcelProperty("联系结果")
    private String contactResult;

    /**
     * 投诉原因
     */
    @ApiModelProperty(value = "投诉原因")
    @ExcelProperty("投诉原因")
    private String complaintReason;

    /**
     * 处理进度
     */
    @ApiModelProperty(value = "处理进度")
    @ExcelProperty("处理进度")
    private String processingProgress;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    private String failReason;

    /**
     * 邮箱号
     */
    @ApiModelProperty(value = "邮箱号")
    @ExcelProperty("邮箱号")
    private String email;

    /**
     * 黑产
     */
    @ApiModelProperty(value = "黑产")
    @ExcelProperty("黑产")
    private String blackIndustry;

    /**
     * 黑产原因描述
     */
    @ApiModelProperty(value = "黑产原因描述")
    @ExcelProperty("黑产原因描述")
    private String blackIndustryComment;

    /**
     * 通话备注
     */
    @ApiModelProperty(value = "通话备注")
    @ExcelProperty("通话备注")
    private String contactComment;



}
