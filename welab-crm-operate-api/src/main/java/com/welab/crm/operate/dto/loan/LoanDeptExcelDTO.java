package com.welab.crm.operate.dto.loan;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;

import lombok.Data;

@Data
@Excel(fileName = "导入债转数据", sheetName = "债转列表")
public class LoanDeptExcelDTO {

    @ExcelTitleMap(title = "贷款号")
    private String applicationId;
    
    @ExcelTitleMap(title = "债转公司")
    private String companyName;
    
    @ExcelTitleMap(title = "债转公司电话")
    private String companyTel;
    
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @ExcelTitleMap(title = "债转时间(yyyy/MM/dd)")
    private Date deptDate;
}
