package com.welab.crm.operate.dto.message;

import com.welab.crm.operate.annotation.AesDecryptParam;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/14
 */
@Data
@ApiModel(value = "短信发送请求对象")
public class SmsSendDTO extends HistoryOperationDTO implements Serializable {

    private static final long serialVersionUID = 5966404386055794713L;

    @ApiModelProperty(value = "短信模板id")
    @NotNull
    private Long messageId;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "客户手机号")
    @NotBlank
    @AesDecryptParam
    //@Length(min = 11,max = 11,message = "请输入11位的手机号")
    private String mobile;

    @ApiModelProperty(value = "期望发送时间, 格式 yyyy-MM-dd HH:mm:ss")
    @NotNull
    private String sendTime;

    @ApiModelProperty(value = "视频录制时需要客户说的话")
    private String readMsg;

    @ApiModelProperty(value = "其他发送参数，用于存放非name的发送变量 json格式,用于后续替换")
    private String otherParams;
}
