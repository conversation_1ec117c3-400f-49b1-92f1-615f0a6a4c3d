package com.welab.crm.operate.vo.blacklist;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 黑名单统计报表返回对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BlackListSummaryReportStrVO {


    /**
     * 员工姓名
     */
    private String staffName;


    /**
     * 组别
     */
    private String groupName;


    /**
     * 拉黑本人量
     */
    private String blockSelfCount;


    /**
     * 拉黑联系人量
     */
    private String blockContactCount;


    /**
     * 拉黑实际客户量
     */
    private String blockUserCount;


    /**
     * 拉黑合同量
     */
    private String blockOrderCount;


    /**
     * 失效客户量
     */
    private String unBlockUserCount;
}
