package com.welab.crm.operate.dto.staff;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/20 11:26
 */

@Data
public class ReportStaffStatusDTO implements Serializable {

    private static final long serialVersionUID = -1054425006298716442L;


    @ApiModelProperty(value = "员工Id，前端不用传，后端自己获取")
    private String staffId;

    @ApiModelProperty(value = "状态,见字典：用户工作状态", example = "phone_online")
    private String status;

    @ApiModelProperty(value = "状态开始时间", example = "2021-11-20 09:00:00")
    private String startTime;

    @ApiModelProperty(value = "状态结束时间", example = "2021-11-20 11:00:00")
    private String endTime;

    @ApiModelProperty(value = "手机号",example = "13077973938")
    private String mobile;

}
