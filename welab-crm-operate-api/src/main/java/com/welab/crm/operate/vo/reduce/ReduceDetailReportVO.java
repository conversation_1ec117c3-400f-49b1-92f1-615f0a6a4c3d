package com.welab.crm.operate.vo.reduce;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 减免退款明细报表返回对象
 */
@Data
public class ReduceDetailReportVO {

	/**
	 * 申请时间
	 */
	@ExcelProperty("申请时间")
	private String applyTime;

	/**
	 * 投诉渠道
	 */
	@ExcelProperty("投诉渠道")
	private String complaintChannel;

	/**
	 * 产品名称
	 */
	@ExcelProperty("产品名称")
	private String productName;

	/**
	 * 姓名
	 */
	@ExcelProperty("姓名")
	private String customerName;

	/**
	 * uuid
	 */
	@ExcelProperty("uuid")
	private String uuid;

	/**
	 * userId
	 */
	@ExcelProperty("userId")
	private String userId;

	/**
	 * 减免原因
	 */
	@ExcelProperty("减免原因")
	private String reduceReason;

	/**
	 * 申请人
	 */
	@ExcelProperty("申请人")
	private String applyStaff;



	/**
	 * 流程结束时间
	 */
	@ExcelProperty("流程结束时间")
	private String processEndTime;
	
	/**
	 * 贷款号
	 */
	@ExcelProperty("贷款号")
	private String applicationId;


	/**
	 * 资金方
	 */
	@ExcelProperty("资金方")
	private String partnerCode;

	/**
	 * 申请减免金额
	 */
	@ExcelProperty("申请减免金额")
	private BigDecimal reduceAmount;



	/**
	 * 退款金额
	 */
	@ExcelProperty("退款金额")
	private BigDecimal refundAmount;
	/**
	 * 实际费率
	 */
	@ExcelProperty("实际费率")
	private BigDecimal actualRate;

	/**
	 * 减免后费率
	 */
	@ExcelProperty("减免后费率")
	private BigDecimal rateAfterReduce;



	/**
	 * 减免类型(减免级别)
	 */
	@ExcelProperty("减免级别")
	private String reduceType;


	/**
	 * 申请类型
	 */
	@ExcelProperty("申请类型")
	private String applyType;
	

	/**
	 * 流程状态
	 */
	@ExcelProperty("流程状态")
	private String processStatus;

	/**
	 * 被拒绝原因
	 */
	@ExcelProperty("被拒绝原因")
	private String failReason;

	/**
	 * 退款级别
	 */
	@ExcelProperty("退款级别")
	private String refundType;


	/**
	 * 流水号
	 */
	@ExcelIgnore
	private String requestNo;
	
	
}
