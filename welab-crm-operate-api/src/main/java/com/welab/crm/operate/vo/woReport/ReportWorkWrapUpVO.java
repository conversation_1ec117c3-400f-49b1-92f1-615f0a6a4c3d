package com.welab.crm.operate.vo.woReport;

import lombok.Getter;
import lombok.Setter;

/**
 * 用于计算一段时间内的工作总时长和会话处理时长的对象
 */
@Getter
@Setter
public class ReportWorkWrapUpVO {

    /**
     * 话后处理总时长数值
     */
    private int wrapUpDuration;

    /**
     * 工作总时长
     */
    private int workDuration;

    /**
     * 空闲时间
     */
    private int idleDuration;

    /**
     * 午餐时长
     */
    private int eatingDuration;

    /**
     * 会议时长
     */
    private int meetingDuration;

    /**
     * 培训时长
     */
    private int trainingDuration;

}
