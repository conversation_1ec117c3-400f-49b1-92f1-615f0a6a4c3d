package com.welab.crm.operate.vo.phone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**  
 * Function: 座机登录信息 <br/>  
 * Date:     2019年6月13日 下午3:11:53 <br/>  
 * <AUTHOR>  
 */
@Getter
@Setter
@Accessors(chain = true)
public class PhoneLoginInfoResVO implements Serializable {
	private static final long serialVersionUID = -4924570393845192505L;
	/**
	 * 主键id
	 */
	private Long id;
	private String staffId;

	/**
	 * 呼叫中心编号
	 */
	@ApiModelProperty(value = "呼叫中心编号")
	private String enterpriseId;
	private String enterpriseName;
	/**
	 * 坐席号
	 */
	@ApiModelProperty(value = "坐席号")
	private String cno;

	/**
	 * 登录密码
	 */
	@ApiModelProperty(value = "登陆密码")
	private String pwd;

	/**
	 * 绑定电话
	 */
	@ApiModelProperty(value = "绑定电话")
	private String bindTel;

	/**
	 * 电话类型
	 */
	@ApiModelProperty(value = "电话类型")
	private String bindType;
	/**
	 * 电话类型名字
	 */
	private String bindTypeName;

	/**
	 * 初始状态
	 */
	@ApiModelProperty(value = "初始状态")
	private String loginStatus;
	private String loginStatusName;
    /**
     * 用户电话号
     */
	@ApiModelProperty(value = "员工号")
	private String userTel;
	private String timestamp;
}
  
