package com.welab.crm.operate.vo.woReport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/2/11
 */
@Data
@ApiModel(value = "员工信息响应对象")
public class ReportStaffInfoVO implements Serializable {

    private static final long serialVersionUID = -3425283349888350537L;

    @ApiModelProperty(value = "时间")
    private String date;

    @ApiModelProperty(value = "工号")
    private String cno;

    @ApiModelProperty(value = "姓名")
    private String staffName;

    @ApiModelProperty(value = "团队")
    private String groupName;

    @ApiModelProperty(value = "首问解决率")
    private String resolvedRate;

    @ApiModelProperty(value = "客户满意度")
    private String satisfiedRate;
}
