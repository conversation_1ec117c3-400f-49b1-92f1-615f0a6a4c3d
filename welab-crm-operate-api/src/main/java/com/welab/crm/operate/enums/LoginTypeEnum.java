package com.welab.crm.operate.enums;

/**
 * 登录类型枚举
 */
public enum LoginTypeEnum {
	CODE("code", "验证码登录"),
	FACE("face", "人脸登录"),
	;
	
	private String code;
	
	private String name;
	
	LoginTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
	
	public String getCode() {
		return code;
	}
	
	
	public String getName() {
		return name;
	}
	
	public static String getNameByCode(String code) {
		for (LoginTypeEnum loginTypeEnum : LoginTypeEnum.values()) {
			if (loginTypeEnum.getCode().equals(code)) {
				return loginTypeEnum.name;
			}
		}
		return "";
	}
	
	
	public static String getCodeByName(String name) {
		for (LoginTypeEnum loginTypeEnum : LoginTypeEnum.values()) {
			if (loginTypeEnum.getName().equals(name)) {
				return loginTypeEnum.code;
			}
		}
		return "";
	}
}
