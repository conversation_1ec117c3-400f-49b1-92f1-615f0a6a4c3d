package com.welab.crm.operate.dto.notice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2023/12/27
 */
@Data
@ApiModel(description = "菜单提示返回对象")
public class ToolsNoticeDTO implements Serializable {

    private static final long serialVersionUID = 527958809915370161L;

    @ApiModelProperty(value = "菜单类别")
    private String tipsType;

    @ApiModelProperty(value = "数量")
    private Integer cnt;

    /*@ApiModelProperty(value = "审批人")
    private String logName;*/

    @ApiModelProperty(value = "手机号")
    private List<String> mobile;
}
