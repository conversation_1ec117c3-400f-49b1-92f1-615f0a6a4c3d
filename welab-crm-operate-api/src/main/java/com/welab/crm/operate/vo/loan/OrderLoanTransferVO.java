package com.welab.crm.operate.vo.loan;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单债转明细查询返回对象
 */
@Data
@ApiModel(description = "工单债转明细查询返回对象")
public class OrderLoanTransferVO {
	
	@ExcelIgnore
	private String id;
	
	/**
	 * 申请时间
	 */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty(value = "申请时间")
	private String applyTime;


	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	@ExcelProperty(value = "姓名")
	private String name;

	/**
	 * 贷款号
	 */
	@ApiModelProperty(value = "贷款号")
	@ExcelProperty(value = "贷款号")
	private String applicationId;
	
	/**
	 * 订单状态
	 */
	@ApiModelProperty(value = "订单状态")
	@ExcelProperty(value = "订单状态")
	private String orderState;

	/**
	 * 债转公司
	 */
	@ApiModelProperty(value = "债转公司")
	@ExcelProperty(value = "债转公司")
	private String company;

	/**
	 * 问题描述
	 */
	@ApiModelProperty(value = "问题描述")
	@ExcelProperty(value = "问题描述")
	private String description;


	/**
	 * 推送状态
	 */
	@ExcelProperty(value = "推送状态")
	@ApiModelProperty(value = "推送状态")
	private String pushState;

	/**
	 * 处理状态
	 */
	@ApiModelProperty(value = "处理状态")
	@ExcelProperty(value = "处理状态")
	private String processState;

	/**
	 * 短信状态
	 */
	@ApiModelProperty(value = "短信状态")
	@ExcelProperty(value = "短信状态")
	private String smsState;
}
