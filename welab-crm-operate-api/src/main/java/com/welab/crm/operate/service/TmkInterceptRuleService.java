package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.tmkRule.TmkInterceptRuleReqDTO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptHistoryVO;
import com.welab.crm.operate.vo.tmkRule.TmkInterceptRuleVO;

/**
 * 电销拦截规则服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface TmkInterceptRuleService {
 
	/**
     * 分单电销拦截规则任务列表
     * @param reqDTO
     * @return
     */
    Page<TmkInterceptRuleVO> queryInterceptRuleList(TmkInterceptRuleReqDTO reqDTO);

    /**
     * 新增电销拦截规则
     * @param reqDTO
     */
    void addInterceptRule(TmkInterceptRuleReqDTO reqDTO);

    /**
     * 更新电销拦截规则
     * @param reqDTO
     */
    void updateInterceptRule(TmkInterceptRuleReqDTO reqDTO);
    
    /**  
	 * 删除电销拦截规则
	 * @param reqDTO
	 * @return  
	 */
	public boolean deleteInterceptRule(BatchInfoReqDTO reqDTO);
	
	/**
     * 电销拦截历史列表
     * @param reqDTO
     * @return
     */
    Page<TmkInterceptHistoryVO> queryInterceptHistoryList(TmkInterceptRuleReqDTO reqDTO);
}
