package com.welab.crm.operate.vo.woReport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.welab.crm.interview.vo.loan.LoanDetailsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/29
 */
@Data
@ApiModel(value = "工单明细报表响应对象")
public class ReportDetailsVO implements Serializable {

    private static final long serialVersionUID = 5178382875709009784L;

    @ApiModelProperty(value = "工单主键")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "工单类型")
    @ExcelProperty(value = "工单类型")
    private String type;

    @ApiModelProperty(value = "工单大类")
    @ExcelProperty(value = "工单大类")
    private String firstType;

    @ApiModelProperty(value = "工单二类")
    @ExcelProperty(value = "工单二类")
    private String secondType;

    @ApiModelProperty(value = "工单三类")
    @ExcelProperty(value = "工单三类")
    private String thirdType;


    @ApiModelProperty(value = "客户姓名")
    @ExcelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "年龄")
    @ExcelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号")
    private String cnid;

    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "备用号码")
    @ExcelProperty(value = "备用号码")
    private String mobileBak;

    @ApiModelProperty(value = "备用号码列表")
    @ExcelIgnore
    private String mobileBaks;

    @ApiModelProperty(value = "创建组")
    @ExcelProperty(value = "创建组")
    private String createGroupName;

    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间")
    private String gmtCreate;

    @ApiModelProperty(value = "反馈内容")
    @ExcelProperty(value = "反馈内容")
    private String description;

    @ApiModelProperty(value = "贷款信息列表")
    @ExcelIgnore
    private LoanDetailsVO loan;

    @ApiModelProperty(value = "uuid")
    @ExcelIgnore
    private Long uuid;

    @ApiModelProperty(value = "uuidStr")
    @ExcelProperty(value = "uuid")
    private String uuidStr;

    @ApiModelProperty(value = "userId")
    @ExcelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "成功贷款笔数", name = "sucLoanCount")
    @ExcelProperty(value = "成功贷款笔数")
    private Integer sucLoanCount;

    @ApiModelProperty(value = "分单类型")
    @ExcelProperty(value = "分单类型")
    private String assignType;

    @ApiModelProperty(value = "分单时间")
    @ExcelProperty(value = "分单时间")
    private String assignTime;


    @ApiModelProperty(value = "当前处理组")
    @ExcelProperty(value = "当前处理组")
    private String curGroupName;

    @ApiModelProperty(value = "当前处理人")
    @ExcelProperty(value = "当前处理人")
    private String curStaffName;

    @ApiModelProperty(value = "当前处理时间")
    @ExcelProperty(value = "当前处理时间")
    private String curProcessTime;

    @ApiModelProperty(value = "当前处理意见")
    @ExcelProperty(value = "当前处理意见")
    private String curComment;

    @ApiModelProperty(value = "上一处理组")
    @ExcelProperty(value = "上一处理组")
    private String preGroupName;

    @ApiModelProperty(value = "上一处理人")
    @ExcelProperty(value = "上一处理人")
    private String preStaffName;

    @ApiModelProperty(value = "上一处理时间")
    @ExcelProperty(value = "上一处理时间")
    private String preProcessTime;

    @ApiModelProperty(value = "上一处理意见")
    @ExcelProperty(value = "上一处理意见")
    private String preComment;

    @ApiModelProperty(value = "工单状态")
    @ExcelProperty(value = "工单状态")
    private String status;

    @ApiModelProperty(value = "工单是否完成标志")
    @ExcelIgnore
    private String completeFlag;

    @ApiModelProperty(value = "结案类型")
    @ExcelProperty(value = "结案类型")
    private String closeType;

    @ApiModelProperty(value = "回访小结")
    @ExcelProperty(value = "回访小结")
    private String callbackNote;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "成功贷款笔数")
    @ExcelProperty(value = "成功贷款笔数")
    private Integer currentLoanCount;

    @ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
    @ExcelIgnore
    private String complaintsChannel;

    @ApiModelProperty(value = "资方简称", name = "fundName")
    @ExcelProperty(value = "资方简称")
    private String fundName;

    @ApiModelProperty(value = "统计字段", name = "matchUp")
    @ExcelIgnore
    private String matchUp;

}
