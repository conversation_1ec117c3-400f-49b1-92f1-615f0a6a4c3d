package com.welab.crm.operate.vo.blacklist.callout;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 外呼黑名单返回对象
 * 
 * <AUTHOR>
 */
@Data
public class CalloutBlackListVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键id")
    @ExcelIgnore
    private Long id;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String custName;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    @ExcelProperty(value = "uuid")
    private String uuid;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人")
    private String applyStaff;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @ExcelProperty(value = "申请时间")
    private Date applyTime;

    /**
     * 申请备注
     */
    @ApiModelProperty(value = "申请备注")
    @ExcelProperty(value = "申请备注")
    private String applyDesc;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    @ExcelProperty(value = "审批结果")
    private String approvalStatus;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    @ExcelProperty(value = "有效期")
    private Integer validTime;

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    @ExcelProperty(value = "生效开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date validStartTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    @ExcelProperty(value = "生效结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date validEndTime;


    /**
     * 是否已经取消拉黑
     */
    @ExcelProperty(value = "是否已经取消拉黑")
    @ApiModelProperty(value = "是否已经取消拉黑")
    private String isDeleted;
}
