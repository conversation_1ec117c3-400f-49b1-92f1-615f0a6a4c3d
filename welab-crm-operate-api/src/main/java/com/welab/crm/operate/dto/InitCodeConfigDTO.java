package com.welab.crm.operate.dto;

import java.io.Serializable;

import org.hibernate.validator.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021-11-25
 */
@Data
@ApiModel(value = "流程任务请求对象")
public class InitCodeConfigDTO implements Serializable {

    private static final long serialVersionUID = -7234706972802469614L;

    @ApiModelProperty(value = "流程定义编号")
    @NotBlank
    private String processCode;
}
