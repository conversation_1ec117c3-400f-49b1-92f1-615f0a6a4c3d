package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 喵达投诉单API查询请求DTO
 * 用于调用喵达API时的参数传递，时间字段为时间戳格式
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@ApiModel(value = "喵达投诉单API查询请求对象")
public class MiaodaComplaintApiQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单状态 4-待回复 6-已回复 7-已完成
     */
    @ApiModelProperty(value = "投诉单状态", name = "status")
    private Integer status;

    /**
     * 起始时间（分配时间）时间戳（秒级）
     */
    @ApiModelProperty(value = "起始时间（时间戳）", name = "st")
    private Long st;

    /**
     * 结束时间（分配时间）时间戳（秒级）
     */
    @ApiModelProperty(value = "结束时间（时间戳）", name = "et")
    private Long et;

    /**
     * 页码，默认第一页
     */
    @ApiModelProperty(value = "页码", name = "page")
    private Integer page = 1;

    /**
     * 每页数量，默认10条，最多30条
     */
    @ApiModelProperty(value = "每页数量", name = "pageSize")
    private Integer pageSize = 30;

    /**
     * 投诉单号
     */
    @ApiModelProperty(value = "投诉单号", name = "sn")
    private String sn;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号", name = "workOrderNo")
    private String workOrderNo;

    /**
     * 投诉人昵称
     */
    @ApiModelProperty(value = "投诉人昵称", name = "nickname")
    private String nickname;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式", name = "phone")
    private String phone;

    /**
     * 同步状态 0-待同步 1-已同步 2-同步失败
     */
    @ApiModelProperty(value = "同步状态", name = "syncStatus")
    private Integer syncStatus;
}
