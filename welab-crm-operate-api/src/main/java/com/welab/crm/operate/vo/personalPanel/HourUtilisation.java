package com.welab.crm.operate.vo.personalPanel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;

/**
 * 工时利用率数据
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "工时利用率数据")
public class HourUtilisation {

    @ApiModelProperty(value = "登陆时长")
    private String loginDuration;

    @ApiModelProperty(value = "忙碌时长")
    private String busyDuration;

    @ApiModelProperty(value = "通话总时长")
    private String callDuration;

    @ApiModelProperty(value = "工时利用率")
    private String hourUtilisation;

    public HourUtilisation() {
        loginDuration = "00:00:00";
        busyDuration = "00:00:00";
        callDuration = "00:00:00";
        hourUtilisation = "0.00%";
    }
    
}
