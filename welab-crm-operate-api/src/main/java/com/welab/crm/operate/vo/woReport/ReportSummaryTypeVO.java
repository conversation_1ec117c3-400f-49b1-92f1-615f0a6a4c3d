package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/12/24
 */
@Data
@ApiModel(value = "工单汇总表工单类型报表响应对象")
@Excel(fileName = "工单统计报表明细")
public class ReportSummaryTypeVO implements Serializable {

    private static final long serialVersionUID = -6087144372032682829L;

    @ApiModelProperty(value = "工单类型")
    @ExcelTitleMap(title = "工单类型")
    private String type;

    @ApiModelProperty(value = "工单大类")
    @ExcelTitleMap(title = "工单大类")
    private String firstType;

    @ApiModelProperty(value = "工单二类")
    @ExcelTitleMap(title = "工单二类")
    private String secondType;

    @ApiModelProperty(value = "工单三类")
    @ExcelTitleMap(title = "工单三类")
    private String thirdType;

    @ApiModelProperty(value = "数量")
    @ExcelTitleMap(title = "数量")
    private Integer number;

    @ApiModelProperty(value = "占比")
    @ExcelTitleMap(title = "占比")
    private String rate;

    @ApiModelProperty(value = "结案量")
    @ExcelTitleMap(title = "结案量")
    private Integer endCount;


    @ApiModelProperty(value = "未结案量")
    @ExcelTitleMap(title = "未结案量")
    private Integer notEndCount;
}
