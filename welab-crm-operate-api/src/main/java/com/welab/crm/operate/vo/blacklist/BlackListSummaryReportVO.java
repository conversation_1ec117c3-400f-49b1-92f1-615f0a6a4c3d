package com.welab.crm.operate.vo.blacklist;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 黑名单统计报表返回对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BlackListSummaryReportVO {


    /**
     * 组别
     */
    private String staffName;


    /**
     * 姓名
     */
    private String groupName;


    /**
     * 拉黑本人量
     */
    private Integer blockSelfCount = 0;


    /**
     * 拉黑联系人量
     */
    private Integer blockContactCount = 0;


    /**
     * 拉黑实际客户量
     */
    private Integer blockUserCount = 0;


    /**
     * 拉黑合同量
     */
    private Integer blockOrderCount = 0;


    /**
     * 失效客户量
     */
    private Integer unBlockUserCount = 0;
}
