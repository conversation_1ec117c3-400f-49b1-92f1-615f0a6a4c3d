package com.welab.crm.operate.dto.blacklist.callout;

import com.welab.crm.operate.dto.BatchInfoReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外呼黑名单审批请求对象
 */
@Data
public class CalloutBlackListApprovalDTO extends BatchInfoReqDTO {


	/**
	 * 审批状态；1-通过；2-拒绝
	 */
	@ApiModelProperty(value = "审批状态")
	private Integer approvalStatus;

	/**
	 * 有效期(单位:天)
	 */
	@ApiModelProperty(value = "有效期")
	private Integer validTime;
	
}
