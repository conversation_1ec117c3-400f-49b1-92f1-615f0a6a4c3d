package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 喵达登录请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达登录请求对象")
public class MiaodaLoginDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家UID
     */
    @ApiModelProperty(value = "商家UID", name = "uid", required = true)
    @NotNull(message = "商家UID不能为空")
    private Integer uid;

    /**
     * 商家密钥
     */
    @ApiModelProperty(value = "商家密钥", name = "key", required = true)
    @NotNull(message = "商家密钥不能为空")
    private String key;
}
