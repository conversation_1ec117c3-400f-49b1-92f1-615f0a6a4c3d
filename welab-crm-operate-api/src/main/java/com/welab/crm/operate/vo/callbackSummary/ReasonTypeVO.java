package com.welab.crm.operate.vo.callbackSummary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 原因类型对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "原因类型对象")
public class ReasonTypeVO implements Serializable {


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 备注，无则不填
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
