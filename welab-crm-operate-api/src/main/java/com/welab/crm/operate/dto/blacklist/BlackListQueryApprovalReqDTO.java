package com.welab.crm.operate.dto.blacklist;

import com.welab.crm.operate.dto.BaseReqDTO;
import lombok.Data;


/**
 * 黑名单请求对象
 */
@Data
public class BlackListQueryApprovalReqDTO extends BaseReqDTO {

    private static final long serialVersionUID = -1393659871508121655L;

    /**
     * 开始时间
     */
    private String gmtStartCreate;

    /**
     * 结束时间
     */
    private String gmtEndCreate;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 拉黑天数
     */
    private Integer blackDay;

    /**
     * 加黑类型
     * {@link com.welab.collection.interview.enums.BlackTypeEnum}
     */
    private String blackType;

    /**
     * 添加原因
     */
    private String addReason;

    /**
     * 审批状态 0初审 1复审 2审批完成 3拒绝
     */
    private Integer operateState;

    /**
     * 创建人
     */
    private String staffName;

}
