package com.welab.crm.operate.dto.callbackSummary;

import com.welab.crm.operate.vo.callbackSummary.ReasonTypeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;

/**
 * 回电小结保存对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "回电小结保存对象")
public class CallbackSummarySaveDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String uuid;

    /**
     * 天润通话唯一ID
     */
    @ApiModelProperty(value = "天润通话唯一ID")
    private String cdrMainUniqueId;


    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 回电结果
     */
    @ApiModelProperty(value = "回电结果")
    private String callbackResult;

    /**
     * 原因类型列表
     */
    @ApiModelProperty(value = "原因类型列表")
    private List<ReasonTypeVO> reasonTypeList;


    /**
     * 通话备注
     */
    @ApiModelProperty(value = "通话备注")
    private String callbackComment;
    
    
    @ApiModelProperty(value = "关联工单列表")
    @NotEmpty(message = "关联工单列表不能为空")
    private List<String> orderNoList;
    
    @ApiModelProperty(value = "处理方案")
    @NotBlank(message = "处理方案不能为空")
    private String resolveContent;

}
