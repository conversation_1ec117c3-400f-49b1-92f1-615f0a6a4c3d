package com.welab.crm.operate.vo.telemarketing;

import java.io.Serializable;
import java.util.Date;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 电销转换报表返回对象
 * @date 2022/3/15 15:59
 */
@Data
@ApiModel("电销钱夹谷谷报表返回对象")
@Excel(fileName = "电销钱夹谷谷报表", sheetName = "电销钱夹谷谷报表")
public class TmkWalletReportVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    @ExcelTitleMap(title = "userId")
    private Integer userId;

    @ApiModelProperty("uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;
    
    @ApiModelProperty("名单类型")
    @ExcelTitleMap(title = "名单类型")
    private String tmkType;
    
    @ApiModelProperty("学历")
    @ExcelTitleMap(title = "学历")
    private String education;
    
    @ApiModelProperty("注册渠道")
    @ExcelTitleMap(title = "注册渠道")
    private String regOrigin;
    
    @ApiModelProperty("额度编号")
    @ExcelTitleMap(title = "额度编号")
    private String applicationId;
    
    @ApiModelProperty("申请时间")
    @ExcelTitleMap(title = "申请时间")
    private Date appliedAt;
    
    @ApiModelProperty("申请渠道")
    @ExcelTitleMap(title = "申请渠道")
    private String applyOrigin;
    
    @ApiModelProperty("审批时间")
    @ExcelTitleMap(title = "审批时间")
    private Date approvedAt;
    
    @ApiModelProperty("授信额度")
    @ExcelTitleMap(title = "授信额度")
    private String creditLine;
    
    @ApiModelProperty("可用额度")
    @ExcelTitleMap(title = "可用额度")
    private String avlCredit;
    
    @ApiModelProperty("第一次消费时间")
    @ExcelTitleMap(title = "第一次消费时间")
    private Date firstConsuDate;
    
    @ApiModelProperty("第一次消费成功时间")
    @ExcelTitleMap(title = "第一次消费成功时间")
    private Date firstConsuSuccDate;
    
    @ApiModelProperty("消费总额")
    @ExcelTitleMap(title = "消费总额")
    private String consuAmount;
    
    @ApiModelProperty("处理组")
    @ExcelTitleMap(title = "处理组")
    private String groupCode;
    
    @ApiModelProperty("处理人员")
    @ExcelTitleMap(title = "处理人员")
    private String staffId;

    @ApiModelProperty("联系结果")
    @ExcelTitleMap(title = "联系结果")
    private String resultCode;
    
    @ApiModelProperty("电话小结")
    @ExcelTitleMap(title = "电话小结")
    private String summaryContent;
    
    @ApiModelProperty("备注")
    @ExcelTitleMap(title = "备注")
    private String comment;
    
    @ApiModelProperty("小结时间")
    @ExcelTitleMap(title = "小结时间")
    private Date summaryAt;
    
    @ApiModelProperty("名单创建时间")
    @ExcelTitleMap(title = "名单创建时间")
    private Date gmtCreate;


    @ApiModelProperty("电话开始时间")
    @ExcelTitleMap(title = "电话开始时间")
    private Date callStartTime;

    @ApiModelProperty("电话结束时间")
    @ExcelTitleMap(title = "电话结束时间")
    private Date callEndTime;

    @ApiModelProperty("通话时长(s)")
    @ExcelTitleMap(title = "通话时长(s)")
    private Long callTime;
}
