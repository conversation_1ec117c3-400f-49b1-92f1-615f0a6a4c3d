package com.welab.crm.operate.vo.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/12 17:35
 */

@Data
@ApiModel(description = "客服库内钱包用户数据返回对象")
public class WalletCustInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;
    @ApiModelProperty(value = "用户名", name = "customerName")
    private String customerName;
    @ApiModelProperty(value = "学历", name = "degree")
    private String degree;
    @ApiModelProperty(value = "地址", name = "address")
    private String address;
    @ApiModelProperty(value = "注册时间", name = "registerTime")
    private String registerTime;
    @ApiModelProperty(value = "注册渠道", name = "registerOrigin")
    private String registerOrigin;
    @ApiModelProperty(value = "信用额度", name = "creditline")
    private BigDecimal creditline;
    @ApiModelProperty(value = "可用额度", name = "avlCreditline")
    private BigDecimal avlCreditline;
    @ApiModelProperty(value = "绑卡时间", name = "bankDate")
    private String bankDate;
    @ApiModelProperty(value = "二类户账户", name = "elecCardNo")
    private String elecCardNo;


}
