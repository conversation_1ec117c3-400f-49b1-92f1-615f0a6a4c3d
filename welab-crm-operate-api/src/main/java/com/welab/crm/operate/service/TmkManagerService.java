package com.welab.crm.operate.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.tmkManager.TmkAssignReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerAdjustReqDTO;
import com.welab.crm.operate.dto.tmkManager.TmkManagerReqDTO;
import com.welab.crm.operate.dto.tmkManager.TotalTmkManagerReqDTO;
import com.welab.crm.operate.vo.tmkManager.TmkAssignVO;
import com.welab.crm.operate.vo.tmkManager.TmkAssignVO;
import com.welab.crm.operate.vo.tmkManager.TmkManagerVO;
import com.welab.crm.operate.vo.tmkManager.TotalTmkManagerVO;

/**
 * 电销数据分配规则服务接口
 *
 * <AUTHOR>
 * @date 2021/9/30 10:14
 */
public interface TmkManagerService {

	/**
     * 电销数据管理查询列表
     * @param reqDTO
     * @return
     */
    Page<TmkManagerVO> queryTmkManagerList(TmkManagerReqDTO reqDTO);
    
    /**
     * 统计电销类型数量
     * @param reqDTO
     * @return
     */
    Page<TotalTmkManagerVO> totalTmkManager(TmkManagerReqDTO reqDTO);

    /**
     * 数据再分配
     * @param reqDTO
     */
    void adjustTmkManager(TmkManagerAdjustReqDTO reqDTO);
    
    /**
     * 电销数据分单历史查询列表
     * @param reqDTO
     * @return
     */
    Page<TmkAssignVO> queryTmkAssignList(TmkAssignReqDTO reqDTO);
}
