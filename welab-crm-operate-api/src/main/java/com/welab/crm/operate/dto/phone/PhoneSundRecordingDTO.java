package com.welab.crm.operate.dto.phone;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/2/23
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "录音文件请求对象")
public class PhoneSundRecordingDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = 6178729043601434743L;

    @ApiModelProperty(value=" 录音文件名", name="recordFile",example = "record-sip-28-1640849027.113855")
    private String recordFile;

    @ApiModelProperty(value = "呼出坐席工号", name = "呼出坐席工号")
    private String staffNo;

    @ApiModelProperty(value = "呼入坐席工号", name = "呼入坐席工号")
    private String cdrCalleeCno;

    @ApiModelProperty(value = "话务员", name = "话务员")
    private Long staffId;

    @ApiModelProperty(value = "所在组", name = "所在组")
    private String groupCode;

    @ApiModelProperty(value = "客户姓名", name = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "电话类型", name = "电话类型")
    private String cdrCustomerNumberType;

    @ApiModelProperty(value = "主叫号", name = "主叫号")
    private String cdrClid;

    @ApiModelProperty(value = "被叫号", name = "被叫号")
    private String cdrCustomerNumber;

    @ApiModelProperty(value = "开始时间，格式 yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "持续开始时间", name = "持续开始时间")
    private String startCdrEndBridgeTime;

    @ApiModelProperty(value = "持续结束时间", name = "持续开始时间")
    private String endCdrEndBridgeTime;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private String userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;
    
    @ApiModelProperty(value = "接通状态 1-已接通，0-未接通")
    private Integer contactResult;
    
    @ApiModelProperty(value = "满意度等级 1-非常满意,2-满意,3-一般,4-不满意,5-非常不满意")
    private Integer satisfactionLevel;
    
    @ApiModelProperty(value = "不满意原因 1-客服业务不熟悉，2-处理时效长，3-服务态度差，4-业务规则不满")
    private Integer dissatisfiedReason;

    @ApiModelProperty(value = "是否评价 1-已评价，0-未评价")
    private Integer isEvaluate;

    @ApiModelProperty(value = "电话小结内容")
    private String callSummary;

    @ApiModelProperty(value = "坐席挂机/客户挂机 0/1")
    private Integer type;
}
