package com.welab.crm.operate.exception;

import com.welab.common.exception.WeLabException;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/29
 */
public class CrmOperateException extends RuntimeException {

    private static final long serialVersionUID = 8801470126346156441L;

    public CrmOperateException() {
        super();
    }

    public CrmOperateException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public CrmOperateException(String message, Throwable cause) {
        super(message, cause);
    }

    public CrmOperateException(String message) {
        super(message);
    }

    public CrmOperateException(Throwable cause) {
        super(cause);
    }
}
