package com.welab.crm.operate.dto.workorder;

import com.welab.crm.operate.dto.BaseRequestDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单调剂请求参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单分配请求对象")
public class AdjustWorkOrderInfo extends BaseRequestDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6041084796947500158L;

	/**
     * 分配组
     */
	@ApiModelProperty(value = "分配组", name = "assignGroupCode")
    private String assignGroupCode;

    /**
     * 分配人
     */
	@ApiModelProperty(value = "分配人", name = "assignStaffId")
    private String assignStaffId;
	
	/**
     * 分配数量
     */
	@ApiModelProperty(value = "分配数量", name = "assignNum")
    private Integer assignNum;
}
