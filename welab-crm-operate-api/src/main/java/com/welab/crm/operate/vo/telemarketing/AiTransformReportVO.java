package com.welab.crm.operate.vo.telemarketing;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: AI转换报表对象
 * @date 2022/7/22 13:43
 */
@Data
@ApiModel(description = "AI转换报表返回对象")
@Excel(fileName = "AI转换报表")
public class AiTransformReportVO implements Serializable {

    private static final Long serialVersionUID = 2L;


    @ApiModelProperty(value = "外呼量")
    @ExcelTitleMap(title = "外呼量")
    private Integer callOutNum;

    @ApiModelProperty(value = "接通量")
    @ExcelTitleMap(title = "接通量")
    private Integer connectedNum;

    @ApiModelProperty(value = "接通率")
    @ExcelTitleMap(title = "接通率")
    private String connectedRate;

    @ApiModelProperty(value = "转化量")
    @ExcelTitleMap(title = "转化量")
    private Integer transformNum;

    @ApiModelProperty(value = "转化率")
    @ExcelTitleMap(title = "转化率")
    private String transformRate;

    @ApiModelProperty(value = "转化金额")
    @ExcelTitleMap(title = "转化金额")
    private BigDecimal transformAmount;


    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private Long callTime;

}
