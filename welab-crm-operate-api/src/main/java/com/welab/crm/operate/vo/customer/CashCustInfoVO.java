package com.welab.crm.operate.vo.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/12 17:47
 */
@Data
@ApiModel(description = "现金贷用户返回对象")
public class CashCustInfoVO {


    @ApiModelProperty(value = "主键Id", name = "id")
    private Long id;
    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;
    @ApiModelProperty(value = "客户名", name = "name")
    private String name;
    @ApiModelProperty(value = "性别", name = "gender")
    private String gender;
    @ApiModelProperty(value = "身份证", name = "cnid")
    private String cnid;
    @ApiModelProperty(value = "年龄", name = "age")
    private String age;
    @ApiModelProperty(value = "信用额度", name = "creditline")
    private BigDecimal creditline;
    @ApiModelProperty(value = "可用额度", name = "avlCreditline")
    private BigDecimal avlCreditline;
    @ApiModelProperty(value = "地址", name = "address")
    private String address;
    @ApiModelProperty(value = "家庭地址", name = "familyAddress")
    private String familyAddress;
    @ApiModelProperty(value = "公司名称", name = "companyName")
    private String companyName;
    @ApiModelProperty(value = "公司地址", name = "companyAddress")
    private String companyAddress;

    @ApiModelProperty(value = "额度状态", name = "creditState")
    private String creditState;
}
