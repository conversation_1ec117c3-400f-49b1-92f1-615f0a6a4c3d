package com.welab.crm.operate.vo.screen;

import lombok.Data;

import java.io.Serializable;

/**
 * 坐席工作概览
 * <AUTHOR>
 */
@Data
public class AgentOverview implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 总签入坐席数量
     */
    private Integer onlineNumber;


    /**
     * 空闲坐席数
     */
    private Integer freeNumber;


    /**
     * 通话中
     */
    private Integer callingNumber;
}
