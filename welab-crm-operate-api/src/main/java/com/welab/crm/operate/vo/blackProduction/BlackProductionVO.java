package com.welab.crm.operate.vo.blackProduction;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 黑产返回对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "黑产返回对象")
public class BlackProductionVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private Long id;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    @ExcelProperty(value = "uuid")
    private String uuid;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    @ExcelProperty(value = "用户姓名")
    private String name;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号")
    @ExcelProperty(value = "用户手机号")
    private String userMobile;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人")
    private String applyStaff;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @ExcelProperty(value = "申请时间")
    private Date applyTime;

    /**
     * 黑产手机号
     */
    @ApiModelProperty(value = "黑产手机号")
    @ExcelProperty(value = "黑产手机号")
    private String blackProductionMobile;

    /**
     * 黑产邮箱
     */
    @ApiModelProperty(value = "黑产邮箱")
    @ExcelProperty(value = "黑产邮箱")
    private String blackProductionEmail;

    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    @ExcelProperty(value = "用户类型")
    private String userType;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    @ExcelProperty(value = "备注信息")
    private String remark;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @ExcelProperty(value = "数据来源")
    private String source;

    /**
     * 附件名称列表
     */
    @ApiModelProperty(value = "附件名称列表")
    @ExcelIgnore
    private List<String> attachmentList;

    /**
     * 附件名称字符串
     */
    @ExcelIgnore
    private String attachmentNames;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    @ExcelIgnore
    private String approvalStatus;
    
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号")
    private String idNo;
}
