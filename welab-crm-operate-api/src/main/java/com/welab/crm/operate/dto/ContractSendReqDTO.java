package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "合同发送请求参数")
public class ContractSendReqDTO extends BaseReqDTO {
	
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	
	@ApiModelProperty(value = "组别")
	private List<String> groupCodes;
	
	@ApiModelProperty(value = "手机号")
	private String mobile;
	
	@ApiModelProperty(value = "uuid")
	private String uuid;
	
	@ApiModelProperty(value = "贷款号")
	private String appNo;
	
	@ApiModelProperty(value = "资金方名称")
	private String partnerName;
	
	@ApiModelProperty(value = "发送类型 1-查看且下载；2-仅查看")
	private Integer sendType;
	
	@ApiModelProperty(value = "状态 0-待审核；1-审核通过；2-审核失败；3-发送成功；4-发送失败")
	private Integer status;
	
	@ApiModelProperty(value = "查询标识 1-普通查询；2-审核查询")
	@NotNull(message = "查询标识不能为空")
	private Integer queryFlag;
}
