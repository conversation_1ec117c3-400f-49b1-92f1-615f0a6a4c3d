package com.welab.crm.operate.dto.reduce;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 退款申请请求对象
 */
@Data
public class RefundApplyDTO {


	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 贷款号
	 */
	private String applicationId;


	/**
	 * 用户ID
	 */
	private String userId;


	/**
	 * 退款金额
	 */
	private BigDecimal amount;


	/**
	 * 退款时效
	 */
	private Integer refundDays;


	/**
	 * 退款发起时间
	 */
	private String refundStartTime;

	/**
	 * 投诉渠道
	 */
	private String complaintChannel;


	/**
	 * uuid
	 */
	private String uuid;

	/**
	 * 产品名称
	 */
	private String productName;


	/**
	 * 资金方名称
	 */
	private String partnerCode;


	/**
	 * 资金方编码
	 */
	private String partnerCodeNew;

	/**
	 * 费率
	 */
	private BigDecimal totalRate;

	/**
	 * 退款原因
	 */
	private String refundReason;

	/**
	 * 减免级别
	 */
	private String refundType;

	/**
	 * 模式类型
	 */
	private String loanModelType;
	
}
