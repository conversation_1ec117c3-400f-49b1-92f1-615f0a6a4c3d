package com.welab.crm.operate.vo.woReport;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel(value = "客户重复来电明细报表返回对象")
@Excel(fileName = "客户重复来电明细报表")
public class ReportDupCallDetailVO {

    /**
     * 来电日期
     */
    @ApiModelProperty(value = "时间")
    @ExcelTitleMap(title = "时间")
    private String callDay;

    /**
     * 热线号码
     */
    @ApiModelProperty(value = "热线号码")
    @ExcelTitleMap(title = "热线号码")
    private String hotline;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    @ExcelTitleMap(title = "客户姓名")
    private String customerName;

    /**
     * 来电号码(手机号)
     */
    @ApiModelProperty(value = "手机号")
    @ExcelTitleMap(title = "手机号")
    private String mobile;


    /**
     * userId
     */
    @ApiModelProperty(value = "userId")
    @ExcelTitleMap(title = "userId")
    private String userId;


    /**
     * userId
     */
    @ApiModelProperty(value = "uuid")
    @ExcelTitleMap(title = "uuid")
    private String uuid;

    /**
     * 真正的客户来电号码
     */
    private String cdrCustomerNumber;

    /**
     * 进入系统时间
     */
    private Date cdrStartTime;

    /**
     * 进入系统时间
     */
    @ApiModelProperty(value = "进入系统时间")
    @ExcelTitleMap(title = "进入系统时间")
    private String enterTime;

    /**
     * 系统接听时间
     */
    private Date cdrAnswerTime;

    /**
     * 系统接听时间
     */
    @ApiModelProperty(value = "系统接听时间")
    @ExcelTitleMap(title = "系统接听时间")
    private String answerTime;

    /**
     * 进入队列时间
     */
    private Date cdrJoinQueueTime;

    /**
     * 进入队列时间
     */
    @ApiModelProperty(value = "进入队列时间")
    @ExcelTitleMap(title = "进入队列时间")
    private String joinQueueTime;

    /**
     * 挂机时间
     */
    private Date cdrEndTime;

    /**
     * 挂机时间
     */
    @ApiModelProperty(value = "挂机时间")
    @ExcelTitleMap(title = "挂机时间")
    private String endTime;

    /**
     * 双方接听时长
     */
    @ApiModelProperty(value = "通话时长")
    @ExcelTitleMap(title = "通话时长")
    private String cdrEndBridgeTime;

    /**
     * 通话状态: 1 座席接听; 2 已呼叫座席,座席未接听;
     */
    @ApiModelProperty(value = "通话状态")
    @ExcelTitleMap(title = "通话状态")
    private String cdrStatus;

    /**
     * 坐席工号(注意这里取的数据库字段是cdr_callee_cno)
     */
    @ApiModelProperty(value = "坐席工号")
    @ExcelTitleMap(title = "坐席工号")
    private String cdrCno;

    /**
     * 坐席姓名
     */
    @ApiModelProperty(value = "坐席姓名")
    @ExcelTitleMap(title = "坐席姓名")
    private String staffName;

    /**
     * 来电次数
     */
    @ApiModelProperty(value = "来电次数")
    @ExcelTitleMap(title = "来电次数")
    private Integer counts;

    /**
     * 是否为会员
     */
    @ApiModelProperty(value = "是否为会员")
    @ExcelTitleMap(title = "是否为会员")
    private String vip;

    /**
     * 是否为会员
     */
    private Boolean iVip;

    /**
     * 来电小结一类
     */
    @ApiModelProperty(value = "来电小结一类")
    @ExcelTitleMap(title = "来电小结一类")
    private String summary1Code;

    /**
     * 来电小结二类
     */
    @ApiModelProperty(value = "来电小结二类")
    @ExcelTitleMap(title = "来电小结二类")
    private String summary2Code;

    /**
     * 来电小结三类
     */
    @ApiModelProperty(value = "来电小结三类")
    @ExcelTitleMap(title = "来电小结三类")
    private String summary3Code;

    /**
     * 备注
     */
    @ApiModelProperty(value = "其他备注")
    @ExcelTitleMap(title = "其他备注")
    private String callComment;

    /**
     * 逗号分隔的来电小结
     */
    private String callSummary;
}
