package com.welab.crm.operate.vo.workorder;

import com.welab.crm.operate.dto.workorder.WorkOrderLoanReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 查询工单详情响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "查询工单详情响应对象")
public class WorkOrderDetailVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;

	@ApiModelProperty(value = "自增ID", name = "id")
    private Long id;
	
	@ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;
	
	@ApiModelProperty(value = "流程实例ID", name = "executionId")
    private String executionId;
    
    @ApiModelProperty(value = "流程定义编号")
    private String processCode;

    @ApiModelProperty(value = "流程是否完成，1为已完成")
    private String completeFlag;

    @ApiModelProperty(value = "预约时间", name = "appointDate")
    private Date appointTime;

    @ApiModelProperty(value = "预约标题", name = "appointTitle")
    private String appointTitle;
    
    @ApiModelProperty(value = "预约状态", name = "appointStatus")
    private String appointStatus;

    @ApiModelProperty(value = "客户ID", name = "custId")
    private String custId;
    
    @ApiModelProperty(value = "身份证号码", name = "cnid")
    private String cnid;
    
    @ApiModelProperty(value = "客户姓名", name = "customerName")
    private String customerName;
    
    @ApiModelProperty(value = "年龄", name = "age")
    private Integer age;
    
    @ApiModelProperty(value = "性别", name = "gender")
    private String gender;
    
    @ApiModelProperty(value = "手机号码", name = "mobile")
    private String mobile;


    @ApiModelProperty(value = "手机号码脱敏", name = "mobileMask")
    private String mobileMask;
    
    @ApiModelProperty(value = "备用号码", name = "mobileBak")
    private String mobileBak;

    private String email;
    
    @ApiModelProperty(value = "邮箱对象")
    private WoEmailVO emailVO;
    
    @ApiModelProperty(value = "工单备用号码列表")
    private List<WoMobileBakVO> mobileBakList;
    
    private String mobileBaks;
    
    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "工单类型", name = "orderType")
    private String type;

    @ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private String orderOneClass;

    @ApiModelProperty(value = "工单二类", name = "orderTwoClass")
    private String orderTwoClass;

    @ApiModelProperty(value = "工单三类", name = "orderThreeClass")
    private String orderThreeClass;

    @ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
    private String complaintsChannel;

    @ApiModelProperty(value = "资方简称", name = "fundName")
    private String fundName;

    @ApiModelProperty(value = "投诉渠道与资金方映射关系", name = "matchUp")
    private String matchUp;

    @ApiModelProperty(value = "加急", name = "urgentFlag")
    private String urgentFlag;

    @ApiModelProperty(value = "回访", name = "callbackFlag")
    private String callbackFlag;
    
    @ApiModelProperty(value = "反馈内容", name = "description")
    private String description;
    
    @ApiModelProperty(value = "处理意见", name = "opinion")
    private String opinion;
    
    @ApiModelProperty(value = "回访小结", name = "callbackNote")
    private String callbackNote;
    
    @ApiModelProperty(value = "工单状态", name = "status")
    private String status;
    
    @ApiModelProperty(value = "是否标记", name = "sign")
    private String sign;
    
    @ApiModelProperty(value = "贷款信息", name = "loanList")
    private List<WorkOrderLoanReqDTO> loanList;
    
    @ApiModelProperty(value = "工单流水", name = "logVO")
    private List<WorkOrderLogVO> logList;

    @ApiModelProperty(value = "附件", name = "fileNameMap")
    private List<WoAttachmentVO> fileList;


    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "uuid")
    private String uuid;
}

