package com.welab.crm.operate.dto.blacklist.callout;

import com.welab.crm.operate.dto.BaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * 外呼黑名单添加请求对象
 * 
 * <AUTHOR>
 */
@Data
public class CalloutBlackListReqDTO extends BaseReqDTO {

    /**
     * 客户姓名，添加黑名单必填
     */
    @ApiModelProperty(value = "客户姓名")
    @NotBlank(message = "姓名不能为空", groups = GroupInsert.class)
    private String custName;

    /**
     * uuid；添加黑名单必填
     */
    @ApiModelProperty(value = "uuid")
    @NotBlank(message = "uuid不能为空", groups = GroupInsert.class)
    private String uuid;

    /**
     * 申请备注 添加黑名单必填
     */
    @ApiModelProperty(value = "申请备注")
    @NotBlank(message = "申请备注不能为空", groups = GroupInsert.class)
    private String applyDesc;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    private String applyStartTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    private String applyEndTime;

    /**
     * 审批状态；0-待审批；1-通过；2-拒绝
     */
    @ApiModelProperty(value = "审批状态")
    private List<Integer> approvalStatus;
}
