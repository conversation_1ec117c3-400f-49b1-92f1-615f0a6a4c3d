package com.welab.crm.operate.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2022/1/11
 */
@Data
@ApiModel(value = "外呼效能统计报表请求对象")
public class OutboundEfficiencySummaryDTO extends WoReportDTO implements Serializable {

    private static final long serialVersionUID = 1309645488596932189L;

    @ApiModelProperty(value = "话务组code")
    private String groupCode;

}
