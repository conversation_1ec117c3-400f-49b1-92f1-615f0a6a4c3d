package com.welab.crm.operate.dto.operate;

import com.welab.crm.operate.dto.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 贷款操作DTO
 *
 * <AUTHOR>
 * @date 2021/9/29 15:02
 */
@Data
public class OperateHistoryQueryReqDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户Id", name = "userId", example = "123456")
    private Integer userId;

    @ApiModelProperty(value = "贷款号", name = "applicationId", example = "20213129903194190")
    private String applicationId;

    @ApiModelProperty(value = "操作类型", name = "type", example = "return_order")
    private String type;

    @ApiModelProperty(value = "员工Id", name = "staffId", example = "leon.li")
    private String staffId;

    @ApiModelProperty(value = "员工所在组", name = "groupCode", example = "test")
    private String groupCode;

    @ApiModelProperty(value = "客服系统客户Id")
    private Long customerId;

}
