package com.welab.crm.operate.vo.phone;

import com.welab.crm.base.excel.annotation.Excel;
import com.welab.crm.base.excel.annotation.ExcelTitleMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @description TODO
 * @date 2022/3/9
 */
@Data
@ApiModel(value = "电话小结报表响应对象")
@Excel(fileName="电话小结报表")
public class ReportPhoneResultSummaryVO implements Serializable {

    private static final long serialVersionUID = 2626222602799415451L;

    @ApiModelProperty(value = "电话小结内容(合并小结123类)", name = "电话小结内容(合并小结123类)")
    private String callSummary;

    @ApiModelProperty(value = "电话小结内容一项", name = "电话小结内容一项")
    @ExcelTitleMap(title = "电话小结内容一项")
    private String callSummaryOne;

    @ApiModelProperty(value = "电话小结内容二项", name = "电话小结内容二项")
    @ExcelTitleMap(title = "电话小结内容二项")
    private String callSummaryTwo;

    @ApiModelProperty(value = "电话小结内容三项", name = "电话小结内容三项")
    @ExcelTitleMap(title = "电话小结内容三项")
    private String callSummaryThree;

    @ApiModelProperty(value = "电话小结类型码(合并小结123类)", name = "电话小结类型码(合并小结123类)")
    //@ExcelTitleMap(title = "电话小结类型码(合并小结123类)")
    private String callSummaryCode;

    @ApiModelProperty(value = "电话小结数量", name = "电话小结数量")
    @ExcelTitleMap(title = "电话小结数量")
    private BigDecimal num;

    @ApiModelProperty(value = "电话小结占比", name = "电话小结占比")
    @ExcelTitleMap(title = "电话小结占比")
    private String summaryPercent;

    @ApiModelProperty(value = "电话小结总数", name = "电话小结总数")
    @ExcelTitleMap(title = "电话小结总数")
    private BigDecimal summaryCount;

}
