package com.welab.crm.operate.vo.tmkReport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "中央监控再分配外呼效能报表响应对象")
public class RedistributedOutboundEfficiencyVO implements Serializable {

    private static final long serialVersionUID = -3351744950698871202L;

    @ApiModelProperty(value = "序号")
    private Integer serialNo;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "话务组code")
    private String groupCode;

    @ApiModelProperty(value = "话务员id")
    private String staffId;

    @ApiModelProperty(value = "话务员名称")
    private String staffName;

    @ApiModelProperty(value = "被回收量")
    private Integer recoveryNum;

    @ApiModelProperty(value = "分配量")
    private Integer assignNum;

    @ApiModelProperty(value = "外呼量")
    private Integer outboundNum;

    @ApiModelProperty(value = "接通量")
    private Integer connectedNum;

    @ApiModelProperty(value = "接通率")
    private String connectedRate;

    @ApiModelProperty(value = "转化量")
    private Integer conversionNum;

    @ApiModelProperty(value = "转化率")
    private String conversionRate;
}
