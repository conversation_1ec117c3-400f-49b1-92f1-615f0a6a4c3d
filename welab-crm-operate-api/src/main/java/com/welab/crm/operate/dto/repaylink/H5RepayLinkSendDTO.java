package com.welab.crm.operate.dto.repaylink;

import com.welab.crm.operate.dto.message.SmsSendDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * h5还款链接发送请求对象
 * <AUTHOR>
 */
@Data
@ApiModel(description = "h5还款链接发送请求对象")
public class H5RepayLinkSendDTO extends SmsSendDTO {

    /**
     * 还款模式
     * YD:单期还款
     * YH:所有逾期
     * YB:结清还款
     * YC:自定义还款
     */
    @ApiModelProperty(value = "还款模式", required = true)
    private String repayMode;

    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额", required = true)
    private BigDecimal repayAmount;

    /**
     * 贷款号
     */
    @ApiModelProperty(value = "贷款号", required = true)
    private String applicationId;


    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid", required = true)
    private String uuid;


    /**
     * 还款链接
     */
    @ApiModelProperty(value = "repayLink", required = true)
    private String repayLink;


    /**
     * 还款口令
     */
    @ApiModelProperty(value = "repayCmd", required = true)
    private String repayCmd;
    
    
    @ApiModelProperty(value = "咨询状态 1-已逾期 2-待还款", required = true)
    private Integer consultationStatus;


    /**
     * 还款方式；helpPayment - 线上帮还，offlinePayment - 线下还款
     */
    @ApiModelProperty(value = "还款方式", required = true)
    private String repayMethod;
}
