package com.welab.crm.operate.dto.dict;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电话小结收藏记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Getter
@Setter
public class CallSummaryRespDTO implements Serializable {

    private static final long serialVersionUID = -5216134616855136229L;

    /**
     * id
     */
    @ApiModelProperty(value="id", name="id")
    private Long id;

    /**
     * 手机
     */
    @ApiModelProperty(value="手机", name="手机")
    private String staffMobile;

    /**
     * 描述
     */
    @ApiModelProperty(value="描述", name="描述")
    private String description;

    /**
     * 子类id
     */
    @ApiModelProperty(value="子类id", name="子类id")
    private Long lastDictId;

    /**
     * 状态 0-无 1-置顶
     */
    @ApiModelProperty(value="状态 0-无 1-置顶", name="状态 0-无 1-置顶")
    private Integer topStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间", name="创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间", name="修改时间")
    private Date gmtModify;

}
