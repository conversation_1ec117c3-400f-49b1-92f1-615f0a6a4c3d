package com.welab.crm.operate.dto.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 喵达投诉单本地查询请求DTO
 * 用于查询本地数据库中的投诉单数据，支持多种查询条件组合
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@ApiModel(value = "喵达投诉单本地查询请求对象")
public class MiaodaLocalQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉单号（精确匹配）
     */
    @ApiModelProperty(value = "投诉单号", name = "sn", example = "20180316248")
    private String sn;

    /**
     * 投诉状态
     */
    @ApiModelProperty(value = "投诉状态", name = "status", example = "待回复")
    private String status;

    /**
     * 申诉状态
     */
    @ApiModelProperty(value = "申诉状态", name = "appealStatus")
    private String appealStatus;

    /**
     * 结案状态
     */
    @ApiModelProperty(value = "结案状态", name = "coCompleteStatus")
    private String coCompleteStatus;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号", name = "workOrderNo")
    private String workOrderNo;

    /**
     * 投诉人昵称（模糊匹配）
     */
    @ApiModelProperty(value = "投诉人昵称", name = "nickname")
    private String nickname;

    /**
     * 联系方式（模糊匹配）
     */
    @ApiModelProperty(value = "联系方式", name = "phone")
    private String phone;

    /**
     * 投诉标题（模糊匹配）
     */
    @ApiModelProperty(value = "投诉标题", name = "title")
    private String title;

    /**
     * 投诉内容（模糊匹配）
     */
    @ApiModelProperty(value = "投诉内容", name = "content")
    private String content;

    /**
     * 分配时间起始时间
     * 格式：yyyy-MM-dd HH:mm:ss，例如：2025-01-01 00:00:00
     */
    @ApiModelProperty(value = "分配时间起始时间（格式：yyyy-MM-dd HH:mm:ss）", name = "assignedAtStart", example = "2025-01-01 00:00:00")
    private String assignedAtStart;

    /**
     * 分配时间结束时间
     * 格式：yyyy-MM-dd HH:mm:ss，例如：2025-01-31 23:59:59
     */
    @ApiModelProperty(value = "分配时间结束时间（格式：yyyy-MM-dd HH:mm:ss）", name = "assignedAtEnd", example = "2025-01-31 23:59:59")
    private String assignedAtEnd;

    /**
     * 创建时间起始时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "创建时间起始时间（格式：yyyy-MM-dd HH:mm:ss）", name = "gmtCreateStart", example = "2025-01-01 00:00:00")
    private String gmtCreateStart;

    /**
     * 创建时间结束时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "创建时间结束时间（格式：yyyy-MM-dd HH:mm:ss）", name = "gmtCreateEnd", example = "2025-01-31 23:59:59")
    private String gmtCreateEnd;

    /**
     * 同步状态 0-待同步 1-已同步 2-同步失败
     */
    @ApiModelProperty(value = "同步状态", name = "syncStatus")
    private Integer syncStatus;

    /**
     * 自动回复状态 NULL-未处理 1-成功 2-失败
     */
    @ApiModelProperty(value = "自动回复状态", name = "autoReplyStatus")
    private Integer autoReplyStatus;

    /**
     * 页码，默认第一页
     */
    @ApiModelProperty(value = "页码", name = "page", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页数量，默认10条，最多100条
     */
    @ApiModelProperty(value = "每页数量", name = "pageSize", example = "10")
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", name = "orderBy", example = "assigned_at")
    private String orderBy = "assigned_at";

    /**
     * 排序方向 ASC-升序 DESC-降序
     */
    @ApiModelProperty(value = "排序方向", name = "orderDirection", example = "DESC")
    private String orderDirection = "DESC";

    /**
     * 是否包含JSON字段数据
     */
    @ApiModelProperty(value = "是否包含JSON字段数据", name = "includeJsonFields")
    private Boolean includeJsonFields = true;

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100;
        }
    }

    /**
     * 验证排序参数
     */
    public void validateOrderParams() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            orderBy = "assigned_at";
        }
        if (orderDirection == null || 
            (!orderDirection.equalsIgnoreCase("ASC") && !orderDirection.equalsIgnoreCase("DESC"))) {
            orderDirection = "DESC";
        }
    }

    /**
     * 获取有效的排序字段列表
     */
    public static String[] getValidOrderFields() {
        return new String[]{
            "assigned_at", "gmt_create", "gmt_modify", 
            "sn", "status", "nickname", "phone", "title"
        };
    }

    /**
     * 验证排序字段是否有效
     */
    public boolean isValidOrderField() {
        if (orderBy == null) {
            return false;
        }
        String[] validFields = getValidOrderFields();
        for (String field : validFields) {
            if (field.equals(orderBy)) {
                return true;
            }
        }
        return false;
    }
}
