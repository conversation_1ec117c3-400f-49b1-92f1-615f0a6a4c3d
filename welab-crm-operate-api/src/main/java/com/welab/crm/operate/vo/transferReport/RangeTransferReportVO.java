package com.welab.crm.operate.vo.transferReport;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 周期债转报表返回结果
 */
@Data
@ApiModel(description = "周期债转报表返回结果")
public class RangeTransferReportVO {
	
	
	@ApiModelProperty(value = "债转公司")
	@ExcelProperty(value = "债转公司")
	private String transferCompany;
	
	@ApiModelProperty(value = "投诉来源")
	@ExcelProperty(value = "投诉来源")
	private String complaintSource;
	
	@ApiModelProperty(value = "暴力催收")
	@ExcelProperty(value = "暴力催收")
	private String violentCollection;
	
	@ApiModelProperty(value = "使用我司名义催收")
	@ExcelProperty(value = "使用我司名义催收")
	private String useOurNominalCollection;
	
	@ApiModelProperty(value = "不接受债转")
	@ExcelProperty(value = "不接受债转")
	private String nonAcceptanceTransfer;
	
		@ApiModelProperty(value = "核实还款问题")
	@ExcelProperty(value = "核实还款问题")
	private String verifyRepaymentIssues;
	
	@ApiModelProperty(value = "结清核实")
	@ExcelProperty(value = "结清核实")
	private String clearanceVerification;
	
	@ApiModelProperty(value = "联系不到债转")
	@ExcelProperty(value = "联系不到债转")
	private String unContactedTransfer;
	
	@ApiModelProperty(value = "联系单位")
	@ExcelProperty(value = "联系单位")
	private String contactCompany;
	
	@ApiModelProperty(value = "联系第三方")
	@ExcelProperty(value = "联系第三方")
	private String contactThirdParties;
	
	@ApiModelProperty(value = "其他还款问题")
	@ExcelProperty(value = "其他还款问题")
	private String otherRepaymentIssues;
	
	@ApiModelProperty(value = "协商还款")
	@ExcelProperty(value = "协商还款")
	private String negotiateRepayment;
	
	@ApiModelProperty(value = "投诉诉讼冻结")
	@ExcelProperty(value = "投诉诉讼冻结")
	private String complaintsActionFrozen;
	
	@ApiModelProperty(value = "延期还款")
	@ExcelProperty(value = "延期还款")
	private String deferredRepayment;
	
	@ApiModelProperty(value = "合计")
	@ExcelProperty(value = "合计")
	private String total;
	
	@ApiModelProperty(value = "环比时间合计")
	@ExcelProperty(value = "环比时间合计")
	private String roundTimeTotal;
	
	@ApiModelProperty(value = "环比")
	@ExcelProperty(value = "环比")
	private String roundCompare;
}
