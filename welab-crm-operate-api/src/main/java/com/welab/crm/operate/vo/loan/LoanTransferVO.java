package com.welab.crm.operate.vo.loan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class LoanTransferVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 导入时的文件名称
     */
    @ApiModelProperty(value = "导入时的文件名称")
    private String fileName;

    /**
     * 合同号数量
     */
    @ApiModelProperty(value = "合同号数量")
    private int quantities;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态", example = "审批状态: 0-待审批,1-审批成功,2-审批拒绝")
    private String approveState;

    /**
     * 是否上传附件
     */
    @ApiModelProperty(value = "是否上传附件")
    private String withAttachment;

    /**
     * 操作者
     */
    @ApiModelProperty(value = "操作者")
    private String createUser;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private String gmtCreate;

    /**
     * 审批说明
     */
    @ApiModelProperty(value = "审批说明")
    private String remark;
}
