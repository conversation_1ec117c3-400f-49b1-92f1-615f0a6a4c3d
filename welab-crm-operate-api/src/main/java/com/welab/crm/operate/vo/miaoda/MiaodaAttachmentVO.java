package com.welab.crm.operate.vo.miaoda;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 喵达投诉单附件VO
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "喵达投诉单附件对象")
public class MiaodaAttachmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附件类型 image-图片 video-视频
     */
    @ApiModelProperty(value = "附件类型", name = "type")
    private String type;

    /**
     * 附件URI
     */
    @ApiModelProperty(value = "附件URI", name = "src")
    private String src;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", name = "name")
    private String name;

    /**
     * 附件大小
     */
    @ApiModelProperty(value = "附件大小", name = "size")
    private Long size;
}
