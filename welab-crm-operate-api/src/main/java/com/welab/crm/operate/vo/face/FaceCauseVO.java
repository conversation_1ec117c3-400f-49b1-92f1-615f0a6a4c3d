package com.welab.crm.operate.vo.face;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ColumnWidth(value = 15)
public class FaceCauseVO {

    @ExcelProperty(value = "失败原因")
    @ApiModelProperty(value = "失败原因", name = "失败原因")
    private String failCause;

    @ExcelProperty(value = "数量")
    @ApiModelProperty(value = "数量", name = "数量")
    private Integer failCount;

    @ExcelProperty(value = "占比")
    @ApiModelProperty(value = "占比", name = "占比")
    private String failRate;
}
