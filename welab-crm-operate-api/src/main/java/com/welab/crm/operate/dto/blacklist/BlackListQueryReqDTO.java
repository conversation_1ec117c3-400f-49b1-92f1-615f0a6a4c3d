package com.welab.crm.operate.dto.blacklist;

import com.welab.crm.operate.dto.BaseReqDTO;
import lombok.Data;


/**
 * 黑名单请求对象
 */
@Data
public class BlackListQueryReqDTO extends BaseReqDTO {

    private static final long serialVersionUID = -1393659871508121655L;


    /**
     * 身份证号
     */
    private String idNo;


    /**
     * 手机号
     */
    private String mobile;


    /**
     * 用户ID
     */
    private String userId;


    /**
     * uuid
     */
    private String uuid;


    /**
     * 添加原因
     */
    private String addReason;


    /**
     * 有效开始时间
     */
    private String validStartTime;


    /**
     * 有效结束时间
     */
    private String validEndTime;


    /**
     * 部门(数据来源)
     */
    private String department;


    /**
     * 加黑类型
     * {@link com.welab.collection.interview.enums.BlackTypeEnum}
     */
    private String blackType;
}
