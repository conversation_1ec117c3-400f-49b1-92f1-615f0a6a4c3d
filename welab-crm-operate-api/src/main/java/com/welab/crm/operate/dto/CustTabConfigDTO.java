package com.welab.crm.operate.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/11/1
 */
@Data
@ApiModel(value = "选项卡配置请求对象")
public class CustTabConfigDTO implements Serializable {

    private static final long serialVersionUID = -7234706972802469614L;

    @ApiModelProperty(value = "员工id")
    @NotBlank
    private String staffId;

    @ApiModelProperty(value = "表名")
    @NotBlank
    private String tabName;

    @ApiModelProperty(value = "不显示的列名")
    private List<String> colNames;
}
