<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.4.RELEASE</version>
  </parent>

  <groupId>com.welab</groupId>
  <artifactId>welab-crm-operate</artifactId>
  <version>1.1.1-RELEASE</version>
  <packaging>pom</packaging>

  <name>welab-crm-operate</name>
  <url>https://maven.apache.org</url>


  <distributionManagement>
    <repository>
      <id>releases-repo</id>
      <name>Internal Releases</name>
      <url>http://${nexus.proxy.location}/nexus/content/repositories/releases/</url>
    </repository>
    <snapshotRepository>
      <id>snapshot-repo</id>
      <name>Development Snapshot</name>
      <url>http://${nexus.proxy.location}/nexus/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <!--module版本 -->
    <project.version>1.1.1-RELEASE</project.version>
    <welab.common.version>1.7.2-RELEASE</welab.common.version>
    <welab.web.springboot.version>1.0.6-RELEASE</welab.web.springboot.version>
    <welab.dds.version>1.2.11-RELEASE</welab.dds.version>
    <welab-domain-base.version>1.1.6-RELEASE</welab-domain-base.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <org.springframework.version>4.3.9.RELEASE</org.springframework.version>
    <org.springframework.boot>1.5.4.RELEASE</org.springframework.boot>
    <welab.redis.springboot.version>1.0.1-RELEASE</welab.redis.springboot.version>

    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons-collections.version>3.2.1</commons-collections.version>
    <tomcat-embed>8.5.15</tomcat-embed>
    <maven.checkstyle.version>2.17</maven.checkstyle.version>
    <maven.jxr.version>2.5</maven.jxr.version>
    <mockito.version>1.10.19</mockito.version>

    <mybatis.plus.boot.starter.version>3.3.1</mybatis.plus.boot.starter.version>
    <mybatis.plus.generator.version>3.3.1</mybatis.plus.generator.version>

    <welab.crm.base.version>1.0.0-RELEASE</welab.crm.base.version>
    <welab.crm.interview.version>1.1.4-RELEASE</welab.crm.interview.version>
    <welab.collection.interview.version>1.4.6-RELEASE</welab.collection.interview.version>
    <marketing-api.version>2.3.6-RELEASE</marketing-api.version>

    <skipTests>true</skipTests>

  </properties>

  <dependencyManagement>
    <dependencies>

      <!-- 依赖的内部jar包 begin -->
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-common</artifactId>
        <version>${welab.common.version}</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-dds</artifactId>
        <version>${welab.dds.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab.base</groupId>
        <artifactId>welab-springboot-web</artifactId>
        <version>${welab.web.springboot.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-base-api</artifactId>
        <version>${welab.crm.base.version}</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-base-bo</artifactId>
        <version>${welab.crm.base.version}</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-base-common</artifactId>
        <version>${welab.crm.base.version}</version>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-interview-api</artifactId>
        <version>${welab.crm.interview.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-collection-interview-api</artifactId>
        <version>${welab.collection.interview.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-operate-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-crm-operate-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-domain-base</artifactId>
        <version>${welab-domain-base.version}</version>
      </dependency>
      
        <dependency>
            <groupId>com.welab</groupId>
            <artifactId>welab-redis-springboot-starter</artifactId>
            <version>${welab.redis.springboot.version}</version>
        </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>marketing-api</artifactId>
        <version>${marketing-api.version}</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>finance-repayment-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>

      <!-- 依赖的内部jar包 end -->

      <!-- Spring相关依赖 begin -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-aop</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-core</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-beans</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <artifactId>spring-expression</artifactId>
        <groupId>org.springframework</groupId>
        <version>${org.springframework.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${org.springframework.version}</version>
      </dependency>
      <!-- Spring相关依赖 end -->

      <!-- Spring Boot相关依赖 begin -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>snakeyaml</artifactId>
            <groupId>org.yaml</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>tomcat-embed-el</artifactId>
            <groupId>org.apache.tomcat.embed</groupId>
          </exclusion>
          <exclusion>
            <artifactId>tomcat-embed-websocket</artifactId>
            <groupId>org.apache.tomcat.embed</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>${org.springframework.boot}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>1.5.4.RELEASE</version>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <!-- Spring Boot相关依赖 end -->

      <dependency>
        <groupId>javax.ws.rs</groupId>
        <artifactId>javax.ws.rs-api</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.boot.starter.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis.plus.generator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>3.4.5</version>
        <exclusions>
          <!-- 剔除低版本，引用新版本 -->
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>netty</artifactId>
            <groupId>org.jboss.netty</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>2.10.0</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-recipes</artifactId>
        <version>2.10.0</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>dubbo</artifactId>
        <version>2.8.4</version>
        <exclusions>
          <exclusion>
            <artifactId>spring-web</artifactId>
            <groupId>org.springframework</groupId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>javax.el</groupId>
        <artifactId>javax.el-api</artifactId>
        <version>2.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.web</groupId>
        <artifactId>javax.el</artifactId>
        <version>2.2.4</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.1.22</version>
      </dependency>

      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>5.1.46</version>
      </dependency>

      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>1.3</version>
      </dependency>

      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>${commons-collections.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>${tomcat-embed}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.16.18</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>1.5.13</version>
      </dependency>

      <!-- 测试相关依赖 Begin -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>${org.springframework.boot}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
        <version>${org.springframework.boot}</version>
        <exclusions>
          <exclusion>
            <artifactId>json-path</artifactId>
            <groupId>com.jayway.jsonpath</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jsonassert</artifactId>
            <groupId>org.skyscreamer</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <!-- 测试相关依赖 end -->

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-authority-api</artifactId>
        <version>1.3.1-SNAPSHOT</version>
      </dependency>

      <!-- 支持excel -->
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>5.2.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>5.2.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.xmlbeans</groupId>
        <artifactId>xmlbeans</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.18.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>2.18.0</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>welab-lang</artifactId>
        <version>1.1.0-RELEASE</version>
        <exclusions>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
      </dependency>

      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>bank-card-api</artifactId>
        <version>2.1.0-RELEASE</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>3.1.3</version>
      </dependency>

      <dependency>
        <groupId>com.dangdang</groupId>
        <artifactId>elastic-job-lite-core</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.dangdang</groupId>
        <artifactId>elastic-job-lite-spring</artifactId>
        <version>2.1.3</version>
      </dependency>

      <!--审批相关服务-->
      <dependency>
        <groupId>com.welab</groupId>
        <artifactId>approval-center-api</artifactId>
        <version>1.4.7-RELEASE</version>
        <exclusions>
          <exclusion>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.welab</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>

  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
            <encoding>UTF-8</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>2.7</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <!--所有子模块都要执行的plugin -->
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <modules>
    <module>welab-crm-operate-api</module>
    <module>welab-crm-operate-core</module>
    <module>welab-crm-operate-web</module>
  </modules>
</project>
